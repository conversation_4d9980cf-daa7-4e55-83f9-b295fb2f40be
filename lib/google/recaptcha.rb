# frozen_string_literal: true

module Google
  class Recaptcha
    # rubocop:disable Naming/MethodName
    # Disabling to allow error-codes since this is what comes back from the API
    Response = Struct.new(:success, :score, :action, :challenge_ts, :hostname, :'error-codes') do
      def score_below?(threshold)
        score < threshold
      end

      def success?
        success
      end
    end
    # rubocop:enable Naming/MethodName

    def initialize(key:)
      @key = key
    end

    def verify(token)
      send_request(token)
    end

    private

    def send_request(token)
      uri = URI.parse(verify_url(token))
      response = Net::HTTP.get_response(uri)
      JSON.parse(response.body, object_class: Response)
    rescue StandardError => e
      Rollbar.error("Cannot send verification request: #{StandardError}", e)
    end

    def verify_url(token)
      "https://www.google.com/recaptcha/api/siteverify?secret=#{@key}&response=#{token}"
    end
  end
end

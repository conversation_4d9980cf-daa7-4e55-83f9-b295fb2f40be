# frozen_string_literal: true

module Mailchimp
  class Gateway
    def initialize(client)
      @client = client
    end

    def sync_subscriber(request)
      add_or_update_member(request)
      update_tags(request)
    end

    private

    def add_or_update_member(request)
      @client.lists.set_list_member(request.list_id, request.subscriber_hash, request.member_body_params)
    end

    def update_tags(request)
      @client.lists.update_list_member_tags(request.list_id, request.subscriber_hash, request.tags_body_params)
    end
  end
end

# frozen_string_literal: true

module CoreExtensions
  module Prawn
    module Table
      module Cell
        module Text
          # This implementation is needed to fix vertical alignment.
          # See https://github.com/rsynnest/prawn-table/commit/326b6222b646a97078eb09ce35d10f249806ecb8
          def draw_content
            with_font do
              case @text_options[:valign]
              when :top
                @pdf.move_up((@pdf.font.line_gap + @pdf.font.descender) / 2)
              when :bottom
                @pdf.move_down(@pdf.font.line_gap + (@pdf.font.descender / 2))
              else
                @pdf.move_up((@pdf.font.line_gap + @pdf.font.descender) / 4)
              end
              with_text_color do
                text_box(width: spanned_content_width + ::Prawn::Table::Cell::Text::FPTolerance,
                         height: spanned_content_height + ::Prawn::Table::Cell::Text::FPTolerance,
                         at: [0, @pdf.cursor]).render
              end
            end
          end
        end
      end
    end
  end
end

# frozen_string_literal: true

module Mailchimp
  DEFAULT_LANGUAGE = 'en'

  def self.sync_subscriber(user)
    request = SyncSubscriberRequest.new(list_id: default_list, user:)

    gateway.sync_subscriber(request)
  end

  def self.client
    api_key = Rails.application.credentials.mailchimp_api_key!
    server = ENV.fetch('MAILCHIMP_API_SERVER', nil)

    MailchimpMarketing::Client.new(api_key:, server:)
  end

  def self.default_group_ids
    ENV.fetch('MAILCHIMP_DEFAULT_GROUP_IDS', '').split(',')&.map(&:strip)
  end

  def self.default_list
    ENV.fetch('MAILCHIMP_MASTER_AUDIENCE_LIST_ID', nil)
  end

  def self.gateway
    Gateway.new(client)
  end

  def self.interest_categories
    response = client.lists.get_list_interest_categories(default_list)

    to_open_struct(response['categories'])
  end

  def self.interest_groups(category_id)
    response = client.lists.list_interest_category_interests(default_list, category_id)

    to_open_struct(response['interests'])
  end

  def self.to_open_struct(response)
    JSON.parse(response.to_json, object_class: OpenStruct)
  end
end

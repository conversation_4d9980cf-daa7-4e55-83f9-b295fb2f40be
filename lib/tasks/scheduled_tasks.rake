# frozen_string_literal: true

namespace :scipi do
  desc 'Sends the opportunities newsletter to all active subscribers'
  task send_opportunities_newsletter: :environment do
    Communications::OpportunitiesNewsletterCoordinatingJob.perform_later
  end

  desc 'Sends a preview of the opportunities newsletter to admins'
  task send_opportunities_preview: :environment do
    list_id = ENV.fetch(Communications::OpportunitiesNewsletterCoordinatingJob::DISTRIBUTION_LIST_ENV_VAR_NAME, nil)
    if Communications::DistributionList.where(id: list_id).where('(NOW()::date - last_delivery_attempted_at::date) = 13').exists?
      Communications::OpportunitiesNewsletterCoordinatingJob.perform_later(preview_recipients: Role.admin.users.to_a)
    end
  end

  namespace :experts do
    desc 'Finds, creates or updates SuggestedExpertise'
    task refresh_suggested_expertise: :environment do
      Experts::RefreshSuggestedExpertiseJob.perform_later
    end

    desc 'Sends confirmation reminders to unconfirmed accounts that are at least a week old'
    task send_confirmation_reminders: :environment do
      Experts::SendConfirmationRemindersJob.perform_later
    end
  end

  namespace :notifications do
    desc 'Sends a daily digest of activity notifications to subscribers'
    task send_activity_digest: :environment do
      Notifications::DailyActivityDigestJob.perform_later
    end
  end

  namespace :pings do
    desc 'Enqueues voting-open notifications for subscribers to a paid Ping'
    task send_voting_open_notifications: :environment do
      Pings::EnqueueVotingOpenNotificationsJob.perform_later
    end
  end

  namespace :reputation do
    desc 'Refreshes the reputation metrics materialized view'
    task refresh_metrics: :environment do
      Reputation::Metric.refresh
    end
  end

  namespace :scipis do
    desc 'Performs housekeeping tasks after question rounds close'
    task back_up_round_submissions: :environment do
      Scipis::BackUpRoundSubmissionsJob.perform_later
    end

    desc 'Performs housekeeping tasks after question rounds close'
    task reset_round_submissions: :environment do
      Scipis::ResetRoundSubmissionsJob.perform_later
    end

    desc 'Enqueues survey midpoint reminder emails'
    task send_survey_midpoint_reminders: :environment do
      Scipis::Reminders::SendSurveyMidpointRemindersJob.perform_later
    end

    desc 'Enqueues survey closing reminder emails'
    task send_survey_closing_reminders: :environment do
      Scipis::Reminders::SendSurveyClosingRemindersJob.perform_later
    end

    desc 'Enqueues debate open notification emails'
    task send_debate_open_emails: :environment do
      Surveys::SendDebateOpenEmailJob.perform_later
    end

    desc 'Enqueues debate summary emails'
    task send_debate_summary_emails: :environment do
      Surveys::SendDebateSummaryEmailJob.perform_later
    end

    desc 'Enqueues debate closing reminder emails'
    task send_debate_closing_reminder_emails: :environment do
      Surveys::SendDebateClosingReminderEmailJob.perform_later
    end

    desc 'Send all pending notification emails'
    task send_round_lifecycle_emails: :environment do
      Scipis::Rounds::SendLifecycleEmailsJob.perform_later
    end

    desc 'Send round-opening-soon emails to SciPi admins'
    task send_round_opening_soon_emails: :environment do
      Scipis::SendRoundOpeningSoonEmailsToAdminsJob.perform_later
    end
  end
end

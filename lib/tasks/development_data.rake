# frozen_string_literal: true

namespace :scipi do
  namespace :dev_data do
    desc 'Creates a totally empty SciPi'
    task empty_scipi: :environment do
      created_by = Role.scipi_admin.users.first
      branding = Surveys::Branding.scipi
      name = 'Totally Empty SciPi'
      description = 'A totally empty SciPi with keywords, or applicants, used for testing the initial state of a SciPi'

      branding.surveys.create!(name:, description:, created_by:, invite_only: true)
    end
  end
end

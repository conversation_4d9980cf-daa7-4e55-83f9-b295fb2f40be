# frozen_string_literal: true

require 'elasticsearch/rails/tasks/import'

namespace :scipi do
  namespace :search do
    if Rails.env.development?
      desc 'Deletes and recreates each index, and indexes all models'
      task rebuild_and_index: :environment do
        [User].each do |model_class|
          model_class.__elasticsearch__.delete_index!
          model_class.__elasticsearch__.create_index!

          model_class.import
        end
      end
    end
  end
end

# frozen_string_literal: true

namespace :responses do
  desc 'Update submitted_at for all previus responses'
  task update_submitted_at: :environment do
    val = ActiveRecord::Base.connection.execute('update answer_groups set submitted_at = updated_at')
    puts "#{val.cmd_status.split[1]} answer_groups updated to have submitted_at = updated_at."
    puts "\n\nTASK COMPLETED\n##############\n"
  end
end

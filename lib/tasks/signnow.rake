# frozen_string_literal: true

require 'terminal-table'

namespace :signnow do
  desc 'Fetch and store an access token'
  task generate_access_token: :environment do
    Rails.logger.info 'Fetching SignNow access token'

    response = SignNow.generate_access_token

    resource_server_name = SignNow.resource_server_name
    access_token = response['access_token']
    expires_at = response['expires_in'].seconds.from_now

    OAuthTokens::ServerToken.create_or_refresh_token!(resource_server_name:, access_token:, expires_at:)

    Rails.logger.info 'Fetching SignNow access token - COMPLETE'
  end

  desc 'Check the SignNow environment'
  task env: :environment do
    gateway = SignNow::Gateway.new
    root_folder = gateway.folders

    top_level_folders = root_folder['folders']

    rows = []

    configuration = SignNow.configuration

    # Documents folder
    documents_folder = top_level_folders.find { |folder| folder['name'] == 'Documents' }
    raise 'Documents folder not found' unless documents_folder

    documents_folder_id = documents_folder['id']

    documents_folder_status = if configuration.documents_folder_id.present?
                                if configuration.documents_folder_id == documents_folder_id
                                  '✅ OK'
                                else
                                  '🚫 API and ENV values do not match'
                                end
                              else
                                '🚫 ENV not set'
                              end
    rows << [
      'SIGN_NOW_DOCUMENTS_FOLDER_ID',
      documents_folder_status,
      configuration.documents_folder_id,
      documents_folder_id.presence || '--'
    ]

    table = Terminal::Table.new(
      headings: ['ENV variable', 'Status', 'ENV Value', 'API Value'],
      rows:
    )

    puts table
  end
end

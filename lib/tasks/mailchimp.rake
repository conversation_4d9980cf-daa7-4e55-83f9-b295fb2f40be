# frozen_string_literal: true

require 'terminal-table'

namespace :mailchimp do
  desc 'Show the default list (MAILCHIMP_MASTER_AUDIENCE_LIST_ID) for this environment'
  task default_list: :environment do
    response = Mailchimp.client.lists.get_list(Mailchimp.default_list, fields: %w[name id])

    puts "The default list of this environment is '#{response['name']}' (ID: #{response['id']})."
  end

  desc 'Show groups'
  task groups: :environment do
    categories = Mailchimp.interest_categories

    rows = []

    categories.each do |category|
      groups = Mailchimp.interest_groups(category.id)

      groups.each do |group|
        is_default = Mailchimp.default_group_ids.include?(group['id'])

        rows << [category.id, category.title, group.id, group.name, is_default]
      end
    end

    table = Terminal::Table.new(
      headings: ['Category ID', 'Category Title', 'Group ID', 'Group ID', 'Default?'],
      rows:
    )

    puts table
  end

  desc 'Show all Mailchimp Lists'
  task lists: :environment do
    response = Mailchimp.client.lists.get_all_lists

    rows = response['lists'].map { |list| [list['name'], list['id'], list['web_id']] }

    table = Terminal::Table.new(headings: ['List', 'API ID', 'Web ID'], rows:)
    puts table
  end

  desc 'Ping Mailchimp API'
  task ping: :environment do
    pp Mailchimp.client.ping.get
  end
end

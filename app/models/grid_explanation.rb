# frozen_string_literal: true

class GridExplanation
  include ActiveModel::Model

  attr_accessor :panelist, :answer, :content, :scitrust_score

  def answered? = !answer.skipped?

  def expert_number
    "Expert #{panelist.expert_number}"
  end

  def scitrust_score?
    scitrust_score.present?
  end

  def to_response_array
    grid_response = answer.to_grid_response

    grid_response.to_array
  end
end

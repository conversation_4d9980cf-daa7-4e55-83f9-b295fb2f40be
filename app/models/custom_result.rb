# frozen_string_literal: true

class CustomResult < ApplicationRecord
  include ChartImage, DebateTopic, Explainable, Summarizable

  DEFAULT_CHART_HEIGHT = 600

  VALID_CONTENT_TYPES = %i[doc pdf ppt jpg gif png].freeze

  belongs_to :scipi,
             class_name: 'QuestionGroup',
             foreign_key: 'question_group_id',
             inverse_of: :result_definitions

  # DEPRECATED: Do not use question_group anymore. Use :survey
  belongs_to :question_group
  belongs_to :section,
             class_name: 'ResultSection',
             optional: true
  belongs_to :survey,
             class_name: 'QuestionGroup',
             foreign_key: 'question_group_id',
             inverse_of: :result_sections
  belongs_to :question, optional: true
  belongs_to :render_type,
             class_name: 'ResultDefinitionRenderType',
             inverse_of: :result_definitions
  belongs_to :result_type,
             class_name: 'ResultDefinitionType',
             inverse_of: :result_definitions
  has_many :data_points,
           foreign_key: 'result_id',
           dependent: :destroy,
           inverse_of: :result
  has_many :result_questions,
           dependent: :destroy,
           foreign_key: 'result_definition_id',
           inverse_of: :result_definition
  has_many :questions, through: :result_questions

  has_one_attached :attachment

  validates :attachment,
            presence: { if: -> { result_type.name == 'file_attachment' } },
            content_type: {
              message: 'Your attachment can only be a document (PDF, Word, Powerpoint) or image (JPG, GIF, PNG)',
              type: VALID_CONTENT_TYPES
            },
            file_size: {
              message: 'Your attachment can be no bigger than 10MB',
              max: 10.megabytes
            }

  default_scope { order('custom_results.position ASC') }

  scope :order_by_id, -> { order(:id) }
  scope :grouped, -> { where.not(filter_by_question: nil) }
  scope :ungrouped, -> { where(filter_by_question: nil) }

  delegate :filename, to: :attachment, prefix: true
  delegate :grid_checkbox_type?,
           :grid_radio_type?,
           :grid_select_type?,
           :question_text,
           :weighted_sum?,
           :weighted_sum_label,
           to: :question,
           allow_nil: true
  delegate :administered_by?, :comments_close_date, :debate_open?, to: :survey
  delegate :id, to: :survey, prefix: true
  delegate :descriptive_statistics?,
           :diagnostic?,
           :file_attachment?,
           :free_form?,
           :grid_responses?,
           :json_template,
           :multi_question_score?,
           :question_based?,
           :result_template,
           :submission_score?,
           :summarizable?,
           :scitrust_score?,
           to: :result_type
  delegate :file_upload?, to: :question, allow_nil: true
  delegate :bar_chart?, :doughnut_chart?, :horizontal_bar_chart?, :pie_chart?, to: :render_type
  delegate :value, to: :render_type, prefix: true

  alias survey question_group
  alias survey= question_group=

  def self.comments?
    joins(:comments).exists?
  end

  def self.greater_than(position)
    where('position > ?', position)
  end

  def self.greater_than_or_equal_to(position)
    where('position >= ?', position)
  end

  def self.less_than(position)
    where('position < ?', position)
  end

  def self.less_than_or_equal_to(position)
    where('position <= ?', position)
  end

  def self.exclude(result)
    where.not(id: result.id)
  end

  def self.for_question_group(question_group)
    where(question_group_id: question_group.id)
  end

  def answer_choice_labels
    question.answer_choices.pluck(:label)
  end

  def base_question
    # This is the opposite of what it should be! These terms are used
    # backwards in the system
    # See group_by_question.
    question
  end

  def chart?
    render_type&.chart?
  end

  def chart_data(show_toolbar: false)
    chart_data_query.result(show_toolbar:)
  end

  def chart_data_query
    query_class_name = "Results::ChartData::#{result_type_name.classify}Query"
    query_class = query_class_name.constantize
    @chart_data_query ||= query_class.new(self)
  end

  def chart_type
    if render_type.bar_chart?
      'bar'
    elsif render_type.doughnut_chart?
      'doughnut'
    else
      render_type.system_name
    end
  end

  def create_comment!(author, content:)
    comments.create!(user: author, content:)
  end

  def details
    question&.question_details
  end

  def hidden?
    section&.hidden?
  end

  def lastmod
    updated_at
  end

  def result_id
    id
  end

  # This might belong elsewhere
  def live_view_path
    "#{Rails.application.routes.url_helpers.survey_result_path(survey_id: question_group_id, id:)}?live=true"
  end

  # ORIGINAL usage of number
  # def number
  #   position
  # end

  # NEW usage of number to match question
  def number
    if section.present?
      "#{section.position}.#{position}"
    else
      position.to_s
    end
  end

  def parent
    section || survey
  end

  def question_number
    if question
      question.number
    else
      'n/a'
    end
  end

  def question_id
    if question
      question.id
    else
      'n/a'
    end
  end

  def file_download?
    if render_type
      render_type.file_download?
    else
      false
    end
  end

  def final_answer_count
    return 0 unless question&.final_answers

    question.final_answers.count { |element| !element.skipped? }
  end

  def grid?
    # Remove the question once default results are gone
    render_type&.grid? || question&.grid_type?
  end

  def grouped?
    !filter_by_question.nil?
  end

  delegate :groupable?, to: :base_question

  # This is really the base question. See #base_question
  def group_by_question
    Question.find_by(id: group_by_question_id)
  end

  def group_by_question_id=(id)
    self.filter_by_question = id
  end

  def group_by_question_id
    filter_by_question
  end

  def ranked_order_plot?
    render_type&.ranked_order_plot?
  end

  def question?
    question.present?
  end

  def explanations?
    question&.allow_answer_explanation && question&.answers&.with_explanation&.any?
  end

  # used anywhere?
  def question_details
    if question
      question.question_details
    else
      ''
    end
  end

  def result_data_summary?
    result_type.scitrust_score? || result_type.descriptive_statistics?
  end

  def result_type_name
    result_type.system_name
  end

  def requires_title?
    result_type&.title?
  end

  def section_name
    ''
  end

  def section_position
    0
  end

  def show_details?
    self['show_question_details'] && details.present?
  end

  def text?
    # Remove question once default results are gone
    render_type&.text? || question&.long_type?
  end

  def title
    if result_type&.title?
      self['title']
    elsif result_type&.allowed_grouping_question_types&.any?
      "\"#{question.title}\" #{result_type.name}"
    elsif grouped? # This is the original grouped question (that is still backwards)
      "#{question.title} Grouped By #{group_by_question.title}"
    else
      question.title
    end
  end
end

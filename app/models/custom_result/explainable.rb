# frozen_string_literal: true

module CustomResult::Explainable
  extend ActiveSupport::Concern

  def grid_explanations?
    return false unless explanations?

    result_type.grid_responses? || result_type.scitrust_score?
  end

  def grid_explanations
    return [] unless explanations?

    answers = question.final_answers.with_rich_text_explanation.sort_by(&:user_display_id)

    answers.map do |answer|
      panelist = survey.panelists.find_by(user_id: answer.submitter.id)
      content = explanation_content(answer.explanation)

      scitrust_score = if result_type.scitrust_score?
                         answer = answer.scitrust_score

                         # answer_choices = question.answer_choices
                         #
                         # answer_rows = (0...question.row_count).map do |row_num|
                         #   answer.row_at(row_num)
                         # end
                         #
                         # answer_values = answer_rows.flatten.map do |label|
                         #   answer_choices.find { |ac| ac.label == label }&.value
                         # end
                         #
                         Math.geometric_mean(answer_values) if answer_values.none?(&:nil?)
                       end

      GridExplanation.new(panelist:, answer:, content:, scitrust_score:)
    end
  end

  def grouped_explanations
    scipi = question.scipi
    answer_choices = question.answer_choices

    answer_explanation_groups = answer_choices.map.with_index do |answer_choice, answer_choice_index|
      answer_with_explanations = question
                                 .final_answers
                                 .joins(:selected_choices)
                                 .with_rich_text_explanation
                                 .where(selected_choices: { answer_choice_id: answer_choice.id })

      explanations = answer_with_explanations.map do |answer|
        panelist = scipi.panelists.find_by!(user_id: answer.answer_group.user_id)
        content = explanation_content(answer.explanation)

        Explanation.new(content:, panelist:)
      end

      ExplanationGroup.new(
        title: answer_choice.label,
        group_index: answer_choice_index,
        explanations: explanations.sort_by(&:expert_number)
      )
    end

    answer_explanation_groups.reject { |answer| answer.explanations.empty? }
  end

  def grouped_explanations?
    return false unless explanations?

    doughnut_chart?
  end

  def ungrouped_explanations
    return [] unless explanations?

    answers = question.final_answers.with_rich_text_explanation.sort_by(&:user_display_id)

    answers.map do |answer|
      panelist = survey.panelists.find_by(user_id: answer.submitter.id)
      content = explanation_content(answer.explanation)

      Explanation.new(panelist:, answer:, content:)
    end
  end

  private

  def explanation_content(explanation)
    content = explanation.to_prawn_formatted_text(default_color: '8E8B86', link_color: Scipis::Reports::Component::LINK_COLOR)

    return [{ text: 'No explanation provided.', styles: [:italic], color: '8E8B86' }] unless content.present?

    content
  end
end

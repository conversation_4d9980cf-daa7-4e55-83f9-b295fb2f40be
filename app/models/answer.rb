# frozen_string_literal: true

class Answer < ApplicationRecord
  WeightedScoreNotSupported = Class.new(StandardError)
  include Attachment, Grid

  belongs_to :question
  belongs_to :answer_group, inverse_of: :answers
  belongs_to :section, inverse_of: :answers, optional: true

  has_many :selected_choices, dependent: :destroy
  has_many :answer_choices, through: :selected_choices

  has_rich_text :text_answer_content
  has_rich_text :explanation

  scope :final, -> { joins(:answer_group).merge(AnswerGroup.submitted) }

  scope :with_explanation, lambda {
    with_rich_text_explanation.where.not(action_text_rich_texts: { record_id: nil })
  }
  scope :with_text_answer_content, lambda {
    with_rich_text_text_answer_content.where.not(action_text_rich_texts: { record_id: nil })
  }

  validates :years_of_experience,
            presence: { if: ->(answer) { answer.required? && answer.validate_years_of_experience? } },
            numericality: { only_integer: true, greater_than: 0, allow_blank: true, if: :validate_years_of_experience? }
  validate :validate_required_answer
  validate :validate_max_selected_choices, if: ->(answer) { answer.question.limit_selection? }
  validate :validate_ranked_choices, if: ->(answer) { answer.question.ranked_choice_type? }

  with_options to: :question do
    delegate :administered_by?, :question_text, :required?, :supports_weighted_score?
    delegate :type, :weight, prefix: true
  end

  delegate :submitter, :survey, :user_display_id, to: :answer_group

  alias submission answer_group
  alias submission= answer_group=

  (1..Questions::Grid::ROW_LIMIT).each do |row|
    (1..Questions::Grid::COLUMN_LIMIT).each do |col|
      field_name = :"cell_#{row}_#{col}_answer"
      attr_accessor field_name
    end
  end

  after_save do
    answer_group.update_attribute(:updated_at, Time.zone.now)
  end

  after_initialize do
    if respond_to? :answer_text_on_1st_row
      split_answer_text_on_rows
    end
  end

  before_save :destroy_blank_rich_text
  after_commit :copy_to_profile, if: -> { question.copy_to_profile? }

  # Smush answer contents into array or string for use by the CSV exporter
  def for_csv_formatter
    if question.grid_type?
      answer_text_array
    elsif question.file_upload?
      attachment.attached? ? attachment_filename : '(None uploaded)'
    elsif question.ranked_choice_type?
      selected_choices.ranked.map { |choice| "#{choice.rank}) #{choice.answer_choice.label}" }.join(', ')
    elsif question.aggregatable_type?
      answer_choices.map(&:label).join(',')
    else
      text_answer_content.to_plain_text
    end
  end

  def skipped?
    if question.checkbox_type? || question.radio_type?
      selected_choices.none?
    elsif question.long_type?
      text_answer_content.blank?
    elsif question.grid_radio_type? || question.grid_checkbox_type?
      skipped_grid_radio_checkbox_answer?
    elsif question.grid_select_type?
      skipped_grid_select_answer?
    elsif question.file_upload?
      !attachment.attached?
    else
      question.skipped?(self)
    end
  end

  def any_data?
    return true if explanation.present?
    return true if text_answer_content.present?
    return true if answered_grid_answer?
    return true if selected_choices.any?
    return true if attachment&.attached?

    false
  end

  def submitted_by?(user)
    submitted_by == user
  end

  def submission_id
    answer_group_id
  end

  def submitted_by
    submitter
  end

  def survey_id
    question.question_group_id
  end

  def validate_years_of_experience?
    question.is_a?(Questions::ProfileYearsOfExperience) || question.is_a?(Questions::ProfileEmploymentSector)
  end

  def weighted_score
    assert_supports_weighted_score!

    answer_score
  end

  private

  def answer_score
    if question.radio_type?
      return nil if answer_choices.first.nil?

      answer_choices.first.value * question_weight
    else # assume WSG, for now
      wsg_answer = WeightedSumGridAnswer.new(
        question:,
        answer: self
      )
      wsg_answer.score
    end
  end

  def copy_to_profile
    Surveys::CopyToProfileJob.perform_later(self)
  end

  def destroy_blank_rich_text
    explanation.destroy if explanation.body.blank?
    text_answer_content.destroy if text_answer_content.body.blank?
  end

  def validate_ranked_choices
    return if selected_choices.empty?

    answer_choice_ids = selected_choices.map(&:answer_choice_id)
    if answer_choice_ids.uniq.length != answer_choice_ids.length
      errors.add(:base, 'Each answer choice can only be ranked once')
    end

    ranks = selected_choices.filter_map(&:rank)
    if ranks.uniq.length != ranks.length
      errors.add(:base, 'Each rank can only be used once')
    end

    unless ranks.all? { |rank| rank.to_i.between?(1, selected_choices.length) }
      errors.add(:base, "Ranks must be between 1 and #{selected_choices.length}")
    end
  end

  def validate_required_answer
    errors.add(:base, 'Answer(s) required') if required? && skipped?
  end

  def validate_max_selected_choices
    errors.add(:base, 'Too many selections') if selected_choices.count > question.max_selected_choices
  end
end

# frozen_string_literal: true

module <PERSON>ipis
  module Reports
    class GridExplanation
      include Component

      def initialize(document, explanation)
        @document = document
        @explanation = explanation

        super
      end

      def height
        with_font 'Lato', size: 10 do
          heading_height = height_of_formatted([{ text: @explanation.expert_number, color: '474744', font: 'Lato', size: 10 }])

          table = make_answer_table(@explanation)

          content_height = height_of_formatted(content, content_options)

          heading_height + table.height + content_height + (HALF_SPACER_HEIGHT * 2)
        end
      end

      def render
        with_font 'Lato', size: 10 do
          first_line = [{ text: @explanation.expert_number, color: '474744', styles: [:bold] }]
          first_line = if @explanation.scitrust_score?

                         score = @explanation.scitrust_score
                         score_color = if score.positive?
                                         '008ffb'
                                       else
                                         'ff8228'
                                       end
                         formatted_score = score.present? ? format('%<score>.2f', score:) : 'N/A'

                         first_line << { text: '  |  ', color: '8E8B86' }
                         first_line << { text: 'Score: ', color: '474744', styles: [:bold] }
                         first_line << { text: formatted_score, color: score_color }
                       end

          formatted_text(first_line)

          spacer(:half)

          table = make_answer_table(@explanation)
          table.draw

          spacer(:half)

          formatted_text(content, content_options)
        end
      end

      private

      def content_options(**additional_options)
        { leading: 1.5 }.merge(additional_options)
      end

      def content
        @explanation.content
      end

      def make_answer_table(explanation)
        table_data = explanation.to_response_array

        make_table(table_data, table_options) do
          row(0).font_style = :bold
          columns(0).font_style = :bold
          row(0).text_color = '474744'
          columns(0).text_color = '474744'
        end
      end

      def table_options
        {
          cell_style: {
            align: :center,
            border_color: '969191',
            border_width: 0.25,
            font: 'Lato',
            size: 8,
            text_color: '8E8B86',
            valign: :center
          }
        }
      end
    end
  end
end

# frozen_string_literal: true

module <PERSON>ip<PERSON>
  module Reports
    class GroupedExplanations < AbstractExplanations
      ANSWER_DOT_RADIUS = 6
      # TODO: This is the palette used for doughnut charts. DRY me up please.
      COLOR_PALETTE = %w[008ffb 00e396 feb019 ff4560 775dd0].freeze
      EXPLANATION_INDENT = ANSWER_DOT_RADIUS * 3

      def initialize(document, explanation_groups, page_header_height: 0)
        super(document, page_header_height:)

        @explanation_groups = explanation_groups
      end

      private

      def first_explanation_height
        first_group = @explanation_groups.first

        height_of_formatted([{ text: first_group.title, color: '474744', font: 'Raleway', size: 10, styles: [:bold] }])
      end

      def explanation_content(explanation)
        [
          { text: explanation.expert_number, color: '474744', font: 'Lato', size: 10, styles: [:bold] },
          { text: '  |  ', color: '8E8B86', font: 'Lato', size: 10 },
          *explanation.content
        ]
      end

      def explanation_height(explanation)
        content = explanation_content(explanation)

        with_font 'Lato', size: 10 do
          height_of_formatted(content, explanation_content_options)
        end
      end

      def explanation_content_options
        super(at: [EXPLANATION_INDENT, cursor])
      end

      def render_group_heading(group, continuation: false)
        label = continuation ? "#{group.title}, continued" : group.title

        content = [{ text: label, font: 'Raleway', color: '474744', size: 10, styles: [:bold] }]
        content_height = height_of_formatted(content)

        group_color = COLOR_PALETTE[group.group_index % COLOR_PALETTE.size]

        render_group_color_dot(color: group_color)
        formatted_text_box(content, at: [EXPLANATION_INDENT, cursor])

        move_down content_height
      end

      def render_explanation(explanation)
        content = explanation_content(explanation)

        with_font 'Lato', size: 10 do
          formatted_text_box(content, explanation_content_options)
        end
      end

      def render_explanations
        @explanation_groups.each do |group|
          # When starting a new group we need to check if the
          # group label and explanation will fit on the page.
          # This is also to avoid an awkward page break between
          # the two.
          #
          # This is a bit redundant in the case of the first group,
          # but there is no easy way around it.
          unless will_initial_answer_content_fit?(group)
            start_new_result_page

            render_section_heading(continued: true)
          end

          spacer(:half)

          render_group_heading(group) if group&.title.present?

          group.explanations.each do |explanation|
            height_of_explanation = explanation_height(explanation)

            # If the current explanation will not fit on the page,
            # start a new page and render continuation headers
            #
            # TODO: Handle the following cases:
            #       - An explanation that is too long to fit on a page, in which case we
            #         need to break it across multiple pages
            #       - An long explanation will will leave too much white space if we start a new page.
            #         In this case break it too.
            if height_of_explanation > cursor
              start_new_result_page

              render_section_heading(continued: true)

              spacer

              # TODO: Add an abstraction here, for grouped explanations. Perhaps even
              #       a subtype of this component for grouped explanations
              render_group_heading(group, continuation: true) if group&.title.present?
            end

            spacer(:half)

            render_explanation(explanation)

            move_down height_of_explanation
          end
        end
      end

      def will_initial_answer_content_fit?(group)
        title_height = if group.title.present?
                         height_of_formatted([{ text: group.title, font: 'Raleway', color: '474744', size: 10, style: :bold }])
                       else
                         0
                       end

        first_explanation_height = with_font 'Lato', size: 10 do
          height_of_formatted(explanation_content(group.explanations.first))
        end

        content_height = title_height + HALF_SPACER_HEIGHT + first_explanation_height

        cursor > content_height
      end

      def render_group_color_dot(color:)
        with_fill_color(color) do
          fill_circle [ANSWER_DOT_RADIUS, cursor - ANSWER_DOT_RADIUS], ANSWER_DOT_RADIUS
        end
      end
    end
  end
end

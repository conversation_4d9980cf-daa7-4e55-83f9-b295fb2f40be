# frozen_string_literal: true

module <PERSON><PERSON>is
  module Reports
    class GridVisualization < BaseVisualization
      HEADER_BG_COLOR = 'e9eef2'
      TOTAL_HEADING = 'TOTAL'

      def render
        render_visualization_header
        render_grid
      end

      private

      def render_grid
        query = build_grid_query
        result = query.result

        table_data = build_table_data(result)
        table_options = build_table_options(result)

        table(table_data, table_options) do
          row(0).background_color = HEADER_BG_COLOR
          column(0).align = :left
        end
      end

      def build_grid_query
        if result_definition.grid_radio_type?
          Results::RadioGridResultQuery.new(result_definition)
        elsif result_definition.grid_checkbox_type?
          Results::CheckboxGridResultQuery.new(result_definition)
        elsif result_definition.grid_select_type?
          Results::SelectGridResultQuery.new(result_definition)
        else
          raise 'Unknown grid result type'
        end
      end

      def build_table_data(result)
        table_data = []
        table_data << build_header_row(result)

        result.each_row do |row|
          table_data << build_data_row(row)
        end

        table_data
      end

      def build_header_row(result)
        [''] + result.column_names.map(&:upcase) + [TOTAL_HEADING]
      end

      def build_data_row(row)
        row_cells = [row.name]

        row.each_cell do |cell|
          row_cells << if cell.is_a?(::Results::GridResult::SingleValueCell)
                         if cell.respond_to?(:percent)
                           "#{cell.percent.round(1)}%\n#{cell.count}"
                         else
                           cell.count
                         end
                       else
                         cell.line_items.map do |line_item|
                           "#{line_item.label} (#{line_item.count})"
                         end.join("\n")
                       end
        end

        row_cells << row.total
        row_cells
      end

      def build_table_options(result)
        width_available = bounds.width
        label_column_width = 100
        total_column_width = 35
        data_column_width = (width_available - label_column_width - total_column_width) / result.column_names.length

        column_count = result.column_names.length + 2 # +2 for row label and total columns
        cell_width = bounds.width / column_count

        cell_style = {
          align: :center,
          border_color: '969191',
          border_width: 0.25,
          size: 8,
          text_color: '4f4e4e',
          valign: :center,
          width: cell_width
        }

        column_widths = [label_column_width, *([data_column_width] * result.column_names.length), total_column_width]
        {
          cell_style: cell_style,
          column_widths: column_widths,
          header: true,
          width: bounds.width
        }
      end
    end
  end
end

# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Reports
    module Component
      extend ActiveSupport::Concern

      # Colors
      BRAND_BLUE = '0b5c9f' # aka SciPi blue
      LINK_COLOR = BRAND_BLUE
      NAVY = '1e2e4d'
      WHITE = 'ffffff'

      # Spacers
      DEFAULT_SPACER_HEIGHT = 18
      HALF_SPACER_HEIGHT = DEFAULT_SPACER_HEIGHT / 2

      included do
        include Prawn::View
        include PrawnGraphicsHelpers
      end

      def initialize(...)
        super()

        initialize_fonts
      end

      def render_divider(color:, line_width: 0.5)
        with_stroke_color color do
          with_line_width line_width do
            stroke_horizontal_rule
          end
        end
      end

      def spacer(distance = :default)
        height = case distance
                 when :half
                   HALF_SPACER_HEIGHT
                 else
                   DEFAULT_SPACER_HEIGHT
                 end

        move_down height
      end

      def start_new_result_page
        start_new_page(
          top_margin: Result::TOP_MARGIN,
          bottom_margin: 36,
          left_margin: Result::SIDE_MARGIN,
          right_margin: Result::SIDE_MARGIN
        )
      end

      private

      def initialize_fonts
        font_dir = Rails.root.join('app/assets/fonts').to_s

        if font_families['Raleway'].blank?
          font_families['Raleway'] = {
            normal: { file: "#{font_dir}/Raleway-Regular.ttf", subset: false }, # 400
            medium: { file: "#{font_dir}/Raleway-Medium.ttf", subset: false }, # 500
            semibold: { file: "#{font_dir}/Raleway-SemiBold.ttf", subset: false }, # Weight 600
            bold: { file: "#{font_dir}/Raleway-Bold.ttf", subset: false } # Weight 700
          }
        end

        if font_families['Lato'].blank?
          font_families['Lato'] = {
            light: { file: "#{font_dir}/Lato-Light.ttf", subset: false },
            normal: { file: "#{font_dir}/Lato-Regular.ttf", subset: false },
            bold: { file: "#{font_dir}/Lato-Bold.ttf", subset: false },
            italic: { file: "#{font_dir}/Lato-Italic.ttf", subset: false },
            bold_italic: { file: "#{font_dir}/Lato-BoldItalic.ttf", subset: false }
          }
        end
      end
    end
  end
end

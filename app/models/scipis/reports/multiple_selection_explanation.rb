# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Reports
    class MultipleSelectionExplanation
      include Component

      def initialize(document, explanation)
        @document = document
        @explanation = explanation

        super
      end

      def height
        with_font 'Lato', size: 10 do
          height_of_formatted(content, content_options)
        end
      end

      def render
        with_font 'Lato', size: 10 do
          formatted_text(content, content_options)
        end
      end

      private

      def content
        # Because the label sides push out into the space surrounding the label text,
        # there needs to be a little bit of extra white space between the pipe separators
        # and the label.
        answer_labels

        text_content = [{ text: @explanation.expert_number, color: '474744', font: 'Lato', size: 10, styles: [:bold] }]
        text_content << divider
        text_content += answer_labels
        text_content << divider
        text_content + @explanation.content
      end

      def divider
        { text: '  |   ', color: '8E8B86', font: 'Lato', size: 10 }
      end

      def answer_labels
        return [{ text: 'Did not answer', color: '8E8B86', font: 'Lato', size: 10, styles: [:italic] }] if @explanation.answered?

        labels = answer.selected_choices.joins(:answer_choice).order(:position).map do |selected_choice|
          { text: selected_choice.label, color: 'ffffff', font: 'Lato', size: 10, callback: LabelCallback.new(color: '1da2ff', document: self) }
        end

        labels.map.with_index do |label, index|
          if index == labels.length - 1
            label
          else
            [label, { text: '     ', font: 'Lato', size: 10 }]
          end
        end.flatten
      end

      def content_options(**additional_options)
        { leading: 1.5 }.merge(additional_options)
      end
    end
  end
end

# frozen_string_literal: true

module <PERSON>ipis
  module Reports
    class Result
      include Component

      PAGE_HEADER_HEIGHT = 54
      TOP_MARGIN = PAGE_HEADER_HEIGHT + 27 # 27 is the spacer below the header
      SIDE_MARGIN = 36

      attr_reader :document

      delegate :explanations?,
               :grid_explanations,
               :grid_explanations?,
               :grouped_explanations,
               :grouped_explanations?,
               :ungrouped_explanations,
               to: :@result_definition

      def initialize(document, result_definition)
        @document = document
        @result_definition = result_definition

        super
      end

      def render
        @start_page = page_count

        render_result_title

        move_down 18

        render_result_visualization

        move_down 18

        render_explanations if explanations?

        if comments?
          start_new_result_page

          debate
        end

        @end_page = page_count

        # The content in the header is added in a repeat block because it is drawn in a canvas
        # outside of the normal margins/flow of the document, and since it varies depending on the page
        # and we don't know in advance how many pages there will be for each result, we need to use
        # a dynamic repeat block like this and check which pages we're on, and pass it the correct result
        # for that page.
        #
        # Also, we need to account for the first page of the result, which has the result title at the top of the page
        # and the continuation-page headers that show the result title
        repeat(->(page) { page == @start_page }, dynamic: true) do
          page_header = PageHeader.new(self, @result_definition, first_page: true)
          page_header.render
        end

        if @end_page > @start_page
          repeat(->(page) { page.between?(@start_page + 1, @end_page) }, dynamic: true) do
            page_header = PageHeader.new(self, @result_definition, first_page: false)
            page_header.render
          end
        end
      end

      private

      def comments?
        @result_definition.comments.any?
      end

      def debate
        comments = @result_definition.comments.order(:created_at).map do |comment|
          OpenStruct.new(
            expert_number: comment.display_id,
            created_at: comment.created_at,
            content: comment.content.to_prawn_formatted_text(default_color: '8E8B86', link_color: LINK_COLOR),
            score: comment.score.to_i
          )
        end.sort_by(&:expert_number)

        comments = comments.reject { |comment| comment.content.blank? }

        debate = Debate.new(self, comments, page_header_height: PAGE_HEADER_HEIGHT)

        debate.render
      end

      def render_explanations
        explanations = if @result_definition.grouped_explanations?
                         GroupedExplanations.new(self, grouped_explanations, page_header_height: PAGE_HEADER_HEIGHT)
                       else
                         explanations = if @result_definition.grid_explanations?
                                          grid_explanations
                                        else
                                          ungrouped_explanations
                                        end
                         UngroupedExplanations.new(self, explanations, page_header_height: PAGE_HEADER_HEIGHT)
                       end

        explanations.render
      end

      def render_result_title
        font 'Raleway', size: 18, style: :bold
        text @result_definition.title, color: '254981'
      end

      def render_result_visualization
        visualization = create_visualization_component
        visualization.render
      end

      def create_visualization_component
        if @result_definition.chart?
          ChartVisualization.new(self, @result_definition, page_header_height: PAGE_HEADER_HEIGHT)
        elsif @result_definition.grid?
          GridVisualization.new(self, @result_definition, page_header_height: PAGE_HEADER_HEIGHT)
        elsif @result_definition.render_type.text_responses?
          TextVisualization.new(self, @result_definition, page_header_height: PAGE_HEADER_HEIGHT)
        elsif @result_definition.file_upload?
          FileUploadVisualization.new(self, @result_definition, page_header_height: PAGE_HEADER_HEIGHT)
        elsif @result_definition.file_download?
          FileDownloadVisualization.new(self, @result_definition, page_header_height: PAGE_HEADER_HEIGHT)
        else
          BaseVisualization.new(self, @result_definition, page_header_height: PAGE_HEADER_HEIGHT)
        end
      end
    end
  end
end

# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Reports
    class AbstractExplanations
      include Component

      def initialize(document, page_header_height: 0)
        @document = document
        @start_page = page_count
        @page_header_height = page_header_height

        super
      end

      def render
        # First check if the section header, first answer label,
        # and first answer explanation will all fit on the page.
        # If not, start a new page to avoid an awkward page break
        unless will_initial_content_fit?
          start_new_result_page
        end

        render_section_heading

        spacer

        render_explanations
      end

      private

      def first_explanation_height = raise NotImplementedError

      def render_explanations = raise NotImplementedError

      def render_section_heading(continued: false)
        content = section_heading_content(continued:)

        formatted_text content
      end

      def section_heading_content(continued:)
        text = 'ANSWER EXPLANATIONS'
        text = "#{text}, CONTINUED" if continued

        [{ text:, font: 'Raleway', color: '474744', size: 12, styles: [:bold] }]
      end

      def section_heading_height(continued: false)
        content = section_heading_content(continued:)

        height_of_formatted(content)
      end

      def will_initial_content_fit?
        initial_content_height = section_heading_height + DEFAULT_SPACER_HEIGHT + first_explanation_height

        cursor > initial_content_height
      end
    end
  end
end

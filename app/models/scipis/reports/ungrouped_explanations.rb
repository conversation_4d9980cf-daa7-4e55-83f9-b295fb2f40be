# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Reports
    class UngroupedExplanations < AbstractExplanations
      def initialize(document, explanations, page_header_height: 0)
        super(document, page_header_height:)

        @explanations = explanations
      end

      private

      def first_explanation_height
        explanation_component = MultipleSelectionExplanation.new(document, @explanations.first)
        explanation_component.height
      end

      def render_explanations
        @explanations.each do |explanation|
          # If the current explanation will not fit on the page,
          # start a new page and render continuation headers
          #
          # TODO: Handle the following cases:
          #       - An explanation that is too long to fit on a page, in which case we
          #         need to break it across multiple pages
          #       - An long explanation will will leave too much white space if we start a new page.
          #         In this case break it too.
          explanation_component = if explanation.is_a?(::GridExplanation)
                                    GridExplanation.new(document, explanation)
                                  else
                                    MultipleSelectionExplanation.new(document, explanation)
                                  end

          unless will_explanation_fit?(explanation_component)
            start_new_result_page

            render_section_heading(continued: true)

            spacer
          end

          explanation_component.render

          spacer unless explanation == @explanations.last
        end
      end

      def will_explanation_fit?(component)
        cursor > component.height
      end
    end
  end
end

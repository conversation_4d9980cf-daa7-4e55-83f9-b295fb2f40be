# frozen_string_literal: true

class ResultDefinitionType < ApplicationRecord
  ANSWERS_GROUPED_BY_REGION_NAME              = 'answers_grouped_by_region'
  ANSWERS_GROUPED_BY_SECTOR_NAME              = 'answers_grouped_by_sector'
  ANSWERS_GROUPED_BY_YEARS_OF_EXPERIENCE_NAME = 'answers_grouped_by_years_of_experience'
  DIAGNOSTIC_NAME                             = 'diagnostic'
  MULTIPLE_CHOICE_RESPONSES_NAME              = 'multiple_choice_responses'
  FILE_ATTACHMENT_NAME                        = 'file_attachment'
  GRID_RESPONSES_NAME                         = 'grid_responses'
  GROUPED_MULTIPLE_CHOICE_RESPONSES_NAME      = 'grouped_multiple_choice_responses'
  FREE_FORM_NAME                              = 'free_form'
  MULTI_QUESTION_SCORE_NAME                   = 'multi_question_score'
  PARTICIPANT_UPLOAD_LIST_NAME                = 'participant_upload_list'
  RANKED_CHOICE_NAME                          = 'ranked_choice'
  SCITRUST_SCORE_NAME                         = 'scitrust_score'
  SUBMISSION_SCORE_NAME                       = 'submission_score'
  TEXT_RESPONSES_NAME                         = 'text_responses'
  WEIGHTED_SUM_SCORES_NAME                    = 'weighted_sum_scores'

  belongs_to :default_render_type, class_name: 'ResultDefinitionRenderType'
  has_many :result_definitions,
           class_name: 'CustomResult',
           foreign_key: :result_type_id,
           inverse_of: :result_type
  has_many :available_render_types
  has_many :render_types, through: :available_render_types

  # rubocop:disable Rails/UniqueValidationWithoutIndex
  validates :name, uniqueness: { case_sensitive: false }
  # rubocop:enable Rails/UniqueValidationWithoutIndex

  scope :summarizable, -> { where(system_name: Results::SummaryPrompt::SUPPORTED_RESULT_TYPES) }

  def self.answers_grouped_by_region
    find_by!(system_name: ANSWERS_GROUPED_BY_REGION_NAME)
  end

  def self.answers_grouped_by_sector
    find_by!(system_name: ANSWERS_GROUPED_BY_SECTOR_NAME)
  end

  def self.answers_grouped_by_years_of_experience
    find_by!(system_name: ANSWERS_GROUPED_BY_YEARS_OF_EXPERIENCE_NAME)
  end

  def self.diagnostic
    find_by!(system_name: DIAGNOSTIC_NAME)
  end

  def self.question_based
    find_by!(system_name: MULTIPLE_CHOICE_RESPONSES_NAME)
  end

  def self.file_attachment
    find_by!(system_name: FILE_ATTACHMENT_NAME)
  end

  def self.free_form
    find_by!(system_name: FREE_FORM_NAME)
  end

  def self.grid_responses
    find_by!(system_name: GRID_RESPONSES_NAME)
  end

  def self.grouped_multiple_choice_responses
    find_by!(system_name: GROUPED_MULTIPLE_CHOICE_RESPONSES_NAME)
  end

  def self.multi_question_score
    find_by!(system_name: MULTI_QUESTION_SCORE_NAME)
  end

  def self.participant_upload_list
    find_by!(system_name: PARTICIPANT_UPLOAD_LIST_NAME)
  end

  def self.ranked_choice
    find_by!(system_name: RANKED_CHOICE_NAME)
  end

  def self.scitrust_score
    find_by!(system_name: SCITRUST_SCORE_NAME)
  end

  def self.text_responses
    find_by!(system_name: TEXT_RESPONSES_NAME)
  end

  def self.submission_score
    find_by!(system_name: SUBMISSION_SCORE_NAME)
  end

  def self.weighted_sum_scores
    find_by!(system_name: WEIGHTED_SUM_SCORES_NAME)
  end

  def allowed_grouping_question_types
    case system_name
    when ANSWERS_GROUPED_BY_REGION_NAME
      [Questions::ProfileCountry]
    when ANSWERS_GROUPED_BY_SECTOR_NAME
      [Questions::ProfileEmploymentSector]
    when ANSWERS_GROUPED_BY_YEARS_OF_EXPERIENCE_NAME
      [Questions::ProfileYearsOfExperience]
    else
      []
    end
  end

  def answers_grouped_by_region?
    system_name == ANSWERS_GROUPED_BY_REGION_NAME
  end

  def answers_grouped_by_sector?
    system_name == ANSWERS_GROUPED_BY_SECTOR_NAME
  end

  def answers_grouped_by_years_of_experience?
    system_name == ANSWERS_GROUPED_BY_YEARS_OF_EXPERIENCE_NAME
  end

  def diagnostic?
    system_name == DIAGNOSTIC_NAME
  end

  def question_based?
    system_name == MULTIPLE_CHOICE_RESPONSES_NAME
  end

  def file_attachment?
    system_name == FILE_ATTACHMENT_NAME
  end

  def free_form?
    system_name == FREE_FORM_NAME
  end

  def grid_responses?
    system_name == GRID_RESPONSES_NAME
  end

  def grouped_multiple_choice_responses?
    system_name == GROUPED_MULTIPLE_CHOICE_RESPONSES_NAME
  end

  def json_template
    system_name
  end

  def multi_question_score?
    system_name == MULTI_QUESTION_SCORE_NAME
  end

  def participant_upload_list?
    system_name == PARTICIPANT_UPLOAD_LIST_NAME
  end

  def profile_question?
    answers_grouped_by_region? || answers_grouped_by_sector? || answers_grouped_by_years_of_experience?
  end

  def ranked_choice?
    system_name == RANKED_CHOICE_NAME
  end

  def scitrust_score?
    system_name == SCITRUST_SCORE_NAME
  end

  def submission_score?
    system_name == SUBMISSION_SCORE_NAME
  end

  def summarizable?
    Results::SummaryPrompt.supported_result_type?(system_name)
  end

  def text_responses?
    system_name == TEXT_RESPONSES_NAME
  end

  def weighted_sum_scores?
    system_name == WEIGHTED_SUM_SCORES_NAME
  end

  ##
  # Template used for rendering the content area of the results page
  def result_template
    if diagnostic?
      'diagnostic'
    elsif file_attachment?
      'file-download'
    elsif participant_upload_list?
      'file-list'
    elsif grid_responses?
      'grid'
    elsif text_responses?
      'text'
    else
      'chart'
    end
  end

  def title?
    diagnostic? ||
      multi_question_score? ||
      submission_score? ||
      free_form? ||
      file_attachment?
  end
end

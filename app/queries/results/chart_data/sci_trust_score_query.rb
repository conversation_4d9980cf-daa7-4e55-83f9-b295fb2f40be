# frozen_string_literal: true

module Results
  module ChartData
    class SciTrustScoreQuery
      NO_CONFIDENCE_COLOR = '#ff8228'
      DEFAULT_COLOR = '#008ffb'

      def initialize(result_definition)
        @result_definition = result_definition
        @bins = [
          (0..0),
          (1...2),
          (2...3),
          (3...4),
          (4...5),
          (5...6),
          (6...7),
          (7...8),
          (8...9),
          (9..10)
        ]
      end

      def result(show_toolbar: false)
        question = @result_definition.question

        scores = scores(question)

        tallies = binned_score_tallies(scores)

        percentages = tallies.map { |tally| ((tally.to_f / scores.size) * 100).round(1) }

        {
          chartType: 'horizontal_bar_chart',
          colors:,
          dataFormat: 'percent',
          labels:, # is mapped to xaxis.categories
          height: @result_definition.chart_height,
          legend: false,
          series: [
            {
              name: '% of responses',
              data: percentages
            }
          ],
          toolbar: {
            show: show_toolbar,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          },
          zero_flags: tallies.last, # 0s are the last in the series
          mean_score: DescriptiveStatistics.mean(scores),
          score_sd: DescriptiveStatistics.standard_deviation(scores)
        }
      end

      private

      def colors
        [[DEFAULT_COLOR] * 9, NO_CONFIDENCE_COLOR].flatten
      end

      def labels
        @bins.reverse.map { |bin| format_bin_label(bin) }
      end

      def scores(question)
        answer_choices = question.answer_choices

        question.final_answers.filter_map do |answer|
          answer_rows = (0...question.row_count).map do |row_num|
            answer.row_at(row_num)
          end

          answer_values = answer_rows.flatten.map do |label|
            answer_choices.find { |ac| ac.label == label }&.value
          end

          next if answer_values.any?(&:nil?)

          Math.geometric_mean(answer_values)
        end
      end

      def binned_score_tallies(scores)
        @bins.reverse.map do |bin|
          scores.count { |score| bin.cover?(score) }
        end
      end

      def format_bin_label(range)
        return range.first.to_s if range.first == range.last

        if range.include?(range.last)
          "#{range.first} - #{range.last}"
        else
          "#{range.first} - #{format('%<value>.2f', value: range.last - 0.01)}"
        end
      end
    end
  end
end

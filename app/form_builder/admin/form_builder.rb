# frozen_string_literal: true

module Admin
  class FormBuilder < ActionView::Helpers::FormBuilder
    def date_field(attr, options = {})
      options = apply_input_classes(attr, options)

      super
    end

    def errors(attr)
      return nil unless errors_on?(attr)

      @template.tag.div(class: %w[text-danger]) do
        @template.tag.small(errors_for(attr).to_sentence)
      end
    end

    def hint(text = nil, **opts)
      classes = opts.delete(:class) || ''
      disabled = opts.delete(:disabled)

      classes = @template.class_names('form-text', 'font-weight-light', classes, 'text-muted': disabled)

      @template.render(TagComponent.new(:small, classes:, **opts)) { text || yield }
    end

    def input_group(**opts, &)
      opts = opts.symbolize_keys!
      classes = opts.try(:fetch, :class, [])
      classes = classes.split if classes.is_a?(String)
      classes << 'input-group' unless classes.include?('input-group')

      opts[:class] = classes

      @template.tag.div(**opts, &)
    end

    def input_group_prepend(content)
      @template.tag.div(content, class: 'input-group-text')
    end

    def label(method, text = nil, options = nil, &)
      options = text.is_a?(Hash) ? text : (options || {})

      classes = options.delete(:class)
      disabled = options.delete(:disabled)

      classes = @template.class_names(classes, 'text-muted': disabled)

      options = options.merge(class: classes)

      super
    end

    def money_field(attr, options = {})
      options[:min] = options.fetch(:min, 0.0)
      options[:step] = options.fetch(:step, 0.01)
      classes = options.delete(:class)

      @template.tag.div(class: "input-group #{classes}") do
        input_group_prepend('$') + number_field(attr, options)
      end
    end

    def multiselect(method, choices = nil, options = {}, html_options = {}, &)
      select(
        method,
        choices,
        options,
        html_options.merge(multiple: true, data: { controller: 'multiselect' })
      )
    end

    def number_field(attr, options = {})
      options = apply_input_classes(attr, options)

      super
    end

    def text_area(attr, options = {})
      options = apply_input_classes(attr, options)

      super
    end

    def text_field(attr, options = {})
      options = apply_input_classes(attr, options)

      super
    end

    private

    def apply_input_classes(attr, options, default_classes: 'form-control')
      options.symbolize_keys.tap do |opts|
        classes = opts.try(:fetch, :class, '')

        opts[:class] = @template.class_names(default_classes, classes, 'border-danger': errors_on?(attr))
      end
    end

    def errors_on?(attr)
      errors_for(attr).any?
    end

    def errors_for(attr)
      object.errors[attr]
    end
  end
end

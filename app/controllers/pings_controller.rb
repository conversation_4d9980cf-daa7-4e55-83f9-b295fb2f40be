# frozen_string_literal: true

class PingsController < ApplicationController
  rate_limit to: 100, within: 3.minutes

  include PingsHelper
  include Pings::Filtering

  before_action :authenticate!, only: %i[create new edit update]

  rescue_from ActiveRecord::RecordNotFound, CanCan::AccessDenied do |exception|
    raise exception unless params[:action] == 'show'

    notice = Ping.hidden.exists?(params[:id]) ? t('.hidden') : t('.not_found')

    redirect_to pings_path, notice:
  end

  def index
    authenticate!(fallback_path: login_path) if user_filter_selected?

    ping_query = Pings::IndexQuery.new(default_scope)
                                  .apply_filter(index_params[:filter], current_user)
                                  .apply_tag_filter(index_params[:tag_id])
                                  .apply_search(index_params[:search])
                                  .apply_ordering

    @pings_filter_navs = build_filter_navigation
    @tags_with_counts = tags_with_counts
    @current_tags = @tags_with_counts.active

    @participant_stats = Pings::ParticipantStats.new(current_user)

    @pings = ping_query.paginate(page: index_params[:page], per_page: 10)
  end

  def show
    @ping = Ping.find(params[:id])

    authorize! :view, @ping

    setup_related_pings

    @answers = answers_for(@ping)

    @meta_tags = {
      description: @ping.content.to_plain_text.truncate(100),
      title: @ping.title,
      url: ping_url(@ping),
      image: view_context.image_url('social/scipinion-ping.jpg')
    }
  end

  def new
    authorize! :create, Ping

    @ping = Ping.new
  end

  def edit
    @ping = edit_scope.find(params[:id])
  end

  def create
    create_params = permitted_params(*creation_params).with_defaults(type_id: Pings.default_type.id)

    @ping = current_user.pings.build(create_params)

    authorize! :create, @ping

    if @ping.save
      redirect_to @ping, notice: ping_creation_notice(@ping)
    else
      render 'new'
    end
  end

  def update
    update_cutoff = Ping::EDIT_WINDOW_DURATION + 10.minutes # fudge factor
    @ping = edit_scope(update_cutoff).find(params[:id])

    if @ping.update(permitted_params(*update_params))
      redirect_to @ping
    else
      render 'edit'
    end
  end

  private

  def answers_for(ping)
    answers = if include_hidden_answers?(ping)
                ping.answers
              else
                ping.answers.visible
              end

    if !pings_admin? && ping.randomize_answers?
      answers.pseudo_random(Current.user&.id)
    elsif params[:answer_order] == 'newest-first'
      answers.order(created_at: :desc)
    else
      answers.accepted_first
    end
  end

  def base_params
    [:content, { expertise_ids: [] }, :title]
  end

  def creation_params
    if pings_admin?
      %i[reward_amount type_id promote_until voting_opens_at_date voting_opens_at_hour voting_closes_at_date voting_closes_at_hour]
    elsif current_user.display_name.blank?
      %i[display_name]
    else
      []
    end
  end

  def default_scope
    Ping.visible.published
  end

  def edit_scope(window = Ping::EDIT_WINDOW_DURATION)
    pings_admin? ? Ping.all : current_user.pings.where('created_at > ?', window.ago)
  end

  def index_params
    ping_filter_params
  end

  def include_hidden_answers?(ping)
    return true if params[:show_hidden].present?
    return true if current_user && ping.authored_by?(current_user)
    return true if current_user&.pings_admin?

    false
  end

  def permitted_params(*)
    params.expect(ping: [*base_params, *])
  end

  def ping_creation_notice(_ping)
    return nil if pings_admin?

    "Success! Your Ping has been posted. #{ping_credit_badge(1)}  has been deducted from your account."
  end

  def preview_suggested_pings
    current_user && params[:show_related]
  end

  def setup_related_pings
    @show_related_pings_notice = session.delete(:ping_just_answered).presence || preview_suggested_pings
    if @show_related_pings_notice
      related_pings_with_possible_dups = Pings::Suggestions.call(current_user, related_to: @ping)
      @related_pings = related_pings_with_possible_dups.uniq.max(3)
    end
  end

  def tags_with_counts
    tags = Expertise.joins(:taggings)
                    .joins('INNER JOIN pings ON taggings.taggable_id = pings.id')
                    .where(taggings: { taggable_type: 'Ping' })
                    .merge(Ping.visible.published)
                    .select('expertises.*, COUNT(taggings.id) as ping_count')
                    .group('expertises.id')
                    .order(ping_count: :desc)
                    .limit(20)

    active_tag_ids = Array(index_params[:tag_id]).flatten.compact.map(&:to_i)

    TagsWithStatus.new(tags, active_tag_ids)
  end

  def update_params
    return [] unless pings_admin?

    %i[reward_amount promote_until voting_opens_at_date voting_opens_at_hour voting_closes_at_date voting_closes_at_hour] + [{ doi_attributes: %i[id identifier] }]
  end
end

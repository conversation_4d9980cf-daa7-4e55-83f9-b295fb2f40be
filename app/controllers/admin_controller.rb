# frozen_string_literal: true

class AdminController < ApplicationController
  layout 'admin'

  before_action :authorize
  before_action :set_active_storage_url_options

  skip_visit_tracking

  def authorize
    redirect_to root_path unless Current.user.internal? || AuditorAssignment.exists?(user_id: Current.user.id)
  end

  def current_ability
    @current_ability ||= Admin::Ability.new(Current.user)
  end

  def set_active_storage_url_options
    ActiveStorage::Current.url_options = { host: request.base_url }
  end
end

# frozen_string_literal: true

module SurveyBuilder
  class ResultSectionsController < ApplicationController
    layout 'application_legacy'

    def new
      @survey = QuestionGroup.find(params[:survey_id])

      authorize! :manage, @survey

      @form = NewResultSectionForm.new(@survey)
    end

    def edit
      @section = ResultSection.find(params[:id])
      @survey = @section.survey

      authorize! :manage, @survey

      @form = EditResultSectionForm.new(@section)
    end

    def create
      @survey = QuestionGroup.find(params[:survey_id])

      authorize! :manage, @survey

      form = CreateResultSectionForm.new(
        **params
          .expect(section: %i[name hidden])
          .to_hash
          .to_options
      )
      action = CreateResultSection.new(@survey)

      if action.perform(form)
        redirect_to survey_builder_survey_result_definitions_path(@survey),
                    notice: 'Section created'
      else
        @form = NewResultSectionForm.new(
          @survey,
          errors: form.errors,
          attributes: form.submitted_values
        )
        render 'new', status: :unprocessable_content
      end
    end

    def update
      @section = ResultSection.find(params[:id])
      @survey = @section.survey

      authorize! :manage, @survey

      form = UpdateResultSectionForm.new(@section)

      if form.submit(params[:section])
        redirect_to(
          survey_builder_survey_result_definitions_path(@survey),
          notice: "#{@section.name} updated"
        )
      else
        @form = EditResultSectionForm.new(
          @section,
          errors: form.errors,
          attributes: form.submitted_values
        )
        render 'edit', status: :unprocessable_content
      end
    end

    def destroy
      @section = ResultSection.find(params[:id])
      @survey = @section.survey

      authorize! :manage, @survey

      if @section.empty?
        @section.destroy!

        redirect_to(
          survey_builder_survey_result_definitions_path(@survey),
          notice: "#{@section.name} deleted"
        )
      else
        redirect_to(
          survey_builder_survey_result_definitions_path(@survey),
          alert: 'Sections with results cannot be deleted.'
        )
      end
    end

    def move
      result_section = ResultSection.find(params[:id])
      survey = result_section.survey

      authorize! :manage, survey

      survey.move_result_section!(result_section, params[:new_position].to_i)

      head :no_content
    end
  end
end

# frozen_string_literal: true

module SurveyBuilder
  class QuestionSectionsController < ApplicationController
    layout 'application_legacy'

    def new
      @survey = QuestionGroup.find(params[:survey_id])

      authorize! :manage, @survey

      @form = CreateQuestionSectionForm.new(
        survey: @survey,
        action_path: survey_builder_survey_question_sections_path(@survey)
      )
    end

    def edit
      section = Section.find(params[:id])
      @survey = section.survey

      authorize! :manage, @survey

      @form = UpdateQuestionSectionForm.new(
        section:,
        action_path: survey_builder_question_section_path(section)
      )
    end

    def create
      @survey = QuestionGroup.find(params[:survey_id])

      authorize! :manage, @survey

      @form = CreateQuestionSectionForm.new(
        survey: @survey,
        action_path: survey_builder_survey_question_sections_path(@survey)
      )

      if @form.submit(params[:section])
        redirect_to question_group_questions_url(@survey),
                    notice: 'Section was successfully created.'
      else
        render 'new', status: :unprocessable_content
      end
    end

    def update
      @section = Section.find(params[:id])
      @survey = @section.survey

      authorize! :manage, @survey

      @form = UpdateQuestionSectionForm.new(
        section: @section,
        action_path: survey_builder_question_section_path(@section)
      )

      if @form.submit(params[:section])
        redirect_to question_group_questions_url(@survey),
                    notice: 'Section was successfully updated.'
      else
        render 'edit', status: :unprocessable_content
      end
    end

    def destroy
      section = Section.find(params[:id])
      survey = section.survey

      authorize! :manage, survey

      survey.delete_question_section!(section)

      redirect_to question_group_questions_url(survey),
                  notice: 'Section was successfully removed.'
    end

    def clone
      @section = Section.find(params[:id])

      @source_survey = @section.question_group
      @target_survey = QuestionGroup.find(params[:section][:question_group_id])

      authorize! :manage, @target_survey

      if @section.copy_to(@target_survey)
        redirect_to question_group_questions_url(@target_survey),
                    notice: "Section was cloned from #{@source_survey.name} to " \
                            "#{@target_survey.name} (this survey) successfully."
      else
        redirect_to question_group_questions_url(@source_survey),
                    notice: 'Section was not cloned. Target survey is not segmented.'
      end
    end
  end
end

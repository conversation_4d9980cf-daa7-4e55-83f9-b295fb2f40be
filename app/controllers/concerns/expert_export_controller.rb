# frozen_string_literal: true

module ExpertExportController
  extend ActiveSupport::Concern

  def export_users(experts:, base_file_name:)
    file_data = csv_file_data(experts)

    send_data CsvUtils.bomify(file_data), filename: "#{base_file_name}.csv"
  end

  private

  def csv_file_data(users)
    CSV.generate(headers: true, force_quotes: true) do |csv|
      csv << exportable_methods
      users.find_each do |user|
        csv << user.to_csv_row(exportable_methods)
      end
    end
  end

  def exportable_methods
    %i[
      id display_id email first_name last_name
      current_employer current_employment_sector_name total_work_experience degree_type_names most_recent_grad_year
      gender country_name region_name legacy_region title publications_count
      publications_first_last_count formatted_expertise other_expertise
      verified? h_index rg_score linkedin_url researchgate_url
      google_scholar_url cv_download_name cv_last_updated_at
    ]
  end
end

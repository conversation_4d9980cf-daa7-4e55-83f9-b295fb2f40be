# frozen_string_literal: true

class ModerationRequestsController < ApplicationController
  def new
    @subject = load_subject!
    @moderation_request = ModerationRequest.new(subject: @subject)
  end

  def create
    @subject = load_subject!
    @moderation_request = ModerationRequest.new(permitted_params)

    if @moderation_request.save
      redirect_to redirect_path,
                  notice: 'Thank you. Your request for moderation has been received.'
    else
      render 'new'
    end
  end

  private

  def load_subject!
    if params.key?(:ping_id)
      Ping.find(params[:ping_id])
    elsif params.key?(:answer_id)
      Pings::Answer.find(params[:answer_id])
    else
      Rollbar.warning("Unknown moderation subject type #{params}")
      redirect_to root_path
    end
  end

  def permitted_params
    params
      .expect(moderation_request: %i[reason_id comment])
      .merge(submitted_by: current_user, subject: @subject)
  end

  def redirect_path
    if @subject.is_a?(Ping)
      @subject
    else
      ping_path(
        @subject.ping,
        anchor: "#{@subject.class.name.downcase.demodulize}-#{@subject.id}"
      )
    end
  end
end

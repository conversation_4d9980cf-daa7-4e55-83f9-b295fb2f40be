# frozen_string_literal: true

class SessionsController < ApplicationController
  rate_limit to: 50, within: 1.minute

  layout 'public'

  skip_authentication! except: :destroy

  def new
    redirect_to dashboard_path if Current.user&.registered?

    @session = Session.new(default_login_values)
  end

  def create
    @session = Session.new(permitted_params)

    if @session.save
      create_session(@session.user!)

      redirect_to after_login_path
    else
      render 'sessions/new'
    end
  end

  # This is session related, but maybe should be elsewhere?
  def set_display
    session[:preferred_display] = params[:preferred_display]
    redirect_back(fallback_location: root_path)
  end

  def destroy
    session[:user_id] = nil
    reset_session

    redirect_to root_path, notice: t('messages.logged_out')
  end

  private

  def default_login_values
    params.permit(:email)
  end

  def permitted_params
    guest_user = Current.user&.anonymous? ? Current.user : nil
    params.expect(session: %i[email password]).merge(guest_user:)
  end
end

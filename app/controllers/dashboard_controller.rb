# frozen_string_literal: true

class DashboardController < ApplicationController
  ITEM_LIMIT = 3

  # TODO: This is bad
  def index
    authorize! :view, :dashboard

    user_scipis_temp = generate_survey_users_from(user_scipis, current_user)
    @scipolls = generate_survey_users_from(user_scipolls, current_user)
    @active_scipis = user_scipis_temp.select(&:active?)
    @past_scipis = user_scipis_temp.select(&:past?)
    @teaser_scipis = user_scipis_temp.any? ? {} : generate_survey_users_from(featured_scipis, current_user)
    @teaser_scipolls = @scipolls.any? ? {} : generate_survey_users_from(featured_scipolls, current_user)
    @created_pings = Ping.visible.created_by(current_user).order(created_at: :desc)
    @answered_pings = Ping.visible.answered_by(current_user).order(created_at: :desc) # TODO: Exclude hidden answers
    @show_user_pings = @created_pings.any? || @answered_pings.any?
    @featured_pings = featured_pings unless @show_user_pings
    @late_drafts = late_draft_submissions_for_active_scipis
    @announcements = announcements || []
  end

  private

  def generate_survey_users_from(surveys, user)
    surveys.map { |survey| Scipis::Scipi.new(survey:, user:) } || {}
  end

  def late_draft_submissions_for_active_scipis
    current_user.answer_groups.select(&:draft_late?).reject { |ag| ag.question_group.closed_for_a_while? }
  end

  def featured_scipis
    QuestionGroup.featured_scipis.limit(ITEM_LIMIT)
  end

  def featured_scipolls
    QuestionGroup.featured_scipolls.limit(ITEM_LIMIT)
  end

  def featured_pings
    Ping.featured.limit(ITEM_LIMIT)
  end

  def user_scipis
    Surveys::Branding.scipi.surveys_for_user(current_user)
  end

  def user_scipolls
    QuestionGroup.published.scipoll.taken_by(current_user).sort_by(&:id).reverse
  end

  def announcements
    Announcement.published.order(published_at: :desc).limit(10)
  end
end

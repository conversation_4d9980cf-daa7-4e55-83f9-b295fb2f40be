# frozen_string_literal: true

class ApplicationController < ActionController::Base
  include Authentication::HttpBasic, Authorization, BrowserTracking, RequestHelpers, VisitTracking

  layout 'application'

  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead of :exception
  protect_from_forgery with: :exception

  # WARNING: :capture_redirect_path must be before :authenticate! to work!
  before_action :set_current_user!
  before_action :capture_redirect_path
  before_action :authenticate!
  enable_visit_tracking

  around_action :within_current_time_zone

  helper_method :admin_or_owner?,
                :admin_signed_in?,
                :current_path?,
                :current_user,
                :guest_signed_in?,
                :logged_in?,
                :main_body_class,
                :recaptcha_verify_requests?,
                :scientist_signed_in?

  add_flash_types :warning, :success, :success_alt

  rescue_from ActionController::Redirecting::UnsafeRedirectError do
    redirect_to root_path
  end

  def self.skip_authentication!(**opts)
    skip_before_action :authenticate!, opts
  end

  def admin_or_owner?(survey)
    return false if current_user.blank?

    survey.administered_by?(current_user)
  end

  def after_login_path
    if session[:redirect_path].present?
      session.delete(:redirect_path)
    elsif current_user.internal?
      admin_root_path
    else
      dashboard_path
    end
  end

  def authenticate!(fallback_path: nil)
    # To set a custom fallback path, call set_authentication_callback_path in a prepend_before_action
    current_fallback_path = fallback_path.presence || @authentication_callback_path.presence || welcome_path

    redirect_to current_fallback_path unless logged_in?
  end

  def capture_redirect_path
    return if logged_in? || !request.get? || request.xhr? || request.format.json?
    return if request.path.in?([root_path,
                                login_path,
                                welcome_path,
                                claim_account_path,
                                new_sign_up_path,
                                sign_ups_path,
                                password_reset_expired_path,
                                confirm_password_reset_request_path,
                                reset_password_path,
                                access_tokens_path])
    return if request.path.starts_with?('/confirmations') || request.path.starts_with?('/password_reset')

    session[:redirect_path] = request.fullpath
  end

  # Used to log a user in. Should only be called
  # from the login and sign up actions
  def create_session(user)
    redirect_path = session[:redirect_path] # preserve through session reset
    reset_session
    session[:user_id] = user.id
    set_current_user!
    session[:redirect_path] = redirect_path
    user.record_sign_in!(ip: request.remote_ip)
  end

  def current_path?(path)
    request.original_fullpath == path
  end

  def current_user
    Current.user
  end

  def set_current_user!
    Current.user = User.find_by(id: session[:user_id])
  end

  # TODO: Move to do a display_preferences JSONB blob on User, so will be
  # accessible through Current.user
  def show_as_cards?
    if session[:preferred_display]
      session[:preferred_display] == 'cards'
    else
      false
    end
  end

  # Fix for invalid authenticity token bug - https://github.com/rails/rails/issues/21948
  def prevent_caching_on_mobile_webkit
    response.headers['Cache-Control'] = 'no-store, no-cache' if browser.device.mobile? && browser.webkit?
  end

  def admin_signed_in?
    logged_in? && current_user.try(:admin?)
  end

  def scientist_signed_in?
    logged_in? && !current_user.try(:admin?)
  end

  def guest_signed_in?
    logged_in? && current_user.guest
  end

  def main_body_class(layout: nil, addon: nil)
    classes = []
    classes << controller_name
    classes << action_name
    classes << layout if layout.present?
    classes << addon if addon.present?
    if logged_in?
      classes << 'admin_signed_in' if admin_signed_in?
      classes << 'user_signed_in' if scientist_signed_in?
      classes << 'guest_signed_in' if guest_signed_in?
    end
    classes.join(' ')
  end

  def logged_in?
    current_user.present?
  end

  private

  def current_ability
    @current_ability ||= Ability.new(current_user)
  end

  def recaptcha_verify_requests?
    # return true if ENV['SCIPINION_VERIFY_REQUESTS'] == 'true'

    # ENV.fetch('SCIPINION_APP_ENV', Rails.env) == 'production'

    # Remove this line, and uncomment the above once we're commited to this
    ENV['SCIPINION_VERIFY_REQUESTS'] == 'true'
  end

  # rubocop:disable Naming/AccessorMethodName
  def set_authentication_callback_path(path)
    @authentication_callback_path = path
  end
  # rubocop:enable Naming/AccessorMethodName

  def set_cookie_value!(name, value, httponly: true, secure: true)
    cookies.permanent.encrypted[name] = { value:, httponly:, secure: }
  end

  def within_current_time_zone(&)
    Time.use_zone(Current.time_zone, &)
  rescue ArgumentError => e
    Rollbar.error(e, "Invalid time zone: #{Current.time_zone}")
    yield
  end
end

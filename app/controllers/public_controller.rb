# frozen_string_literal: true

class PublicController < ApplicationController
  layout 'public', only: 'welcome'
  layout 'application', except: 'welcome'

  skip_authentication!

  ITEM_LIMIT = 5

  before_action :clear_redirect_path, only: :index

  def index
    if Current.user&.registered? && current_path?(root_path)
      redirect_to dashboard_path
      return
    end

    # TODO: This has SMALL N+1 problem
    @scipis = featured_scipis.map { |survey| Scipis::Scipi.new(survey:, user: current_user) }
    @scipolls = featured_scipolls.map { |survey| Scipis::Scipi.new(survey:, user: current_user) }
    @pings = featured_pings
    @announcements = announcements
  end

  def welcome
    @recaptcha = true
    @available_roles = Role.available_for_signup
    @show_role_select = true
    @sign_up = SignUp.new

    redirect_to dashboard_path if logged_in?
  end

  private

  def clear_redirect_path
    session.delete(:redirect_path)
  end

  def featured_scipis
    QuestionGroup.featured_scipis.limit(ITEM_LIMIT)
  end

  def featured_scipolls
    QuestionGroup.featured_scipolls.limit(ITEM_LIMIT)
  end

  def featured_pings
    Ping.featured.limit(ITEM_LIMIT)
  end

  def announcements
    Announcement.published.order(published_at: :desc).limit(10)
  end
end

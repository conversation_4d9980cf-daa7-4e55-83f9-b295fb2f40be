# frozen_string_literal: true

class AnswerGroupsController < ApplicationController
  prepend_before_action -> { set_authentication_callback_path(session.delete(:authentication_callback_path)) }, only: :new

  skip_authentication! only: :prefill

  before_action :prevent_caching_on_mobile_webkit
  before_action :find_question_group!
  before_action :set_seed_params_flash, only: %i[new edit]

  after_action :send_submission_receipt, only: %i[create update]

  helper_method :show_slim_user_nav?

  def show
    @answer_group = AnswerGroup.find(params[:id])
    authorize! :read, @answer_group
  end

  def submitted
    @recaptcha = true
    @answer_group = AnswerGroup.find(params[:id])
    authorize! :read, @answer_group
    session.delete(:redirect_path) # Prevent weird redirect when registering from here
  end

  def new
    redirect_previous_submission and return

    authorize! :create_submission, @question_group

    # Implements find_or_create_by answer_group, so we always have a draft
    @answer_group_builder = AnswerGroupForm.new(answer_group_params.merge(seed_params:))

    @answer_group_builder.save! if @question_group.auto_submit?
  end

  def survey_preview
    authorize! :preview, @question_group

    @answer_group_builder = AnswerGroupForm.new(
      answer_group_params.merge(preview: true)
    )
  end

  def edit
    submission = AnswerGroup.find(params[:id])

    authorize! :update, submission

    @answer_group_builder = AnswerGroupForm.new(
      answer_group_params.merge(answer_group: submission).merge(seed_params:)
    )
  end

  def create
    authorize! :create_submission, @question_group

    @answer_group_builder = AnswerGroupForm.new(answer_group_params)

    save_progress? ? save_progress : submit_response
  end

  def update
    submission = AnswerGroup.find(params[:id])

    authorize! :update, submission

    @answer_group_builder = AnswerGroupForm.new(
      answer_group_params.merge(answer_group: submission)
    )

    save_progress? ? save_progress : submit_response
  end

  def prefill
    if params[:access_token].present?
      forwardable_params = params.permit(:access_token).merge(prefill_answer_choice: params[:answer_choice_id])

      redirect_to access_tokens_path(forwardable_params)
      return
    end

    unless logged_in?
      set_login_register_flash_alert
      save_callback_path_for_email(params.delete(:email))
    end

    redirect_to new_question_group_answer_group_path(@survey, prefill_answer_choice: params[:answer_choice_id])
  end

  private

  def after_submission_path
    return submitted_question_group_answer_group_path(@answer_group_builder.answer_group.survey, @answer_group_builder.answer_group) if Current.user&.anonymous?

    helpers.survey_path_for(@question_group)
  end

  def answer_group_params
    answer_params = { params: params[:answer_group] }
    answer_params.merge(user: current_user, question_group: @question_group)
    # add strong params
  end

  # Convert prefill route params to format expected by AnswerGroupForm.update_answer_attributes
  def build_seed_params
    answer_choice_id = params.delete(:prefill_answer_choice)

    question_id = AnswerChoice.where(id: answer_choice_id).pick(:question_id)

    return {} unless question_id

    { question_id => { answer_choice_ids: [answer_choice_id] } }
  end

  def clear_seed_and_prefill
    params.delete(:prefill_answer_choice)
    @seed_params = nil
  end

  def edit_submission_path
    edit_question_group_answer_group_path(@survey, @survey.submission_for(current_user), prefill_answer_choice:)
  end

  def find_question_group!
    @survey = @question_group = QuestionGroup.find(params[:question_group_id])
    @scipi = Scipis::Scipi.new(survey: @survey, user: current_user)
  end

  def previous_submission?
    @question_group.submission_for(current_user).present?
  end

  def prefill_answer_choice
    params[:prefill_answer_choice] || seed_params.first&.last&.dig(:answer_choice_ids)&.first
  end

  def redirect_previous_submission
    return unless previous_submission?

    submission = @question_group.submission_for(Current.user)

    if can?(:update, submission)
      redirect_to edit_submission_path
    else
      redirect_to root_path, alert: 'This survey is closed.'
    end
  end

  def save_progress?
    params['button'] == 'save_progress'
  end

  def show_slim_user_nav? = true

  def submit?
    params['button'] == 'submit'
  end

  def save_progress
    clear_seed_and_prefill
    if @question_group.past_submission_deadline?
      submit_response
    else
      @answer_group_builder.save_progress
      flash[:success] = 'Progress Saved.'
      redirect_to edit_submission_path
    end
  end

  def submit_response
    clear_seed_and_prefill
    if @answer_group_builder.save
      if @question_group.past_submission_deadline?
        flash[:success] = 'Submitted. (LATE)'
      else
        flash[:submission_success_notice] = true
      end
      redirect_to after_submission_path
    else
      flash[:warning] = @answer_group_builder.errors.full_messages.join('<br/>').html_safe
      redirect_to edit_submission_path
    end
  end

  def save_callback_path_for_email(email)
    email = Emails.decode_spaces(email) # correct the email encoding from Mailchimp merge tags
    email = nil unless Emails.valid?(email)

    session[:authentication_callback_path] = User.exists?(email:) ? login_path(email:) : new_sign_up_path(email:)
  end

  # rubocop:disable Rails/ActionControllerFlashBeforeRender
  def set_login_register_flash_alert
    flash[:notice] = I18n.t('scipoll.prefill.login_encouragement')
  end
  # rubocop:enable Rails/ActionControllerFlashBeforeRender

  def set_seed_params_flash
    flash.now[:notice] = I18n.t('scipoll.prefill.finish_reminder') if seed_params.present?
  end

  def seed_params
    @seed_params ||= build_seed_params
  end

  def send_submission_receipt
    submission = @answer_group_builder.answer_group

    # Allow for delivery of receipts past the close of the final round
    round = @survey.current_round || (@survey.final_round if @survey.final_round&.past?)

    return unless round&.send_submission_receipt?(submission)

    template = @survey.final_round?(round) ? 'final' : 'non_final'
    submission.submission_receipt_sent!(round, template)
    Scipis::PanelistsMailer.submission_receipt(panelist: current_user, scipi: @survey, template:).deliver_later
  end
end

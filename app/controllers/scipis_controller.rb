# frozen_string_literal: true

class ScipisController < ApplicationController
  before_action :find_survey!, only: %i[show observe]
  before_action :redirect_observer, only: :show
  before_action :redirect_show_action_to_preferred_path, only: :show
  before_action :set_default_filter_params_if_none_set, only: :index

  skip_authentication! except: :observe

  SCIPI_FILTERS = %w[all recruiting active results mine].freeze
  SCIPOLL_FILTERS = %w[all open complete mine].freeze
  LEGACY_FILTERS = %w[all mine].freeze

  rescue_from CanCan::AccessDenied, NotAuthorized do |_exception|
    user = current_user.nil? ? 'A logged out user' : "User ##{current_user.id}"
    message = "#{user} attempted to access this page, but does not have access."
    raise ActionController::RoutingError, message
  end

  def index
    @show_as_cards = show_as_cards?

    @branding = Surveys::Branding.find_by(id: params[:branding]) || Surveys::Branding.default_branding

    # TODO: This has N+1 problem
    @scipis = filtered_scipis&.map { |survey| Scipis::Scipi.new(survey:, user: current_user) } || []

    @survey_filter_navs = available_filters.map { |survey_name| surveys_data_for(survey_name) }
  end

  def show
    authorize! :view_landing_page, @survey
    @recaptcha = true if Current.user&.anonymous?

    @scipi = Scipis::Scipi.new(survey: @survey, user: current_user)
    @branding = @survey.branding

    @view_model = Scipis::ViewModel::ShowScipi.new(scipi: @scipi, current_ability:)
    set_meta

    @messages = @survey.parent_messages.where(user_id: Current.user.id).order(created_at: :desc) if can?(:view_messages, @survey)
    @comments = Comment.for_all_individual_results(@survey).order(created_at: :desc) if @view_model.show_comments?
    @show_full_names = @view_model.show_comments? && can?(:view_panelists, @survey)

    # Prevent caching for authenticated users to ensure fresh message data
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate' if current_user
  end

  def observe
    authorize! :view_observe_page, @survey
    @branding = @survey.branding

    @scipi = @survey
    @comments = Comment.for_all_individual_results(@survey).order(created_at: :desc)
  end

  private

  def redirect_observer
    redirect_to action: :observe if current_user && @survey&.observed_by?(current_user)
  end

  def redirect_show_action_to_preferred_path
    preferred_path = helpers.survey_path_for(@survey)

    redirect_to preferred_path if request.path != preferred_path
  end

  def find_survey!
    @survey = QuestionGroup.find(params[:id])
  end

  def viewable_surveys
    if @branding.scipi?
      QuestionGroup.featured_scipis
    elsif @branding.scipoll?
      QuestionGroup.featured_scipolls
    else
      @branding.surveys.featured
    end
  end

  def user_surveys
    return nil unless current_user

    @branding.surveys_for_user(current_user)
  end

  def filtered_scipis
    active_filter = available_filters.select { |filter| params[filter.to_sym] } || nil
    surveys_for(active_filter&.first)
  end

  def surveys_for(filter_name)
    return viewable_surveys.recruiting if filter_name == 'recruiting'
    return viewable_surveys.open if %w[active open].include?(filter_name)
    return viewable_surveys.results_public if filter_name == 'results'
    return viewable_surveys.closed if filter_name == 'complete'
    return user_surveys if filter_name == 'mine'

    viewable_surveys
  end

  def surveys_data_for(filter_name)
    surveys = surveys_for(filter_name)
    count = surveys&.count || 0

    link_name = link_title = filter_name.humanize.titleize

    # Override names with better descriptions in some cases
    link_name = "My #{@branding.name.pluralize}" if filter_name == 'mine'

    NavFilter.new(filter_name:, link_name:, link_title:, count:)
  end

  def available_filters
    survey_filters = brand_filters
    survey_filters.delete('mine') unless current_user
    survey_filters.delete('all') if @branding.legacy? && !admin_signed_in?
    survey_filters
  end

  def set_default_filter_params_if_none_set
    params[:all] = true unless all_filters.any? { |filter| params[filter.to_sym] }
  end

  def set_meta
    @meta_tags = {
      image: view_context.image_url(meta_image)
    }
  end

  def meta_image
    if @branding.scipoll?
      'social/scipinion-scipoll.jpg'
    elsif @scipi.recruitment.open?
      'social/scipinion-scipi-recruiting.jpg'
    else
      'social/scipinion-scipi.jpg'
    end
  end

  def all_filters
    (SCIPI_FILTERS + SCIPOLL_FILTERS + LEGACY_FILTERS).uniq
  end

  def brand_filters
    if @branding.scipoll?
      SCIPOLL_FILTERS.dup
    elsif @branding.scipi?
      SCIPI_FILTERS.dup
    else
      LEGACY_FILTERS.dup
    end
  end

  def empty_or_nil?(item)
    return true if item.nil?

    item.to_s.strip.empty?
  end
end

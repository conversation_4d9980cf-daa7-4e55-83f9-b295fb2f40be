# frozen_string_literal: true

# Move to debate namespace
class CommentsController < ApplicationController
  skip_authentication! # Ehh? Why?

  def create
    result = CustomResult.find(params[:custom_result_id])

    authorize! :create_comment, result.to_debate

    form = Debate::CreateCommentForm.new(current_user, result)

    form.submit!(comment_params)
    @comments = result.comments.order(:created_at)
    @comment = Comment.new

    respond_to do |format|
      format.js
    end
  end

  def destroy
    @comment = Comment.find(params[:id])

    authorize! :delete, @comment

    debate_topic = @comment.debate_topic
    @comment.destroy
    @comments = debate_topic.comments

    respond_to do |format|
      format.js
    end
  end

  private

  def comment_params
    params.expect(comment: [:content]).to_h.to_options
  end

  def find_debate_topic(params)
    if params.include?(:custom_result_id)
      CustomResult.find(params[:custom_result_id])
    else
      raise ArgumentError, "Unknown debate topic: #{params}"
    end
  end
end

# frozen_string_literal: true

module Admin
  module Scipis
    class ReportsController < AdminController
      default_form_builder Admin::FormBuilder

      def index
        @scipi = QuestionGroup.find(params[:scipi_id])

        authorize! :view_reports, @scipi

        previous_report = @scipi.reports.order(created_at: :desc).last

        @report = @scipi.reports.new(prepared_for: previous_report&.prepared_for)
        @reports = @scipi.reports.includes(:created_by).order(created_at: :desc)
      end

      def create
        @scipi = QuestionGroup.find(params[:scipi_id])

        authorize! :create_report, @scipi

        @report = @scipi.reports.build(report_params)

        if @report.save
          redirect_to admin_scipi_reports_path(@scipi)
        else
          @reports = []
          render :index
        end
      end

      def destroy
        @scipi = QuestionGroup.find(params[:scipi_id])
        @report = @scipi.reports.find(params[:id])

        authorize! :delete, @report

        @report.destroy

        redirect_to admin_scipi_reports_path(@scipi)
      end

      private

      def report_params
        params
          .expect(report: %i[final certified prepared_for])
          .merge(created_by: Current.user)
      end
    end
  end
end

# frozen_string_literal: true

module Admin
  module Surveys
    module Results
      class SummaryController < AdminController
        def preview
          @custom_result = CustomResult.find(params[:result_id])
          authorize! :edit, @custom_result

          generator = ::Results::SummaryGenerator.new(@custom_result)
          summary = generator.preview(prompt: params[:full_prompt])
          render json: { summary: summary }
        rescue StandardError => e
          render json: { error: e.message }, status: :unprocessable_content
        end

        # generate AND save
        def create
          @custom_result = CustomResult.find(params[:result_id])
          authorize! :edit, @custom_result
          @custom_result.broadcast_replace_to(@survey, :results_list,
                                              target: "summary_status_#{@custom_result.id}",
                                              partial: 'admin/surveys/results/summary_status_indicator',
                                              locals: { custom_result: @custom_result, processing: true })

          generator = ::Results::SummaryGenerator.new(@custom_result)

          generator.generate_and_save!

          redirect_to admin_survey_results_path(@custom_result.survey),
                      notice: "Summary generated and saved! (\"#{summary_excerpt}\")"
        rescue StandardError => e
          redirect_to admin_survey_results_path(@custom_result.survey),
                      alert: "Failed to generate summary. #{e.message}"
        end

        def destroy
          @custom_result = CustomResult.find(params[:result_id])
          authorize! :edit, @custom_result

          @custom_result.summary&.destroy!

          redirect_to admin_survey_results_path(@custom_result.survey),
                      notice: t('.success')
        rescue StandardError => e
          redirect_to admin_survey_results_path(@custom_result.survey),
                      alert: "Failed to delete summary. #{e.message}"
        end

        private

        def summary_excerpt
          @custom_result.reload
          plain_text = @custom_result.result_summary.to_plain_text
          plain_text.length > 100 ? "#{plain_text[0..99]}..." : plain_text
        end
      end
    end
  end
end

# frozen_string_literal: true

module Admin
  module Surveys
    class ResultsController < AdminController
      include ::Results::PromptHelper

      default_form_builder FormBuilder

      def index
        @survey = QuestionGroup.find(params[:survey_id])
        authorize! :manage, @survey

        @results = @survey.result_definitions
                          .includes(:result_type, :render_type, :section)
                          .left_joins(:section)
                          .reorder(Arel.sql('COALESCE(result_sections.position, 0), custom_results.position'))
        setup_summary_collection_ids
      end

      def edit
        @custom_result = CustomResult.find(params[:id])
        @survey = @custom_result.survey
        @full_prompt = default_prompt if @custom_result.summarizable?

        authorize! :edit, @custom_result
      end

      def update
        @custom_result = CustomResult.find(params[:id])
        @survey = @custom_result.survey

        authorize! :edit, @custom_result

        if @custom_result.update(permitted_params)
          redirect_to edit_admin_result_path(@custom_result), notice: t('.success')
        else
          render :edit, status: :unprocessable_content
        end
      end

      def generate_summaries
        @survey = QuestionGroup.find(params[:survey_id])
        authorize! :manage, @survey

        result_ids = params[:result_ids]&.compact_blank

        results = @survey.result_definitions.where(id: result_ids)

        results.each do |result|
          result.broadcast_replace_to(@survey, :results_list,
                                      target: "summary_status_#{result.id}",
                                      partial: 'admin/surveys/results/summary_status_indicator',
                                      locals: { custom_result: result, processing: true })
        end

        ::Surveys::BulkSummaryGenerationJob.perform_later(@survey, results.map(&:id))

        # A redirect here *would* clear spinners, do we do this instead
        # (Would be nice to show a flash but not worth the complexity)
        head :ok
      end

      private

      def setup_summary_collection_ids
        @missing_summary_ids = @survey.result_definitions.summarizable.missing_summaries.pluck(:id)
        @needs_generation_ids = @survey.result_definitions.summarizable.needs_summary_generation.pluck(:id)
        @existing_summary_ids = @survey.result_definitions.summarizable.joins(:summary).pluck(:id)
      end

      def default_prompt
        ::Results::SummaryPrompt.new(result: @custom_result).default_prompt
      end

      def permitted_params
        params.expect(custom_result: [:summary_content])
      end
    end
  end
end

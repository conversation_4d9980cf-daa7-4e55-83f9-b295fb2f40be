# frozen_string_literal: true

class QuestionsController < ApplicationController
  layout 'application_legacy'

  before_action :find_question_group!
  before_action :find_question!, only: :edit

  def index
    authorize! :manage, @question_group

    @sectioned_questions = @question_group.sectioned_questions
    @owned_segmented_surveys = current_user.owned_surveys.segmented.order(id: :desc)
  end

  def new
    authorize! :manage, @question_group

    @question = QuestionForm.new(question_group: @question_group)
    set_flash_alerts
  end

  def edit
    authorize! :manage, @question_group

    @question = QuestionForm.new(question: @question)
    set_flash_alerts
  end

  private

  def set_flash_alerts
    flash_alerts = []
    flash_alerts << 'This question has already been answered. You may edit chart and display settings, but the question itself is locked.<br/>' if @question.answered?
    flash_alerts << 'This is an <strong>auto-submit survey</strong>. <u>Free text question type</u>, <u>answer explanation</u> and <u>required answer</u> options are disabled.<br/>' if @question.question_group.auto_submit?
    unless flash_alerts.empty?
      flash.now[:alert] = flash_alerts.join('<br />'.html_safe)
    end
  end

  def find_question_group!
    @question_group = QuestionGroup.find(params[:question_group_id])
  end

  def find_question!
    @question = @question_group.questions.find(params[:id])
  end
end

# frozen_string_literal: true

module Profiles
  class OrcidsController < ApplicationController
    rescue_from ORCID::AuthenticationError do
      redirect_to edit_profile_path, alert: 'An error occurred connecting your ORCID iD. Please try again.'
    end

    def new
      redirect_to ORCID.authorization_url(redirect_uri: orcid_redirect_url), allow_other_host: true
    end

    def authentication_complete
      code = params[:code]
      redirect_uri = orcid_redirect_url
      user = Current.user

      set_access_token_and_orcid_id(code:, redirect_uri:, user:)

      redirect_to edit_profile_path
    end

    private

    def set_access_token_and_orcid_id(code:, redirect_uri:, user:)
      gateway = ORCID::OAuthGateway.new
      response = gateway.access_token_and_orcid_id(code:, redirect_uri:)

      user.oauth_tokens.create!(
        resource_server_name: ORCID.resource_server_name,
        access_token: response['access_token'],
        token_type: response['token_type'],
        expires_at: response['expires_in'].to_i.seconds.from_now,
        refresh_token: response['refresh_token'],
        scope: response['scope']
      )
      user.profile.create_orcid_record!(orcid_id: response['orcid'], person_name: response['name'])
    rescue StandardError => e
      Rollbar.error(e)
      raise ORCID::AuthenticationError, e.message, cause: e
    end
  end
end

# frozen_string_literal: true

class VotesController < ApplicationController
  def create
    @comment = Comment.find(params[:comment_id])
    @yay = params[:yay]

    authorize! :vote, @comment

    @vote = @comment.votes.create!(user: Current.user, yay: @yay)
    @vote.save

    respond_to do |format|
      format.js
    end
  end

  def destroy
    @vote = Vote.find(params[:id])
    @comment = @vote.comment

    authorize! :vote, @comment

    @vote.destroy

    respond_to do |format|
      format.js
    end
  end
end

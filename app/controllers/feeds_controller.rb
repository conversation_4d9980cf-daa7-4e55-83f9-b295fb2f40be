# frozen_string_literal: true

class FeedsController < ApplicationController
  skip_authentication!

  ITEM_LIMIT = 5

  def index
    render json: feed_collection.map { |l| l.to_feed_listing(base_url: root_url) }.to_json
  end

  private

  def feed_collection
    case params[:branding]
    when 'announcements'
      Announcement.published.order(published_at: :desc).limit(ITEM_LIMIT)
    when 'pings'
      Ping.featured.limit(ITEM_LIMIT)
    when 'scipolls'
      QuestionGroup.featured_scipolls.limit(ITEM_LIMIT)
    when 'scipis'
      QuestionGroup.featured_scipis.limit(ITEM_LIMIT)
    when 'featured'
      featured_listings
    else
      QuestionGroup.none # return an empty relation
    end
  end

  def featured_listings
    featured = QuestionGroup.featured_scipis.limit(2) + QuestionGroup.featured_scipolls.limit(2)
    featured.sort_by(&:published_at).reverse
  end
end

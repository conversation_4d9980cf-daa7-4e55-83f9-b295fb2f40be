# frozen_string_literal: true

module Scipis
  class ReportGenerationJob < ApplicationJob
    discard_on StandardError do |job, error|
      Rollbar.error(error, job: job.class.name, arguments: job.arguments)
      Rails.logger.error("#{job.class.name} failed: #{error.message}")
    end

    def perform(report)
      host = ENV.fetch('MAILER_URL_HOST', 'localhost:3000')
      protocol = Rails.env.local? ? 'http' : 'https'

      Rails.application.routes.default_url_options = { host:, protocol: }
      ActiveStorage::Current.url_options = { host:, protocol: }

      admins = report.scipi.notifiable_owners
      scipi = report.scipi

      report.generate!

      Scipis::AdminsMailer.report_complete(scipi:, report:, admins:).deliver_later
    end
  end
end

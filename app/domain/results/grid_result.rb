# frozen_string_literal: true

module Results
  class GridResult
    include ValueObject

    class CellLineItem
      include ValueObject

      attr_reader :count, :label

      def initialize(label:, count:, cell_total:)
        @count = count
        @label = label
        @cell_total = cell_total
      end

      def percent
        return 0 if @cell_total.zero?

        (count / @cell_total.to_f) * 100
      end
    end

    class MultiValueCell
      include ValueObject

      attr_reader :count, :line_items

      def initialize(line_items:)
        @line_items = line_items
      end

      def each_line(&)
        @line_items.each(&)
      end

      def template
        'multi_value_cell'
      end
    end

    class SingleValueCell
      include ValueObject

      attr_reader :count

      def initialize(count:, total:)
        @count = count
        @total = total
      end

      def percent
        (count / @total.to_f) * 100
      end

      def template
        'single_value_cell'
      end
    end

    class Row
      include ValueObject

      attr_reader :name, :total

      def initialize(name:, cells:, total:)
        @name = name
        @cells = cells
        @total = total
      end

      def each_cell(&)
        @cells.each(&)
      end
    end

    attr_reader :column_names, :id, :rows

    def initialize(column_names:, first_cell_text:, id:, rows:)
      @column_names = column_names
      @first_cell_text = first_cell_text
      @id = id
      @rows = rows
    end

    def each_row(&)
      @rows.each(&)
    end

    def result_type
      'grid'
    end
  end
end

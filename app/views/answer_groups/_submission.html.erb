<div class="list-group">

  <% answer_group.answers.order( 'id asc' ).each do |answer| %>
    <div class="list-group-item item <%= "grid-row" if answer.question.grid_type? %>" id="question_<%= answer.question.id %>" data-question-id="<%= answer.question.id %>" data-answer-id="<%= answer.id %>">
      <h4 class="list-group-item-heading text-bold mb-3">
        <%= answer.question.title_with_number %>
      </h4>

      <% if answer.question.question_details.present? %>
        <div class="mb-3">
          <%= raw answer.question.question_details %>
        </div>
      <% end %>


      <div class="list-group-item-text">
        <div>
          <% if answer.question.grid_type? %>
            <% if answer.question.weighted_sum? %>
              <% weighted_sum_answer = WeightedSumGridAnswer.new(question: answer.question, answer: answer) %>
              <%= render 'answer_groups/weighted_grid_answer', answer: weighted_sum_answer %>
            <% else %>
              <%= grid_answer(answer).html_safe %>
            <% end %>
          <% elsif answer.selected_choices.any? %>
            <% answer.selected_choices.each do |selected_choice| %>
              <strong><%= tag.span(selected_choice.rank, class:'rank')  if selected_choice.rank.present? %> <span class="value text-decoration-underline"><%= selected_choice.answer_choice.label %></span></strong> &nbsp;
            <% end %>
          <% elsif answer.question.long_type? %>
            <%= answer.text_answer_content %>
          <% end %>
          <% if answer.attachment.attached? %>
              Attachment:
              <%= link_to answer.attachment_filename, scipis_answer_attachment_path(answer) %><br/>
          <% end %>
        </div>

        <% if answer.explanation.present? %>
          <div class="text-italic text-sm mt-2"><%= answer.explanation %></div>
        <% end %>
      </div>
    </div>
  <% end %>

</div>

<%# locals: (result:) -%>

<div class="row" data-controller="summary-generator"
     data-summary-generator-result-id-value="<%= result.id %>">

  <div class="col-lg-6">
    <%= form_with model: result,
                  url: admin_result_path(result),
                  local: true,
                  data: { summary_generator_target: "form" } do |f| %>


      <div class="form-group">
        <%= f.label :summary_content, "Result Summary" %>
        <div data-summary-generator-target="richText">
          <%= f.rich_text_area :summary_content, class: 'form-control' %>
        </div>
      </div>


      <div class="form-group d-flex align-items-center justify-content-between my-2">
        <%= f.submit 'Save Summary', class: 'btn btn-primary', data: { summary_generator_target: "saveButton" } %>
        <%= link_to 'Cancel', edit_admin_result_path(result), class: 'btn btn-secondary' %>
      </div>
    <% end %>





  </div>


  <div class="col-lg-6">
    <%= render(CardComponent.new(classes: 'shadow-sm')) do |card| %>
      <% card.with_header do %>
        <h3 class="m-0">AI Summary Generator</h3>
      <% end %>
      <% card.with_body do %>
        <% if result.summarizable? %>

          <%= button_tag class: "btn btn-primary mb-2",
                         data: {
                           action: "summary-generator#generate",
                           summary_generator_target: "button"
                         } do %>
            <i class="fa fa-spinner fa-spin me-2 d-none" data-summary-generator-target="spinner"></i>
            <span data-summary-generator-target="buttonText">Generate Summary</span>
          <% end %>

          <div data-summary-generator-target="status" class="status-message my-2"></div>
          <details class="text-sm">
            <summary>Preview & Edit AI Prompt</summary>
            <div class="mt-2 d-flex  flex-column gap-1">
              <div class="text-xs text-muted ">System Instructions (non-editable)</div>
              <div class="text-muted text-xs">
                <%= t('claude.instructions.system') %>
              </div>
              <div class="text-xs">Full Prompt (editable)</div>
              <textarea class="form-control text-xs h-auto" rows="50" data-summary-generator-target="fullPrompt">
                <%= @full_prompt.strip %>
              </textarea>
            </div>
          </details>
        <% else %>
          <p>This result type does not yet support AI summary generation. <br>
            If you create one manually, you can save it from here.</p>
        <% end %>

      <% end %>
    <% end %>



    <% if result.summary.present? %>
      <%= render(CardComponent.new(classes: '')) do |card| %>
        <% card.with_header(classes: '') do %>
          <h3 class="m-0">Saved Summary Information</h3>
        <% end %>
        <% card.with_body(classes: '') do %>
          <div class="row text-sm">
            <div class="col-md-6">
              <strong>Timestamps:</strong><br>
              <div class="text-xs">
              <strong>Created:</strong> <%= result.summary.created_at.strftime("%B %d, %Y at %l:%M %p") %><br>
              <strong>Updated:</strong> <%= result.summary.updated_at.strftime("%B %d, %Y at %l:%M %p") %>
              </div>
            </div>
            <div class="col-md-6">
              <strong>Based on <%= pluralize(result.summary.answers.count, 'answer') %>:</strong>
              <% if result.summary.answers.any? %>
                <ul class="list-unstyled mt-1 mb-0 text-xs">
                  <% result.summary.answers.includes(answer_group: :submitter).each_with_index do |answer, index| %>
                    <li class="mb-1">
                      <%= link_to "#{admin_surveys_submission_path(answer.answer_group)}#question_#{result.question.id}",
                                  class: 'text-decoration-none',
                                  title: "View answer by #{prompt_display_id(result.survey, answer.answer_group.submitter)}",
                                  target: '_blank' do %>
                        <%= render(Fa5::Icon.new('external-link-alt', classes: 'me-1')) %>
                        Answer by <%= prompt_display_id(result.survey, answer.answer_group.submitter) %>
                        <span class="text-muted">(Answer ID: <%= answer.id %>)</span>
                      <% end %>
                    </li>
                  <% end %>
                </ul>
              <% else %>
                <span class="text-muted">No answers recorded</span>
              <% end %>
            </div>
          </div>
          <% if result.summary.needs_regeneration? %>
            <div class="alert alert-warning mt-2 mb-0" role="alert">
              <%= render(Fa5::Icon.new('exclamation-triangle', classes: 'me-2')) %>
              <strong>Note:</strong> This summary may be outdated. Answers have been modified since this summary was
              generated.
            </div>
          <% end %>
        <% end %>
      <% end %>

    <% end %>


  </div>

</div>

<header class="mb-2">
  <div class="row">
    <div class="col">
      <%= render(Admin::BreadcrumbComponent.new) do |crumbs| %>
        <% crumbs.with_crumb(href: admin_surveys_preferred_path(@survey.branding)) { @survey.branding_name.pluralize } %>
        <% crumbs.with_crumb(href: admin_survey_path(@survey)) { @survey.display_name.truncate(50) } %>
      <% end %>
    </div>
  </div>
  <div class="row  mb-2">
    <div class="col">
      <h1 class="m-0">
        Results AI Management (Beta)
      </h1>
      <p class="text-muted">
        This currently gives a list overview of AI summaries and will be expanded to handle bulk summary generations and
        results admin.
      </p>
    </div>

    <div class="col-auto">
      <%= link_to 'Results Builder', survey_builder_survey_result_definitions_path(@survey), class: 'me-3', title: "Legacy Results Builder" %>
      <%= link_to 'View Results', survey_results_path(@survey), class: '', title: "View Results" %>
    </div>

  </div>
</header>

<%= turbo_stream_from(@survey, :bulk_generation) %>
<div id="reload_trigger"></div>

<%
  select_list = stimulus_select_list(selected_item_class: 'table-primary')
  dependent_actions = stimulus_dependent_actions
%>

<%= form_with url: generate_summaries_admin_survey_results_path(@survey), local: true,
              data: {
                controller: "#{select_list.controller_name} #{dependent_actions.controller_name} summary-generation-confirm",
                action: 'select-list:selectionChanged->dependent-actions#selectionChanged',
                summary_generation_confirm_existing_summary_ids_value: @existing_summary_ids.to_json,
                **select_list.controller_attrs,
                **dependent_actions.controller_attrs
              } do |form| %>

  <div class="d-flex align-items-center justify-content-between p-2 px-4 mb-4 border border-1 border-secondary rounded">
    <div class="d-flex align-items-center justify-content-start">
      <h2 class="mb-0">Survey Results</h2>
      <div class="d-none font-weight-light small ms-3" data-select-list-target="selectionCount">
        <span class="count">0</span> Results Selected
      </div>
    </div>
    <div class="d-flex align-items-center justify-content-start">
      <%= form.button type: :submit,
                      class: 'btn btn-primary btn-sm',
                      data: {
                        **dependent_actions.action_data_attrs('generate_summaries')
                      } do %>
        <%= render(Fa5::Icon.new('wand-magic-sparkles')) %> Generate Summaries
      <% end %>
    </div>
  </div>



  <main>
    <div class="row">
      <div class="col-md-12">

        <%= turbo_stream_from(@survey, :results_list) %>
        <table class="table table-striped table-borderless table-sm">
          <thead>
          <tr>
            <th scope="col" class="text-center" style="width: 50px;">
              <%= render(Bootstrap::DropdownComponent.new) do |dropdown| %>
                <% dropdown.with_toggle(variant: :link, classes: 'font-weight-bold p-0') { 'Select' } %>
                <% dropdown.with_menu do |menu| %>
                  <% menu.with_item(href: '#', data: { action: select_list.action('selectAll') }) { 'All' } %>
                  <% menu.with_item(href: '#', data: { action: select_list.action('unselectAll') }) { 'None' } %>
                  <% menu.with_item(divider: true) %>
                  <% menu.with_item(header: true) { 'By Summary Status' } %>
                  <% menu.with_item(href: '#', data: { action: select_list.action('selectByFilter'), select_list_filter: 'missing-summary' }) { 'Missing Summary' } %>
                  <% menu.with_item(href: '#', data: { action: select_list.action('selectByFilter'), select_list_filter: 'needs-generation' }) { 'Needs (re)Generation' } %>
                <% end %>
              <% end %>
            </th>
            <th>ID</th>
            <th>Num</th>
            <th>Result Title</th>
            <th><span data-controller="tooltip" title="Number of final answers currently included in this specific result">Answers</span>
            </th>
            <th>Type</th>
            <th>AI:<br>Status</th>
            <th><span data-controller="tooltip" title="Current summary content">AI:<br>Summary</span></th>
            <th>
              <span data-controller="tooltip" title="Number of answers available when summary was last generated">AI:<br>Answers</span>
            </th>
            <th>AI:<br>Actions</th>
            <th>Links</th>
          </tr>
          </thead>

          <tbody>
          <% @results.each do |result| %>
            <%
              filter_values = []
              filter_values << 'missing-summary' if @missing_summary_ids.include?(result.id)
              filter_values << 'needs-generation' if @needs_generation_ids.include?(result.id)
            %>
            <%= tag.tr class: "result-#{result.id} #{select_list.item_class if result.result_type&.summarizable?}",
                       data: result.result_type&.summarizable? ? select_list.item_data_attrs(filter_values: filter_values) : {} do %>
              <td class="text-center">
                <% if result.result_type&.summarizable? %>
                  <%= form.check_box 'result_ids[]',
                                     {
                                       id: "result_#{result.id}",
                                       class: 'select-item-selection-target',
                                       data: dependent_actions.item_data_attrs('generate_summaries')
                                     },
                                     result.id,
                                     nil %>
                <% else %>
                  <input type="checkbox" title="This result type does NOT support AI summaries" disabled />
                <% end %>
              </td>

              <td><%= result.id %></td>
              <td><%= result.number %></td>
              <td>
                <%= link_to truncate(result.title, length: 30), survey_result_path(@survey, result), class: '', title: "View #{result.title}" %>
              </td>
              <td><%= result.final_answer_count if result.final_answer_count.positive? %></td>
              <td>
                <% unless result.result_type&.summarizable? %>
                  <%= render(
                        Fa5::Icon.new(
                          'warning',
                          data: {
                            controller: 'tooltip',
                          },
                          title: 'AI Summary Generation not supported',
                          class: 'icon-button'
                        )
                      )
                  %>
                <% end %>
                <span title="<%= result.result_type.name %>"><%= truncate(result.result_type.name, length: 30) %></span>
              </td>
              <td>
                <%= render 'summary_status_indicator', custom_result: result %>
              </td>
              <td>
                <% if result.result_summary.present? %>
                  <%= render(
                        Fa5::Icon.new(
                          'quote-left',
                          data: {
                            controller: 'popover',
                            bs_content: simple_format(result.result_summary.to_plain_text),
                            bs_placement: 'bottom',
                            bs_container: 'body',
                            bs_trigger: 'hover',
                            bs_html: 'true',
                          },
                          title: 'Results Summary',
                          class: 'icon-button'
                        )
                      )
                  %>
                <% end %>
              </td>

              <td>
                <%= result.summary.answers.count if result.result_summary.present? %>
              </td>

              <td>
                <div class="d-flex align-items-center justify-content-between">
                  <% # Button_to helpers will break here, specifically the 'needs update' button  %>
                  <% if result.result_type&.summarizable? %>
                    <% if result.summary.nil? %>
                      <button type="submit"
                              formaction="<%= admin_result_summary_path(result) %>"
                              formmethod="post"
                              class="btn btn-success btn-sm"
                              data-controller="tooltip"
                              title="This result has submission data, but no summary. Click to Generate an AI summary using the default prompt"
                              data-turbo-submits-with="<i class='fa fa-spinner fa-spin'></i> Generating...">
                        <%= render(Fa5::Icon.new('wand-magic-sparkles')) %> Generate
                      </button>
                    <% elsif result.summary.needs_regeneration? %>
                      <button type="submit"
                              formaction="<%= admin_result_summary_path(result) %>"
                              formmethod="post"
                              class="btn btn-warning btn-sm"
                              data-controller="tooltip"
                              title="Answers may have changed since last generation - regeneration recommended"
                              data-confirm="This will overwrite the existing summary. Are you sure?"
                              data-turbo-submits-with="<i class='fa fa-spinner fa-spin'></i> Regenerating...">
                        <%= render(Fa5::Icon.new('exclamation-triangle')) %> Needs Update
                      </button>
                    <% else %>
                      <button type="submit"
                              formaction="<%= admin_result_summary_path(result) %>"
                              formmethod="post"
                              class="btn btn-secondary btn-sm"
                              data-controller="tooltip"
                              title="This result has a current summary, but you can regenerate it if you wish"
                              data-confirm="This will overwrite the existing summary. Are you sure?"
                              data-turbo-submits-with="<i class='fa fa-spinner fa-spin'></i> Regenerating...">
                        <%= render(Fa5::Icon.new('sync-alt')) %> Regenerate
                      </button>
                    <% end %>
                  <% else %>
                    <%= link_to 'Manual Summary',
                                edit_admin_result_path(result),
                                'data-controller': 'tooltip',
                                title: 'This result does not support AI summary generation, but you can create a manual summary if you wish',
                                class: 'btn btn-sm btn-outline-secondary' %>
                  <% end %>

                  <div>
                    <%= link_to edit_admin_result_path(result),
                                class: 'btn btn-outline-secondary btn-sm ms-2',
                                data: {
                                  controller: 'tooltip',
                                },
                                title: 'Manually edit or create summary using a custom prompt' do %>
                      <%= render(Fa5::Icon.new('pencil-alt')) %>
                    <% end %>

                    <% if result.result_summary.present? %>
                      <%= link_to admin_result_summary_path(result),
                                  method: :delete,
                                  class: 'btn btn-outline-secondary btn-sm ms-2',
                                  data: {
                                    controller: 'tooltip',
                                    confirm: 'Are you sure you want to delete the AI summary? This action cannot be undone.'
                                  },
                                  title: 'Delete AI summary' do %>
                        <%= render(Fa5::Icon.new('trash')) %>
                      <% end %>
                    <% else %>
                      <button type="button"
                              class="btn btn-outline-secondary btn-sm ms-2 opacity-50"
                              disabled>
                        <%= render(Fa5::Icon.new('trash')) %>
                      </button>
                    <% end %>
                  </div>

                </div>
              </td>
              <td>
                <%= link_to 'View', survey_result_path(@survey, result), class: 'me-3', title: "View #{result.title}" %>
                <%= link_to 'Edit Settings', edit_survey_builder_result_definition_path(result), class: '', title: "Edit #{result.title}" %>
              </td>
            <% end %>
          <% end %>
          </tbody>
        </table>

      </div>
    </div>
  </main>

<% end %>

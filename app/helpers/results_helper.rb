# frozen_string_literal: true

module ResultsHelper
  include ResultsTemplateHelper

  def answer_count_label(result)
    answer_count_value, skip_count_value = if result.respond_to?(:answer_count)
                                             [result.answer_count, result.skip_count]
                                           elsif result.is_a?(Hash) && result.key?(:answerCount)
                                             [result[:answerCount], result[:skipCount]]
                                           end

    return if answer_count_value.nil?

    answer_count = pluralize(answer_count_value, 'Answer')
    skip_count = pluralize(skip_count_value, 'Skip') if skip_count&.positive?

    tag.small "(#{[answer_count, skip_count].compact.join(', ')})"
  end

  def explanation_badge_class(result_definition, series_index)
    return if controller.controller_name == 'reports'
    return "legend-item-#{series_index}" if result_definition.doughnut_chart?
    # Gray. Do not correspond with legend colors in this context (because question and group-by-question are reversed)
    return 'text-bg-secondary' if result_definition.grouped?

    'legend-item-default'
  end

  def render_attachment_preview(result)
    ActiveStorage::Current.url_options = { host: request.base_url }
    image_tag(result.attachment.url)
  end

  def render_spinner
    tag.i('', class: 'fa fa-spinner fa-pulse fa-5x fa-fw')
  end

  def survey_results_home_path(survey)
    survey.scipoll? ? survey_path_for(survey) : survey_results_path(survey)
  end

  def results_conclusion_path(survey)
    survey_results_path(survey, conclusion: true)
  end

  def render_content(survey:, active_type:)
    render "surveys/results/#{active_type}", survey:
  end

  def render_answer_explanations(result_definition)
    return unless result_definition.explanations?

    question = result_definition.question
    question_type = results_template_question_type(question)

    render 'surveys/results/answer_explanations',
           question_type:,
           answers: question.final_answers.with_explanation
  end

  def render_file_upload_badge(answer)
    return tag.em '(No file uploaded)' unless answer.attachment.attached?

    link_to scipis_answer_attachment_path(answer), class: 'badge text-bg-info', rel: 'noopener', target: '_blank' do
      fa_icon('file', class: 'me-1', fa_style: 'far', text: answer.attachment_filename)
    end
  end

  def render_results_nav_links(custom_results, current_result)
    html_values = custom_results.map { |r| render_result_nav_link(r, current_result) }
    safe_join(html_values)
  end

  def render_result_nav_link(result, current_result)
    result_number_html = tag.i(result.number.to_s)
    link_text_html = tag.span(result_number_html + strip_tags(result.title), class: 'result-title')
    li_class = result == current_result ? 'result active' : 'result'
    path_params = results_template_path_params_for(result)
    link_path = survey_result_path(path_params)
    tag.li(link_to(link_text_html, link_path), class: li_class)
  end

  def render_live_view_toggle(survey, custom_result, live)
    return unless can?(:view_live_results, survey)
    return if custom_result.nil?

    link_text = "<i class='fa fa-video-camera'></i>".html_safe
    link_path = live ? survey_result_path(survey_id: survey.id, id: custom_result.id) : custom_result.live_view_path
    link_class = live ? 'btn btn-sm btn-danger live-results-toggle' : 'btn btn-sm live-results-toggle'
    link_title = live ? 'Live Results View Engaged, click to disengage' : 'Live Results Disabled, click to enable'
    link_to link_text, link_path, class: link_class, title: link_title
  end

  # rubocop:disable Metrics/PerceivedComplexity
  def scitrust_score_explanation_badge(answer)
    question = answer.question
    answer_choices = question.answer_choices

    answer_rows = (0...question.row_count).map do |row_num|
      answer.row_at(row_num)
    end

    answer_values = answer_rows.flatten.map do |label|
      answer_choices.find { |ac| ac.label == label }&.value
    end

    score = Math.geometric_mean(answer_values) if answer_values.none?(&:nil?)

    formatted_score = score.present? ? format('%<score>.2f', score:) : 'N/A'

    badge_bg_color = if score&.positive?
                       Results::ChartData::SciTrustScoreQuery::DEFAULT_COLOR
                     elsif score&.zero?
                       Results::ChartData::SciTrustScoreQuery::NO_CONFIDENCE_COLOR
                     else
                       '#dddddd'
                     end

    tag.div formatted_score,
            class: 'badge',
            title: "SciTrust Score: #{formatted_score}",
            style: "background-color: #{badge_bg_color};"
  end
  # rubocop:enable Metrics/PerceivedComplexity
end

# frozen_string_literal: true

module Admin
  module Surveys
    module ResultsHelper
      def summary_status_indicator(result, processing: false)
        if processing
          return content_tag(:i, '', class: 'fa fa-spinner fa-spin text-secondary',
                                     title: 'Generating summary...')
        end

        if result.summary.present?
          if result.summary.needs_regeneration?
            content_tag :i, '', class: 'fa fa-circle text-warning',
                                title: 'Has summary, but needs regeneration'
          else
            content_tag :i, '', class: 'fa fa-circle text-success',
                                title: 'Summary generated'
          end
        elsif result.summarizable?
          content_tag :i, '', class: 'fa fa-circle text-muted',
                              title: 'No summary yet'
        else
          content_tag :i, '', class: 'fa fa-circle-o text-muted',
                              title: 'AI Summary not supported (manual can be included)'
        end
      end
    end
  end
end

# frozen_string_literal: true

class DailyActivityDigestSerializer < ActiveJob::Serializers::ObjectSerializer
  def serialize?(obj) = obj.is_a? DailyActivityDigest

  def serialize(digest)
    super(
      'ping_author_notifications' => digest.ping_author_notifications,
      'ping_subscriber_notifications' => digest.ping_subscriber_notifications,
      'pings' => digest.pings,
      'relevant_expertise' => digest.relevant_expertise,
      'profile_alerts' => digest.profile_alerts
    )
  end

  def deserialize(hash)
    DailyActivityDigest.new(
      ping_author_notifications: hash['ping_author_notifications'],
      ping_subscriber_notifications: hash['ping_subscriber_notifications'],
      pings: hash['pings'],
      relevant_expertise: hash['relevant_expertise'],
      profile_alerts: hash['profile_alerts']
    )
  end
end

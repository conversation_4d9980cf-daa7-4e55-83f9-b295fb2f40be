import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    existingSummaryIds: Array,
  };

  connect() {
    this.element.addEventListener("submit", this.handleSubmit.bind(this));
  }

  handleSubmit(event) {
    const selectedIds = this.getSelectedResultIds();

    if (selectedIds.length === 0) {
      return;
    }

    const existingSummaryIds = this.existingSummaryIdsValue || [];
    const willOverwrite = selectedIds.some((id) =>
      existingSummaryIds.includes(parseInt(id))
    );

    if (willOverwrite) {
      const confirmMessage =
        "Some selected results already have summaries that will be overwritten. Are you sure you want to continue?";

      if (!confirm(confirmMessage)) {
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    }
  }

  getSelectedResultIds() {
    const checkboxes = this.element.querySelectorAll(
      'input[name="result_ids[]"]:checked'
    );
    return Array.from(checkboxes).map((checkbox) => checkbox.value);
  }
}

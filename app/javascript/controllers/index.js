// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"

import ActionTextAttachmentValidatorController from "./action_text_attachment_validator_controller"
application.register("action-text-attachment-validator", ActionTextAttachmentValidatorController)

import Admin__BulkMessageController from "./admin/bulk_message_controller"
application.register("admin--bulk-message", Admin__BulkMessageController)

import Admin__ExpertSearchFormController from "./admin/expert_search_form_controller"
application.register("admin--expert-search-form", Admin__ExpertSearchFormController)

import Admin__ExpertSearchResultsController from "./admin/expert_search_results_controller"
application.register("admin--expert-search-results", Admin__ExpertSearchResultsController)

import Admin__ExportController from "./admin/export_controller"
application.register("admin--export", Admin__ExportController)

import Admin__PublishButtonController from "./admin/publish_button_controller"
application.register("admin--publish-button", Admin__PublishButtonController)

import AnswerGroupTabsController from "./answer_group_tabs_controller"
application.register("answer-group-tabs", AnswerGroupTabsController)

import AnswerGroupsController from "./answer_groups_controller"
application.register("answer-groups", AnswerGroupsController)

import AutocompleteController from "./autocomplete_controller"
application.register("autocomplete", AutocompleteController)

import AutosubmitController from "./autosubmit_controller"
application.register("autosubmit", AutosubmitController)

import BulkActionsController from "./bulk_actions_controller"
application.register("bulk-actions", BulkActionsController)

import ChartController from "./chart_controller"
application.register("chart", ChartController)

import CheckableRowController from "./checkable_row_controller"
application.register("checkable-row", CheckableRowController)

import CheckboxSelectAllController from "./checkbox_select_all_controller"
application.register("checkbox-select-all", CheckboxSelectAllController)

import ClipboardController from "./clipboard_controller"
application.register("clipboard", ClipboardController)

import CustomChartController from "./custom_chart_controller"
application.register("custom-chart", CustomChartController)

import DefaultValuesController from "./default_values_controller"
application.register("default-values", DefaultValuesController)

import DependentActionsController from "./dependent_actions_controller"
application.register("dependent-actions", DependentActionsController)

import DialogController from "./dialog_controller"
application.register("dialog", DialogController)

import DisallowFieldValueController from "./disallow_field_value_controller"
application.register("disallow-field-value", DisallowFieldValueController)

import DownloadStatusController from "./download_status_controller"
application.register("download-status", DownloadStatusController)

import DynamicFieldsController from "./dynamic_fields_controller"
application.register("dynamic-fields", DynamicFieldsController)

import EnableSubmitController from "./enable_submit_controller"
application.register("enable-submit", EnableSubmitController)

import EnablerController from "./enabler_controller"
application.register("enabler", EnablerController)

import FetchController from "./fetch_controller"
application.register("fetch", FetchController)

import FormChangesController from "./form_changes_controller"
application.register("form-changes", FormChangesController)

import FormRowsController from "./form_rows_controller"
application.register("form-rows", FormRowsController)

import GeometricMeanAnswerController from "./geometric_mean_answer_controller"
application.register("geometric-mean-answer", GeometricMeanAnswerController)

import LazyModalController from "./lazy_modal_controller"
application.register("lazy-modal", LazyModalController)

import LimitSelectedChoicesController from "./limit_selected_choices_controller"
application.register("limit-selected-choices", LimitSelectedChoicesController)

import MarkForDestructionController from "./mark_for_destruction_controller"
application.register("mark-for-destruction", MarkForDestructionController)

import ModalController from "./modal_controller"
application.register("modal", ModalController)

import MultiTabDetectionController from "./multi_tab_detection_controller"
application.register("multi-tab-detection", MultiTabDetectionController)

import MultiselectController from "./multiselect_controller"
application.register("multiselect", MultiselectController)

import NotesController from "./notes_controller"
application.register("notes", NotesController)

import PingFormController from "./ping_form_controller"
application.register("ping-form", PingFormController)

import PopoverController from "./popover_controller"
application.register("popover", PopoverController)

import RankedChoiceQuestionController from "./ranked_choice_question_controller"
application.register("ranked-choice-question", RankedChoiceQuestionController)

import RecaptchaController from "./recaptcha_controller"
application.register("recaptcha", RecaptchaController)

import RedirectsController from "./redirects_controller"
application.register("redirects", RedirectsController)

import RejectionModalController from "./rejection_modal_controller"
application.register("rejection-modal", RejectionModalController)

import ResultsAdminToolsController from "./results_admin_tools_controller"
application.register("results-admin-tools", ResultsAdminToolsController)

import ResultsSidebarController from "./results_sidebar_controller"
application.register("results-sidebar", ResultsSidebarController)

import RoleFormController from "./role_form_controller"
application.register("role-form", RoleFormController)

import ScipiApplicationController from "./scipi_application_controller"
application.register("scipi-application", ScipiApplicationController)

import ScipollSettingsController from "./scipoll_settings_controller"
application.register("scipoll-settings", ScipollSettingsController)

import ScoreWeightsController from "./score_weights_controller"
application.register("score-weights", ScoreWeightsController)

import SelectAllInputController from "./select_all_input_controller"
application.register("select-all-input", SelectAllInputController)

import SelectListController from "./select_list_controller"
application.register("select-list", SelectListController)

import SelectionController from "./selection_controller"
application.register("selection", SelectionController)

import ShowHideController from "./show_hide_controller"
application.register("show-hide", ShowHideController)

import StickyActionBarController from "./sticky_action_bar_controller"
application.register("sticky-action-bar", StickyActionBarController)

import SummaryGenerationConfirmController from "./summary_generation_confirm_controller"
application.register("summary-generation-confirm", SummaryGenerationConfirmController)

import SummaryGeneratorController from "./summary_generator_controller"
application.register("summary-generator", SummaryGeneratorController)

import SurveySettingsFormController from "./survey_settings_form_controller"
application.register("survey-settings-form", SurveySettingsFormController)

import SwitchToController from "./switch_to_controller"
application.register("switch-to", SwitchToController)

import TextScansController from "./text_scans_controller"
application.register("text-scans", TextScansController)

import ToggleClassController from "./toggle_class_controller"
application.register("toggle-class", ToggleClassController)

import ToggleDisabledController from "./toggle_disabled_controller"
application.register("toggle-disabled", ToggleDisabledController)

import TogglePasswordController from "./toggle_password_controller"
application.register("toggle-password", TogglePasswordController)

import ToggleableFieldsController from "./toggleable_fields_controller"
application.register("toggleable-fields", ToggleableFieldsController)

import TooltipController from "./tooltip_controller"
application.register("tooltip", TooltipController)

import ValueExistsController from "./value_exists_controller"
application.register("value-exists", ValueExistsController)

import ValueUpdaterController from "./value_updater_controller"
application.register("value-updater", ValueUpdaterController)

import VoteController from "./vote_controller"
application.register("vote", VoteController)

import WeightedSumAnswerController from "./weighted_sum_answer_controller"
application.register("weighted-sum-answer", WeightedSumAnswerController)

import WorkingController from "./working_controller"
application.register("working", WorkingController)

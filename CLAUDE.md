# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
SciPinion is a Ruby on Rails application for scientific peer review and polling. It enables scientific experts to participate in surveys, polls, and contribute to scientific opinion collection.

The application serves several related product types:
- **SciPi**: Scientific peer review system
- **Sci<PERSON>oll**: Polling system for collecting scientific opinions
- **Pings**: Quick-response questions

## Code Style Guidelines
- Ruby version: 3.4.3
- No spaces inside array literal brackets
- Use 2 spaces for indentation
- Use snake_case for method and variable names
- Use CamelCase for class and module names
- Prefer fixtures for test data
- Keep methods small and focused on a single responsibility
- Add appropriate tests for new functionality

### Controllers
- Follow RESTful conventions for controllers

### Active Record models
- Use rails generators to generate models and migrations

## Important rules for Claude
- NEVER edit db/schema.rb directly

## Development Environment Setup
### Prerequisites
- Ruby (version in `.ruby-version`)
- Node.js (^22.0)
- Yarn (^1.22)
- PostgreSQL
- Elasticsearch (v7.2.0)
- Redis

### Setup Commands
```bash
# Install dependencies
bundle install
yarn install

# Database setup
cp config/database.yml.sample config/database.yml
# Edit database.yml with your PostgreSQL credentials
bin/rails db:setup

# Start the development server
bin/rails s

# Start all development processes with Foreman
bin/dev
```

### Testing
```bash
# Run all tests
bin/rails test

# Run specific test file
bin/rails test test/models/user_test.rb

# Run specific test
bin/rails test test/models/user_test.rb:42

# Run system tests
bin/rails test:system

# Run JavaScript tests
bin/rails test:js
# OR
yarn test

# Run pre-commit checks (bundle audit, rubocop, tests)
bin/rails test:precommit
```

### Code Quality
```bash
# Run RuboCop linting
bin/rails rubocop

# Run RuboCop auto-correction
bin/rails rubocop:auto_correct

# Run bundle audit for security vulnerabilities
bin/rails bundle:audit
```

## Architecture Overview
SciPinion is structured as a Rails application with several key components:

### Core Models
- `User`: Represents users of the system, including experts and administrators
- `Profile`: Contains expert profile information, including expertise, education, and CV
- `QuestionGroup`: The central model representing a survey, SciPoll, or SciPi
- `Question`: Questions within a survey
- `Answer`: Responses to questions
- `AnswerGroup`/`Submission`: A collection of answers from a single user for a survey
- `Invite`: Used for recruiting experts and managing participation
- `Comment`: For discussions on results

### Key Components
- **Expertise Management**: Tracks expert qualifications and areas of expertise
- **Survey Management**: Creation and management of surveys, polls, and review panels
- **Participant Selection**: Tools for reviewing applicants and selecting participants
- **Comment/Discussion System**: For scientific debate around results
- **Result Visualization**: Processing and display of survey/poll results

### Feature Modules
- **SciPi**: Scientific peer review system
- **SciPoll**: Scientific polling system
- **Pings**: Quick-response questions
- **Contracts**: Integration with SignNow for electronic signatures
- **Moderation**: Content moderation and reporting system
- **ORCID Integration**: For researcher identity verification

## External Integrations
- **SignNow**: Used for electronic signatures and contract management
- **Postmark**: For email delivery
- **Elasticsearch**: For expert search and profile indexing
- **AWS S3**: For file storage (CVs, file uploads)
- **ORCID**: Researcher identification and CV verification

## Testing Approach
- Standard Rails testing with MiniTest
- JavaScript testing with Jest
- System tests using Capybara
- Factory Bot for test data generation
- WebMock for stubbing external services
- Avoid using mocks and stubs, unless specifically told to.
- Do not assert the beginning state in tests, only assert on the results.

### Controller testing
- Do not test authentication or authorization in controller tests, unless specifically told to. 

## Deployment
The application is deployed on Heroku with the following considerations:
- Puma web server with custom query string length configuration
- Delayed Job for background processing
- Multiple environments (staging, production)
- Environment-specific credentials stored in encrypted credential files

{"name": "app.scipinion.com", "license": "UNLICENSED", "private": true, "engines": {"node": "^22.0", "yarn": "^1.22"}, "jest": {"automock": false, "roots": ["<rootDir>/app/javascript"], "setupFilesAfterEnv": ["<rootDir>/app/javascript/testSupport/setup.js"], "testEnvironment": "jsdom", "transform": {"^.+\\.js$": "esbuild-jest"}}, "dependencies": {"@hotwired/stimulus": "^3.1.0", "@hotwired/turbo-rails": "^8.0.13", "@popperjs/core": "^2.11.6", "@rails/actioncable": "~8.0", "@rails/actiontext": "~8.0", "@rails/activestorage": "~8.0", "@rails/request.js": "^0.0.12", "@rails/ujs": "~7.1.400", "@stimulus/polyfills": "^2.0.0", "apexcharts": "^5.2.0", "bootstrap": "^5.3.3", "bootswatch": "^5.3.3", "bs-custom-file-input": "^1.3.4", "esbuild": "^0.25.4", "jquery": "^3.7.1", "list.js": "^2.3.0", "rollbar": "^2.26.4", "sass": "^1.90.0", "slim-select": "^2.12.0", "sortablejs": "^1.15.6", "trix": "^2.1.15"}, "devDependencies": {"@eslint/js": "^9.29.0", "esbuild-jest": "^0.5.0", "eslint": "^9.29.0", "eslint-plugin-jest": "^29.0.1", "globals": "^16.4.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jest-fetch-mock": "^3.0.3", "mutationobserver-shim": "^0.3.7", "prettier": "^3.6.1"}, "prettier": {"trailingComma": "es5"}, "scripts": {"test": "jest", "build:css": "sass app/assets/stylesheets/admin/application.scss:app/assets/builds/admin/application.css app/assets/stylesheets/application.scss:app/assets/builds/application.css --no-source-map --load-path=vendor/assets --load-path=vendor/assets/stylesheets --load-path=node_modules", "analyze": "esbuild app/javascript/*.* --bundle --sourcemap --outdir=app/assets/builds --analyze --target=es2017", "build": "esbuild app/javascript/*.* --bundle --sourcemap --outdir=app/assets/builds --target=es2017", "lint": "eslint . --ext .js && prettier --check 'app/javascript/**/*.js'", "lint:fix": "eslint . --ext .js --fix && prettier --write 'app/javascript/**/*.js'"}, "type": "module"}
// Patches Chart.js 1 to allow y-axis labels
// http://stackoverflow.com/questions/31913967/how-to-set-chartjs-y-axis-title
Chart.types.Line.extend({
  name: "LineAlt",
  draw: function () {
    Chart.types.Line.prototype.draw.apply(this, arguments);

    var ctx = this.chart.ctx;
    ctx.save();
    // text alignment and color
    ctx.textAlign = "center";
    ctx.textBaseline = "bottom";
    ctx.fillStyle = this.options.scaleFontColor;
    // position
    var x = this.scale.xScalePaddingLeft * 0.4;
    var y = this.chart.height / 2;
    // change origin
    ctx.translate(x, y);
    // rotate text
    ctx.rotate(-90 * Math.PI / 180);
    ctx.fillText(this.datasets[0].label, 0, 0);
    ctx.restore();
  }
});
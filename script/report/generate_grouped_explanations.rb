#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative 'script_helper'

if ARGV.empty?
  puts "Usage: #{$PROGRAM_NAME} <result_id>"
  exit 1
end

$page_header_height = 54

result_id = ARGV[0].to_i

result = CustomResult.find(result_id)
grouped_explanations = result.grouped_explanations

file_path = Rails.root.join("tmp/Result #{result.id} Grouped Explanations.pdf")

def page_header(title)
  canvas do
    fill_color '1e2e4d'
    fill_rectangle [0, bounds.top], bounds.width, $page_header_height
  end

  move_down 4

  font 'Lato', size: 12, style: :bold
  text title, color: 'ffffff'
end

generate_and_open_pdf(file_path) do
  move_down $page_header_height

  grouped_component = Scipis::Reports::GroupedExplanations.new(
    self,
    grouped_explanations,
    page_header_height: $page_header_height
  )
  grouped_component.render

  repeat(0..page_count, dynamic: true) do
    page_header('Grouped explanations')
  end
end

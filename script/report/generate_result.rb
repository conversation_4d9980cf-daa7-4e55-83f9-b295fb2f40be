#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative 'script_helper'

if ARGV.empty?
  puts "Usage: #{$PROGRAM_NAME} <result_id>"
  exit 1
end

result_id = ARGV[0].to_i
result = CustomResult.find(result_id)

file_path = Rails.root.join("tmp/SciPi ##{result.question_group_id} Result ##{result_id}: #{result.title.parameterize.truncate(30)}.pdf").to_s

generate_and_open_pdf(file_path) do
  result_pages = Scipis::Reports::Result.new(self, result)

  result_pages.render
end

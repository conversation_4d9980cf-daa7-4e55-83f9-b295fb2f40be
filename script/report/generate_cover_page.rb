#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative 'script_helper'

if ARGV.empty?
  puts "Usage: #{$PROGRAM_NAME} <scipi_id>"
  exit 1
end

scipi_id = ARGV[0].to_i
file_path = Rails.root.join("tmp/Cover Page for SciPi #{scipi_id}.pdf")
scipi = QuestionGroup.scipis.find(scipi_id)
report = Scipis::Report.new(scipi:, prepared_for: 'Test Client', final: true, certified: true)

generate_and_open_pdf(file_path, margin: Scipis::Reports::CoverPage::PAGE_MARGINS) do |document|
  cover_page = Scipis::Reports::CoverPage.new(document, report)
  cover_page.render
end

# frozen_string_literal: true

require_relative '../../config/environment'

def generate_pdf(file_path, margin: Scipis::Reports::CoverPage::PAGE_MARGINS, &)
  Prawn::Document.generate(file_path, margin: margin, &)
end

def generate_and_open_pdf(file_path, margin: Scipis::Reports::CoverPage::PAGE_MARGINS, &)
  generate_pdf(file_path, margin:, &)

  puts "Opening #{file_path}"
  system("open \"#{file_path}\"")
end

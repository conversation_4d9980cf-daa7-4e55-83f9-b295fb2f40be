#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative '../../config/environment'
require 'ferrum'

if ARGV.empty?
  puts "Usage: #{$PROGRAM_NAME} <result_id>"
  exit 1
end

result_id = ARGV[0].to_i

result = CustomResult.find(result_id)
scipi_id = result.question_group.id
signed_id = result.signed_id(expires_in: 1.hour, purpose: :chart_image)

url = "http://localhost:3000/scipis/#{scipi_id}/results/#{signed_id}/chart?show_toolbar=true"

downloader = ApexChartDownloader.new(result)

file = downloader.download_chart(url)

result.reload

if result.chart_image.attached?
  puts "Chart downloaded successfully to: #{file.inspect}"
  exit 0
else
  warn "Chart download failed: #{file.inspect}"
  exit 1
end

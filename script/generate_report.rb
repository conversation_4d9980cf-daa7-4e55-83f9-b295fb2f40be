#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative '../config/environment'

host = ENV.fetch('MAILER_URL_HOST', 'localhost:3000')
protocol = Rails.env.local? ? 'http' : 'https'

Rails.application.routes.default_url_options = { host:, protocol: }
ActiveStorage::Current.url_options = { host:, protocol: }

if ARGV.empty?
  puts "Usage: #{$PROGRAM_NAME} <scipi_id>"
  exit 1
end

final = ARGV[1] == 'final'
scipi_id = ARGV[0].to_i
scipi = QuestionGroup.scipis.find(scipi_id)

scipi.result_definitions.each { |r| r.attach_chart_image!(overwrite: false) }

report = Scipis::Report.new(scipi:, prepared_for: 'Test Client', final:)
filename = Rails.root.join("tmp/#{report.filename}")

pdf_report = Scipis::Reports::PDFDocument.new(report, debug: false, filename:)
pdf_report.generate_report
pdf_report.save!

# open the file in the default PDF viewer
puts "Opening #{pdf_report.filename}"
system("open \"#{pdf_report.filename}\"")

#!/usr/bin/env ruby
# frozen_string_literal: true

require File.expand_path(File.join(File.dirname(__FILE__), '..', 'config', 'environment'))

# IMPORTANT: Ensure the input file has the "New Geocoded Country" column 
#            with two-letter country codes

# Map country codes to countries to save a few lookups
country_cache = {}

CSV.foreach('script/geolocated_ips.csv', headers: true) do |row|
  expert_id = row['id']
  country_code = row['New Geocoded Country']
  next if expert_id.blank? || country_code.blank?

  profile = Profile.find_by(expert_id:)
  country = country_cache[country_code] ||= Country.find_by(two_letter_code: country_code)

  (puts "Profile not found for expert #{expert_id}"; next) if profile.nil?
  (puts "Country code #{country_code} not found"; next) if country.nil?

  profile.update_columns(geolocated_country_id: country.id)
end

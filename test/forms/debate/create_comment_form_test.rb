# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'active_model'

require_relative '../../../app/forms/debate/create_comment_form'

module Debate
  class CreateCommentFormTest < Minitest::Test
    def test_create_comment
      comment_content = 'Something insighful'
      user = Object.new
      result = mock('mock result')
      result.expects(:create_comment!).with(user, content: comment_content)

      form = CreateCommentForm.new(user, result)
      form.submit!(content: comment_content)
    end

    def test_cannot_create_empty_commment
      user = Object.new
      result = Object.new

      form = CreateCommentForm.new(user, result)

      assert_raises(ActiveModel::ValidationError) do
        form.submit!(content: '')
      end
    end
  end
end

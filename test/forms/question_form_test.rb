# frozen_string_literal: true

require 'test_helper'

class QuestionFormTest < ActiveSupport::TestCase
  test 'create non-grid question' do
    survey = create(:survey, :draft)

    answer_choice_labels = %w[one two three]

    params = {
      question_group: survey,
      type: Questions::Radio.name,
      question_text: 'This is a radio question',
      question_details: 'These are the details',
      answer_choice_labels: answer_choice_labels.join("\r\n"),
      allow_answer_explanation: '1',
      answer_presence: '1',
      question_layout_type: '0'
    }

    form = QuestionForm.new(params)
    form.save

    question = form.question

    assert question.persisted?, 'should have saved a new question'
    assert_equal survey, question.survey
    assert_equal Questions::Radio, question.class
    assert_equal 'This is a radio question', question.question_text
    assert_equal 'These are the details', question.question_details
    assert_equal true, question.allow_answer_explanation?
    assert_equal 0, question.question_layout_type

    answer_choices = question.answer_choices
    assert_equal 3, answer_choices.count
    answer_choices.each do |choice|
      assert answer_choice_labels.include?(choice.label),
             "AnswerChoice value '#{choice.label}' is not in #{answer_choice_labels}"
    end
  end

  test 'update non-grid question' do
    question = create(:radio)

    new_answer_labels = %w[foo bar baz]
    new_text = 'Updated text'
    new_details = 'Updated details'

    params = {
      question:,
      type: Questions::Radio.name,
      question_text: new_text,
      question_details: new_details,
      answer_choice_labels: new_answer_labels.join("\r\n"),
      allow_answer_explanation: '1',
      answer_presence: '1',
      question_layout_type: '0'
    }

    form = QuestionForm.new(params)
    form.save

    question = form.question

    assert_equal Questions::Radio, question.class
    assert_equal new_text, question.question_text
    assert_equal new_details, question.question_details
    assert_equal true, question.allow_answer_explanation?
    assert_equal 0, question.question_layout_type

    answer_choices = question.answer_choices
    assert_equal 3, answer_choices.count
    answer_choices.each do |choice|
      assert new_answer_labels.include?(choice.label),
             "AnswerChoice value '#{choice.label}' is not in #{new_answer_labels}"
    end
  end
end

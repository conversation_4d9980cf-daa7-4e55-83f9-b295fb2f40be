# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class AddPanelistFormTest < ActiveSupport::TestCase
      test 'add approved panelist' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        admin = scipi.created_by

        form = AddPanelistForm.new(created_by: admin, expert_id: expert.id, status: 'Approved', survey: scipi)

        assert_difference -> { scipi.panelists.not_suspended.count } do
          assert form.save
        end

        assert scipi.panelists.exists?(user_id: expert.id)
      end

      test 'add suspended panelist' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        admin = scipi.created_by

        form = AddPanelistForm.new(created_by: admin, expert_id: expert.id, status: 'Suspended', survey: scipi)

        assert_difference -> { scipi.panelists.suspended.count } do
          assert form.save
        end

        assert scipi.panelists.suspended.exists?(user_id: expert.id)
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class AddObserverFormTest < ActiveSupport::TestCase
      setup do
        @survey = FactoryBot.create(:scipi, :invite_only)
      end

      test 'add observer by user id' do
        user = FactoryBot.create(:user)

        form = AddObserverForm.new(survey: @survey, user_id: user.id)

        assert_no_difference -> { User.count } do
          assert_difference -> { @survey.observers.count } do
            assert form.save
          end
        end
      end

      test 'add observer by email' do
        email = Faker::Internet.email
        Current.user = FactoryBot.create(:scipi_admin)

        form = AddObserverForm.new(survey: @survey, email:)

        assert_difference [-> { User.count }, -> { @survey.observers.count }] do
          assert form.save
        end
      end

      test 'must have either an email or user id' do
        form = AddObserverForm.new(survey: @survey)

        assert_not form.save

        assert form.errors.added?(:base, 'You must either select an existing user or provide an email to create a new one')
      end

      test 'should send welcome email' do
        form = AddObserverForm.new(send_welcome_email: '1')

        assert form.send_welcome_email?
      end

      test 'should not send welcome email' do
        form = AddObserverForm.new(send_welcome_email: '0')

        assert_not form.send_welcome_email?
      end
    end
  end
end

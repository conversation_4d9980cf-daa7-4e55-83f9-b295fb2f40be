# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'active_support'
require 'active_model'

require_relative '../../../app/forms/survey_builder/update_free_form_result_definition_form'

module SurveyBuilder
  class UpdateFreeFormResultDefinitionFormTest < Minitest::Test
    def test_submit_form
      title = Object.new
      file = Object.new
      data_set = stub('data set', data: {}, x_label: '', y_label: '')

      result_def_stub = mock('result def')
      result_def_stub.expects(:update!)
                     .with({ title:, data_points: [], x_label: '', y_label: '' })

      parser_stub = stub('data file parser')
      parser_stub.stubs(:parse).returns(data_set)

      form = UpdateFreeFormResultDefinitionForm.new(
        result_definition: result_def_stub,
        data_file_parser: parser_stub
      )
      assert form.submit(title:, data_file: file)
    end

    def test_requires_title
      form = UpdateFreeFormResultDefinitionForm.new(
        result_definition: Object.new,
        data_file_parser: Object.new
      )
      refute form.submit(title: '', data_file: Object.new)
      assert !form.errors[:title].nil?, 'title should be required'
    end

    def test_allow_nil_file
      title = 'foo bar'
      result_def_stub = mock('result def')
      result_def_stub.expects(:update!).with({ title: })

      form = UpdateFreeFormResultDefinitionForm.new(
        result_definition: result_def_stub,
        data_file_parser: Object.new
      )
      assert form.submit(title:, data_file: nil)
    end
  end
end

# frozen_string_literal: true

require 'form_test_helper'

require_relative '../../../app/forms/survey_builder/result_section_submission_form'
require_relative '../../../app/forms/survey_builder/update_result_section_form'

module SurveyBuilder
  class UpdateResultSectionFormTest < Minitest::Test
    include SubmissionFormTest

    def test_create_result_section
      name = 'Foo'

      section = mock('result section')
      section.expects(:update!).with(name:, hidden: false)

      form = UpdateResultSectionForm.new(section)

      assert form.submit(name:, hidden: '0')
    end

    def test_name_is_required
      section = Object.new

      form = UpdateResultSectionForm.new(section)

      refute form.submit(name: '', hidden: '0')
      assert_error_on(form, :name, :blank)
    end
  end
end

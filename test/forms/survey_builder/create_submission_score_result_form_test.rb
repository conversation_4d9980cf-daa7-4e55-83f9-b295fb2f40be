# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../../app/forms/survey_builder/create_submission_score_result_form'

module SurveyBuilder
  class CreateSubmissionScoreResultFormTest < Minitest::Test
    def test_create_submission_score_result
      survey = mock('survey')
      survey.expects(:create_submission_score_result_definition!)

      form = CreateSubmissionScoreResultForm.new(survey)
      form.submit!(title: 'foobar')
    end
  end
end

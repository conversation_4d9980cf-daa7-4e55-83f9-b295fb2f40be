# frozen_string_literal: true

require 'active_model'
require 'faker'
require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../../app/forms/survey_builder/create_question_form'

module SurveyBuilder
  class CreateQuestionFormTest < Minitest::Test
    def test_create_file_upload_question
      mock_survey = mock('survey')
      mock_survey.expects(:create_question!)
      mock_survey.stubs(:last_section).returns(nil)

      form = CreateQuestionForm.new(
        survey: mock_survey,
        model: Object.new
      )

      assert form.submit(
        answer_presence: '1',
        question_text: Faker::Lorem.words(number: 4),
        question_details: Faker::Lorem.sentences(number: 4),
        type: 'Questions::FileUpload'
      )
    end

    def test_question_text_is_required
      form = CreateQuestionForm.new(
        survey: Object.new,
        model: Object.new
      )

      refute form.submit(
        question_text: nil,
        question_details: Faker::Lorem.sentences(number: 4),
        answer_presence: '1'
      )
      refute_nil form.errors[:question_text]
    end
  end
end

# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../../app/forms/survey_builder/answer_choice_params_util'

module SurveyBuilder
  class AnswerChoiceParamsUtilTest < Minitest::Test
    DELIMITER = "\r\n"
    def test_parse_labels
      labels = %w[foo bar baz]
      param_values = labels.join(DELIMITER)

      parsed_values = AnswerChoiceParamsUtil.new(DELIMITER).parse(param_values)

      assert_equal labels, parsed_values
    end

    def test_parse_label_and_values
      labels = %w[foo bar baz]
      values = %w[1 2 3]

      param_values = labels.zip(values).map { |a| a.join('::') }.join(DELIMITER)
      expected_parsed_values = labels.zip(values).to_h

      parsed_values = AnswerChoiceParamsUtil.new(DELIMITER).parse(param_values)

      assert_equal expected_parsed_values, parsed_values
    end

    def test_format_labels
      labels = %w[foo bar]
      answer_choice_stubs = labels.map { |label| stub(label:, value: nil) }

      formatted_values = AnswerChoiceParamsUtil.new(DELIMITER).format(answer_choice_stubs)

      expected_labels = labels.join(DELIMITER)

      assert_equal expected_labels, formatted_values
    end

    def test_format_label_and_values
      labels = %w[foo bar]
      values = [1.0, 2.0]
      label_values_pairs = labels.zip(values)
      answer_choice_stubs = label_values_pairs.to_h.map { |label, value| stub(label:, value:) }

      formatted_values = AnswerChoiceParamsUtil.new(DELIMITER).format(answer_choice_stubs)

      expected_labels = label_values_pairs.map { |pair| pair.join('::') }.join(DELIMITER)

      assert_equal expected_labels, formatted_values
    end
  end
end

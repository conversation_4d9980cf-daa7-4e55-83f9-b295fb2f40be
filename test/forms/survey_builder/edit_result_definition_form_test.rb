# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'active_model'

require_relative '../../../app/forms/survey_builder/edit_result_definition_form'

module SurveyBuilder
  class EditResultFormTest < Minitest::Test
    def test_free_form_results_require_data_file
      result_definition = stub(free_form?: true, file_attachment?: false)

      form = EditResultDefinitionForm.new(result_definition:)

      assert form.requires_data_file?, 'free form results require a data file'
    end

    def test_file_attachment_results_require_data_file
      result_definition = stub(free_form?: false, file_attachment?: true)

      form = EditResultDefinitionForm.new(result_definition:)

      assert form.requires_data_file?, 'free form results require a data file'
    end

    def test_does_require_data_file
      result_definition = stub(free_form?: false, file_attachment?: false)

      form = EditResultDefinitionForm.new(result_definition:)

      refute form.requires_data_file?, 'non-free form results do not require a data file'
    end
  end
end

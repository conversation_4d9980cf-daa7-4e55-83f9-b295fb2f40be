# frozen_string_literal: true

require 'active_support'
require 'active_model'
require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../../app/forms/submission_form'
require_relative '../../../app/forms/survey_builder/answer_choice_params_util'
require_relative '../../../app/forms/survey_builder/update_question_form'

module SurveyBuilder
  class UpdateQuestionFormTest < Minitest::Test
    def test_update_unanswered_question
      question = stub(answered?: false, grid_type?: false, multiple_choice?: true)
      question.expects(:update_question!)

      form = UpdateQuestionForm.new(question:)

      assert form.submit(question_text: 'Foobar', answer_choice_labels: "foo\r\nbar")
    end

    def test_does_not_validate_answer_choices_non_multiple_choice
      question = stub(answered?: false, grid_type?: false, multiple_choice?: false)
      question.expects(:update_question!)

      form = UpdateQuestionForm.new(question:)

      assert form.submit(question_text: 'Foobar')
    end

    def test_validation_fails_on_unanswered_question
      question = stub(answered?: false, grid_type?: false, multiple_choice?: true)

      form = UpdateQuestionForm.new(question:)

      refute form.submit(weight: 'I should be a number')
    end

    def test_update_answered_question
      question = stub(answered?: true, multiple_choice?: true)
      question.expects(:update_display_fields!)

      form = UpdateQuestionForm.new(question:)

      assert form.submit(
        answer_required: false,
        question_layout_type: 0,
        weight: ''
      )
    end

    def test_validation_fails_on_answered_question
      question = stub(answered?: true, multiple_choice?: true)

      form = UpdateQuestionForm.new(question:)

      refute form.submit(weight: 'I should be a number')
    end
  end
end

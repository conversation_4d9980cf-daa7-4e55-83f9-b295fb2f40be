# frozen_string_literal: true

require 'active_support'
require 'active_model'
require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../form_test_helper'
require_relative '../../../app/forms/survey_builder/section_form'
require_relative '../../../app/forms/survey_builder/update_question_section_form'

module SurveyBuilder
  class UpdateSectionFormTest < Minitest::Test
    include SubmissionFormTest

    def test_update_section
      section = mock('mock section')
      section.stubs(:footer, 'footer')
      section.stubs(:hidden, false)
      section.stubs(:introduction, 'introduction')
      section.stubs(:name, 'name')
      section.expects(:update!)
      form = UpdateQuestionSectionForm.new(section:, action_path: '')

      assert form.submit(name: 'Foobar', introduction: '', footer: '')
    end

    def test_name_required
      section = stub('mock section')
      section.stubs(:footer, 'footer')
      section.stubs(:hidden, false)
      section.stubs(:introduction, 'introduction')
      section.stubs(:name, 'name')

      form = UpdateQuestionSectionForm.new(section:, action_path: '')
      form.valid?

      assert_error_on(form, :name, :blank)
    end
  end
end

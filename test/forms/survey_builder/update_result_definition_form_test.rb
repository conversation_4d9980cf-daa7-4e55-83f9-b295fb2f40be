# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../../app/forms/survey_builder/update_question_result_definition_form'

module SurveyBuilder
  class UpdateResultDefinitionFormTest < Minitest::Test
    def test_submit_form
      result_definition = stub(
        'result_def',
        requires_title?: false,
        update!: true
      )

      form = UpdateQuestionResultDefinitionForm.new(result_definition)

      assert form.submit(group_by_question_id: 23),
             'should have submitted successfully'
    end

    def test_set_blank
      skip('implement me')
    end

    def test_group_question_validity
      skip('implement me')
    end
  end
end

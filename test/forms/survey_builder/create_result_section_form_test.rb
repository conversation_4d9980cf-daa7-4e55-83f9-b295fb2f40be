# frozen_string_literal: true

require 'form_test_helper'

require_relative '../../../app/forms/survey_builder/result_section_submission_form'
require_relative '../../../app/forms/survey_builder/create_result_section_form'

module SurveyBuilder
  class CreateResultSectionFormTest < Minitest::Test
    include SubmissionFormTest

    def test_valid
      name = 'Name'

      form = CreateResultSectionForm.new(name:, hidden: '0')

      assert form.valid?
    end

    def test_name_required
      form = CreateResultSectionForm.new(name: '', hidden: '0')

      refute form.valid?
      assert_error_on(form, :name, :blank)
    end
  end
end

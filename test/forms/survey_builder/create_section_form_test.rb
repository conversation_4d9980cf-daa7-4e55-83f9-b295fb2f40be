# frozen_string_literal: true

require 'active_support'
require 'active_model'
require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../form_test_helper'
require_relative '../../../app/forms/submission_form'
require_relative '../../../app/forms/survey_builder/section_form'
require_relative '../../../app/forms/survey_builder/create_question_section_form'

module SurveyBuilder
  class CreateSectionFormTest < Minitest::Test
    include SubmissionFormTest

    def test_create_section
      survey = mock('mock survey')
      survey.expects(:add_question_section!)
      form = CreateQuestionSectionForm.new(survey:, action_path: '')

      assert form.submit(name: 'Foobar', introduction: '', footer: '')
    end

    def test_name_required
      survey = Object.new
      form = CreateQuestionSectionForm.new(survey:, action_path: '')
      form.valid?

      assert_error_on(form, :name, :blank)
    end
  end
end

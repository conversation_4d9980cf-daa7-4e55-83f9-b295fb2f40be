# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'rack/test/uploaded_file'

require_relative '../../../app/forms/survey_builder/create_file_attachment_result_form'

module SurveyBuilder
  class CreateFileAttachmentResultFormTest < Minitest::Test
    def test_create_file_attachment
      title = Object.new
      file = Object.new

      survey = mock('survey')
      survey.expects(:create_file_attachment_result!)
            .with(title:, attachment: file, download_disabled: false)

      form = CreateFileAttachmentResultForm.new(survey)
      form.submit!(
        title:,
        file:
      )
    end
  end
end

# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'rack/test/uploaded_file'

require_relative '../../../app/forms/submission_form'
require_relative '../../../app/forms/survey_builder/create_free_form_result_form'

module SurveyBuilder
  class CreateFreeFromResultTest < Minitest::Test
    def test_create_free_form_result
      title = Object.new
      file = Object.new
      data_set = Object.new

      survey_stub = stub('survey')
      survey_stub
        .expects(:create_free_form_result!)
        .with(title:, data_set:)
        .returns(Object.new)

      parser_stub = stub('data file parser')
      parser_stub.stubs(:parse).returns(data_set)

      form = CreateFreeFormResultForm.new(
        survey: survey_stub,
        data_file_parser: parser_stub
      )

      assert form.submit!(
        title:,
        data_file: file
      )
    end
  end
end

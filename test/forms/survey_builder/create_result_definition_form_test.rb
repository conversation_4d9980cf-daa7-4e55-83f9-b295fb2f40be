# frozen_string_literal: true

require 'test_helper'

module SurveyBuilder
  class CreateResultDefinitionFormTest < ActiveSupport::TestCase
    test 'create result definition' do
      question = stub(id: 222)
      group_by_question = stub(id: 333)
      render_type_id = 1

      survey = mock('survey')
      result_type = mock('result type', allowed_grouping_question_types: [])
      survey.stubs(id: 111)
      survey
        .expects(:create_result_definition!)
        .with(
          question_id: question.id,
          group_by_question_id: group_by_question.id,
          result_type:,
          render_type_id:
        )
        .returns(Object.new)

      form = CreateResultDefinitionForm.new(survey, result_type)

      assert form.submit!(
        question_id: question.id,
        group_by_question_id: group_by_question.id,
        render_type_id:
      )
    end

    test 'create multi question score' do
      title = 'Title'
      question_ids = %w[1 2]

      render_type_id = '23'
      result_type = mock('result type', allowed_grouping_question_types: [])

      survey = mock('survey')
      survey
        .expects(:create_result_definition!)
        .with(
          title:,
          question_ids:,
          result_type:,
          render_type_id: render_type_id.to_i
        )
        .returns(Object.new)

      form = CreateResultDefinitionForm.new(survey, result_type)

      assert form.submit!(
        question_ids:,
        title:,
        render_type_id:
      )
    end

    test 'create Answers Grouped By Region result' do
      survey = FactoryBot.create(:survey)
      multiple_choice_question = FactoryBot.create(:radio, survey:)
      _country_question = FactoryBot.create(:profile_country_question, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_region)

      form = CreateResultDefinitionForm.new(survey, result_type)

      assert form.submit!(question_id: multiple_choice_question.id)
    end

    test 'cannot create a Answers Grouped By Region result without a profile question' do
      survey = FactoryBot.create(:survey)
      multiple_choice_question = FactoryBot.create(:radio, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_region)

      form = CreateResultDefinitionForm.new(survey, result_type)
      form.submit!(question_id: multiple_choice_question.id)

      assert_error_on form, :question_id, "can only be used when a 'Country (Profile)' question exists"
    end

    test 'cannot create Answers Grouped By Region result for a non-radio or checkbox question' do
      survey = FactoryBot.create(:survey)
      grid_question = FactoryBot.create(:grid_question, survey:)
      _country_question = FactoryBot.create(:profile_country_question, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_region)

      form = CreateResultDefinitionForm.new(survey, result_type)
      form.submit!(question_id: grid_question.id)

      assert_error_on form, :question_id, 'must be a Checkbox and Radio question'
    end

    test 'create Answers Grouped By Sector result' do
      survey = FactoryBot.create(:survey)
      multiple_choice_question = FactoryBot.create(:radio, survey:)
      _sector_question = FactoryBot.create(:profile_sector_question, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_sector)

      form = CreateResultDefinitionForm.new(survey, result_type)

      assert form.submit!(question_id: multiple_choice_question.id)
    end

    test 'cannot create a Answers Grouped By Sector result without a profile question' do
      survey = FactoryBot.create(:survey)
      multiple_choice_question = FactoryBot.create(:radio, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_sector)

      form = CreateResultDefinitionForm.new(survey, result_type)
      form.submit!(question_id: multiple_choice_question.id)

      assert_error_on form,
                      :question_id,
                      "can only be used when a 'Current Sector of Employment (Profile)' question exists"
    end

    test 'cannot create Answers Grouped By Sector result for a non-radio or checkbox question' do
      survey = FactoryBot.create(:survey)
      grid_question = FactoryBot.create(:grid_question, survey:)
      _sector_question = FactoryBot.create(:profile_sector_question, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_sector)

      form = CreateResultDefinitionForm.new(survey, result_type)
      form.submit!(question_id: grid_question.id)

      assert_error_on form, :question_id, 'must be a Checkbox and Radio question'
    end

    test 'create Answers Grouped By Years of Experience result' do
      survey = FactoryBot.create(:survey)
      multiple_choice_question = FactoryBot.create(:radio, survey:)
      _yoe_question = FactoryBot.create(:profile_years_of_experience_question, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_years_of_experience)

      form = CreateResultDefinitionForm.new(survey, result_type)

      assert form.submit!(question_id: multiple_choice_question.id)
    end

    test 'cannot create a Answers Grouped By Years of Experience result without a profile question' do
      survey = FactoryBot.create(:survey)
      multiple_choice_question = FactoryBot.create(:radio, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_years_of_experience)

      form = CreateResultDefinitionForm.new(survey, result_type)
      form.submit!(question_id: multiple_choice_question.id)

      assert_error_on form,
                      :question_id,
                      "can only be used when a 'Years of Experience (Profile)' question exists"
    end

    test 'cannot create Answers Grouped By Years of Experience result for a non-radio or checkbox question' do
      survey = FactoryBot.create(:survey)
      grid_question = FactoryBot.create(:grid_question, survey:)
      _yoe_question = FactoryBot.create(:profile_years_of_experience_question, survey:)
      result_type = FactoryBot.create(:result_type, :answers_grouped_by_years_of_experience)

      form = CreateResultDefinitionForm.new(survey, result_type)
      form.submit!(question_id: grid_question.id)

      assert_error_on form, :question_id, 'must be a Checkbox and Radio question'
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class ApiClientTest < ActiveSupport::TestCase
  test 'test 400 error' do
    stub_request(:post, 'https://www.example.com/path')
      .to_return(status: [400, 'You made a mistake!'])

    client = ApiClient.new(host: 'https://www.example.com')

    assert_raises UnrecoverableError, match: 'You made a mistake!' do
      client.post('/path')
    end
  end
end

# frozen_string_literal: true

module SlimSelectHelper
  def select_from_slim_select(value, from:)
    unless from.include?('#')
      label = find('label', text: from)
      from = "##{label['for']}"
    end

    # Find the slimselect generated id for this select
    select_field = find(from, visible: false, wait: 2)
    slim_select_id = select_field['data-id']

    # Open the combo box
    ss_combo_box = find("div[role=\"combobox\"][data-id=\"#{slim_select_id}\"]")
    within(ss_combo_box) do
      find('.ss-arrow').click
    end

    # HACK: Wait for the list to appear
    sleep(0.2)

    # Select an item from the list
    ss_listbox = find("div[role=\"listbox\"][data-id=\"#{slim_select_id}\"]")
    within(ss_listbox) do
      find('.ss-list .ss-option', text: value).click
    end

    # Close the combo box
    within(ss_combo_box) do
      find('.ss-arrow').click
    end
  end
end

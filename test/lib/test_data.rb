# frozen_string_literal: true

class TestData
  module Degree
    def self.grad_year
      ::Degree.grad_years.sample
    end

    def self.subject_area
      Faker::Educator.subject
    end
  end

  def self.any_db_admin = FactoryBot.create(:db_admin)

  def self.any_user
    User.order('RANDOM()').first
  end

  def self.any_scipi
    QuestionGroup.order('RANDOM()').first
  end

  def self.display_name
    Faker::Internet.username
  end

  def self.email
    Faker::Internet.email
  end

  def self.expert
    FactoryBot.create(:expert)
  end

  def self.any_future_time
    rand(1..10).days.from_now
  end

  def self.password(min_length: 8, max_length: 64)
    Faker::Internet.password(min_length:, max_length:)
  end

  def self.any_past_time
    rand(1..10).days.ago
  end

  def self.scipi
    FactoryBot.create(
      :scipi,
      :invite_only,
      created_by: FactoryBot.create(:admin, email:)
    )
  end

  def self.scipi_intro_text
    'This is intro text for a scipi.'
  end

  def self.short_password
    password(min_length: 7, max_length: 7)
  end
end

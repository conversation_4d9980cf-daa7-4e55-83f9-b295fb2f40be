# frozen_string_literal: true

# This class is not intended to replace or duplicate FactoryBot. The intent
# is to centralize the building common, complex things
class TestData<PERSON><PERSON>ator
  def create_weight_sum_grid_question(survey,
                                      submitted_by: FactoryBot.create(:expert),
                                      score_label: 'WSG Label')
    question = FactoryBot.create(:grid_question, survey:)

    FactoryBot.create(
      :grid_structure,
      :weight_sum_grid,
      question:,
      row_headers: ['row1::0.2', 'row2::0.3', 'row3::0.5'],
      column_headers: ['col::0.4', 'col::0.2', 'col3::0.4'],
      weighted_sum_label: score_label
    )
    question.answer_choices.create!(label: '10', value: 10, position: 1)
    question.answer_choices.create!(label: '20', value: 20, position: 2)
    question.answer_choices.create!(label: '30', value: 30, position: 3)

    submission = FactoryBot.create(
      :submission,
      :final,
      user: submitted_by,
      survey:
    )
    submission.answers.create!(
      question:,
      answer_text_on_1st_row: "30\r\n20\r\n20",
      answer_text_on_2nd_row: "30\r\n30\r\n20",
      answer_text_on_3rd_row: "20\r\n30\r\n30"
    )
    question
  end
end

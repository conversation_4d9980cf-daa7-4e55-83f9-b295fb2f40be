# frozen_string_literal: true

module AbilityTestHelper
  private

  def assert_can(permission, subject, message: '')
    message = message.presence || default_message('should', permission, subject)

    assert ability.can?(permission, subject), message
  end

  def assert_cannot(permission, subject, message: '')
    message = message.presence || default_message('should not', permission, subject)

    assert_not ability.can?(permission, subject), message
  end

  def assert_attribute_permitted(subject, permission, attribute, message: '')
    message = message.presence || "#{default_message('should', permission, subject)} with #{attribute} attribute"

    attributes = ability.permitted_attributes(permission, subject)

    assert_includes(attributes, attribute, message)
  end

  def assert_attribute_not_permitted(subject, permission, attribute, message: '')
    message = message.presence || "#{default_message('should not', permission, subject)} with #{attribute} attribute"

    attributes = ability.permitted_attributes(permission, subject)

    assert_not_includes(attributes, attribute, message)
  end

  def default_message(base, permission, subject)
    "#{base} be able to #{permission} #{humanized_subject_name(subject)}"
  end

  def humanized_subject_name(subject)
    subject.class.name.tableize.singularize.humanize
  end
end

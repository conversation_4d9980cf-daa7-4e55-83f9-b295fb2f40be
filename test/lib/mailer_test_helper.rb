# frozen_string_literal: true

module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def assert_list_unsubscribe_headers(mail, url)
    assert_mail_header mail, 'List-Unsubscribe', url
    assert_mail_header mail, 'List-Unsubscribe-Post', 'List-Unsubscribe=One-Click'
  end

  def assert_mail_from(mail, email)
    assert_equal [email], mail.from, "should be from '#{email}', was '#{mail.from}'"
  end

  def assert_mail_header(mail, header, value)
    assert_equal value,
                 mail.header[header].to_s,
                 "should have the header '#{header}: #{value}', was '#{mail.header[header]}'"
  end

  def assert_mail_subject(mail, subject)
    assert_equal subject, mail.subject, "should have the subject '#{subject}', was '#{mail.subject}'"
  end

  def assert_mail_tag(mail, tag)
    assert_equal tag, mail.tag, "should have the tag '#{tag}', was '#{mail.tag}'"
  end

  def assert_mail_to(mail, *emails)
    assert_equal emails, mail.to, "should be sent to '#{emails.join(', ')}', was '#{mail.to.join(', ')}'"
  end

  def assert_receiving_reason(mail, reason)
    assert_match "You are receiving this email because #{reason}",
                 mail.body.encoded,
                 "should have the reason '#{reason}', was '#{mail.body.encoded}'"
  end

  def assert_unsubscribe_link(mail, subscription)
    subscribable = subscription.subscribable

    assert_match public_unsubscribe_url(subscribable.token, subscription.token),
                 mail.body.encoded,
                 'Broadcast emails should have an unsubscribe link'
  end
end

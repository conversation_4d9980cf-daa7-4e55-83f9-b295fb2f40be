# frozen_string_literal: true

module Scipis
  module EngagementInfoTestHelper
    # These are used both in the admin and scipi landing page tests
    # If for some reason they start to diverge, we can split them
    # back up into their respective test classes, or split them into
    # separate modules, e.g. Admin::Scipis::EngagementInfoTestHelper,
    # and Scipis::EngagementInfoTestHelper
    def assert_engagement_details(label:, details:)
      assert_text "#{label} #{details}", normalize_ws: true
    end
  end
end

# frozen_string_literal: true

module DownloadHelper
  TIMEOUT = 1
  PATH = Rails.root.join('tmp/downloads')

  def self.download(filename)
    PATH.join(filename)
  end

  def self.download_content(filename)
    wait_for_download(filename)
    File.read(download(filename))
  end

  def self.wait_for_download(filename)
    Timeout.timeout(TIMEOUT) do
      sleep 0.1 until downloaded?(PATH.join(filename))
    end
  end

  def self.downloaded?(filename)
    !downloading?(PATH.join(filename)) && File.exist?(PATH.join(filename))
  end

  def self.downloading?(filename)
    File.exist?(PATH.join("#{filename}.part"))
  end

  def self.clear_download(filename)
    FileUtils.rm_f(PATH.join(filename))
  end
end

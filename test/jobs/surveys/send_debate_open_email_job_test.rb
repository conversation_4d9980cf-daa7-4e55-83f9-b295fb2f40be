# frozen_string_literal: true

require 'test_helper'

module Surveys
  class SendDebateOpenEmailJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'send scipi debate open email' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, survey: scipi)
      scipi.panelists.create!(expert:)

      mailer_args = { scipi:, round:, panelist: expert }

      travel_to(1.hour.after(round.opens_at)) do
        assert_changes -> { round.reload.round_open_email_sent_at } do
          assert_enqueued_email_with(Scipis::PanelistsMailer, :debate_open, args: [mailer_args]) do
            SendDebateOpenEmailJob.perform_now
          end
        end
      end
    end

    test 'do not send scipi debate open email if round is not yet open' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, survey: scipi)
      scipi.panelists.create!(expert:)

      travel_to(1.hour.before(round.opens_at)) do
        assert_no_changes -> { round.reload.round_open_email_sent_at } do
          assert_no_enqueued_emails do
            SendDebateOpenEmailJob.perform_now
          end
        end
      end
    end

    test 'do not send scipi debate open email if it was already sent' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :open_email_sent, survey: scipi)
      scipi.panelists.create!(expert:)

      travel_to(1.hour.after(round.opens_at)) do
        assert_no_changes -> { round.reload.round_open_email_sent_at } do
          assert_no_enqueued_emails do
            SendDebateOpenEmailJob.perform_now
          end
        end
      end
    end

    test 'do not send scipi debate open email if round is already closed' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, survey: scipi)
      scipi.panelists.create!(expert:)

      travel_to(1.hour.after(round.closes_at)) do
        assert_no_changes -> { round.reload.round_open_email_sent_at } do
          assert_no_enqueued_emails do
            SendDebateOpenEmailJob.perform_now
          end
        end
      end
    end
  end
end

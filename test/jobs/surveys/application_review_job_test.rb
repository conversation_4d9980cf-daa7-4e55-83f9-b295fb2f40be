# frozen_string_literal: true

require 'test_helper'

module Surveys
  class ApplicationReviewJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'send application-complete email when applicant has CV when applied' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:)

      assert_enqueued_email_with(ApplicantMailer, :application_complete, params: { applicant: expert, survey: scipi }) do
        assert_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ApplicationReviewJob.perform_now(applicant)
        end
      end
    end

    test 'do not send application-complete email when unconfirmed applicant has CV when applied' do
      expert = FactoryBot.create(:expert, :unconfirmed, :with_cv)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:)

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ApplicationReviewJob.perform_now(applicant)
        end
      end
    end

    test 're-enqueue job when applicant does not have CV when applied' do
      expert = FactoryBot.create(:expert, :confirmed)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:, applied_at: 5.seconds.ago, cv_needed_email_sent_at: nil)

      about_a_from_now = ->(run_at) { ((1.day.from_now - 1.minute)..1.day.from_now).cover?(run_at) }

      assert_enqueued_with(job: ApplicationReviewJob, at: about_a_from_now, args: [applicant]) do
        assert_no_enqueued_emails do
          assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
            assert_no_changes(-> { applicant.reload.cv_needed_email_sent_at }) do
              ApplicationReviewJob.perform_now(applicant)
            end
          end
        end
      end
    end

    test 'send application-complete email if applicant has CV after re-review period if not already sent' do
      # This is more a less a fallback since we re-check all pending applications after then upload a CV
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:, application_completion_email_sent_at: nil)

      assert_enqueued_email_with(ApplicantMailer, :application_complete, params: { applicant: expert, survey: scipi }) do
        assert_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ApplicationReviewJob.perform_now(applicant)
        end
      end
    end

    test 'do not send application-complete after re-review when application complete email is already sent' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:, application_completion_email_sent_at: 12.hours.ago)

      assert_no_enqueued_jobs do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ApplicationReviewJob.perform_now(applicant)
        end
      end
    end

    test 'send cv-needed email if applicant does not have a CV after application re-review period' do
      expert = FactoryBot.create(:expert, :confirmed)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:, applied_at: 25.hours.ago, cv_needed_email_sent_at: nil)

      assert_enqueued_email_with(ApplicantMailer, :application_incomplete, params: { applicant: expert, survey: scipi }) do
        assert_changes(-> { applicant.reload.cv_needed_email_sent_at }) do
          assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
            ApplicationReviewJob.perform_now(applicant)
          end
        end
      end
    end

    test 'do not send cv-needed email if unconfirmed applicant does not have a CV after application re-review period' do
      expert = FactoryBot.create(:expert, :unconfirmed)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:, applied_at: 25.hours.ago, cv_needed_email_sent_at: nil)

      assert_no_enqueued_jobs do
        assert_no_changes(-> { applicant.reload.cv_needed_email_sent_at }) do
          assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
            ApplicationReviewJob.perform_now(applicant)
          end
        end
      end
    end

    test 'do not send application-complete email when if the application is no longer pending' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on: 2.weeks.from_now)
      applicant = scipi.applicants.create!(expert:, rejected_at: Time.current)

      assert_no_enqueued_jobs do
        assert_no_changes(-> { applicant.reload.cv_needed_email_sent_at }) do
          assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
            ApplicationReviewJob.perform_now(applicant)
          end
        end
      end
    end
  end
end

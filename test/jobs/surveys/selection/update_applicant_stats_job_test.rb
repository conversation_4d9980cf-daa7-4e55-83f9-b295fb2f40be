# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Selection
    class UpdateApplicantStatsJobTest < ActiveJob::TestCase
      test 'stats do not update when selection closed' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, selection_closes_on: 1.week.ago)
        keyword = scipi.selection_keywords.create!(terms: ['foobar'], terms_updated_at: 1.week.ago)
        expert = FactoryBot.create(:expert)
        expert.profile.update!(
          cv_text: 'foobar',
          general_expertise_updated_at: Time.current,
          extracted_cv_text_updated_at: Time.current
        )
        applicant = scipi.applicants.create!(expert:, general_expertise_updated_at: 1.day.ago)
        keyword_count = applicant.keyword_counts.create!(keyword:, count: 1, updated_at: 1.day.ago)

        assert_no_changes -> { applicant.reload.general_expertise_updated_at } do
          assert_no_changes -> { keyword_count.reload.updated_at } do
            UpdateApplicantStatsJob.perform_now(expert)
          end
        end
      end

      test 'stats update when selection open' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, selection_closes_on: 1.week.from_now)
        keyword = scipi.selection_keywords.create!(terms: ['foobar'], terms_updated_at: 1.week.ago)
        expert = FactoryBot.create(:expert)
        expert.profile.update!(
          cv_text: 'foobar',
          general_expertise_updated_at: Time.current,
          extracted_cv_text_updated_at: Time.current
        )
        applicant = scipi.applicants.create!(expert:, general_expertise_updated_at: 1.day.ago)
        keyword_count = applicant.keyword_counts.create!(keyword:, count: 1, updated_at: 1.day.ago)

        assert_changes -> { applicant.reload.general_expertise_updated_at }, 'general expertise stats should have changed' do
          assert_changes -> { keyword_count.reload.updated_at }, 'keyword count timestamp should have changed' do
            UpdateApplicantStatsJob.perform_now(expert)
          end
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Surveys
  class SendDebateClosingReminderEmailJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'send closing reminder when has participated' do
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :open_email_sent, survey: scipi)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(user: expert)

      add_participation(round:, expert:)

      travel_to(1.second.after(round.closing_reminder_deliverable_at)) do
        assert_reminder_sent(round:, panelist: expert, participated: true)
      end
    end

    test 'send closing reminder when has not participated' do
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :open_email_sent, survey: scipi)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(user: expert)

      travel_to(1.second.after(round.closing_reminder_deliverable_at)) do
        assert_reminder_sent(round:, panelist: expert, participated: false)
      end
    end

    test 'do not send closing reminder if close date is too far out' do
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :open_email_sent, survey: scipi)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(user: expert)

      travel_to(1.second.before(round.closing_reminder_deliverable_at)) do
        assert_no_reminder_sent(round)
      end
    end

    test 'do not send closing reminder if round is closed' do
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :open_email_sent, survey: scipi)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(user: expert)

      travel_to(1.second.after(round.closes_at)) do
        assert_no_reminder_sent(round)
      end
    end

    test 'do not send closing reminder if already sent' do
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :open_email_sent, :closing_reminder_sent, survey: scipi)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(user: expert)

      travel_to(1.second.after(round.closes_at)) do
        assert_no_reminder_sent(round)
      end
    end

    test 'do not send closing reminder if open email is not sent' do
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, survey: scipi, round_open_email_sent_at: nil)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(user: expert)

      travel_to(1.second.after(round.closing_reminder_deliverable_at)) do
        assert_no_reminder_sent(round)
      end
    end

    private

    def add_participation(round:, expert:)
      scipi = round.survey

      result = FactoryBot.create(:result_definition, scipi:)
      FactoryBot.create(:comment, debate_topic: result, user: expert)
    end

    def assert_no_reminder_sent(round)
      assert_no_changes -> { round.reload.closing_reminder_sent_at } do
        assert_no_enqueued_emails do
          SendDebateClosingReminderEmailJob.perform_now
        end
      end
    end

    def assert_reminder_sent(round:, panelist:, participated:)
      scipi = round.survey

      mailer_args = { scipi:, round:, panelist:, participated: }

      assert_changes -> { round.reload.closing_reminder_sent_at } do
        assert_enqueued_email_with(Scipis::PanelistsMailer, :debate_closing_reminder, args: [mailer_args]) do
          SendDebateClosingReminderEmailJob.perform_now
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Surveys
  class SendDebateSummaryEmailJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'send debate summary' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :last_debate_summary_sent, survey: scipi)
      scipi.panelists.create!(expert:)
      result = FactoryBot.create(:result_definition, scipi:)

      FactoryBot.create(
        :comment,
        debate_topic: result,
        created_at: 1.hour.after(round.last_debate_summary_sent_at)
      )

      mailer_args = { scipi:, round:, panelist: expert, activity_summaries: [Surveys::ResultActivitySummary.new(result:, new_comment_count: 1)] }

      travel_to 1.hour.after(round.next_debate_summary_delivery_possible_at) do
        assert_changes -> { round.reload.last_debate_summary_sent_at } do
          assert_enqueued_email_with(Scipis::PanelistsMailer, :debate_summary, args: [mailer_args]) do
            SendDebateSummaryEmailJob.perform_now
          end
        end
      end
    end

    test 'do not send anything if round is closed' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :last_debate_summary_sent, survey: scipi)
      scipi.panelists.create!(expert:)
      result = FactoryBot.create(:result_definition, scipi:)

      FactoryBot.create(
        :comment,
        debate_topic: result,
        created_at: 1.hour.after(round.last_debate_summary_sent_at)
      )

      travel_to 1.hour.after(round.closes_at) do
        assert_no_changes -> { round.reload.last_debate_summary_sent_at } do
          assert_no_enqueued_emails do
            SendDebateSummaryEmailJob.perform_now
          end
        end
      end
    end

    test 'do not send anything if round if open email has not been sent' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi, :published, :results_published)
      round = FactoryBot.create(:debate_round, :last_debate_summary_sent, survey: scipi)
      scipi.panelists.create!(expert:)
      result = FactoryBot.create(:result_definition, scipi:)

      FactoryBot.create(
        :comment,
        debate_topic: result,
        created_at: 1.hour.after(round.opens_at)
      )

      travel_to 49.hours.after(round.opens_at) do
        assert_no_changes -> { round.reload.last_debate_summary_sent_at } do
          assert_no_enqueued_emails do
            SendDebateSummaryEmailJob.perform_now
          end
        end
      end
    end
  end
end

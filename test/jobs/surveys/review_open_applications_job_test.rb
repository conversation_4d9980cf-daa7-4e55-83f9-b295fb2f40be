# frozen_string_literal: true

require 'test_helper'

module Surveys
  class ReviewOpenApplicationsJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'sent application complete email with a CV' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: 2.weeks.from_now)
      applicant = survey.applicants.create!(expert:, applied_at: 2.days.ago, application_completion_email_sent_at: nil)

      assert_enqueued_email_with(ApplicantMailer, :application_complete, params: { applicant: expert, survey: }) do
        assert_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'do not sent application complete email without a CV' do
      expert = FactoryBot.create(:expert, :confirmed)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: 2.weeks.from_now)
      applicant = survey.applicants.create!(expert:, applied_at: 2.days.ago, application_completion_email_sent_at: nil)

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'do not sent application complete email when unconfirmed' do
      expert = FactoryBot.create(:expert, :unconfirmed)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: 2.weeks.from_now)
      applicant = survey.applicants.create!(expert:, applied_at: 2.days.ago, application_completion_email_sent_at: nil)

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'do not sent application complete email when already sent' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: 2.weeks.from_now)
      applicant = survey.applicants.create!(expert:, applied_at: 2.days.ago, application_completion_email_sent_at: 1.week.ago)

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'do not sent application complete email when recruitment is closed' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: nil)
      applicant = survey.applicants.create!(expert:, applied_at: 2.days.ago, application_completion_email_sent_at: nil)
      survey.update!(recruitment_closes_on: 1.day.ago)

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'send application complete email within fallback period when recruitment_closes_on is nil' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: nil)
      applicant = survey.applicants.create!(expert:, applied_at: 12.days.ago, application_completion_email_sent_at: nil)

      assert_enqueued_email_with(ApplicantMailer, :application_complete, params: { applicant: expert, survey: }) do
        assert_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'do not sent application complete email when recruitment fallback period has closed' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: nil)
      applicant = survey.applicants.create!(expert:, applied_at: 16.days.ago, application_completion_email_sent_at: nil)

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end

    test 'do not sent application complete email when application is no longer pending' do
      expert = FactoryBot.create(:expert, :confirmed, :with_cv)
      survey = FactoryBot.create(:survey, :scipi, :published, recruitment_closes_on: 2.weeks.from_now)
      applicant = survey.applicants.create!(
        expert:,
        applied_at: 2.days.ago,
        application_completion_email_sent_at: nil,
        rejected_at: Time.current
      )

      assert_no_enqueued_emails do
        assert_no_changes(-> { applicant.reload.application_completion_email_sent_at }) do
          ReviewOpenApplicationsJob.perform_now(expert)
        end
      end
    end
  end
end

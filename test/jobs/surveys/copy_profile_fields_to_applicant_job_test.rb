# frozen_string_literal: true

require 'test_helper'

module Surveys
  class CopyProfileApplicantJobTest < ActiveJob::TestCase
    test 'job copies the profile and keyword counts' do
      survey = FactoryBot.create(:survey)
      keyword = survey.selection_keywords.create!(terms: ['foobar'])
      expert = FactoryBot.create(:expert)
      expert.profile.update!(cv_text: 'I am CV text', general_expertise_updated_at: 1.day.ago)
      # Don't enqueue CopyProfileApplicantJobTest since we're going to run it directly
      applicant = Delayed::Job.suppress do
        survey.applicants.create!(expert:)
      end

      Surveys::CopyProfileDataToApplicantJob.perform_now(applicant)

      assert_not applicant.general_expertise_out_of_date?
      keyword_count = applicant.reload.keyword_counts.find_by!(keyword:)
      assert keyword_count.updated_at.after?(keyword.terms_updated_at)
    end
  end
end

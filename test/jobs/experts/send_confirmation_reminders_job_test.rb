# frozen_string_literal: true

require 'test_helper'

module Experts
  class SendConfirmationRemindersJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'send confirmation reminders' do
      user = FactoryBot.create(:user, :unconfirmed)
      user.update!(confirmation_reminder_sent_at: nil, created_at: 2.weeks.ago)

      assert_enqueued_email_with UserMailer, :confirmation_reminder, args: [user] do
        SendConfirmationRemindersJob.perform_now
      end
    end

    test 'do not send confirmation reminders anonymous users' do
      user = FactoryBot.create(:guest, :unconfirmed)
      user.update!(confirmation_reminder_sent_at: nil, created_at: 2.weeks.ago)

      assert_no_enqueued_emails { SendConfirmationRemindersJob.perform_now }
    end
  end
end

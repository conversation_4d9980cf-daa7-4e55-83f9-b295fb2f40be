# frozen_string_literal: true

require 'test_helper'

module Contracts
  class CreateScipiFoldersJobTest < ActiveJob::TestCase
    test 'create contracts folder' do
      scipi = FactoryBot.create(:scipi)

      SignNow::Gateway.any_instance.tap do |gateway|
        gateway
          .expects(:create_folder)
          .with(name: scipi.sign_now_contracts_folder_name, parent_id: SignNow.configuration.documents_folder_id)
          .returns('abc123')
        gateway
          .expects(:create_folder)
          .with(name: 'Previews', parent_id: 'abc123')
          .returns('def456')
      end

      CreateScipiFoldersJob.perform_now(scipi)

      assert_equal 'abc123', scipi.sign_now_contracts_folder_id
      assert_equal 'def456', scipi.sign_now_previews_folder_id
    end
  end
end

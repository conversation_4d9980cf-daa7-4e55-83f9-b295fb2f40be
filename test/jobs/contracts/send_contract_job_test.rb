# frozen_string_literal: true

require 'test_helper'

module Contracts
  class SendContractJobTest < ActiveJob::TestCase
    test 'sends signature requests' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(expert:)
      callback_url = 'https://example.com/callback'
      redirect_uri = 'https://example.com/redirect'

      Contracts::Sender.any_instance.expects(:run!).once

      SendContractJob.perform_now(panelist, callback_url, redirect_uri)
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Contracts
  class RequestEmbedLinkJobTest < ActiveSupport::TestCase
    test 'perform' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'document_id_abc123',
        sign_now_invite: { 'data' => [{ 'id' => 'invite_id_abc123' }] }
      )

      mock_gateway = SignNow::Gateway.any_instance
      mock_gateway
        .expects(:create_embedded_sending_link)
        .with(document_id: 'document_id_abc123', field_invite_id: 'invite_id_abc123')
        .returns({ 'data' => { 'link' => 'https://example.com/embed' } })

      assert_turbo_stream_broadcasts "contract-status-#{panelist.id}", count: 1 do
        RequestEmbedLinkJob.perform_now(panelist:)
      end

      assert_equal 'https://example.com/embed', panelist.contract_embed_link
    end

    test 'error' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'document_id_abc123',
        sign_now_invite: { 'data' => [{ 'id' => 'invite_id_abc123' }] }
      )

      mock_gateway = SignNow::Gateway.any_instance
      mock_gateway
        .expects(:create_embedded_sending_link)
        .with(document_id: 'document_id_abc123', field_invite_id: 'invite_id_abc123')
        .raises(UnrecoverableError)

      assert_turbo_stream_broadcasts "contract-status-#{panelist.id}", count: 1 do
        RequestEmbedLinkJob.perform_now(panelist:)
      end

      assert_not_nil 'https://example.com/embed', panelist.contract_embed_last_error
      assert_not_nil 'https://example.com/embed', panelist.contract_embed_last_error_at
    end
  end
end

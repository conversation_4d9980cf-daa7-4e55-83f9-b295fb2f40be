# frozen_string_literal: true

require 'test_helper'

module Scipis
  module Reminders
    class SendSurveyMidpointRemindersJobTest < ActiveJob::TestCase
      test 'SurveyMidpointReminders responds to find_and_send_all' do
        assert_respond_to SurveyMidpointReminders, :find_and_send_all
      end

      test 'run job' do
        SurveyMidpointReminders.expects(:find_and_send_all).once

        SendSurveyMidpointRemindersJob.perform_now
      end
    end
  end
end

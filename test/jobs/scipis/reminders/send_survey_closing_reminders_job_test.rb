# frozen_string_literal: true

require 'test_helper'

module Scipis
  module Reminders
    class SendSurveyClosingRemindersJobTest < ActiveJob::TestCase
      test 'SurveyClosingReminders responds to find_and_send_all' do
        assert_respond_to SurveyClosingReminders, :find_and_send_all
      end

      test 'run job' do
        SurveyClosingReminders.expects(:find_and_send_all).once

        SendSurveyClosingRemindersJob.perform_now
      end
    end
  end
end

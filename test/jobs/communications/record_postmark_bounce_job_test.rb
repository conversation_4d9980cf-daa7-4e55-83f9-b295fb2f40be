# frozen_string_literal: true

# frozen_string_literal => true

require 'test_helper'

module Communications
  class RecordPostmarkBounceJobTest < ActiveJob::TestCase
    test 'adds a bounce to a user' do
      user = FactoryBot.create(:user)
      bounced_at = Time.current
      fractional_digits = 7

      bounce = {
        'ID' => 4_323_372_036_854_775_807,
        'Type' => 'HardBounce',
        'TypeCode' => 1,
        'Name' => 'Hard bounce',
        'Tag' => 'Test',
        'MessageID' => '883953f4-6105-42a2-a16a-77a8eac79483',
        'ServerID' => 23,
        'Description' => 'The server was unable to deliver your message (ex => unknown user, mailbox not found).',
        'Details' => 'Test bounce details',
        'Email' => user.email,
        'From' => '<EMAIL>',
        'BouncedAt' => bounced_at.iso8601(fractional_digits),
        'DumpAvailable' => true,
        'Inactive' => true,
        'CanActivate' => true,
        'RecordType' => 'Bounce',
        'Subject' => 'Test subject'
      }

      assert_difference(-> { user.email_bounces.count }) do
        Communications::RecordPostmarkBounceJob.perform_now(bounce)
      end
    end
  end
end

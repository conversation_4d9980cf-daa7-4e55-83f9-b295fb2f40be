# frozen_string_literal: true

require 'test_helper'

module Communications
  class OpportunitiesNewsletterCoordinatingJobTest < ActiveJob::TestCase
    test 'deliver newsletter to subscribers' do
      list = FactoryBot.create(:distribution_list, last_delivery_attempted_at: 14.days.ago)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      FactoryBot.create_list(:scipi, 2, :invite_only, :published)
      FactoryBot.create_list(:survey, 1, :published, branding: FactoryBot.create(:survey_branding, :scipoll))

      matcher = lambda { |parameters|
        args = parameters.first

        args[:subscriber_ids] == [subscriber.id] &&
          args[:list] == list &&
          args[:scipolls].all? { |s| s.scipoll? && s.published? } &&
          args[:recruiting_scipis].all? { |s| s.scipi? && s.published? && s.recruiting? } &&
          !args[:preview]
      }

      assert_enqueued_with(job: OpportunitiesNewsletterBatchJob, args: matcher) do
        OpportunitiesNewsletterCoordinatingJob.perform_now
      end
    end

    test 'newsletter only contains promotable announcements' do
      list = FactoryBot.create(:distribution_list, last_delivery_attempted_at: 14.days.ago)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      FactoryBot.create_list(:announcement, 2, :promotable)
      FactoryBot.create(:announcement, :unpromotable)
      FactoryBot.create(:announcement, :draft)

      matcher = lambda { |parameters|
        args = parameters.first

        args[:subscriber_ids] == [subscriber.id] &&
          args[:list] == list &&
          args[:announcements].all?(&:promotable?) &&
          !args[:preview]
      }

      assert_enqueued_with(job: OpportunitiesNewsletterBatchJob, args: matcher) do
        OpportunitiesNewsletterCoordinatingJob.perform_now
      end
    end

    test 'newsletter works without any promotable announcements' do
      list = FactoryBot.create(:distribution_list, last_delivery_attempted_at: 14.days.ago)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      FactoryBot.create(:announcement, :unpromotable)

      matcher = lambda { |parameters|
        args = parameters.first

        args[:subscriber_ids] == [subscriber.id] &&
          args[:list] == list &&
          args[:announcements].empty? &&
          !args[:preview]
      }

      assert_enqueued_with(job: OpportunitiesNewsletterBatchJob, args: matcher) do
        OpportunitiesNewsletterCoordinatingJob.perform_now
      end
    end

    test 'preview deliver newsletter to subscribers regardless of last_send_at' do
      list = FactoryBot.create(:distribution_list, deliver_every: 14.days, last_delivery_attempted_at: 1.day.ago)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      # Preview recipients should still be subscribed so that
      # unsubscribe links and receiving reason still work
      preview_recipient = FactoryBot.create(:user)
      list.subscribe(preview_recipient)

      FactoryBot.create_list(:scipi, 2, :invite_only, :published)
      FactoryBot.create_list(:survey, 1, :published, branding: FactoryBot.create(:survey_branding, :scipoll))

      matcher = lambda { |parameters|
        args = parameters.first

        args[:subscriber_ids] == [preview_recipient.id] &&
          args[:list] == list &&
          args[:scipolls].all? { |s| s.scipoll? && s.published? } &&
          args[:recruiting_scipis].all? { |s| s.scipi? && s.published? && s.recruiting? } &&
          args[:preview]
      }

      assert_enqueued_with(job: OpportunitiesNewsletterBatchJob, args: matcher) do
        OpportunitiesNewsletterCoordinatingJob.perform_now(preview_recipients: [preview_recipient])
      end
    end

    test 'deliver newsletter in batches' do
      list = FactoryBot.create(:distribution_list)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber1 = FactoryBot.create(:expert)
      subscriber2 = FactoryBot.create(:expert)
      list.subscribe(subscriber1)
      list.subscribe(subscriber2)

      FactoryBot.create_list(:scipi, 2, :invite_only, :published)
      FactoryBot.create_list(:survey, 1, :published, branding: FactoryBot.create(:survey_branding, :scipoll))

      assert_enqueued_jobs(2, only: OpportunitiesNewsletterBatchJob) do
        OpportunitiesNewsletterCoordinatingJob.perform_now(batch_size: 1)
      end
    end

    test 'do not run job more frequently than its deliver_every duration' do
      deliver_every = 2.weeks
      list = FactoryBot.create(:distribution_list, deliver_every:, last_delivery_attempted_at: 10.days.ago)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      assert_no_enqueued_jobs do
        OpportunitiesNewsletterCoordinatingJob.perform_now
      end
    end

    test 'do not send when delivery is suspended' do
      list = FactoryBot.create(:distribution_list, :suspended, last_delivery_attempted_at: 14.days.ago)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = list.id.to_s
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      assert_no_enqueued_jobs do
        OpportunitiesNewsletterCoordinatingJob.perform_now
      end
    end

    test 'do not enqueue when list id ENV variable is missing' do
      list = FactoryBot.create(:distribution_list)
      ENV['SCIPINION_OPPORTUNITIES_DISTRIBUTION_LIST_ID'] = nil
      subscriber = FactoryBot.create(:expert)
      list.subscribe(subscriber)

      assert_raises(MissingEnvironmentVariable) do
        OpportunitiesNewsletterCoordinatingJob.perform_later
      end
    end
  end
end

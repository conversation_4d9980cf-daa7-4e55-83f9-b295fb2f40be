# frozen_string_literal: true

require 'test_helper'

module Communications
  class SubscribeAllToListJobTest < ActiveJob::TestCase
    test 'all users in a list\'s roles are subscribed' do
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, roles: [role])
      list = FactoryBot.create(:distribution_list, roles: [role])

      SubscribeAllToListJob.perform_now(list)

      assert list.subscriptions.exists?(subscriber: user), 'should be subscribed to the list'
    end

    test 'existing users are not resubscribed to a list' do
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, roles: [role])
      list = FactoryBot.create(:distribution_list, roles: [role])
      list.subscriptions.create!(subscriber: user)

      assert_no_difference -> { list.subscriptions.count } do
        SubscribeAllToListJob.perform_now(list)
      end
    end

    test 'unsubscribed users are not resubscribed to a list' do
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, roles: [role])
      list = FactoryBot.create(:distribution_list, roles: [role])
      list.subscriptions.create!(subscriber: user, unsubscribed_at: 1.week.ago)

      assert_no_difference -> { list.subscriptions.count } do
        SubscribeAllToListJob.perform_now(list)
      end
    end
  end
end

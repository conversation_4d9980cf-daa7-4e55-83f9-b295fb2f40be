# frozen_string_literal: true

require 'test_helper'

module Communications
  class OpportunitiesNewsletterBatchJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'deliver opportunities newsletter batch' do
      user = FactoryBot.create(:expert)
      list = FactoryBot.create(:distribution_list)
      subscription = list.subscribe(user)
      subscriber_ids = [user.id]
      recruiting_scipis = QuestionGroup.featured_scipis.recruiting.limit(2)
      scipolls = QuestionGroup.featured_scipolls.limit(2)
      announcements = Announcement.published.promotable.order(published_at: :desc).to_a

      postmark_response = [{ error_code: 0, error_message: 'OK', to: user.email }]

      PostmarkApi.expects(:bulk_deliver!).with do |emails|
        emails.size == 1 && emails.first.to == [user.email]
      end.returns(postmark_response).once

      assert_changes -> { subscription.reload.last_delivery_attempted_at } do
        OpportunitiesNewsletterBatchJob.perform_now(subscriber_ids:, list:, scipolls:, recruiting_scipis:, announcements:)
      end
    end

    test 'email enqueued for verified, unconfirmed subscribers' do
      user = FactoryBot.create(:expert, :unconfirmed, :neverbounce_validated)
      list = FactoryBot.create(:distribution_list, :allow_unconfirmed)
      subscription = list.subscribe(user)
      subscriber_ids = [user.id]
      recruiting_scipis = QuestionGroup.featured_scipis.recruiting.limit(2)
      scipolls = QuestionGroup.featured_scipolls.limit(2)
      announcements = Announcement.published.promotable.order(published_at: :desc).to_a

      postmark_response = [{ error_code: 0, error_message: 'OK', to: user.email }]

      PostmarkApi.expects(:bulk_deliver!).with do |emails|
        emails.size == 1 && emails.first.to == [user.email]
      end.returns(postmark_response).once

      assert_changes -> { subscription.reload.last_delivery_attempted_at } do
        OpportunitiesNewsletterBatchJob.perform_now(subscriber_ids:, list:, scipolls:, recruiting_scipis:, announcements:)
      end
    end

    test 'email not enqueued for un-verified, unconfirmed subscribers' do
      user = FactoryBot.create(:expert, :unconfirmed)
      list = FactoryBot.create(:distribution_list)
      subscription = list.subscribe(user)
      subscriber_ids = [user.id]
      recruiting_scipis = QuestionGroup.featured_scipis.recruiting.limit(2)
      scipolls = QuestionGroup.featured_scipolls.limit(2)

      PostmarkApi.expects(:bulk_deliver!).never

      assert_no_changes -> { subscription.reload.last_delivery_attempted_at } do
        OpportunitiesNewsletterBatchJob.perform_now(subscriber_ids:, list:, scipolls:, recruiting_scipis:, announcements: [])
      end
    end

    test 'email not enqueued for unsubscribed subscribers' do
      list = FactoryBot.create(:distribution_list)
      user = FactoryBot.create(:expert, :confirmed)
      subscription = list.subscriptions.create!(subscriber: user, unsubscribed_at: Time.current)
      subscriber_ids = [user.id]
      recruiting_scipis = QuestionGroup.featured_scipis.recruiting.limit(2)
      scipolls = QuestionGroup.featured_scipolls.limit(2)

      PostmarkApi.expects(:bulk_deliver!).never

      assert_no_changes -> { subscription.reload.last_delivery_attempted_at } do
        OpportunitiesNewsletterBatchJob.perform_now(subscriber_ids:, list:, scipolls:, recruiting_scipis:, announcements: [])
      end
    end

    test 'do not send to a suspended list' do
      list = FactoryBot.create(:distribution_list, :suspended)
      user = FactoryBot.create(:expert, :confirmed)
      subscription = list.subscribe(user)
      subscriber_ids = [user.id]
      recruiting_scipis = QuestionGroup.featured_scipis.recruiting.limit(2)
      scipolls = QuestionGroup.featured_scipolls.limit(2)

      PostmarkApi.expects(:bulk_deliver!).never

      assert_no_changes -> { subscription.reload.last_delivery_attempted_at } do
        OpportunitiesNewsletterBatchJob.perform_now(subscriber_ids:, list:, scipolls:, recruiting_scipis:, announcements: [])
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Pings
  class CreateNewAnswerNotificationsJobTest < ActiveJob::TestCase
    test 'new answer notifies subscribers' do
      ping = FactoryBot.create(:ping)
      answer = FactoryBot.create(:ping_answer, ping:)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      assert_difference(-> { subscriber.notifications.where(notification_type: 'pings:new-answer:subscriber').count }) do
        CreateNewAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'new answer notifies author' do
      author = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping, author:)
      answer = FactoryBot.create(:ping_answer, ping:)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      assert_difference(-> { author.notifications.where(notification_type: 'pings:new-answer:ping-author').count }) do
        CreateNewAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'does not notify unsubscribed subscribers' do
      answer = FactoryBot.create(:ping_answer)
      ping = answer.ping
      unsubscribed = FactoryBot.create(:user)
      ping.subscriptions.create!(subscriber: unsubscribed, unsubscribed_at: Time.current)

      assert_no_difference(-> { unsubscribed.notifications.count }) do
        CreateNewAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'does not notify answerer' do
      answer = FactoryBot.create(:ping_answer)
      ping = answer.ping
      answerer = answer.author
      ping.subscribe(answerer)

      assert_no_difference(-> { answerer.notifications.count }) do
        CreateNewAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'cancel_for_answer only cancels notifications for the specific answer author' do
      ping = FactoryBot.create(:ping)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      target_answer = FactoryBot.create(:ping_answer, ping:)
      CreateNewAnswerNotificationsJob.perform_now(ping, target_answer)

      other_answer = FactoryBot.create(:ping_answer, ping:)
      CreateNewAnswerNotificationsJob.perform_now(ping, other_answer)

      assert_equal 4, Notification.count

      assert_difference(-> { Notification.count }, -2) do
        CreateNewAnswerNotificationsJob.cancel_for_answer(target_answer)
      end

      remaining_messages = Notification.pluck(:message)
      assert(remaining_messages.all? { |msg| msg.include?(other_answer.author.display_name) })
    end

    test 'cancel_for_answer only cancels notifications within time window' do
      ping = FactoryBot.create(:ping)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      old_answer = FactoryBot.create(:ping_answer, ping:, created_at: 10.minutes.ago)
      CreateNewAnswerNotificationsJob.perform_now(ping, old_answer)

      recent_answer = FactoryBot.create(:ping_answer, ping:, created_at: 2.minutes.ago)
      CreateNewAnswerNotificationsJob.perform_now(ping, recent_answer)

      assert_equal 4, Notification.count

      assert_difference(-> { Notification.count }, -2) do
        CreateNewAnswerNotificationsJob.cancel_for_answer(recent_answer)
      end
    end

    test 'cancel_for_answer only cancels undelivered notifications' do
      ping = FactoryBot.create(:ping)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      answer = FactoryBot.create(:ping_answer, ping:)
      CreateNewAnswerNotificationsJob.perform_now(ping, answer)

      Notification.first.update!(delivery_attempted_at: Time.current)

      assert_equal 2, Notification.count

      assert_difference(-> { Notification.count }, -1) do
        CreateNewAnswerNotificationsJob.cancel_for_answer(answer)
      end

      assert Notification.first.delivery_attempted_at.present?
    end
  end
end

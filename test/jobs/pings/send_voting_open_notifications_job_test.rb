# frozen_string_literal: true

require 'test_helper'

module Pings
  class SendVotingOpenNotificationsJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'enqueues voting open notification emails' do
      ping = FactoryBot.create(:ping, :paid, :published, :voting_open)
      subscriber = FactoryBot.create(:user, :confirmed)
      subscription = ping.subscriptions.create!(subscriber:)

      SendVotingOpenNotificationsJob.perform_now(ping)

      assert_enqueued_email_with(NotificationMailer, :voting_open, args: [subscription])
    end

    test 'does not send to unsubscribed users' do
      ping = FactoryBot.create(:ping, :paid, :published)
      subscriber = FactoryBot.create(:user)
      ping.subscriptions.create!(subscriber:, unsubscribed_at: Time.current)

      SendVotingOpenNotificationsJob.perform_now(ping)

      assert_no_enqueued_emails
    end

    test 'does not send to unconfirmed users' do
      ping = FactoryBot.create(:ping, :paid, :published)
      subscriber = FactoryBot.create(:user, :unconfirmed)
      ping.subscriptions.create!(subscriber:)

      SendVotingOpenNotificationsJob.perform_now(ping)

      assert_no_enqueued_emails
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Pings
  class SendAnswerSelectedNotificationsJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'send notifications' do
      ping = FactoryBot.create(:ping, :paid, :answer_accepted)
      expert = FactoryBot.create(:expert)
      subscription = ping.subscriptions.create!(subscriber: expert)

      SendAnswerSelectedNotificationsJob.perform_now(ping)

      assert_not_nil ping.answer_accepted_notification_sent_at
      assert_enqueued_email_with(NotificationMailer, :answer_accepted, args: [subscription])
    end

    test 'notifications already sent' do
      ping = FactoryBot.create(:ping, :paid, :answer_accepted, answer_accepted_notification_sent_at: Time.current)

      SendAnswerSelectedNotificationsJob.perform_now(ping)

      assert_no_enqueued_emails
    end

    test 'does not send to unsubscribed users' do
      ping = FactoryBot.create(:ping, :paid, :answer_accepted)
      subscriber = FactoryBot.create(:user)
      ping.subscriptions.create!(subscriber:, unsubscribed_at: Time.current)

      SendAnswerSelectedNotificationsJob.perform_now(ping)

      assert_no_enqueued_emails
    end

    test 'does not send to unconfirmed users' do
      ping = FactoryBot.create(:ping, :paid, :answer_accepted)
      subscriber = FactoryBot.create(:user, :unconfirmed)
      ping.subscriptions.create!(subscriber:)

      SendAnswerSelectedNotificationsJob.perform_now(ping)

      assert_no_enqueued_emails
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Pings
  class EnqueueVotingOpenNotificationsJobTest < ActiveJob::TestCase
    test 'enqueue Pings with unsent voting notifications' do
      ping = FactoryBot.create(:ping, :unsent_open_voting_notification)

      EnqueueVotingOpenNotificationsJob.perform_now

      assert_enqueued_with(job: SendVotingOpenNotificationsJob, args: [ping])
    end
  end
end

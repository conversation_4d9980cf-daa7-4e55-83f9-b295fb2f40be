# frozen_string_literal: true

require 'test_helper'

module Pings
  class CreateAcceptedAnswerNotificationsJobTest < ActiveJob::TestCase
    test 'notifies subscribers' do
      ping = FactoryBot.create(:ping)
      answer = FactoryBot.create(:ping_answer, ping:)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      assert_difference(-> { subscriber.notifications.where(notification_type: 'pings:answer-accepted:subscriber').count }) do
        CreateAcceptedAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'notifies answerer' do
      answer = FactoryBot.create(:ping_answer)
      ping = answer.ping
      answerer = answer.author
      ping.subscribe(answerer)

      assert_difference(-> { answerer.notifications.where(notification_type: 'pings:answer-accepted:answer-author').count }) do
        CreateAcceptedAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'does not notify Ping author subscribers' do
      answer = FactoryBot.create(:ping_answer)
      ping = answer.ping
      ping_author = ping.author
      ping.subscriptions.create!(subscriber: ping_author)

      assert_no_difference(-> { ping_author.notifications.count }) do
        CreateAcceptedAnswerNotificationsJob.perform_now(ping, answer)
      end
    end

    test 'does not notify unsubscribed subscribers' do
      answer = FactoryBot.create(:ping_answer)
      ping = answer.ping
      unsubscribed = FactoryBot.create(:user)
      ping.subscriptions.create!(subscriber: unsubscribed, unsubscribed_at: Time.current)

      assert_no_difference(-> { unsubscribed.notifications.count }) do
        CreateAcceptedAnswerNotificationsJob.perform_now(ping, answer)
      end
    end
  end
end

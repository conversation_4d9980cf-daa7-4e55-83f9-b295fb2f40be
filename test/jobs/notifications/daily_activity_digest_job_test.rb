# frozen_string_literal: true

require 'test_helper'

module Notifications
  class DailyActivityDigestJobTest < ActiveJob::TestCase
    include ActionMailer::TestHelper

    test 'mark attempted delivery, but do not send to unconfirmed users' do
      list = FactoryBot.create(:distribution_list)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:user, :unconfirmed)
      list.subscribe(user)
      source = FactoryBot.create(:ping)
      notification = user.notifications.create!(message: Faker::Lorem.sentence, notification_type: 'test', source:)

      assert_no_enqueued_emails do
        DailyActivityDigestJob.perform_now
      end

      assert_not_nil notification.reload.delivery_attempted_at
    end

    test 'nothing sent when already delivered' do
      list = FactoryBot.create(:distribution_list)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:user, :confirmed)
      list.subscribe(user)
      source = FactoryBot.create(:ping)
      notification = user.notifications.create!(
        delivery_attempted_at: Time.current,
        message: Faker::Lorem.sentence,
        notification_type: 'test',
        source:
      )

      assert_no_enqueued_emails do
        assert_no_changes -> { notification.reload.delivery_attempted_at } do
          DailyActivityDigestJob.perform_now
        end
      end
    end

    test 'nothing sent when read' do
      list = FactoryBot.create(:distribution_list)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:user, :confirmed)
      list.subscribe(user)
      source = FactoryBot.create(:ping)
      notification = user.notifications.create!(
        read_at: Time.current,
        message: Faker::Lorem.sentence,
        notification_type: 'test',
        source:
      )

      assert_no_enqueued_emails do
        assert_no_changes -> { notification.reload.delivery_attempted_at } do
          DailyActivityDigestJob.perform_now
        end
      end
    end

    test 'nothing sent when unsubscribed from list' do
      list = FactoryBot.create(:distribution_list)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:user, :confirmed)
      subscription = list.subscribe(user)
      subscription.unsubscribe!
      source = FactoryBot.create(:ping)
      notification = user.notifications.create!(message: Faker::Lorem.sentence, notification_type: 'test', source:)

      assert_no_enqueued_emails do
        DailyActivityDigestJob.perform_now
      end

      assert_not_nil notification.reload.delivery_attempted_at
    end

    test 'send unread and undelivered notification' do
      list = FactoryBot.create(:distribution_list)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:user, :confirmed, :with_display_name)
      list.subscribe(user)
      source = FactoryBot.create(:ping, author: user)
      notification = user.notifications.create!(message: Faker::Lorem.sentence, notification_type: 'test', source:)

      assert_enqueued_jobs 1, only: ActionMailer::MailDeliveryJob do
        assert_changes -> { notification.reload.delivery_attempted_at } do
          DailyActivityDigestJob.perform_now
        end
      end

      # Verify the right mailer method was called
      enqueued_job = enqueued_jobs.find { |job| job[:job] == ActionMailer::MailDeliveryJob }
      assert_equal 'SubscriptionsMailer', enqueued_job[:args][0]
      assert_equal 'pings', enqueued_job[:args][1]
    end

    test 'suspended list fails and does nothing' do
      list = FactoryBot.create(:distribution_list, :suspended)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:user, :confirmed, :with_display_name)
      list.subscribe(user)
      source = FactoryBot.create(:ping, author: user)
      notification = user.notifications.create!(message: Faker::Lorem.sentence, notification_type: 'test', source:)

      assert_no_enqueued_emails do
        assert_no_changes -> { notification.reload.delivery_attempted_at } do
          DailyActivityDigestJob.perform_now
        end
      end
    end

    test 'digest serialization works through real job lifecycle' do
      list = FactoryBot.create(:distribution_list)
      ENV['ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID'] = list.id.to_s
      user = FactoryBot.create(:expert, :with_display_name)
      list.subscribe(user)

      # Create real data that would trigger the job
      expertise = FactoryBot.create(:expertise)
      user.profile.expertises = [expertise]

      ping = FactoryBot.create(:ping, author: user)
      notification = user.notifications.create!(
        source: ping,
        notification_type: 'pings:new-answer:ping-author',
        message: 'Test notification'
      )

      digest = DailyActivityDigest.for(user, user.notifications.where(id: notification.id))

      job = DailyActivityDigestJob.new
      job.arguments = [digest, list]

      # This will fail if serialization is broken
      assert_nothing_raised do
        ActiveJob::Base.queue_adapter.enqueued_jobs << {
          job: job,
          args: ActiveJob::Arguments.serialize([digest, list])
        }
      end

      # This will fail if deserialization is broken
      assert_nothing_raised do
        serialized_args = ActiveJob::Arguments.serialize([digest, list])
        deserialized_args = ActiveJob::Arguments.deserialize(serialized_args)

        deserialized_digest = deserialized_args[0]
        assert_equal digest.pings.count, deserialized_digest.pings.count
        assert_equal digest.relevant_expertise.count, deserialized_digest.relevant_expertise.count
        assert_equal digest.ping_author_notifications.count, deserialized_digest.ping_author_notifications.count
      end
    end
  end
end

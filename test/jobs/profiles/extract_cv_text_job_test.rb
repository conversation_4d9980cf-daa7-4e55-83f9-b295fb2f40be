# frozen_string_literal: true

require 'test_helper'

module Profiles
  class ExtractCvTextJobTest < ActiveJob::TestCase
    test 'extract CV text' do
      # Skip callback so it does not run on save
      Profile.skip_callback(:commit, :after, :sync_cv_text_after_commit)

      old_cv_text = 'I am going to change'
      profile = FactoryBot.create(:profile, cv_text: old_cv_text)
      filename = 'test_attachment.pdf'
      new_cv = load_test_file(filename)
      profile.cv.attach(
        io: new_cv,
        filename:,
        content_type: 'application/pdf'
      )

      ExtractCvTextJob.perform_now(profile)

      assert_not_equal old_cv_text,
                       profile.reload.cv_text,
                       'CV text should have been updated'

      # Re-enable so it does not fail in other tests.
      Profile.set_callback(:commit, :after, :sync_cv_text_after_commit)
    end

    test 'extract unprocessable CV' do
      profile = FactoryBot.create(:profile, cv_text: 'existing CV text', last_text_extraction_error: nil)
      filename = 'unprocessable_cv.pdf'
      cv_file = load_test_file(filename)
      profile.cv.attach(io: cv_file, filename:, content_type: 'application/pdf')

      ExtractCvTextJob.perform_now(profile)

      assert_not_nil profile.last_text_extraction_error
      assert_nil profile.cv_text
    end
  end
end

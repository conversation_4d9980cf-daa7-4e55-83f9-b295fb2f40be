# frozen_string_literal: true

require 'test_helper'

module Profiles
  class AddExpertiseToProfilesJobTest < ActiveJob::TestCase
    test 'add expertise to profile with term in other expertise' do
      expert = FactoryBot.create(:expert)
      expert.profile.update!(other_expertise: 'matching term')
      expertise = FactoryBot.create(:expertise, name: 'matching term')

      Profiles::AddExpertiseToProfilesJob.perform_now(expertise)

      assert_includes expert.profile.expertises, expertise
    end
  end
end

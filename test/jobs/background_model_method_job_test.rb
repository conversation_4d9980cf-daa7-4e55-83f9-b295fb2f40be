# frozen_string_literal: true

require 'test_helper'

class BackgroundModelMethodJobTest < ActiveJob::TestCase
  class TestModel
    attr_reader :called_with

    def test_method(*args)
      @called_with = args
    end
  end

  test 'job calls specified method on record with arguments' do
    test_model = TestModel.new
    args = [1, 'two', { three: 3 }]

    BackgroundModelMethodJob.perform_now(test_model, :test_method, *args)

    assert_equal args, test_model.called_with
  end

  test 'job discards errors' do
    test_model = TestModel.new

    # Create a method that raises an error
    def test_model.error_method
      raise StandardError, 'Test error'
    end

    # Asserting no error is raised because it's discarded
    assert_nothing_raised do
      BackgroundModelMethodJob.perform_now(test_model, :error_method)
    end
  end
end

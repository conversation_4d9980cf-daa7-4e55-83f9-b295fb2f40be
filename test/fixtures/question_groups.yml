_fixture:
  ignore:
    - base_scipi
    - base_scipoll

DEFAULTS: &DEFAULTS
  created_by: scipi_admin
  name: $LABEL

base_scipi: &base_scipi
  <<: *DEFAULTS
  branding: scipi
  invite_only: true

base_scipoll: &base_scipoll
  <<: *DEFAULTS
  branding: scipoll

base_legacy: &base_legacy
  <<: *DEFAULTS
  branding: legacy

# A general-purpose survey that should be used for testing
# functionality without regard to branding, status, paid, etc.
survey:
  <<: *DEFAULTS
  branding: survey
  name: "Generic Survey"
  published: true

# a general-purpose, published SciPi
scipi:
  <<: *base_scipi
  published: true

# a general-purpose, published SciPoll
scipoll:
  <<: *base_scipoll
  published: true
  closes_at: <%= 1.month.from_now.to_date %>

# a legacy survey with access code protection
legacy:
  <<: *base_legacy
  published: true
  access_code: 'accesscode'

# Use when the test explicitly requires applications being closed as a precondition
applications_closed:
  <<: *base_scipi
  recruitment_closes_on: <%= 1.month.ago.to_date %>
  published: true

# Use when the test explicitly requires applications open as a precondition
applications_open:
  <<: *base_scipi
  recruitment_closes_on: <%= 1.month.from_now.to_date %>
  published: true

draft:
  <<: *base_scipoll
  published: false

published:
  <<: *base_scipoll
  published: true

complete_scipoll:
  <<: *base_scipoll
  name: 'Complete SciPoll'
  published: true
  closes_at: <%= 1.day.ago.to_date %>

# To be used for the testing the confirmation process
db_admin:
  email: $<EMAIL>
  password_digest: <%= BCrypt::Password.create('secret123') %>
  display_id: $LABEL-000001

expert:
  confirmed_at: <%= 1.year.ago %>
  email: $<EMAIL>
  password_digest: <%= BCrypt::Password.create('secret123') %>
  country: usa
  current_employment_sector: industry
  display_id: $LABEL-000001

pings_admin:
  confirmed_at: <%= 1.year.ago %>
  email: $<EMAIL>
  password_digest: <%= BCrypt::Password.create('secret123') %>
  display_id: $LABEL-000001

scipi_admin:
  email: $<EMAIL>
  password_digest: <%= BCrypt::Password.create('secret123') %>
  display_id: $LABEL-000002

suspicious:
  email: $<EMAIL>
  password_digest: <%= BCrypt::Password.create('secret123') %>
  display_id: $LABEL-000001
  marked_suspicious_at: <%= 1.week.ago %>
  marked_suspicious_reason: "I am a shady character"

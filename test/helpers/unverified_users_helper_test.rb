# frozen_string_literal: true

require 'test_helper'

class UnverifiedUsersHelperTest < ActionView::TestCase
  test 'verified users see nothing' do
    Current.user = FactoryBot.create(:expert)

    unverified_user_alert

    assert_dom '.row', false, 'should not show unverified user alert'
  end

  test 'show unverified user header' do
    Current.user = FactoryBot.create(:expert, :unverified)

    unverified_user_alert

    assert_alert_header 'Your Account is Pending'
  end

  test 'show unverified user with incomplete profile ' do
    Current.user = FactoryBot.create(:expert, :unverified)

    unverified_user_alert

    assert_unconfirmed_alert_content do
      assert_dom 'ul' do
        assert_dom 'li' do
          assert_dom 'a', 'Complete your profile'
        end
      end
    end
  end

  test 'show unverified user with unconfirmed email ' do
    Current.user = FactoryBot.create(:expert, :unverified, :unconfirmed)

    unverified_user_alert

    assert_unconfirmed_alert_content do
      assert_dom 'ul' do
        assert_dom 'li', 'Confirm your email address'
      end
    end
  end

  test 'show unverified user with confirmed email and complete profile ' do
    Current.user = FactoryBot.create(:expert, :confirmed, :complete_profile, :unverified)

    unverified_user_alert

    assert_unconfirmed_alert_content do
      assert_dom 'div', /an admin should review your account/
    end
  end

  private

  def assert_alert(&)
    assert_dom '.row' do
      assert_dom '.col' do
        assert_dom '.alert', &
      end
    end
  end

  def assert_alert_header(value)
    assert_alert do
      assert_dom '.alert-heading', value
    end
  end

  def assert_unconfirmed_alert_content(&)
    assert_alert do
      assert_dom 'div.d-flex' do
        assert_dom 'div' do
          assert_dom 'div', &
        end
      end
    end
  end
end

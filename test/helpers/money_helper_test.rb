# frozen_string_literal: true

require 'test_helper'

class MoneyHelperTest < ActionView::TestCase
  test 'format min only' do
    assert_equal '$1,000', format_money_range(min: 1000)
  end

  test 'format range' do
    assert_equal '$1,000 - $2,000', format_money_range(min: 1000, max: 2000)
  end

  test 'format range with precision' do
    assert_equal '$1,000.00 - $2,000.00', format_money_range(min: 1000, max: 2000, precision: 2)
  end
end

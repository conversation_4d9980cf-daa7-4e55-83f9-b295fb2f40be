# frozen_string_literal: true

require 'test_helper'

class ContractsHelperTest < ActionView::TestCase
  test 'do not show contract actions when logged out' do
    scipi = FactoryBot.create(:scipi, :invite_only, contract_required: true)
    Current.user = nil

    assert_not show_contract_actions?(scipi:), 'should not show to logged out users'
  end

  test 'do not show contract actions to panelists unless contracts are required' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi, :invite_only, contract_required: false)
    scipi.panelists.create!(user: expert)
    Current.user = expert

    assert_not show_contract_actions?(scipi:), 'should not show to panelists when contracts are not required'
  end

  test 'do not show contract actions to non-panelists' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi, :invite_only, contract_required: true)
    scipi.applicants.create!(user: expert)
    Current.user = expert

    assert_not show_contract_actions?(scipi:), 'should not show to non-panelists'
  end

  test 'show contract actions to panelists when contracts are required' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi, :invite_only, contract_required: true)
    scipi.panelists.create!(user: expert)
    Current.user = expert

    assert show_contract_actions?(scipi:), 'should show to panelists when contracts are required'
  end
end

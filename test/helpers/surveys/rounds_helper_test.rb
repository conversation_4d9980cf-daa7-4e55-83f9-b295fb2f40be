# frozen_string_literal: true

require 'test_helper'

class Surveys::RoundsHelperTest < ActionView::TestCase
  setup do
    # Freeze to avoid DST bugs in the tests. The code itself is correct.
    travel_to(Date.new(2025, 1, 1))
  end

  teardown do
    travel_back
  end

  test 'round_time_remaining_in_words returns empty string for non-Time objects' do
    assert_equal '', round_time_remaining_in_words(nil)
    assert_equal '', round_time_remaining_in_words('not a time')
  end

  test "returns 'in a few minutes' for less than 30 minutes" do
    ends_at = 15.minutes.from_now
    assert_equal 'in a few minutes', round_time_remaining_in_words(ends_at)

    ends_at = 29.minutes.from_now
    assert_equal 'in a few minutes', round_time_remaining_in_words(ends_at)
  end

  test "returns 'less than an hour' for less than 60 minutes" do
    ends_at = 30.minutes.from_now
    assert_equal 'less than an hour', round_time_remaining_in_words(ends_at)

    ends_at = 59.minutes.from_now
    assert_equal 'less than an hour', round_time_remaining_in_words(ends_at)
  end

  test "returns 'about an hour' for between 60 and 90 minutes" do
    ends_at = 60.minutes.from_now
    assert_equal 'about an hour', round_time_remaining_in_words(ends_at)

    ends_at = 89.minutes.from_now
    assert_equal 'about an hour', round_time_remaining_in_words(ends_at)
  end

  test "returns 'less than X hours' for between 90 minutes and 48 hours" do
    ends_at = 90.minutes.from_now
    assert_equal 'less than 2 hours', round_time_remaining_in_words(ends_at)

    ends_at = 24.hours.from_now
    assert_equal 'less than 24 hours', round_time_remaining_in_words(ends_at)

    ends_at = 48.hours.from_now
    assert_equal 'less than 48 hours', round_time_remaining_in_words(ends_at)
  end

  test "returns 'less than X days' for between 48 hours and 28 days" do
    ends_at = (5.days + 7.hours).from_now
    assert_equal 'less than 6 days', round_time_remaining_in_words(ends_at)

    ends_at = (27.days + 11.hours).from_now
    assert_equal 'less than 28 days', round_time_remaining_in_words(ends_at)
  end

  test "returns 'about X months' for above 28 days" do
    ends_at = 28.days.from_now
    assert_equal 'about a month', round_time_remaining_in_words(ends_at)

    ends_at = 60.days.from_now
    assert_equal 'about 2 months', round_time_remaining_in_words(ends_at)

    ends_at = 6.months.from_now
    assert_equal 'about 6 months', round_time_remaining_in_words(ends_at)
  end
end

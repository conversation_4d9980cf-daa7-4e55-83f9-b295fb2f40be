# frozen_string_literal: true

require 'test_helper'

class CommentsHelperTest < ActionView::TestCase
  test 'can delete comment - current user is comment user' do
    current_user = build(:user)
    comment_user = current_user

    assert can_delete_comment?(current_user, comment_user)
  end

  test 'can delete comment - current user is admin' do
    current_user = build(:admin)
    comment_user = build(:user)

    assert can_delete_comment?(current_user, comment_user)
  end

  test 'cannot delete comment - current user is not comment user' do
    current_user = build(:user, first_name: 'Curly')
    comment_user = build(:user, first_name: '<PERSON>')

    assert_not can_delete_comment?(current_user, comment_user)
  end
end

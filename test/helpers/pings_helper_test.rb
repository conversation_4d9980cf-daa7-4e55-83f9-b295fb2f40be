# frozen_string_literal: true

require 'test_helper'

class PingsHelperTest < ActionView::TestCase
  test 'do not show insufficient credit for logged out users' do
    logged_out!

    assert_not pings_show_insufficient_credit?,
               'logged out users should not have insufficient credit'
  end

  test 'do not show insufficient credit for unconfirmed users' do
    unconfirmed = FactoryBot.create(:expert, :unconfirmed)

    logged_in_as(unconfirmed)

    assert_not pings_show_insufficient_credit?,
               'unconfirmed users should not have insufficient credit'
  end

  test 'do not show insufficient credit' do
    expert = FactoryBot.create(:expert, ping_credits: 5)

    logged_in_as(expert)

    assert_not pings_show_insufficient_credit?,
               'do not show insufficient credit'
  end

  test 'show insufficient credit' do
    expert = FactoryBot.create(:expert, :out_of_ping_credit, :with_display_name)

    logged_in_as(expert)

    assert pings_show_insufficient_credit?, 'show insufficient credit'
  end
end

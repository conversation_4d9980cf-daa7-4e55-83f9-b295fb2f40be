# frozen_string_literal: true

require 'test_helper'

class CurrentUserHelperTest < ActionView::TestCase
  test 'logged out user is not unconfirmed' do
    logged_out!

    assert_not current_user_show_unconfirmed?,
               'logged out users are not considered unconfirmed'
  end

  test 'logged in user is not unconfirmed' do
    expert = FactoryBot.create(:expert)

    logged_in_as(expert)

    assert_not current_user_show_unconfirmed?,
               'the current user should not be unconfirmed'
  end

  test 'logged in user is unconfirmed' do
    expert = FactoryBot.create(:expert, :unconfirmed)

    logged_in_as(expert)

    assert current_user_show_unconfirmed?, 'the current user should be confirmed'
  end
end

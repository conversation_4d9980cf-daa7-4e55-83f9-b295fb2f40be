# frozen_string_literal: true

require 'test_helper'

class VotesHelperTest < ActionView::TestCase
  test 'has already voted' do
    survey = create(:question_group)
    user = create(:expert)
    user2 = create(:expert, first_name: 'First')
    comment = create(:comment, debate_topic: survey)
    comment2 = create(:comment, debate_topic: survey)
    create(:vote, user:, comment:, yay: true)

    assert voted?(user.id, comment.id, true)
    assert_not voted?(user.id, comment.id, false)
    assert_not voted?(user.id, comment2.id, true)
    assert_not voted?(user.id, comment2.id, false)
    assert_not voted?(user2.id, comment.id, false)
    assert_not voted?(user2.id, comment.id, false)
  end

  test 'up down' do
    assert_equal 'up', up_down(true)
    assert_equal 'down', up_down(false)
  end
end

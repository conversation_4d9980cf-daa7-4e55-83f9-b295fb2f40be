# frozen_string_literal: true

require 'test_helper'

class NavHelperTest < ActionView::TestCase
  test 'show slim user nav to anonymous user' do
    Current.user = User.new(email: nil)

    assert show_slim_user_nav?, 'should show slim user nav to anonymous users'
  end

  test 'do not show slim user nav to registered user' do
    Current.user = User.new(email: '<EMAIL>')

    assert_not show_slim_user_nav?, 'should not show slim user nav to registered users'
  end

  test 'do not show slim to logged out users' do
    Current.user = nil

    assert_not show_slim_user_nav?, 'should not show slim user nav to logged out users'
  end
end

# frozen_string_literal: true

require 'test_helper'

module Scipis
  class MessagesHelperTest < ActionView::TestCase
    test 'expert_unread_message_badge when there are no unread admin replies' do
      user = FactoryBot.create(:expert)
      message = FactoryBot.create(:message, user: user, sent_by: user, last_read_by_user_at: 1.hour.ago)

      result = expert_unread_message_badge(message)

      assert_nil result
    end

    test 'expert_unread_message_badge when message has never been read' do
      user = FactoryBot.create(:expert)
      message = FactoryBot.create(:message, user: user, sent_by: user, last_read_by_user_at: nil)

      result = expert_unread_message_badge(message)

      assert_includes result, 'New Message'
    end

    test 'expert_unread_message_badge with one unread admin reply' do
      user = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)
      message = FactoryBot.create(:message, user: user, sent_by: user, last_read_by_user_at: 2.hours.ago)
      FactoryBot.create(:message, :reply, user: user, sent_by: admin, parent: message, created_at: 1.hour.ago)

      result = expert_unread_message_badge(message)

      assert_includes result, '1 new reply'
    end

    test 'expert_unread_message_badge with multiple unread admin replies' do
      user = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)
      message = FactoryBot.create(:message, user: user, sent_by: user, last_read_by_user_at: 3.hours.ago)
      FactoryBot.create(:message, :reply, user: user, sent_by: admin, parent: message, created_at: 2.hours.ago)
      FactoryBot.create(:message, :reply, user: user, sent_by: admin, parent: message, created_at: 1.hour.ago)
      FactoryBot.create(:message, :reply, user: user, sent_by: user, parent: message, created_at: 30.minutes.ago)

      result = expert_unread_message_badge(message)

      assert_includes result, '2 new replies'
    end

    test 'expert_last_message_label when expert sent the original message' do
      user = FactoryBot.create(:expert)
      message = FactoryBot.create(:message, user: user, sent_by: user, created_at: 2.hours.ago)

      result = expert_last_message_label(message)

      assert_equal 'Sent by you about 2 hours ago', result
    end

    test 'expert_last_message_label when expert replied' do
      user = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)

      original_message = FactoryBot.create(:message, user: user, sent_by: admin, created_at: 3.hours.ago)
      FactoryBot.create(:message, user: user, sent_by: user, parent: original_message, subject: nil, created_at: 1.hour.ago)

      result = expert_last_message_label(original_message)

      assert_equal 'You replied about 1 hour ago', result
    end

    test 'expert_last_message_label when admin sent the original message' do
      user = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)
      message = FactoryBot.create(:message, user: user, sent_by: admin, created_at: 5.hours.ago)

      result = expert_last_message_label(message)

      assert_equal 'Sent by SciPi Admin about 5 hours ago', result
    end

    test 'expert_last_message_label when admin replied' do
      user = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)

      original_message = FactoryBot.create(:message, user: user, sent_by: user, created_at: 4.hours.ago)
      FactoryBot.create(:message, user: user, sent_by: admin, parent: original_message, subject: nil, created_at: 30.minutes.ago)

      result = expert_last_message_label(original_message)

      assert_equal 'SciPi Admin replied 30 minutes ago', result
    end
  end
end

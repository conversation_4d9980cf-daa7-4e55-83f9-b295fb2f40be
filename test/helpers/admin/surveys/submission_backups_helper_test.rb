# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class SubmissionBackupsHelperTest < ActionView::TestCase
      include ActionDispatch::TestProcess::FixtureFile
      include IconHelper

      test 'submission_backup_label shows "Running..." when backup is running' do
        scipi = FactoryBot.create(:scipi)
        backup = ::Surveys::SubmissionBackup.create!(survey: scipi)

        result = submission_backup_label(backup)

        assert_match(/Running/, result)
      end

      test 'submission_backup_label shows error message when backup failed' do
        scipi = FactoryBot.create(:scipi)
        backup = ::Surveys::SubmissionBackup.create!(survey: scipi, last_error: 'Test error')

        result = submission_backup_label(backup)

        assert_equal 'Error: Test error', result
      end

      test 'submission_backup_label shows a link when backup is complete' do
        scipi = FactoryBot.create(:scipi)
        backup = ::Surveys::SubmissionBackup.create!(
          survey: scipi,
          file: fixture_file_upload('fake_submission_backup.csv', 'text/csv')
        )
        file_url = '/path/to/fake_submission_backup.csv'
        # Need to stub here since file storage is used in tests and a URL cannot be generated here.
        backup.file.stubs(:url).returns(file_url)

        result = submission_backup_label(backup)

        assert_match(/fake_submission_backup\.csv/, result)
        assert_match(%r{<a .*href="#{file_url}".*>fake_submission_backup\.csv</a>}, result)
      end

      test 'submission_backup_status_icon shows spinner when running' do
        scipi = FactoryBot.create(:scipi)
        backup = ::Surveys::SubmissionBackup.create!(survey: scipi)

        result = submission_backup_status_icon(backup)

        assert_match(/fa-spinner/, result)
      end

      test 'submission_backup_status_icon shows exclamation when failed' do
        scipi = FactoryBot.create(:scipi)
        backup = ::Surveys::SubmissionBackup.create!(survey: scipi, last_error: 'Test error')

        result = submission_backup_status_icon(backup)

        assert_match(/fa-circle-exclamation/, result)
      end
    end
  end
end

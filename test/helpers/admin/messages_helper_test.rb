# frozen_string_literal: true

require 'test_helper'

module Admin
  class MessagesHelperTest < ActionView::TestCase
    include Admin::MessagesHelper

    test 'admin_last_read_label with nil last_read_by_user_at returns not yet read' do
      message = Message.new(last_read_by_user_at: nil)

      result = admin_last_read_label(message)

      assert_match(/Not yet read/, result)
    end

    test 'admin_last_read_label returns user name with time distance' do
      user = FactoryBot.build(:user, first_name: '<PERSON>', last_name: '<PERSON><PERSON>')
      message = Message.new(last_read_by_user_at: 2.hours.ago, user: user)

      result = admin_last_read_label(message)

      assert_match(/Last read by <PERSON> about 2 hours ago/, result)
    end

    test 'admin_last_read_label with different time shows correct distance' do
      user = FactoryBot.build(:user, first_name: '<PERSON>', last_name: '<PERSON>')
      message = Message.new(last_read_by_user_at: 1.day.ago, user: user)

      result = admin_last_read_label(message)

      assert_match(/Last read by <PERSON> 1 day ago/, result)
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class GridHelperTest < ActionView::TestCase
  setup do
    setup_grid_question_test_data
  end

  test 'row header by position' do
    assert_equal :answer_text_on_1st_row, row_header_by_position(1)
    assert_equal :answer_text_on_2nd_row, row_header_by_position(2)
    assert_equal :answer_text_on_10th_row, row_header_by_position(10)
  end

  test 'translate answer text for radio type' do
    assert_equal '</td><td></td></tr>', translate_answer_text(nil, 'Radio', 2)
    assert_equal '1</td><td>0</td></tr>', translate_answer_text('1-0', 'Radio', 2)
    assert_equal '0</td><td>1</td><td>0</td></tr>', translate_answer_text('2-1', 'Radio', 3)
  end

  test 'translate answer text for checkbox type' do
    assert_equal '</td><td></td><td></td></tr>', translate_answer_text(nil, 'Checkbox', 3)
    assert_equal '1</td><td>0</td></tr>', translate_answer_text("1\r\n0", 'Checkbox', 2)
    assert_equal '0</td><td>1</td><td>0</td></tr>', translate_answer_text("0\r\n1\r\n0", 'Checkbox', 3)
  end

  test 'translate answer text for select type' do
    assert_equal '</td><td></td></tr>', translate_answer_text(nil, 'Select', 2)
    assert_equal '1</td><td></td></tr>', translate_answer_text("1\r\n", 'Select', 2)
    assert_equal '4</td><td>3</td><td>5</td></tr>', translate_answer_text("4\r\n3\r\n5", 'Select', 3)
  end

  test 'grid answer - radio question' do
    response = create(:submission, question_group: @survey)
    answer = create(:answer, answer_group: response, question: @grid_radio_question, answer_text_on_1st_row: '0-1', answer_text_on_2nd_row: '1-1')
    assert_equal "<table class='table table-bordered'><thead><tr><td>First Cell</td><td>Up</td><td>Down</td></tr></thead><tr><td class='grid-row-header'>Yes</td><td>0</td><td>1</td></tr><tr><td class='grid-row-header'>No</td><td>0</td><td>1</td></tr></table>", grid_answer(answer)
  end

  test 'grid answer - checkbox question' do
    response = create(:submission, question_group: @survey)
    answer = create(:answer, answer_group: response, question: @grid_checkbox_question, answer_text_on_1st_row: "1\r\n0", answer_text_on_2nd_row: "0\r\n1", answer_text_on_3rd_row: "0\r\n1")

    assert_equal "<table class='table table-bordered'><thead><tr><td>Top Cell</td><td>Delicious</td><td>Pricey</td></tr></thead><tr><td class='grid-row-header'>Chocolate</td><td>1</td><td>0</td></tr><tr><td class='grid-row-header'>Vanilla</td><td>0</td><td>1</td></tr><tr><td class='grid-row-header'>Strawberry</td><td>0</td><td>1</td></tr></table>", grid_answer(answer)
  end

  test 'grid answer - select question' do
    response = create(:submission, question_group: @survey)
    answer = create(:answer, answer_group: response, question: @grid_select_question, answer_text_on_1st_row: "5\r\n4\r\n", answer_text_on_2nd_row: "\r\n2\r\n5", answer_text_on_3rd_row: "1\r\n3\r\n4")

    assert_equal "<table class='table table-bordered'><thead><tr><td>Top Left Cell</td><td>Prestige</td><td>Pay</td><td>Convenience</td></tr></thead><tr><td class='grid-row-header'>Apple</td><td>5</td><td>4</td><td></td></tr><tr><td class='grid-row-header'>Google</td><td></td><td>2</td><td>5</td></tr><tr><td class='grid-row-header'>Tesla</td><td>1</td><td>3</td><td>4</td></tr></table>", grid_answer(answer)
  end
end

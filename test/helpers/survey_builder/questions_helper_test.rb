# frozen_string_literal: true

require 'test_helper'

module SurveyBuilder
  class QuestionsHelperTest < ActionView::TestCase
    setup do
      @survey = create(:question_group)
    end

    test 'question type select options for radio' do
      radio_question_type = 'Questions::Radio'
      checkbox_question_type = 'Questions::Checkbox'
      question = create(:question, question_group: @survey, type: radio_question_type)

      options = allowed_question_types(question:)

      options.each do |option|
        disabled = [radio_question_type, checkbox_question_type].exclude?(option[1])
        assert_equal option[2][:disabled], disabled
      end
    end

    test 'question type select options for free text' do
      free_text_question_type = 'Questions::Long'
      question = create(:question, question_group: @survey, type: free_text_question_type)

      options = allowed_question_types(question:)

      options.each do |option|
        disabled = (option[1] != free_text_question_type)
        assert_equal option[2][:disabled], disabled
      end
    end
  end
end

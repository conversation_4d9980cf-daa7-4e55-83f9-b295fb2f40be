# frozen_string_literal: true

ENV['RAILS_ENV'] ||= 'test'
require File.expand_path('../config/environment', __dir__)
require 'rails/test_help'
require 'faker'
require 'factory_bot_rails'
require 'minitest/test_profile'
require 'mocha/minitest'
require 'webmock/minitest'

require 'lib/ability_test_helper'
require 'lib/scipis/reminders/test_helper'
require 'lib/turbo_assertions_helper'
require 'lib/mailer_test_helper'

require_relative 'lib/test_data_generator'
require_relative 'lib/test_data'

# Set Bcrypt to use the minimum cost factor to speed up tests
ActiveModel::SecurePassword.min_cost = true

# Print 10 slowest tests
Minitest::TestProfile.use!

WebMock.disable_net_connect!(allow_localhost: true)

module ActiveSupport
  class TestCase
    parallelize

    fixtures :all

    setup do
      FactoryBot.rewind_sequences
    end

    include FactoryBot::Syntax::Methods

    def assert_error_on(model, attr, kind)
      assert_not model.valid?, 'model should be invalid'
      assert model.errors.of_kind?(attr, kind),
             "expected an error of :#{kind} on :#{attr}"
    end

    def assert_same_elements(expected, actual, message = nil)
      assert_equal(expected.sort, actual.sort, message)
    end

    def attachable_file_fixture(filename)
      file = file_fixture(filename)
      Rack::Test::UploadedFile.new(file)
    end

    def create_panelist(survey, **)
      expert = create_expert
      survey.applicants.create!(expert:, approved_at: Time.current, **)
    end

    def create_expert!(**)
      Role.expert.users.create!(email: Faker::Internet.email, password: Faker::Internet.password, **).reload
    end
    alias create_expert create_expert!

    def load_all_result_types
      FactoryBot.factories[:result_type].defined_traits.each do |trait|
        FactoryBot.create(:result_type, trait.name)
      end
    end

    def load_all_render_types
      FactoryBot.factories[:render_type].defined_traits.each do |trait|
        FactoryBot.create(:render_type, trait.name)
      end
    end

    def load_test_file(filename)
      path = file_fixture(filename)
      File.new(path)
    end

    def setup_grid_question_test_data
      creator = create(:creator)
      @survey = create(:question_group, created_by: creator)

      @grid_radio_question = create(:grid_question, question_group: @survey, question_text: 'Radio question text')
      create(:grid_structure, input_type: 'Radio', question: @grid_radio_question, row_headers: %w[Yes No], column_headers: %w[Up Down], first_cell_text: 'First Cell')

      @grid_checkbox_question = create(:grid_question, question_group: @survey, question_text: 'Checkbox question text')
      create(:grid_structure, input_type: 'Checkbox', question: @grid_checkbox_question, row_headers: %w[Chocolate Vanilla Strawberry], column_headers: %w[Delicious Pricey], first_cell_text: 'Top Cell')

      @grid_select_question = create(:grid_question, question_group: @survey, question_text: 'Select question text')
      create(:grid_structure, input_type: 'Select', question: @grid_select_question, row_headers: %w[Apple Google Tesla], column_headers: %w[Prestige Pay Convenience], first_cell_text: 'Top Left Cell')
    end

    def uploaded_fixture_file(filename)
      Rack::Test::UploadedFile.new(File.join(file_fixture_path, filename))
    end
  end
end

module ActionMailer
  class TestCase
    include MailerTestHelper
    include Rails.application.routes.url_helpers

    def default_url_options = Rails.application.config.action_mailer.default_url_options
  end
end

module ActionController
  class TestCase
    def assert_files_equal(expected, actual)
      expected_hash = Digest::MD5.hexdigest(expected)
      actual_hash = Digest::MD5.hexdigest(actual)
      assert_equal expected_hash, actual_hash, "files should be the same: the expected_hash was #{expected_hash} and the actual_hash was #{actual_hash}"
    end

    def sign_in(user)
      session[:user_id] = user.id
    end

    def sign_out!
      session[:user_id] = nil
    end
  end
end

module ActionDispatch
  class IntegrationTest
    include TurboAssertionsHelper

    def sign_in(user)
      post sessions_path, params: { session: { email: user.email, password: 'secret123' } }
    end

    def assert_content_disposition(filename:, disposition: 'attachment', ascii_only: false)
      expected = if ascii_only
                   "#{disposition}; filename=\"#{filename}\""
                 else
                   ActionDispatch::Http::ContentDisposition.format(filename:, disposition:)
                 end

      assert_equal expected, response.headers['Content-Disposition']
    end

    def assert_content_type(extension)
      content_type = MiniMime.lookup_by_extension(extension).content_type

      assert_equal content_type, response.content_type
    end
  end
end

module ActionView
  class TestCase
    def logged_in_as(user)
      stubs(:current_user).returns(user)
      stubs(:logged_in?).returns(true)
    end

    def logged_in_as_unconfirmed!
      unconfirmed = FactoryBot.create(:expert, :unconfirmed)
      logged_in_as(unconfirmed)
    end

    def logged_out!
      stubs(:current_user).returns(nil)
      stubs(:logged_in?).returns(false)
    end
  end
end

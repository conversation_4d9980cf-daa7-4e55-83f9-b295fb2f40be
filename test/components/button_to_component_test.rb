# frozen_string_literal: true

require 'test_helper'

class ButtonToComponentTest < ViewComponent::TestCase
  def test_default
    text = 'Do it!'
    url = '/do/it'

    render_button_to(url, text:)

    assert_selector(:xpath, "//form[@action='#{url}']")
    assert_selector(:button, text, class: 'btn btn-primary')
  end

  def test_with_confirm
    confirm = 'You really, really sure?'

    render_button_to(data: { confirm: })

    assert_selector(:xpath, "//button[@type='submit'][@data-confirm='#{confirm}']")
  end

  def test_with_extra_classes
    extra_classes = 'foo bar'

    render_button_to(classes: extra_classes)

    assert_selector(:button, class: extra_classes)
  end

  def test_with_form_classes
    form_class = 'form-class'

    render_button_to(form_class:)

    assert_selector(:xpath, "//form[contains(@class,#{form_class})]")
  end

  def test_with_type
    type = 'success'

    render_button_to(type: 'success')

    assert_selector(:button, class: "btn btn-#{type}")
  end

  def test_with_content_block
    content_block = 'This is a content block!'

    render_button_to { content_block }

    assert_text(content_block)
  end

  private

  def render_button_to(url = '/foo/bar', **opts, &block)
    text = 'Do Something' unless block || opts[:text]

    render_inline(ButtonToComponent.new(url, text:, **opts), &block)
  end
end

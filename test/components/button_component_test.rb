# frozen_string_literal: true

require 'test_helper'

class ButtonComponentTest < ViewComponent::TestCase
  def test_basic_button
    render_inline(ButtonComponent.new) do
      'Click me!'
    end

    assert_selector('button.btn.btn-primary[type=button]', text: 'Click me!')
  end

  def test_with_icon
    render_inline(ButtonComponent.new) do |button|
      button.with_icon('exclamation')
      'Click me'
    end

    assert_selector('i.fas.fa-exclamation')
  end

  def test_link
    render_inline(ButtonComponent.new(tag: :a, href: '/foobar')) do
      'I am a link!'
    end

    assert_selector('a.btn.btn-primary[href="/foobar"]', text: 'I am a link!')
  end

  def test_white_variant
    render_inline(ButtonComponent.new(variant: :white)) do
      'In the White Room...'
    end

    assert_selector('.btn-white')
  end

  def test_small
    render_inline(ButtonComponent.new(size: :small)) do
      'Small Button'
    end

    assert_selector('.btn-sm')
  end
end

# frozen_string_literal: true

require 'test_helper'

class CounterComponentTest < ViewComponent::TestCase
  def test_default
    render_inline(CounterComponent.new(1))

    assert_selector('span.badge.text-bg-primary.rounded-pill', text: '1')
  end

  def test_with_type
    render_inline(CounterComponent.new(1, type: :light))

    assert_selector('.text-bg-light')
  end

  def test_with_classes
    render_inline(CounterComponent.new(1, classes: 'test-class'))

    assert_selector('.test-class')
  end
end

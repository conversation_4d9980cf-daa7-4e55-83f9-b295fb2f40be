# frozen_string_literal: true

require 'test_helper'

class BreadcrumbsComponentTest < ViewComponent::TestCase
  def test_default
    render_inline(BreadcrumbsComponent.new) do |crumbs|
      crumbs.with_item(href: '/one', title: 'Back to One') { 'One' }
      crumbs.with_item(href: '/two', title: 'Back to Two') { 'Two' }
      crumbs.with_item(current: true) { 'Three' }
    end

    assert_selector('nav.header-pretitle[aria-label="breadcrumb"]') do
      assert_selector("a[href='/one'][title='Back to One']", text: 'One')
      assert_selector("a[href='/two'][title='Back to Two']", text: 'Two')
      assert_selector("[aria-current='page']", text: 'Three')
    end
  end
end

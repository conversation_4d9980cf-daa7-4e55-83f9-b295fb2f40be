# frozen_string_literal: true

require 'test_helper'

module Surveys
  class RecruitmentStatusLabelComponentTest < ViewComponent::TestCase
    def test_closed
      recruitment = Recruitment.new(recruitable: true, closes_on: 1.day.ago, published: false)

      render_inline(RecruitmentStatusLabelComponent.new(status: recruitment))

      assert_selector 'span.text-danger', text: label_text('closed')
    end

    def test_open_indefinitely
      recruitment = Recruitment.new(recruitable: true, closes_on: nil, published: true)

      render_inline(RecruitmentStatusLabelComponent.new(status: recruitment))

      assert_selector 'span.text-success', text: label_text('open')
    end

    def test_open_with_date
      closes_on = 2.days.from_now

      recruitment = Recruitment.new(recruitable: true, closes_on:, published: true)

      render_inline(RecruitmentStatusLabelComponent.new(status: recruitment))

      assert_selector 'span.text-success',
                      text: label_text('recruiting_until', closes_on: closes_on.strftime('%m/%-d/%y'))
    end

    def label_text(key, **)
      I18n.t("surveys.recruitment_status_label_component.#{key}", **)
    end
  end
end

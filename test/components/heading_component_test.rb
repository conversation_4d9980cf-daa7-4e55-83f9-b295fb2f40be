# frozen_string_literal: true

require 'test_helper'

class HeadingComponentTest < ViewComponent::TestCase
  def test_content
    content = 'Heading'

    render_inline(HeadingComponent.new(tag: :h2).with_content(content))

    assert_text(content)
  end

  def test_h1_tag
    render_inline(HeadingComponent.new(tag: :h1).with_content('Title'))

    assert_selector('h1')
  end

  def test_h6_tag
    render_inline(HeadingComponent.new(tag: :h6).with_content('Title'))

    assert_selector('h6')
  end

  def test_fallback
    render_inline(HeadingComponent.new(tag: :div).with_content('Title'))

    assert_selector(HeadingComponent::FALLBACK_TAG.to_s)
  end
end

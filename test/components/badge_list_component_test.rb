# frozen_string_literal: true

require 'test_helper'

class BadgeListComponentTest < ViewComponent::TestCase
  def test_container
    render_inline(BadgeListComponent.new(tags: %w[tag]))

    container_selector = '//div[contains(@class, "d-flex") ' \
                         'and contains(@class,"flex-wrap") ' \
                         'and contains(@class, "align-items-center") ' \
                         'and contains(@class, "gap-2")]'

    assert_selector(:xpath, container_selector)
  end

  def test_items
    render_inline(BadgeListComponent.new(tags: %w[foo bar]))

    assert_selector('span.badge.text-bg-light', text: 'foo')
    assert_selector('span.badge.text-bg-light', text: 'bar')
  end

  def test_type_injection
    render_inline(BadgeListComponent.new(tags: %w[foo], type: 'info'))

    assert_selector('span.badge.text-bg-info', text: 'foo')
  end
end

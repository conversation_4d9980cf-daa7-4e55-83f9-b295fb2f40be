# frozen_string_literal: true

require 'test_helper'

class CardComponentTest < ViewComponent::TestCase
  def test_basic_card
    title = 'Hello!'
    body = 'I am content'

    render_inline(CardComponent.new) do |card|
      card.with_header { title }
      card.with_body { body }
    end

    assert_selector('div.card div.card-header', text: title)
    assert_selector('div.card div.card-body', text: body)
  end

  def test_component_classes
    classes = %w[foo bar]

    render_inline(CardComponent.new(classes: classes.join(' ')))

    assert_selector(".card#{classes.map { |c| ".#{c}" }.join}")
  end

  def test_header_classes
    classes = %w[foo bar]

    render_inline(CardComponent.new) do |card|
      card.with_header(classes: classes.join(' ')) { '' }
    end

    selectors = classes.map { |c| ".#{c}" }.join

    assert_selector(".card .card-header#{selectors}")
  end

  def test_body_classes
    classes = %w[foo bar]

    render_inline(CardComponent.new) do |card|
      card.with_body(classes: classes.join(' ')) { '' }
    end

    selectors = classes.map { |c| ".#{c}" }.join

    assert_selector(".card .card-body#{selectors}")
  end

  def test_details_card
    render_inline(CardComponent.new(tag: :details)) do |card|
      card.with_header(tag: :summary) { 'Header' }
    end

    assert_selector('details.card summary.card-header')
  end
end

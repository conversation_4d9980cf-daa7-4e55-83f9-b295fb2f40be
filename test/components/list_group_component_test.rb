# frozen_string_literal: true

require 'test_helper'

class ListGroupComponentTest < ViewComponent::TestCase
  def test_basic_list_group
    first_item = 'Item 1'
    second_item = 'Item 2'

    render_inline(ListGroupComponent.new) do |list_group|
      list_group.with_item { first_item }
      list_group.with_item { second_item }
    end

    assert_item(first_item)
    assert_item(second_item)
  end

  def test_list_group_flush_variant
    render_inline(ListGroupComponent.new(variant: :flush)) do |list_group|
      list_group.with_item { 'Item' }
    end

    assert_selector('.list-group.list-group-flush')
  end

  def test_list_group_with_surveys_variant
    render_inline(ListGroupComponent.new(variant: :surveys)) do |list_group|
      list_group.with_item(variant: :survey) { 'Survey' }
    end

    assert_selector('.list-group.list-group-flush.list-group-products.list-group-surveys .list-group-item.list-group-item-product')
  end

  def test_list_group_timeline_variant
    render_inline(ListGroupComponent.new(variant: :timeline)) do |list_group|
      list_group.with_item { 'Item' }
    end

    assert_selector('.list-group.list-group-flush.list-group-activity.my-n3')
  end

  private

  def assert_item(item_text)
    assert_selector('.list-group .list-group-item', text: item_text)
  end
end

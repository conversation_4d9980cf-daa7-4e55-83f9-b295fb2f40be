# frozen_string_literal: true

require 'test_helper'

module Bootstrap
  class DropdownToggleComponentTest < ViewComponent::TestCase
    def test_renders_dropdown_toggle
      render_inline(Bootstrap::DropdownToggleComponent.new) { 'Toggle me' }

      assert_selector 'button.dropdown-toggle', text: 'Toggle me'
    end

    def test_renders_without_caret
      render_inline(Bootstrap::DropdownToggleComponent.new(caret: false)) { 'Toggle me' }

      assert_selector 'button.dropdown-toggle.dropdown-toggle-no-caret', text: 'Toggle me'
    end
  end
end

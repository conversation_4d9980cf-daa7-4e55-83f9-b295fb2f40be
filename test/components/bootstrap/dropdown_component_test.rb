# frozen_string_literal: true

require 'test_helper'

module Bootstrap
  class DropdownComponentTest < ViewComponent::TestCase
    def test_renders_dropdown
      render_inline(Bootstrap::DropdownComponent.new) do |d|
        d.with_toggle { 'Toggle me' }
        d.with_menu do |menu|
          menu.with_item(href: '/foo') { 'Item One' }
          menu.with_item(tag: :button, type: :submit) { 'Item Two' }
        end
      end

      assert_selector '.dropdown' do
        assert_selector 'button.dropdown-toggle', text: 'Toggle me'
        assert_selector '.dropdown-menu' do
          # Find out why assert_link, and attribute selector [href="/one"]
          # do not work even with visible: false
          assert_selector 'a.dropdown-item', text: 'Item One'
          assert_selector 'button[type="submit"].dropdown-item', text: 'Item Two'
        end
      end
    end

    # The menu is left-aligned by default
    # See https://getbootstrap.com/docs/4.6/components/dropdowns/#menu-alignment
    def test_default_menu_alignment
      render_inline(Bootstrap::DropdownComponent.new) do |d|
        d.with_toggle { 'Toggle me' }
        d.with_menu(right_aligned: true) do |menu|
          menu.with_item(href: '/foo') { 'Item One' }
        end
      end

      assert_selector '.dropdown' do
        assert_selector 'button.dropdown-toggle', text: 'Toggle me'
        assert_selector '.dropdown-menu.dropdown-menu-end'
      end
    end

    def test_renders_dropdown_with_divider
      render_inline(Bootstrap::DropdownComponent.new) do |d|
        d.with_toggle { 'Toggle me' }
        d.with_menu do |menu|
          menu.with_item(href: '/foo') { 'Item One' }
          menu.with_item(divider: true)
          menu.with_item(href: '/bar') { 'Item Two' }
        end
      end

      assert_selector '.dropdown' do
        assert_selector 'button.dropdown-toggle', text: 'Toggle me'
        assert_selector '.dropdown-menu' do
          assert_selector 'a.dropdown-item', text: 'Item One'
          assert_selector 'div.dropdown-divider', text: ''
          assert_selector 'a.dropdown-item', text: 'Item Two'
        end
      end
    end

    def test_renders_dropdown_with_header
      render_inline(Bootstrap::DropdownComponent.new) do |d|
        d.with_toggle { 'Toggle me' }
        d.with_menu do |menu|
          menu.with_item(header: true) { 'Header' }
          menu.with_item(href: '/foo') { 'Foo' }
        end
      end

      assert_selector '.dropdown' do
        assert_selector 'button.dropdown-toggle', text: 'Toggle me'
        assert_selector '.dropdown-menu' do
          assert_selector 'h6.dropdown-header', text: 'Header'
          assert_selector 'a.dropdown-item', text: 'Foo'
        end
      end
    end

    def test_renders_menu_content
      render_inline(Bootstrap::DropdownComponent.new) do |d|
        d.with_toggle { 'Toggle me' }
        d.with_menu { 'This is menu content' }
      end

      assert_selector '.dropdown' do
        assert_selector 'button.dropdown-toggle', text: 'Toggle me'
        assert_selector '.dropdown-menu', text: 'This is menu content'
      end
    end
  end
end

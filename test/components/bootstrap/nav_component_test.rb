# frozen_string_literal: true

require 'test_helper'

module Bootstrap
  class NavComponentTest < ViewComponent::TestCase
    def test_base_nav
      render_inline(Bootstrap::NavComponent.new)

      assert_selector '.nav'
    end

    def test_tabs
      render_inline(Bootstrap::NavComponent.new(variant: :tabs))

      assert_selector '.nav.nav-tabs'
    end

    def test_pills
      render_inline(Bootstrap::NavComponent.new(variant: :pills))

      assert_selector '.nav.nav-pills'
    end

    def test_justified
      render_inline(Bootstrap::NavComponent.new(justified: true))

      assert_selector '.nav.nav-justified'
    end

    def test_vertical_supersedes_justified
      render_inline(Bootstrap::NavComponent.new(justified: true, orientation: :vertical))

      assert_selector '.nav.flex-column'
    end

    def test_custom_nav_classes
      render_inline(Bootstrap::NavComponent.new(classes: 'foo bar'))

      assert_selector '.nav.foo.bar'
    end

    def test_base_link
      render_inline(Bootstrap::NavComponent.new) do |nav|
        nav.with_item(href: '/foobar') { 'Link' }
      end

      assert_link 'Link', href: '/foobar', class: 'nav-link'
    end

    def test_active_link
      render_inline(Bootstrap::NavComponent.new) do |nav|
        nav.with_item(href: '/foobar', active: true) { 'Active Link' }
      end

      assert_link 'Active Link', class: 'active'
    end

    def test_disabled_link
      render_inline(Bootstrap::NavComponent.new) do |nav|
        nav.with_item(href: '', disabled: true) { 'Disabled Link' }
      end

      assert_link 'Disabled Link', class: 'disabled'
    end

    def test_vertical
      render_inline(Bootstrap::NavComponent.new(orientation: :vertical))

      assert_selector '.nav.flex-column'
    end
  end
end

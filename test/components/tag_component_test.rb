# frozen_string_literal: true

require 'test_helper'

class TagComponentTest < ViewComponent::TestCase
  def test_tag
    render_inline(TagComponent.new(:div).with_content('Content'))

    assert_selector('div', text: 'Content')
  end

  def test_with_classes
    render_inline(TagComponent.new(:div, classes: 'foo bar').with_content('I have classes'))

    assert_selector('div.foo.bar', text: 'I have classes')
  end

  def test_with_pass_through_attributes
    render_inline(TagComponent.new(:div, title: 'Title').with_content('With Title'))

    assert_selector('div[title=Title]', text: 'With Title')
  end
end

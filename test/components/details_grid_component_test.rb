# frozen_string_literal: true

require 'test_helper'

class DetailsGridComponentTest < ViewComponent::TestCase
  def test_default
    render_inline(DetailsGridComponent.new) do |details_grid|
      details_grid.with_row do |row|
        row.with_label { 'Label' }
        row.with_details { 'Details' }
      end
    end

    assert_selector('div.vstack.gap-1')
    assert_selector('div.row.mx-md-n2')
    assert_selector('div.bg-light.col-12.col-md-4.text-md-end.py-2', text: 'Label')
    assert_selector('div.col-12.col-md-8.mb-2.mb-md-0.py-2', text: 'Details')
  end

  def test_extra_classes_on_root_element
    render_inline(DetailsGridComponent.new(extra_classes: 'foobar')) do |details_grid|
      details_grid.with_row do |row|
        row.with_label { 'Label' }
        row.with_details { 'Details' }
      end
    end

    assert_selector('div.vstack.gap-1.foobar')
  end

  def test_extra_classes_on_label
    render_inline(DetailsGridComponent.new(extra_classes: 'foobar')) do |details_grid|
      details_grid.with_row do |row|
        row.with_label(extra_classes: 'foobar') { 'Label' }
        row.with_details { 'Details' }
      end
    end

    assert_selector('div.bg-light.col-12.col-md-4.text-md-end.py-2.foobar', text: 'Label')
  end

  def test_extra_classes_on_details
    render_inline(DetailsGridComponent.new(extra_classes: 'foobar')) do |details_grid|
      details_grid.with_row do |row|
        row.with_label { 'Label' }
        row.with_details(extra_classes: 'foobar') { 'Details' }
      end
    end

    assert_selector('div.col-12.col-md-8.mb-2.mb-md-0.py-2.foobar', text: 'Details')
  end
end

# frozen_string_literal: true

require 'test_helper'

class AlertComponentTest < ViewComponent::TestCase
  def test_default
    alert_content = 'Hello!'

    render_inline(AlertComponent.new) do
      alert_content
    end

    assert_selector('.alert.alert-primary', text: alert_content)
  end

  def test_with_type
    alert_content = 'I am an alert with a type'

    render_inline(AlertComponent.new(type: 'light')) do
      alert_content
    end

    assert_selector('.alert.alert-light', text: alert_content)
  end
end

# frozen_string_literal: true

require 'test_helper'

class LinkComponentTest < ViewComponent::TestCase
  def test_default
    render_inline(LinkComponent.new('/some/path')) { 'click here' }

    assert_selector 'a[href="/some/path"]', text: 'click here'
  end

  def test_with_icon
    render_inline(LinkComponent.new('/some/path')) do |link|
      link.with_icon('pepper-hot')
      'Spicy!'
    end

    assert_selector 'a[href="/some/path"]' do
      assert_selector 'i.fas.fa-pepper-hot'
      assert_text 'Spicy!'
    end
  end

  def test_with_classes
    render_inline(LinkComponent.new('', classes: 'foobar')) { 'click here' }

    assert_selector '.foobar'
  end

  def test_with_attributes
    render_inline(LinkComponent.new('', title: 'Title')) { 'click here' }

    assert_selector '[title=Title]'
  end
end

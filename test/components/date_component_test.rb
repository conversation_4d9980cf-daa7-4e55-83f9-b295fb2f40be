# frozen_string_literal: true

require 'test_helper'

class DateComponentTest < ViewComponent::TestCase
  def test_today
    assert_friendly_date('Today', Time.zone.today)
  end

  def test_tomorrow
    assert_friendly_date('Tomorrow', Time.zone.tomorrow)
  end

  def test_yesterday
    assert_friendly_date('Yesterday', Time.zone.yesterday)
  end

  def test_after_tomorrow
    date = 2.days.from_now.to_date
    expected = format_date(date)

    assert_friendly_date(expected, date)
  end

  def test_before_yesterday
    date = 2.days.ago.to_date
    expected = format_date(date)

    assert_friendly_date(expected, date)
  end

  private

  def format_date(date)
    date.strftime("%A %B #{date.day.ordinalize}, %Y")
  end

  def assert_friendly_date(expected, date)
    render_inline(DateComponent.new(date))

    assert_content(expected)
  end
end

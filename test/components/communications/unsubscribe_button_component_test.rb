# frozen_string_literal: true

require 'test_helper'

module Communications
  class UnsubscribeButtonComponentTest < ViewComponent::TestCase
    def test_default
      url = '/unsubscribe/me'

      render_inline UnsubscribeButtonComponent.new(url:)

      assert_selector(:css, "form[action='#{url}']") do
        assert_selector(:css, "input[type='hidden'][name='_method'][value='delete']", visible: false)
        assert_selector(:css, 'i.fa-regular.fa-bell-slash')
        assert_selector(:button, 'Unsubscribe')
      end
    end
  end
end

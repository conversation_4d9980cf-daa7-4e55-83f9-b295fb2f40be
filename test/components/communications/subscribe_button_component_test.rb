# frozen_string_literal: true

require 'test_helper'

module Communications
  class SubscribeButtonComponentTest < ViewComponent::TestCase
    def test_default
      url = '/resubscribe/me'

      render_inline SubscribeButtonComponent.new(url:)

      assert_selector(:css, "form[action='#{url}'][method='post']") do
        assert_selector(:css, 'i.fa-regular.fa-bell')
        assert_selector(:button, 'Subscribe')
      end
    end

    def test_custom_button_text
      url = '/resubscribe/me'

      render_inline SubscribeButtonComponent.new(url:, button_text: 'Resubscribe')

      assert_selector(:css, "form[action='#{url}'][method='post']") do
        assert_selector(:css, 'i.fa-regular.fa-bell')
        assert_selector(:button, 'Resubscribe')
      end
    end
  end
end

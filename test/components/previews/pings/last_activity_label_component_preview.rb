# frozen_string_literal: true

require 'faker'

module Pings
  class LastActivityLabelComponentPreview < ViewComponent::Preview
    def published_without_answers
      ping = FactoryBot.create(:ping)

      render(Pings::LastActivityLabelComponent.new(ping))
    end

    def published_with_answers
      ping = FactoryBot.create(:ping)
      FactoryBot.create(:ping_answer, ping:)

      render(Pings::LastActivityLabelComponent.new(ping))
    end

    def default
      ping = FactoryBot.create(:ping, :paid, :draft)

      render(Pings::LastActivityLabelComponent.new(ping))
    end
  end
end

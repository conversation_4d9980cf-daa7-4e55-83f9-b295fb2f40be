# frozen_string_literal: true

class HeadingComponentPreview < ViewComponent::Preview
  def h1
    render(HeadingComponent.new(tag: :h1).with_content('An h1 Heading'))
  end

  def h2
    render(HeadingComponent.new(tag: :h2).with_content('An h2 Heading'))
  end

  def h3
    render(HeadingComponent.new(tag: :h3).with_content('An h3 Heading'))
  end

  def h4
    render(HeadingComponent.new(tag: :h4).with_content('An h4 Heading'))
  end

  def h5
    render(HeadingComponent.new(tag: :h5).with_content('An h5 Heading'))
  end

  def h6
    render(HeadingComponent.new(tag: :h6).with_content('An h6 Heading'))
  end
end

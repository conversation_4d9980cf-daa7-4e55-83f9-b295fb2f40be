# frozen_string_literal: true

module Bootstrap
  class DropdownComponentPreview < ViewComponent::Preview
    # A basic dropdown, with standard bootstrap options
    #
    # @param label text "The text to display on the dropdown's toggle button"
    # @param toggle_variant select { choices: [primary, secondary, outline-primary, outline-secondary, white, link] } "Pick a variant for the toggle"
    # @param right_aligned toggle "Right align the dropdown menu"
    def basic(label: 'Click me', toggle_variant: :primary, right_aligned: false)
      render(Bootstrap::DropdownComponent.new) do |dropdown|
        dropdown.with_toggle(variant: toggle_variant) { label }
        dropdown.with_menu(right_aligned:) do |menu|
          menu.with_item(href: '#') { 'One' }
          menu.with_item(tag: :button, type: :button) { 'Two' }
        end
      end
    end

    # A dropdown, with a header and divider
    def with_extra_content
      render(Bootstrap::DropdownComponent.new) do |dropdown|
        dropdown.with_toggle { 'Open Sesame!' }
        dropdown.with_menu do |menu|
          menu.with_item(href: '#') { 'One' }
          menu.with_item(divider: true)
          menu.with_item(header: true) { 'Extra Items' }
          menu.with_item(href: '#') { 'Two' }
        end
      end
    end

    # A dropdown, with menu content, instead of items
    def with_menu_content
      render(Bootstrap::DropdownComponent.new) do |dropdown|
        dropdown.with_toggle { 'Open Sesame!' }
        dropdown.with_menu { 'Look ma! No items.' }
      end
    end
  end
end

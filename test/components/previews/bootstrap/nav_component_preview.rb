# frozen_string_literal: true

module Bootstrap
  class NavComponentPreview < ViewComponent::Preview
    # A base nav component
    #
    # @param orientation select { choices: [horizontal, vertical] }
    # @param variant select { choices: [default, pills, tabs] }
    # @param justified "Only works with horizontal orientations" toggle
    def default(justified: false, orientation: 'horizontal', variant: 'default')
      render(Bootstrap::NavComponent.new(justified:, orientation:, variant:)) do |nav|
        nav.with_item(href: '/active', active: true) { 'Active' }
        nav.with_item(href: '/link') { 'Link' }
        nav.with_item(href: '/link') { 'Link' }
        nav.with_item(href: '/disabled', disabled: true) { 'Disabled' }
      end
    end

    # A tabs nav component
    def tabs
      render(Bootstrap::NavComponent.new(variant: :tabs)) do |nav|
        nav.with_item(href: '/active', active: true) { 'Active' }
        nav.with_item(href: '/one') { 'One' }
        nav.with_item(href: '/two') { 'Two' }
        nav.with_item(href: '/disabled', disabled: true) { 'Disabled' }
      end
    end
  end
end

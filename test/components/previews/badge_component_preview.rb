# frozen_string_literal: true

class BadgeComponentPreview < ViewComponent::Preview
  # Default badge
  # ---------------
  # Adds an informational badge, it uses Bootstrap's `alert-primary` as the default type
  def default
    render(BadgeComponent.new(text: 'A Badge'))
  end

  # Badge with a type
  # ---------------
  # Types correspond to Bootstraps contextual colors: primary, secondary, success, danger, light, etc.
  def with_type
    render(BadgeComponent.new(text: 'Badge with Type', type: 'info'))
  end

  # Badge with content block
  # ---------------
  # Use this to add more complex content to Badges like icons, or additional markup
  def with_content_block
    render(BadgeComponent.new(type: 'success')) do
      'I am a content block'
    end
  end
end

# frozen_string_literal: true

class BadgeListComponentPreview < ViewComponent::Preview
  # Without highlights
  # ---------------
  # This shows only regular badges, without any highlighted
  def without_highlights
    render(BadgeListComponent.new(tags: %w[Apple Banana Cherry Watermelon]))
  end

  # Without highlights
  # ---------------
  # Shows an example of a highlighted badges. Highlighted badges are displayed first
  def with_highlights
    render(BadgeListComponent.new(tags: %w[Apple Cherry], highlights: %w[Banana Watermelon]))
  end
end

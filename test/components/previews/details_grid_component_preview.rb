# frozen_string_literal: true

class DetailsGridComponentPreview < ViewComponent::Preview
  def default
    render(DetailsGridComponent.new) do |details_grid|
      details_grid.with_row do |row|
        row.with_label { 'First Label' }
        row.with_details { 'These are the details for the first label' }
      end
      details_grid.with_row do |row|
        row.with_label { 'Second Label' }
        row.with_details { 'These are the details for the second label, which are slightly longer which should wrap on most screens.' }
      end
      details_grid.with_row do |row|
        row.with_label { 'Third Label' }
        row.with_details { 'These are the details for the third label' }
      end
    end
  end
end

# frozen_string_literal: true

class ListGroupComponentPreview < ViewComponent::Preview
  def default
    render(ListGroupComponent.new) do |list_group|
      list_group.with_item { 'First Item' }
      list_group.with_item { 'Second Item' }
      list_group.with_item { 'Third Item' }
    end
  end

  def flush_variant
    render(ListGroupComponent.new(variant: :flush)) do |list_group|
      list_group.with_item { 'First Item' }
      list_group.with_item { 'Second Item' }
      list_group.with_item { 'Third Item' }
    end
  end

  # This one needs more work. We need a timeline item variant, or sub-component
  # that takes an avatar, and does the additional needed formatting.
  def timeline_variant
    render(ListGroupComponent.new(variant: :timeline)) do |list_group|
      list_group.with_item { 'Once upon a time...' }
      list_group.with_item { '...something happened' }
      list_group.with_item { 'The End.' }
    end
  end

  def link_item_variant
    render(ListGroupComponent.new) do |list_group|
      list_group.with_item(variant: :link_item) { 'First Item' }
      list_group.with_item(variant: :link_item) { 'Second Item' }
      list_group.with_item(variant: :link_item) { 'Third Item' }
    end
  end
end

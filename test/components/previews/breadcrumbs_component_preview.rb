# frozen_string_literal: true

class BreadcrumbsComponentPreview < ViewComponent::Preview
  # Default breadcrumbs
  # ---------------
  # Basic breadcrumbs
  def default
    render(BreadcrumbsComponent.new) do |crumbs|
      crumbs.with_item(href: '/one', title: 'Back to One') { 'One' }
      crumbs.with_item(href: '/two', title: 'Back to Two') { 'Two' }
      crumbs.with_item(current: true) { 'Three' }
    end
  end
end

# frozen_string_literal: true

class AlertComponentPreview < ViewComponent::Preview
  # Default alert
  # ---------------
  # This is intended to call something to a user's attention, it uses Bootstrap's `alert-primary` as the default type
  def default
    render(AlertComponent.new) do
      'This is the default alert!'
    end
  end

  # Alert with a type
  # ---------------
  # Types correspond to Bootstraps contextual colors: primary, secondary, success, danger, light, etc.
  def with_type
    render(AlertComponent.new(type: 'light')) do
      'This is an alert with a type!'
    end
  end
end

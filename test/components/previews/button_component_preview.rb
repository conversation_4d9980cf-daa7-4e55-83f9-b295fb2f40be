# frozen_string_literal: true

class ButtonComponentPreview < ViewComponent::Preview
  # Default button
  # ------------------
  # This is basic button with no extra styling
  #
  # @param text
  # @param variant select [primary, link, outline-primary, secondary, white]
  def default(text: 'Click me!', variant: :primary)
    render(ButtonComponent.new(variant:)) do
      text
    end
  end

  def with_icon
    render(ButtonComponent.new) do |button|
      button.with_icon('exclamation')
      'Click me'
    end
  end
end

# frozen_string_literal: true

class CardComponentPreview < ViewComponent::Preview
  # @!group Sections
  # Body only
  # ---------------
  # A basic card with only a body
  def body_only
    render CardComponent.new do |card|
      card.with_body do
        'I am the card body'
      end
    end
  end

  # Header and body
  # ---------------
  # A basic card with a header and body
  def header_and_body
    render CardComponent.new do |card|
      card.with_header do
        'Header Text'
      end

      card.with_body do
        'Body Text'
      end
    end
  end
  # @!endgroup

  # @!group Customization
  # With extra HTML classes.
  # --------------------------
  # The component itself, or any section can take extra classes. Classes should be space-separated strings
  def with_html_classes
    render CardComponent.new do |card|
      card.with_body(classes: 'text-success') do
        'A body with an extra classs set'
      end
    end
  end
  # @!endgroup
end

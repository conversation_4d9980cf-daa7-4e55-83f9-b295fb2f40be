# frozen_string_literal: true

class BlankSlateComponentPreview < ViewComponent::Preview
  # @param size select [default, compact]
  def blank_slate(size: 'default')
    render(BlankSlateComponent.new(size:)) do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('There is nothing to see here!')
    end
  end

  def basic
    render BlankSlateComponent.new do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('There is nothing to see here!')
    end
  end

  def with_icon
    render BlankSlateComponent.new do |blank_slate|
      blank_slate.with_icon('ghost')
      blank_slate.with_heading(tag: :h2).with_content('Boo!')
    end
  end

  def with_description
    render BlankSlateComponent.new do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('There is no content')
      blank_slate.with_description do
        'Create some!'
      end
    end
  end

  def with_primary_action
    render BlankSlateComponent.new do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('You need something to click.')
      blank_slate.with_primary_action(href: '/foobar').with_content('Click me!')
    end
  end

  def with_everything
    render BlankSlateComponent.new do |blank_slate|
      blank_slate.with_icon('sink')
      blank_slate.with_heading(tag: :h2).with_content("I've got everything.")
      blank_slate.with_description do
        'This has every slot type.'
      end
      blank_slate.with_primary_action(href: '/foobar').with_content('Click me!')
    end
  end
end

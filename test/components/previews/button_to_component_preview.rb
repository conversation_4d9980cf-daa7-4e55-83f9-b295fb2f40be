# frozen_string_literal: true

class ButtonToComponentPreview < ViewComponent::Preview
  def default
    render_button_to
  end

  def with_confirm
    render_button_to(data: { confirm: 'Are you sure?' })
  end

  def with_type
    render_button_to(type: 'success')
  end

  def with_content_block
    render(ButtonToComponent.new('/somewhere')) do
      '<i class="fas fa-hand-pointer"></i> Click me!'
    end
  end

  private

  def render_button_to(url = '/foobar', text: 'Submit', **)
    render(ButtonToComponent.new(url, text:, **))
  end
end

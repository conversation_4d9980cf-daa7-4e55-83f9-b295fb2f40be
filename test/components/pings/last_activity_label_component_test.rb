# frozen_string_literal: true

require 'test_helper'

module Pings
  class LastActivityLabelComponentTest < ViewComponent::TestCase
    def test_published_without_answers
      ping = FactoryBot.create(:ping)

      render_inline(Pings::LastActivityLabelComponent.new(ping))

      assert_text "Posted #{ping.last_active_at.strftime('%m/%d/%y')}"
    end

    def test_published_with_answers
      ping = FactoryBot.create(:ping)
      FactoryBot.create(:ping_answer, ping:)

      render_inline(Pings::LastActivityLabelComponent.new(ping))

      assert_text "Answered #{ping.last_active_at.strftime('%m/%d/%y')}"
    end

    def test_draft
      ping = FactoryBot.create(:ping, :paid, :draft)

      render_inline(Pings::LastActivityLabelComponent.new(ping))

      assert_text 'Draft'
    end
  end
end

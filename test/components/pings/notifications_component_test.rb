# frozen_string_literal: true

require 'test_helper'

module Pings
  class NotificationsComponentTest < ViewComponent::TestCase
    test 'does not render without a user' do
      ping = FactoryBot.create(:ping)
      user = nil

      assert_equal('', render_inline(Pings::NotificationsComponent.new(ping:, user:)).to_html)
    end

    test 'unsubscribed user for paid Ping' do
      ping = FactoryBot.create(:ping, :paid, :published)
      user = FactoryBot.create(:user)

      render_inline(Pings::NotificationsComponent.new(ping:, user:))

      assert_selector(:xpath, "//form[@action='#{ping_subscribe_path(ping)}']")
      assert_button('Subscribe')
      assert_selector('.text-sm', text: 'Subscribe to receive notifications when voting is open, or a winner has been selected.')
    end

    test 'unsubscribed user for public Ping' do
      ping = FactoryBot.create(:ping, :published)
      user = FactoryBot.create(:user)

      render_inline(Pings::NotificationsComponent.new(ping:, user:))

      assert_selector(:xpath, "//form[@action='#{ping_subscribe_path(ping)}']")
      assert_button('Subscribe')
      assert_selector('.text-sm', text: 'Subscribe to receive notifications when new answers are posted, or when an answer is accepted.')
    end

    test 'subscribed to Ping' do
      ping = FactoryBot.create(:ping, :published)
      user = FactoryBot.create(:user, :confirmed)
      ping.subscribe(user)

      render_inline(Pings::NotificationsComponent.new(ping:, user:))

      assert_selector(:xpath, "//form[@action='#{ping_unsubscribe_path(ping)}']")
      assert_button('Unsubscribe')
      assert_selector('.text-sm', text: 'You are receiving notifications because you subscribed.')
    end

    test 'answered Ping' do
      user = FactoryBot.create(:user, :confirmed, :with_display_name)
      answer = FactoryBot.create(:ping_answer, author: user)
      ping = answer.ping
      ping.subscribe(user)

      render_inline(Pings::NotificationsComponent.new(ping:, user:))

      assert_selector(:xpath, "//form[@action='#{ping_unsubscribe_path(ping)}']")
      assert_button('Unsubscribe')
      assert_selector('.text-sm', text: 'You are receiving notifications because you answered this Ping.')
    end

    test 'posted Ping' do
      user = FactoryBot.create(:user, :confirmed, :with_display_name)
      ping = FactoryBot.create(:ping, author: user)
      ping.subscribe(user)

      render_inline(Pings::NotificationsComponent.new(ping:, user:))

      assert_selector(:xpath, "//form[@action='#{ping_unsubscribe_path(ping)}']")
      assert_button('Unsubscribe')
      assert_selector('.text-sm', text: 'You are receiving notifications because you posted this Ping.')
    end

    test 'subscribed and unconfirmed users get a warning' do
      ping = FactoryBot.create(:ping, :published)
      user = FactoryBot.create(:user, :unconfirmed)
      ping.subscribe(user)

      render_inline(Pings::NotificationsComponent.new(ping:, user:))

      assert_selector(:xpath, "//form[@action='/confirmations/resend']")
      assert_button('Resend confirmation')
      assert_selector('.text-sm', text: 'You are subscribed, but you will not receive notifications until you confirm your email address.')
    end

    private

    def ping_subscribe_path(ping)
      "/pings/#{ping.id}/subscribe"
    end

    def ping_unsubscribe_path(ping)
      "/pings/#{ping.id}/unsubscribe"
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Pings
  class EditWindowTooltipComponentTest < ViewComponent::TestCase
    def test_editable_ping
      duration = 30
      starts_at = Time.current
      ends_at = duration.minutes.from_now
      window = EditWindow.new(starts_at, ends_at)

      render_inline(Pings::EditWindowTooltipComponent.new(window:))

      assert_selector(:xpath, "//span[@title='This ping is editable for #{duration} minutes']")
      assert_selector('i.fas.fa-question-circle')
    end

    def test_uneditable_ping
      window = EditWindow.new(10.minutes.ago, 1.second.ago)

      render_inline(Pings::EditWindowTooltipComponent.new(window:))

      assert_no_selector('i.fas.fa-question-circle')
    end
  end
end

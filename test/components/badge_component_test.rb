# frozen_string_literal: true

require 'test_helper'

class BadgeComponentTest < ViewComponent::TestCase
  def test_default
    text = 'I am a badge'

    render_inline(BadgeComponent.new(text:))

    assert_selector '.badge.text-bg-primary', text:
  end

  def test_with_type
    text = 'With Type'

    render_inline(BadgeComponent.new(text:, type: 'info'))

    assert_selector '.badge.text-bg-info', text:
  end

  def test_with_content_block
    content_text = 'Content block'

    render_inline(BadgeComponent.new) { content_text }

    assert_text content_text
  end

  def test_with_classes
    render_inline(BadgeComponent.new(classes: 'foobar')) { 'Foo' }

    assert_selector '.foobar'
  end

  def test_with_url
    text = 'Link Badge'
    url = '/example'
    render_inline(BadgeComponent.new(text: text, url: url))

    assert_selector 'a.badge.text-decoration-none.text-bg-primary[href="/example"]', text: text
  end
end

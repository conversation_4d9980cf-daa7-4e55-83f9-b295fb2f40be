# frozen_string_literal: true

require 'test_helper'

module Admin
  class BreadcrumbComponentTest < ViewComponent::TestCase
    def test_breadcrumb
      render_inline(Admin::BreadcrumbComponent.new) do |crumbs|
        crumbs.with_crumb(href: '/path') { 'Parent' }
        crumbs.with_crumb(active: true) { 'Active' }
      end

      assert_selector('.breadcrumb-item', count: 2)
      assert_selector('.breadcrumb-item.active', count: 1)
    end
  end
end

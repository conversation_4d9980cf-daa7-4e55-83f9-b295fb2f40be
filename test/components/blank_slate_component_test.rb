# frozen_string_literal: true

require 'test_helper'

class BlankSlateComponentTest < ViewComponent::TestCase
  def test_heading_only
    render_inline(BlankSlateComponent.new) do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('Title')
    end

    assert_selector('div.blank-slate')
    assert_selector('h2', text: 'Title')
  end

  def test_icon
    render_inline(BlankSlateComponent.new) do |blank_slate|
      blank_slate.with_icon('biking')
      blank_slate.with_heading(tag: :h2).with_content('Title')
    end

    assert_selector('i.fa-biking')
  end

  def test_description
    render_inline(BlankSlateComponent.new) do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('Title')
      blank_slate.with_description do
        'Description'
      end
    end

    assert_selector('div', text: 'Description')
  end

  def test_primary_action
    render_inline(BlankSlateComponent.new) do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('Title')
      blank_slate.with_primary_action(href: '/foobar') do
        'Do it!'
      end
    end

    assert_selector('a[href="/foobar"]', text: 'Do it!')
  end

  def test_default_size
    render_inline(BlankSlateComponent.new) do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('Title')
    end

    assert_selector('div.blank-slate.p-5')
  end

  def test_compact
    render_inline(BlankSlateComponent.new(size: 'compact')) do |blank_slate|
      blank_slate.with_heading(tag: :h2).with_content('Title')
    end

    assert_selector('div.blank-slate.py-3')
  end
end

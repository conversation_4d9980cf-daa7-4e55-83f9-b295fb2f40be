# frozen_string_literal: true

require 'test_helper'
require 'lib/download_helper'
require 'lib/scipis/engagement_info_test_helper'
require 'lib/slim_select_helper'

class ApplicationSystemTestCase < ActionDispatch::SystemTestCase
  include SlimSelectHelper

  def self.driver_options
    { timeout: ENV.fetch('SELENIUM_TIMEOUT', 120).to_i }
  end

  driven_by(:selenium, using: :headless_chrome, options: driver_options) do |driver_option|
    download_pref = {
      prompt_for_download: false,
      default_directory: DownloadHelper::PATH.to_s
    }

    driver_option.add_preference(:download, download_pref)
  end

  def any_user
    FactoryBot.create(:expert)
  end
  alias any_expert any_user

  def assert_alert(text:, type: nil)
    selector = '.alert'
    selector = "#{selector}.alert-#{type}" if type

    assert_selector(selector, text:)
  end

  def assert_downloaded(filename)
    contents = DownloadHelper.download_content(filename)
    if block_given?
      yield(contents)
    else
      assert_not_nil contents
    end
  rescue Timeout::<PERSON>rror
    flunk("The file '#{filename}' was not downloaded")
  ensure
    DownloadHelper.clear_download(filename)
  end

  def assert_field_required(label, message: "can't be blank")
    within_form_group(label:) do
      assert_text message
    end
  end

  def assert_no_url_helper(url_helper, message: nil)
    assert_not Rails.application.routes.url_helpers.respond_to?(url_helper.to_sym), message
  end

  # password is from factory
  def submit_login_form(email, password: 'secret123')
    within('.login-form') do
      fill_in 'email address', with: email
      fill_in 'password', with: password

      click_button('Log In')
    end
  end

  def login_as(user, password: 'secret123', after_login_path: nil)
    visit login_path

    submit_login_form(user.email, password:)

    redirect_path = if after_login_path.present?
                      after_login_path
                    elsif user.internal?
                      admin_root_path
                    else
                      dashboard_path
                    end

    sleep(0.1)

    assert_current_path redirect_path
  end

  def within_form_group(label: nil, &)
    match = xpath_contains_class('form-group')

    match = "#{match} and .//label[contains(text(), '#{label}')]" if label

    within(:xpath, "//div[#{match}]", &)
  end

  def xpath_contains_class(class_name)
    "contains(concat(' ',normalize-space(@class),' '),' #{class_name} ')"
  end

  private

  def click_off
    page.execute_script('document.querySelector("body").click();')
  end
end

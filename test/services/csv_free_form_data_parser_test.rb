# frozen_string_literal: true

require 'minitest/autorun'

require_relative '../../app/services/csv_free_form_data_parser'

class CsvFreeFormDataParserTest < Minitest::Test
  def test_parse_csv_data
    file = Tempfile.new('data_file.csv')

    begin
      CSV.open(file, 'wb') do |csv|
        csv << ['x label', 'y label']
        csv << ['label 1', 34]
        csv << ['label 2', 42]
      end

      parser = CsvFreeFormDataParser.new
      dataset = parser.parse(file)

      assert_equal('x label', dataset.x_label)
      assert_equal('y label', dataset.y_label)
      assert_equal({ 'label 1' => 34, 'label 2' => 42 }, dataset.data)
    ensure
      file.close
      file.unlink # deletes the temp file
    end
  end
end

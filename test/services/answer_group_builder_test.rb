# frozen_string_literal: true

require 'test_helper'

class AnswerGroupFormTest < ActiveSupport::TestCase
  test 'save new final submission' do
    user = create(:expert)
    survey = create(:survey, :published)
    radio = create(:radio, survey:)
    radio_selected_answer = radio.answer_choices.first

    checkbox = create(:checkbox, survey:)
    checkbox_selected_answers = checkbox.answer_choices[0..1]

    structure = build(:grid_structure,
                      input_type: 'Radio',
                      first_cell_text: '???',
                      row_headers: "a\r\nbee\r\ncee",
                      column_headers: "one\r\ntwo")
    grid = create(:grid_question,
                  survey:,
                  grid_structure: structure)

    params = {
      question_group: survey,
      user:,
      params: {
        radio.id.to_s => {
          answer_choice_ids: [radio_selected_answer.id.to_s]
        },
        checkbox.id.to_s => {
          answer_choice_ids: ['0', checkbox_selected_answers[0].id.to_s, '0', checkbox_selected_answers[1].id.to_s, '0']
        },
        grid.id.to_s => {
          answer_text_on_1st_row: '0-0',
          answer_text_on_2nd_row: '1-1',
          answer_text_on_3rd_row: '2-1'
        }
      }
    }

    builder = AnswerGroupForm.new(params)
    builder.save!

    answer_group = builder.answer_group
    assert answer_group.submitted?, 'should be submitted'

    radio_answer = answer_group.answers.find_by(question_id: radio.id)
    assert_equal [radio_selected_answer], radio_answer.answer_choices

    checkbox_answer = answer_group.answers.find_by(question_id: checkbox.id)
    assert_equal checkbox_selected_answers, checkbox_answer.answer_choices

    grid_answer = answer_group.answers.find_by(question_id: grid.id)
    assert_equal '0-0', grid_answer.answer_text_on_1st_row
    assert_equal '1-1', grid_answer.answer_text_on_2nd_row
    assert_equal '2-1', grid_answer.answer_text_on_3rd_row
  end

  test 'save new draft submission' do
    user = create(:expert)
    survey = create(:survey, :published)
    radio = create(:radio, survey:)
    radio_answer_choice = radio.answer_choices.first

    checkbox = create(:checkbox, survey:)

    params = {
      question_group: survey,
      user:,
      params: {
        radio.id.to_s => {
          answer_choice_ids: [radio_answer_choice.id.to_s]
        }
      }
    }

    builder = AnswerGroupForm.new(params)
    builder.save_progress

    answer_group = builder.answer_group
    assert answer_group.draft?, 'should be a draft'

    radio_answer = answer_group.answers.find_by(question_id: radio.id)
    assert_equal [radio_answer_choice], radio_answer.answer_choices

    checkbox_answer = answer_group.answers.find_by(question_id: checkbox.id)
    assert_equal checkbox_answer.selected_choices, []
  end

  test 'update answer group' do
    answer_group = create(:submission)
    survey = answer_group.survey
    user = answer_group.user

    radio = create(:radio, survey:)

    original_answer_choice = radio.answer_choices[0]
    new_answer_choice = radio.answer_choices[1]

    create(:answer,
           answer_group:,
           question: radio,
           answer_choices: [original_answer_choice])

    params = {
      question_group: survey,
      user:,
      answer_group:,
      params: {
        radio.id.to_s => {
          answer_choice_ids: [new_answer_choice.id.to_s]
        }
      }
    }

    builder = AnswerGroupForm.new(params)
    builder.save

    radio_answer = answer_group.answers.find_by(question_id: radio.id)
    assert_equal [new_answer_choice], radio_answer.answer_choices
  end
end

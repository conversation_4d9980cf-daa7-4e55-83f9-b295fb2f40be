# frozen_string_literal: true

require 'test_helper'

module Pings
  class NotificationMailerTest < ActionMailer::TestCase
    test 'voting open to subscriber' do
      @ping = FactoryBot.create(:ping, :paid, :published)
      subscriber = FactoryBot.create(:user)
      @subscription = @ping.subscriptions.create!(subscriber:)

      create_notification!(:voting_open)

      assert_voting_open_subject
      assert_sent_to_subscriber
      assert_default_from
      assert_voting_open_call_to_action
      assert_subscribed_reason
      assert_subscription_change_message
    end

    test 'voting open to answerer' do
      @ping = FactoryBot.create(:ping, :paid, :published)
      answer_author = FactoryBot.create(:user, :confirmed)
      answer_author.profile.update!(display_name: Faker::Internet.username)
      @ping.answers.create!(author: answer_author, content: '<p>Foo</p>')
      @subscription = @ping.subscriptions.create!(subscriber: answer_author)

      create_notification!(:voting_open)

      assert_voting_open_subject
      assert_sent_to_subscriber
      assert_default_from
      assert_voting_open_call_to_action
      assert_answered_reason
      assert_subscription_change_message
    end

    test 'answer accepted notification to subscriber' do
      @ping = FactoryBot.create(:ping, :paid, :answer_accepted)
      subscriber = FactoryBot.create(:user)
      @subscription = @ping.subscriptions.create!(subscriber:)

      create_notification!(:answer_accepted)

      assert_answer_accepted_subject
      assert_sent_to_subscriber
      assert_default_from
      assert_answer_accepted_call_to_action
      assert_subscribed_reason
      assert_subscription_change_message
    end

    test 'answer accepted notification to answerer' do
      @ping = FactoryBot.create(:ping, :paid, :answer_accepted)
      answer_author = FactoryBot.create(:user, :confirmed)
      answer_author.profile.update!(display_name: Faker::Internet.username)
      @ping.answers.create!(author: answer_author, content: '<p>Foo</p>')
      @subscription = @ping.subscriptions.create!(subscriber: answer_author)

      create_notification!(:answer_accepted)

      assert_answer_accepted_subject
      assert_sent_to_subscriber
      assert_default_from
      assert_answer_accepted_call_to_action
      assert_answered_reason
      assert_subscription_change_message
    end

    private

    def assert_answer_accepted_subject
      assert_equal "An Answer Has Been Accepted for #{@ping.title}", @mail.subject
    end

    def assert_answer_accepted_call_to_action
      title = @ping.title

      cta = "An answer has been accepted for <span style=\"font-style: italic; font-weight: bold;\">#{title}</span>"

      assert_encoded_email_text(cta)
    end

    def assert_answered_reason
      assert_subscription_reason(action: 'answered')
    end

    def assert_default_from
      assert_equal ['<EMAIL>'], @mail.from
    end

    def assert_encoded_email_text(expected)
      assert_match expected, @mail.html_part.body.to_s
    end

    def assert_sent_to_subscriber
      assert_equal [@subscription.subscriber_email], @mail.to
    end

    def assert_subscribed_reason
      assert_subscription_reason(action: 'subscribed to')
    end

    def assert_subscription_change_message
      subscription_url = pings_subscription_url(token: @subscription.token, **default_url_options)

      assert_encoded_email_text "Change your <a href=\"#{subscription_url}\">notification preferences</a> for this Ping"
    end

    def assert_subscription_reason(action:)
      assert_encoded_email_text "You are receiving this email because you #{action} this Ping"
    end

    def assert_voting_open_call_to_action
      title = @ping.title

      cta = "Voting is now open for <span style=\"font-style: italic; font-weight: bold;\">#{title}</span>"

      assert_encoded_email_text(cta)
    end

    def assert_voting_open_subject
      assert_equal "Voting is open for #{@ping.title}", @mail.subject
    end

    def create_notification!(notification)
      @mail = mailer.public_send(notification, @subscription)
    end

    def mailer
      self.class.name.gsub(/Test$/, '').constantize
    end
  end
end

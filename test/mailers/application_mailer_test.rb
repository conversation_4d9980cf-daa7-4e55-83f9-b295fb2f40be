# frozen_string_literal: true

require 'test_helper'

class ApplicationMailerTest < ActionMailer::TestCase
  test 'salutation user with first_name only' do
    user = FactoryBot.create(:expert)

    am = ApplicationMailer.new

    salutation = am.salutation(user)

    assert_match salutation, user.first_name
  end

  test 'salutation with blank name' do
    user = FactoryBot.create(:expert, first_name: nil, last_name: nil)

    am = ApplicationMailer.new

    salutation = am.salutation(user)

    assert_match salutation, 'Dear Colleague'
  end

  test 'salutation for doctor' do
    expert = expert_with_doctorate

    am = ApplicationMailer.new

    salutation = am.salutation(expert)

    assert_match salutation, "Dr. #{expert.last_name.strip}"
  end

  private

  def expert_with_doctorate
    expert = FactoryBot.create(:expert)

    doctorate_degree_type = FactoryBot.create(:degree_type, :doctorate)

    FactoryBot.create(:degree, expert:, degree_type: doctorate_degree_type)

    expert
  end
end

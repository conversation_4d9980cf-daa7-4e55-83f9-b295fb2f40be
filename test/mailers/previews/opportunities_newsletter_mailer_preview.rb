# frozen_string_literal: true

class OpportunitiesNewsletterMailerPreview < ActionMailer::Preview
  def default
    newsletter = OpportunitiesNewsletter.new(pings:, relevant_expertise:, scipolls:, scipis:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  # Set the recipient to an applicant of a scipi with the most applicants (big net) if possible
  def with_recruiting_and_applied_scipis
    scipis = QuestionGroup.featured_scipis.recruiting.limit(4)

    if scipis.any?
      scipi_with_most_applicants = scipis.max_by { |scipi| scipi.applicants.count }
      applicant_ids = scipi_with_most_applicants.applicants.pluck(:user_id) || []

      subscribing_applicant = list.active_subscriptions.where(subscriber_id: applicant_ids).order('RANDOM()').first

      recipient = subscribing_applicant.subscriber if subscribing_applicant.present?
    end

    newsletter = OpportunitiesNewsletter.new(pings:, relevant_expertise:, scipis:, scipolls:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  # One untaken prefill, one untaken regular, and one taken
  def with_all_scipoll_flavors
    all_scipolls = QuestionGroup.featured_scipolls

    scipoll_with_prefill = all_scipolls.find(&:supports_prefill?)

    scipolls_without_prefill = all_scipolls.reject(&:supports_prefill?)

    other_active_scipoll = all_scipolls
                           .where.not(id: scipoll_with_prefill.id)
                           .joins(:answer_groups)
                           .group('question_groups.id')
                           .having('count(answer_groups.id) > 0')
                           .order('COUNT(answer_groups.id) DESC')
                           .first

    # user ids who did not take the prefill scipoll
    viable_submitter_ids = other_active_scipoll.submitters.map(&:id) - scipoll_with_prefill.submitters.map(&:id)

    # find a user from that collection with a subscription
    subscribing_submitter = list.active_subscriptions.where(subscriber_id: viable_submitter_ids).order('RANDOM()').first

    recipient = subscribing_submitter.subscriber if subscribing_submitter.present?

    # find a non-prefill untaken scipoll for the given recepient
    other_untaken_scipoll = scipolls_without_prefill.find { |scipoll| scipoll.submitters.exclude?(recipient) }

    scipolls = []
    scipolls << scipoll_with_prefill
    scipolls << other_active_scipoll
    scipolls << other_untaken_scipoll

    newsletter = OpportunitiesNewsletter.new(pings:, relevant_expertise:, scipolls:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  def with_all_ping_flavors
    ping_answerers = Pings::Answer.pluck(:author_id)
    ping_voters = Pings::Vote.pluck(:voter_id)

    full_ping_participants = (ping_answerers & ping_voters).uniq

    subscribing_participants = list.active_subscriptions.where(subscriber_id: full_ping_participants)

    subscribing_participant = subscribing_participants.order('RANDOM()').first

    recipient = subscribing_participant.subscriber if subscribing_participant.present?

    pings = all_ping_flavors(recipient)

    newsletter = OpportunitiesNewsletter.new(pings:, relevant_expertise:, scipis: [], scipolls: [], announcements: [])

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  def with_incomplete_profile_alert
    profile_alerts = [I18n.t('alerts.profiles.profile_incomplete_html', url: Faker::Internet.url)]

    newsletter = OpportunitiesNewsletter.new(pings:, profile_alerts:, relevant_expertise:, scipolls:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  def with_stale_cv_alert
    profile_alerts = [I18n.t('alerts.profiles.cv_stale_html', cv_date: 2.years.ago.strftime('%B %Y'), url: Faker::Internet.url)]

    newsletter = OpportunitiesNewsletter.new(pings:, profile_alerts:, relevant_expertise:, scipolls:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  def with_profile_missing_cv_alert
    profile_alerts = [I18n.t('alerts.profiles.cv_missing_html', url: Faker::Internet.url)]

    newsletter = OpportunitiesNewsletter.new(pings:, profile_alerts:, relevant_expertise:, scipolls:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  def with_announcements
    newsletter = OpportunitiesNewsletter.new(pings:, relevant_expertise:, scipolls:, announcements:)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  def with_everything
    pings = pings(expertises: relevant_expertise)

    newsletter = OpportunitiesNewsletter.new(pings:, relevant_expertise:, scipis:, scipolls:, announcements: announcement)

    OpportunitiesNewsletterMailer.opportunities(recipient, list, any_subscription, newsletter)
  end

  private

  # rubocop:disable Metrics/AbcSize
  def all_ping_flavors(recipient)
    return pings if recipient.blank?

    available_pings = Ping.published.visible.featureable

    answered_ping = available_pings.answered_by(recipient).order('RANDOM()').first
    voted_ping = available_pings.voted_on_by(recipient).order('RANDOM()').first
    voting_ping = available_pings.paid.voting_open.where.not(id: voted_ping.id).order('RANDOM()').first
    answering_ping = available_pings.paid.answering_open.where.not(id: answered_ping.id).order('RANDOM()').first
    complete_ping = available_pings.paid.with_accepted_answers.order('RANDOM()').first
    promoted_public_ping = available_pings.promotable.unpaid.order('RANDOM()').first
    public_ping = available_pings.unpaid.where.not(id: [promoted_public_ping.id, answering_ping.id, voted_ping.id]).order('RANDOM()').first

    pings = []
    pings << complete_ping
    pings << answered_ping
    pings << voted_ping
    pings << voting_ping
    pings << answering_ping
    pings << promoted_public_ping
    pings << public_ping

    pings.uniq(&:id)
  end
  # rubocop:enable Metrics/AbcSize

  def announcement
    [announcements.first]
  end

  # Default collection should use same scope as OpportunitiesNewsletterCoordinatingJob
  def announcements
    Announcement.published.promotable.order(published_at: :desc).to_a
  end

  def any_subscription
    @any_subscription ||= list.active_subscriptions.order('RANDOM()').first
  end

  def list
    env_var = ENV.fetch(Communications::OpportunitiesNewsletterCoordinatingJob::DISTRIBUTION_LIST_ENV_VAR_NAME, nil)
    Communications::DistributionList.find(env_var)
  end

  def list_name = 'Opportunities'

  def pings(expertises: nil)
    Ping.promoted_relevant_random(expertises:)
  end

  def recipient = any_subscription.subscriber

  def relevant_expertise = Expertise.limit(rand(1..5))

  # Default collection should use same scope as OpportunitiesNewsletterCoordinatingJob
  def scipolls = QuestionGroup.open_and_closed_scipolls

  # Default collection should use same scope as OpportunitiesNewsletterCoordinatingJob
  def scipis = QuestionGroup.featured_scipis.recruiting.limit(10)
end

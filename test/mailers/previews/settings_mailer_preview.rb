# frozen_string_literal: true

# Preview all emails at http://localhost:3000/rails/mailers/settings_mailer

require_relative '../../lib/test_data'

class SettingsMailerPreview < ActionMailer::Preview
  def confirm_email_change
    user = FactoryBot.create(:expert, :unconfirmed)
    previous_email = TestData.email

    SettingsMailer.confirm_email_change(user, previous_email:)
  end

  def notify_email_change
    user = FactoryBot.create(:expert, :unconfirmed)
    previous_email = TestData.email

    SettingsMailer.notify_email_change(user, previous_email:)
  end

  def notify_password_change
    user = FactoryBot.create(:expert)

    SettingsMailer.notify_password_change(user)
  end
end

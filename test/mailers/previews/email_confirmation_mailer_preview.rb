# frozen_string_literal: true

# Preview all emails at http://localhost:3000/rails/mailers/email_confirmation_mailer
class EmailConfirmationMailerPreview < ActionMailer::Preview
  # Preview this email at http://localhost:3000/rails/mailers/email_confirmation_mailer/confirm_email
  def confirm_email
    confirmation = EmailConfirmation.last

    EmailConfirmationMailer.with(confirmation:).confirm_email
  end
end

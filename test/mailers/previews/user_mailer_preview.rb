# frozen_string_literal: true

# Preview all emails at http://localhost:3000/rails/mailers/user_mailer
class UserMailerPreview < ActionMailer::Preview
  def confirmation
    user = User.unconfirmed.last

    UserMailer.confirmation(user)
  end

  def confirmation_reminder_to_expert
    expert = Role.expert.users.last

    UserMailer.confirmation_reminder(expert)
  end

  def confirmation_reminder_to_observer
    expert = Role.observer.users.last

    UserMailer.confirmation_reminder(expert)
  end

  def password_reset
    user = User.last

    UserMailer.password_reset(user)
  end
end

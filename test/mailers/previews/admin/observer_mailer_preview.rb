# frozen_string_literal: true

module Admin
  class ObserverMailerPreview < ActionMailer::Preview
    def notify_new_user
      scipi = FactoryBot.create(:scipi, :invite_only)
      observer = FactoryBot.create(:invite, :observer, survey: scipi)

      Admin::ObserverMailer
        .with(scipi:, user: observer.user)
        .notify_new_user
    end

    def notify_existing_user
      scipi = FactoryBot.create(:scipi, :invite_only)
      observer = FactoryBot.create(:invite, :observer, survey: scipi)

      Admin::ObserverMailer
        .with(scipi:, user: observer.user)
        .notify_existing_user
    end
  end
end

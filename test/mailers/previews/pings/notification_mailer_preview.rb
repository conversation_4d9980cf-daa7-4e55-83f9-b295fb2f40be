# frozen_string_literal: true

# Preview all emails at http://localhost:3000/rails/mailers/pings/notification_mailer
module Pings
  class NotificationMailerPreview < ActionMailer::Preview
    def voting_open
      paid_pings = Ping.paid
      subscription = Communications::Subscription.where(subscribable_type: 'Ping', subscribable_id: paid_pings.pluck(:id)).order('RANDOM()').first

      Pings::NotificationMailer.voting_open(subscription)
    end

    def paid_answer_accepted
      pings_with_accepted_answers = Ping.paid.with_accepted_answers

      subscription = Communications::Subscription.where(subscribable_type: 'Ping', subscribable_id: pings_with_accepted_answers.pluck(:id)).order('RANDOM()').first

      Pings::NotificationMailer.answer_accepted(subscription)
    end
  end
end

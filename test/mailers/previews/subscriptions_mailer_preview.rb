# frozen_string_literal: true

class SubscriptionsMailerPreview < ActionMailer::Preview
  def pings
    activity_digest_list_id = ENV.fetch('ACTIVITY_NOTIFICATIONS_DISTRIBUTION_LIST_ID', nil)
    list = Communications::DistributionList.find(activity_digest_list_id)
    subscribers = list.active_subscribers.joins(:notifications).merge(Notification.deliverable).distinct
    user = subscribers.find_by(id: params[:user_id]) || subscribers.last!

    expertises = user.profile.expertises

    pings = Ping.promoted_relevant_random(expertises:)

    relevant_expertise = expertises.limit(5)

    source = Ping.last
    notification1 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )
    notification2 = user.notifications.create!(
      source:,
      notification_type: 'pings:answer-accepted:answer-author',
      message: 'foo'
    )

    source = Ping.second
    notification3 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )
    notification4 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )
    notification5 = user.notifications.create!(
      source:,
      notification_type: 'pings:answer-accepted:subscriber',
      message: 'foo'
    )

    source = Ping.third

    notification6 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.new(
      ping_author_notifications: [notification6],
      ping_subscriber_notifications: [notification1, notification2, notification3, notification4, notification5],
      pings:,
      relevant_expertise:,
      profile_alerts: ProfileAlerts.new([])
    )

    SubscriptionsMailer.pings(user, digest:, list:)
  end
end

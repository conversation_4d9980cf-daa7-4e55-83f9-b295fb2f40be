# frozen_string_literal: true

module Surveys
  class ApplicantMailerPreview < ActionMailer::Preview
    def application_complete
      applicant = User.last
      survey = Branding.scipi.surveys.last

      ApplicantMailer.with(applicant:, survey:).application_complete
    end

    def application_incomplete
      applicant = User.last
      survey = Branding.scipi.surveys.last

      ApplicantMailer.with(applicant:, survey:).application_incomplete
    end
  end
end

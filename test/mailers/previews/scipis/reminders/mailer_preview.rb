# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Reminders
    class MailerPreview < ActionMailer::Preview
      def survey_mid_point_not_started
        panelist = User.new(email: Faker::Internet.email)
        scipi = QuestionGroup.new(id: 123, name: 'Test SciPi')
        closes_at = 10.days.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: false)

        Mailer.with(reminder:).survey_midpoint
      end

      def survey_mid_point_not_complete
        panelist = User.new(email: Faker::Internet.email)
        scipi = QuestionGroup.new(id: 123, name: 'Test SciPi')
        closes_at = 10.days.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: true)

        Mailer.with(reminder:).survey_midpoint
      end

      def survey_closing_not_started
        panelist = User.new(email: Faker::Internet.email)
        scipi = QuestionGroup.new(id: 123, name: 'Test SciPi')
        closes_at = 2.days.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: false)

        Mailer.with(reminder:).survey_closing
      end

      def survey_closing_not_complete
        panelist = User.new(email: Faker::Internet.email)
        scipi = QuestionGroup.new(id: 123, name: 'Test SciPi')
        closes_at = 2.days.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: true)

        Mailer.with(reminder:).survey_closing
      end
    end
  end
end

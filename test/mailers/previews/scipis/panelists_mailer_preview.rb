# frozen_string_literal: true

class Scipis::PanelistsMailerPreview < ActionMailer::Preview
  def question_round_open_initial_round
    scipi = QuestionGroup.joins(:panelists, :rounds).scipi.last
    round = scipi.rounds.order(:position).first
    panelist = scipi.panelists.first.user

    Scipis::PanelistsMailer.question_round_open(scipi:, round:, panelist:)
  end

  def question_round_open_middle_round
    scipi = QuestionGroup
            .joins(:panelists)
            .scipi
            .where(id: Surveys::Round.group(:survey_id).having('COUNT(*) > 3').select(:survey_id))
            .last
    round = scipi.rounds.find_by!(position: 3)
    panelist = scipi.panelists.first.user

    Scipis::PanelistsMailer.question_round_open(scipi:, round:, panelist:)
  end

  def question_round_open_last_round
    scipi = QuestionGroup.joins(:panelists, :rounds).scipi.last
    round = scipi.rounds.order(:position).last
    panelist = scipi.panelists.first.user

    Scipis::PanelistsMailer.question_round_open(scipi:, round:, panelist:)
  end

  def debate_open
    scipi = QuestionGroup.joins(:panelists, :rounds).scipi.last
    round = scipi.rounds.find_by!(position: 2)
    panelist = scipi.panelists.first.user

    Scipis::PanelistsMailer.debate_open(scipi:, round:, panelist:)
  end

  def debate_summary
    scipi = QuestionGroup.joins(:panelists, :rounds).scipi.last
    round = scipi.rounds.find_by!(type: 'Surveys::Rounds::Debate')
    panelist = scipi.panelists.first.user

    result = scipi.result_definitions.create!(
      title: 'In the primary review material for ethyl acrylate did you identify any issues that warrant attention ' \
             'and discussion with your fellow panel members?',
      render_type: ResultDefinitionRenderType.diagnostic,
      result_type: ResultDefinitionType.diagnostic
    )
    activity_summaries = [Surveys::ResultActivitySummary.new(result:, new_comment_count: 4)]

    Scipis::PanelistsMailer.debate_summary(scipi:, round:, panelist:, activity_summaries:)
  end

  def debate_closing_reminder_with_participation
    scipi = QuestionGroup.joins(:panelists, :rounds).scipi.last
    round = scipi.rounds.find_by!(type: 'Surveys::Rounds::Debate')
    panelist = scipi.panelists.first.user

    Scipis::PanelistsMailer.debate_closing_reminder(scipi:, round:, panelist:, participated: true)
  end

  def debate_closing_reminder_without_participation
    scipi = QuestionGroup.joins(:panelists, :rounds).scipi.last
    round = scipi.rounds.find_by!(type: 'Surveys::Rounds::Debate')
    panelist = scipi.panelists.first.user

    Scipis::PanelistsMailer.debate_closing_reminder(scipi:, round:, panelist:, participated: false)
  end

  def submission_receipt_non_final
    scipi = Surveys::Branding.scipi.surveys.last
    template = 'non_final'

    Scipis::PanelistsMailer.submission_receipt(panelist:, scipi:, template:)
  end

  def submission_receipt_final
    scipi = Surveys::Branding.scipi.surveys.last
    template = 'final'

    Scipis::PanelistsMailer.submission_receipt(panelist:, scipi:, template:)
  end

  private

  def panelist
    Role.expert.users.order('RANDOM()').first
  end
end

# frozen_string_literal: true

module Scipis
  class AdminsMailerPreview < ActionMailer::Preview
    def question_round_opening_soon
      round = ::Surveys::Rounds::Question.where('opens_at > ?', Time.current).first
      scipi = round.scipi
      admin = scipi.created_by

      <PERSON><PERSON><PERSON>ail<PERSON>.question_round_opening_soon(scipi:, round:, admin:)
    end

    def debate_round_opening_soon
      round = ::Surveys::Rounds::Debate.where('opens_at > ?', Time.current).first
      scipi = round.scipi
      admin = scipi.created_by

      Scipis::AdminsMailer.debate_round_opening_soon(scipi:, round:, admin:)
    end

    def new_message_notification
      scipi = QuestionGroup.scipi.joins(:panelists, :messages).last
      admin = scipi.created_by
      message = scipi.messages.last

      Scipis::AdminsMailer.new_message_notification(scipi:, message:, admin: admin)
    end

    def new_reply_notification
      scipi = QuestionGroup.scipi.joins(:panelists, :messages).last
      admin = scipi.created_by
      message = scipi.messages.last

      Scipis::AdminsMailer.new_reply_notification(scipi:, message:, admin: admin)
    end

    def report_complete
      scipi = QuestionGroup.scipi.joins(:reports).last
      admins = scipi.notifiable_owners
      report = scipi.reports.last

      Scipis::AdminsMailer.report_complete(scipi:, report:, admins:)
    end
  end
end

# frozen_string_literal: true

class Scipis::ApplicantsMailerPreview < ActionMailer::Preview
  def new_message_notification
    scipi = QuestionGroup.scipi.joins(:panelists, :messages).last
    applicant = scipi.created_by
    message = scipi.messages.last

    Scipis::ApplicantsMailer.new_message_notification(scipi: scipi, applicant: applicant, message: message)
  end

  def new_reply_notification
    scipi = QuestionGroup.scipi.joins(:panelists, messages: :replies).last
    applicant = scipi.created_by
    message = scipi.messages.where.not(parent_id: nil).last

    Scipis::ApplicantsMailer.new_reply_notification(scipi: scipi, applicant: applicant, message: message)
  end
end

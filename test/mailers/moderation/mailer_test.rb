# frozen_string_literal: true

require 'test_helper'

module Moderation
  class MailerTest < ActionMailer::TestCase
    test 'notify' do
      request = FactoryBot.create(:moderation_request)
      moderator_role = FactoryBot.create(:role, name: 'Moderator')
      ENV['MODERATOR_ROLE_ID'] = moderator_role.id.to_s
      FactoryBot.create(:user, roles: [moderator_role])

      mail = Moderation::Mailer.notify(request)

      assert_mail_to mail, *Moderation.moderators.pluck(:email)
      assert_mail_subject mail, "New Moderation Request for #{request.subject.class.humanize_name}: #{request.subject.title}"
      assert_mail_tag mail, 'admin-moderation-request'
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class OpportunitiesNewsletterMailerTest < ActionMailer::TestCase
  test 'header fields and layout content' do
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    unsubscribe_token = subscription.unsubscribe_token
    newsletter = OpportunitiesNewsletter.new(pings: [], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_mail_subject mail, "SciPinion Opportunities for #{Time.zone.today.strftime('%B')}"
    assert_mail_to mail, recipient.email
    assert_mail_from mail, '<EMAIL>'
    assert_receiving_reason mail, "you are subscribed to '#{list.name}'"
    assert_unsubscribe_link mail, subscription
    assert_list_unsubscribe_headers mail, unsubscribe_url(unsubscribe_token)
  end

  test 'opportunities mailer preview subject' do
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter, preview: true)

    assert_mail_subject mail, "[PREVIEW] SciPinion Opportunities for #{Time.zone.today.strftime('%B')}"
  end

  test 'profile alert' do
    scipolls = FactoryBot.create_list(:survey, 2)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(
      pings: [],
      scipolls:,
      relevant_expertise: [],
      profile_alerts: ['Bad profile!']
    )

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match 'Bad profile!', mail.body.encoded
  end

  test 'recruiting SciPis' do
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    scipi = FactoryBot.create(:scipi, recruitment_closes_on: Time.current)
    newsletter = OpportunitiesNewsletter.new(
      pings: [],
      scipis: [scipi],
      relevant_expertise: []
    )

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "#{scipi.product_type} #{scipi.id}", mail.body.encoded
    assert_match scipi.name, mail.body.encoded
    assert_match scipi.product_uri, mail.body.encoded
    assert_match scipi.description, mail.body.encoded if scipi.description.present?
    if scipi.recruitment_closes_on?
      assert_match "Recruiting until #{scipi.recruitment_closes_on&.strftime('%m/%-d/%y')}", mail.body.encoded
    end
  end

  test 'applied scipi' do
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    scipi = FactoryBot.create(:scipi)
    FactoryBot.create(:invite, :approved, survey: scipi, user: recipient)

    newsletter = OpportunitiesNewsletter.new(scipis: [scipi], pings: [], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match scipi.name, mail.body.encoded
    assert_match 'Applied', mail.body.encoded
    assert_match 'You have applied to this SciPi.', mail.body.encoded
  end

  test 'no recruiting SciPis' do
    scipolls = FactoryBot.create_list(:survey, 2)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], scipolls:, relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_no_match 'Paid Opportunities', mail.body.encoded
  end

  test 'open scipolls (with prefill links)' do
    scipoll = FactoryBot.create(:scipoll, :open)
    question = FactoryBot.create(:radio, survey: scipoll)

    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], scipolls: [scipoll], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "#{scipoll.product_type} #{scipoll.id}", mail.body.encoded
    assert_match scipoll.name, mail.body.encoded
    assert_match 'Poll Open', mail.body.encoded
    assert_match scipoll.product_uri, mail.body.encoded

    question.answer_choices.each do |answer_choice|
      assert_match answer_choice.label, mail.body.encoded
      assert_match scipoll_prefill_url(scipoll, answer_choice, email: recipient.email), mail.body.encoded
    end
  end

  test 'complete scipoll' do
    scipoll = FactoryBot.create(:scipoll, :published, closes_at: Time.in_last_time_zone.yesterday, results_published: true, results_public: true)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], scipolls: [scipoll], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "#{scipoll.product_type} #{scipoll.id}", mail.body.encoded
    assert_match scipoll.name, mail.body.encoded
    assert_match 'View Results', mail.body.encoded
    assert_match "Poll closed #{scipoll.closes_at.strftime('%m/%-d/%y')}", mail.body.encoded
    assert_match survey_results_url(scipoll), mail.body.encoded
  end

  test 'no scipolls' do
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_no_match 'Scientific Polls', mail.body.encoded
  end

  test 'taken scipoll' do
    scipoll = FactoryBot.create(:scipoll, :published, closes_at: Time.in_last_time_zone.yesterday)
    recipient = FactoryBot.create(:user)
    FactoryBot.create(:submission, survey: scipoll, user: recipient)

    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], scipolls: [scipoll], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match scipoll.name, mail.body.encoded
    assert_match 'Poll Taken', mail.body.encoded
    assert_match 'You have already taken this SciPoll.', mail.body.encoded
  end

  test 'pings' do
    ping = FactoryBot.create(:ping)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [ping], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "Ping #{ping.id}", mail.body.encoded
    assert_match ping.title, mail.body.encoded
    assert_match ping.product_uri, mail.body.encoded
  end

  test 'promoted ping gets featured flag' do
    ping = FactoryBot.create(:ping, :promoted)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [ping], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "Ping #{ping.id}", mail.body.encoded
    assert_match 'Featured', mail.body.encoded
  end

  test 'participated pings get badges' do
    ping = FactoryBot.create(:ping)
    recipient = FactoryBot.create(:user, :confirmed, :with_display_name)
    FactoryBot.create(:ping_answer, ping:, author: recipient)
    other_answer = FactoryBot.create(:ping_answer, ping:, author: FactoryBot.create(:user, :confirmed, :with_display_name))
    FactoryBot.create(:ping_vote, answer: other_answer, voter: recipient)

    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [ping], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "Ping #{ping.id}", mail.body.encoded
    assert_match 'Answered', mail.body.encoded
    assert_match 'Voted', mail.body.encoded
  end

  test 'paid pings include status in title and CTA' do
    ping = FactoryBot.create(:ping, :paid, :voting_open)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [ping], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match "Ping #{ping.id}", mail.body.encoded
    assert_match '(Accepting Votes)', mail.body.encoded
    assert_match 'Vote Now', mail.body.encoded
  end

  test 'no pings' do
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], relevant_expertise: [])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_no_match 'Questions from the Scientific Community', mail.body.encoded
  end

  test 'with announcements' do
    announcement = FactoryBot.create(:announcement, :promotable)
    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)
    newsletter = OpportunitiesNewsletter.new(pings: [], relevant_expertise: [], announcements: [announcement])

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    assert_match announcement.title, mail.body.encoded
    assert_match announcement.content, mail.body.encoded
  end

  # mostly load it all up for one test to emulate most common scenario, keep the other test targeted
  test 'with everything' do
    announcements = FactoryBot.create_list(:announcement, 2, :promotable)
    relevant_expertise = FactoryBot.create_list(:expertise, 5)
    pings = FactoryBot.create_list(:ping, 5)
    scipolls = FactoryBot.create_list(:scipoll, 2, :open)
    scipis = FactoryBot.create_list(:survey, 2, :scipi, recruitment_closes_on: Time.current)

    recipient = FactoryBot.create(:user)
    list = FactoryBot.create(:distribution_list)
    subscription = list.subscriptions.create!(subscriber: recipient)

    newsletter = OpportunitiesNewsletter.new(
      announcements:,
      pings:,
      scipolls:,
      scipis:,
      relevant_expertise:,
      profile_alerts: ['Bad profile!']
    )

    mail = OpportunitiesNewsletterMailer.opportunities(recipient, list, subscription, newsletter)

    announcements.each do |announcement|
      assert_match announcement.title, mail.body.encoded
    end

    pings.each do |ping|
      assert_match "Ping #{ping.id}", mail.body.encoded
    end

    scipolls.each do |scipoll|
      assert_match "SciPoll #{scipoll.id}", mail.body.encoded
    end

    scipis.each do |scipi|
      assert_match "SciPi #{scipi.id}", mail.body.encoded
    end

    relevant_expertise.each do |expertise|
      assert_match expertise.name, mail.body.encoded
    end
  end
end

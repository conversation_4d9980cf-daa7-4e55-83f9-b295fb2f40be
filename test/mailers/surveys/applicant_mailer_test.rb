# frozen_string_literal: true

require 'test_helper'

module Surveys
  class ApplicantMailerTest < ActionMailer::TestCase
    test 'application complete' do
      applicant = FactoryBot.create(:user)
      survey = FactoryBot.create(:survey)

      mail = ApplicantMailer.with(applicant:, survey:).application_complete

      assert_equal 'Your application was received', mail.subject
      assert_equal [applicant.email], mail.to
      assert_equal ['<EMAIL>'], mail.from

      assert_match "Thank you for applying to <b>#{survey.display_name}</b>.", mail.body.encoded
      assert_match 'We have your CV on file and your application is complete.', mail.body.encoded
      assert_match "You are receiving this email because you applied to SciPi #{survey.display_name}.", mail.body.encoded
    end

    test 'application incomplete' do
      applicant = FactoryBot.create(:user)
      survey = FactoryBot.create(:survey)

      mail = ApplicantMailer.with(applicant:, survey:).application_incomplete

      assert_equal 'Your CV is Needed', mail.subject
      assert_equal [applicant.email], mail.to
      assert_equal ['<EMAIL>'], mail.from

      assert_match "Thank you for applying to <b>#{survey.display_name}</b>, however your application is not complete.",
                   mail.body.encoded
      assert_match "You are receiving this email because you applied to SciPi #{survey.display_name}.", mail.body.encoded
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class SettingsMailerTest < ActionMailer::TestCase
  test 'confirm email change for new address' do
    previous_email = Faker::Internet.email
    user = FactoryBot.create(:expert, :unconfirmed)

    email = SettingsMailer.confirm_email_change(user, previous_email:)

    assert_equal [user.email], email.to
    assert_equal '[SciPinion] Please confirm your new email address', email.subject

    email_body = email.body.encoded
    assert_match "Please confirm your recent email change from #{previous_email} to #{user.email} " \
                 'by clicking the confirmation link below.',
                 email_body
  end

  test 'notify old email address of change' do
    previous_email = Faker::Internet.email
    user = FactoryBot.create(:expert, :unconfirmed)

    email = SettingsMailer.notify_email_change(user, previous_email:)

    assert_equal [previous_email], email.to
    assert_equal '[SciPinion] Your email address has changed', email.subject
    assert_match "Your email address on file has changed from #{previous_email} to #{user.email}.",
                 email.body.encoded
  end

  test 'notify of password change' do
    user = FactoryBot.create(:expert)

    email = SettingsMailer.notify_password_change(user)

    assert_equal [user.email], email.to
    assert_equal '[SciPinion] Your password has changed', email.subject
    assert_match 'This email is to notify you that your password was just changed.',
                 email.body.encoded
  end
end

# frozen_string_literal: true

require 'test_helper'

class EmailConfirmationMailerTest < ActionMailer::TestCase
  test 'confirm_email' do
    confirmation = FactoryBot.create(:email_confirmation)

    mail = EmailConfirmationMailer.with(confirmation: confirmation).confirm_email

    assert_equal 'Please confirm your email address', mail.subject
    assert_equal [confirmation.email.address], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match "Click 'Confirm My Email' to confirm your association with the institution at #{confirmation.email.hostname}",
                 mail.body.encoded
  end
end

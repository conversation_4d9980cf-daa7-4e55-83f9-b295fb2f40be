# frozen_string_literal: true

require 'test_helper'

class SubscriptionsMailerTest < ActionMailer::TestCase
  test 'header fields and layout content' do
    list = FactoryBot.create(:distribution_list)
    user = FactoryBot.create(:expert)
    subscription = list.subscribe(user)
    profile_alerts = ProfileAlerts.new([])
    source = FactoryBot.create(:ping)
    notification = user.notifications.create!(source:, notification_type: 'test', message: 'Test')
    digest = DailyActivityDigest.new(
      ping_author_notifications: [],
      ping_subscriber_notifications: [notification],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    mail = SubscriptionsMailer.pings(user, digest:, list:)

    assert_mail_to mail, user.email
    assert_mail_from mail, '<EMAIL>'
    assert_mail_tag mail, 'activity-digest'
    assert_receiving_reason mail, "you are subscribed to '#{list.name}'"
    assert_unsubscribe_link mail, subscription
    assert_list_unsubscribe_headers mail, unsubscribe_url(subscription.unsubscribe_token)
  end

  test 'single notification subject' do
    list = FactoryBot.create(:distribution_list)
    user = FactoryBot.create(:expert)
    list.subscribe(user)
    profile_alerts = ProfileAlerts.new([])
    source = FactoryBot.create(:ping)
    notification = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:subscriber',
      message: 'Test'
    )
    digest = DailyActivityDigest.new(
      ping_author_notifications: [notification],
      ping_subscriber_notifications: [],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    mail = SubscriptionsMailer.pings(user, digest:, list:)

    assert_mail_subject mail, "Ping Update: #{notification.description}"
  end

  test 'multiple notification subject' do
    list = FactoryBot.create(:distribution_list)
    user = FactoryBot.create(:expert)
    list.subscribe(user)
    profile_alerts = ProfileAlerts.new([])
    source = FactoryBot.create(:ping)
    notification1 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:subscriber',
      created_at: 12.hours.ago,
      message: 'Test'
    )
    notification2 = user.notifications.create!(
      source:,
      notification_type: 'pings:answer-accepted:subscriber',
      created_at: 2.hours.ago,
      message: 'Test'
    )
    digest = DailyActivityDigest.new(
      ping_author_notifications: [notification1],
      ping_subscriber_notifications: [notification2],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    mail = SubscriptionsMailer.pings(user, digest:, list:)

    assert_mail_subject mail, "Ping Updates: #{notification2.description} (and more)"
  end

  test 'authored pings notifications' do
    list = FactoryBot.create(:distribution_list)
    user = FactoryBot.create(:expert, :with_display_name)
    list.subscribe(user)
    profile_alerts = ProfileAlerts.new([])
    ping = FactoryBot.create(:ping, author: user)
    notification = user.notifications.create!(
      source: ping,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.new(
      ping_author_notifications: [notification],
      ping_subscriber_notifications: [],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    mail = SubscriptionsMailer.pings(user, digest:, list:)

    assert_match ping.title, mail.body.encoded
    assert_match ping.product_uri, mail.body.encoded
    assert_match '1 New Answer', mail.body.encoded
  end

  test 'subscribed pings notifications' do
    list = FactoryBot.create(:distribution_list)
    user = FactoryBot.create(:expert, :with_display_name)
    list.subscribe(user)
    profile_alerts = ProfileAlerts.new([])
    ping = FactoryBot.create(:ping, author: user)
    notification1 = user.notifications.create!(
      source: ping,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )
    notification2 = user.notifications.create!(
      source: ping,
      notification_type: 'pings:answer-accepted:answer-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.new(
      ping_author_notifications: [],
      ping_subscriber_notifications: [notification1, notification2],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    mail = SubscriptionsMailer.pings(user, digest:, list:)

    assert_match ping.title, mail.body.encoded
    assert_match ping.product_uri, mail.body.encoded
    assert_match '1 New Answer', mail.body.encoded
    assert_match 'Your Answer Was Accepted', mail.body.encoded
  end

  test 'profile alerts' do
    list = FactoryBot.create(:distribution_list)
    user = FactoryBot.create(:expert, :with_display_name)
    list.subscribe(user)
    profile_alerts = ProfileAlerts.new(['cv_missing'])
    source = FactoryBot.create(:ping, author: user)
    notification = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.new(
      ping_author_notifications: [],
      ping_subscriber_notifications: [notification],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    mail = SubscriptionsMailer.pings(user, digest:, list:)

    assert_match 'You have not uploaded a CV', mail.body.encoded
  end
end

# frozen_string_literal: true

require 'test_helper'

class UserMailerTest < ActionMailer::TestCase
  test 'send confirmation email ' do
    user = FactoryBot.create(:expert, :unconfirmed)

    email = UserMailer.confirmation(user)

    assert_equal [user.email], email.to
    assert_equal 'Welcome to SciPinion! Please confirm your email address', email.subject
    email_body = email.body.encoded
    assert_match 'Please confirm your account', email_body
  end

  test 'confirmation reminder to expert' do
    token = 'i_am_a_token'
    expert = User.new(email: Faker::Internet.email, confirmation_token: token)
    confirmation_link = confirmation_url(token, **default_url_options)

    mail = UserMailer.confirmation_reminder(expert)

    assert_equal 'Reminder: Confirm Your SciPinion Account (it\'s quick)', mail.subject
    assert_equal [expert.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match(confirmation_link, mail.body.encoded)
    assert_match(I18n.t('user_mailer.confirmation_reminder.products_intro.expert'), mail.body.encoded)
    assert_match(I18n.t('marketing.scipoll.one_liner.expert_html'), mail.body.encoded)
    assert_match(I18n.t('marketing.scipi.one_liner.expert_html'), mail.body.encoded)
    assert_match(I18n.t('marketing.ping.one_liner.expert_html'), mail.body.encoded)
  end

  test 'observer gets alt content in confirmation reminder email' do
    observer = User.new(email: Faker::Internet.email, confirmation_token: 'xyz', roles: [Role.observer])

    mail = UserMailer.confirmation_reminder(observer)

    assert_equal 'Reminder: Confirm Your SciPinion Account (it\'s quick)', mail.subject
    assert_equal [observer.email], mail.to
    assert_match(I18n.t('user_mailer.confirmation_reminder.products_intro.observer'), mail.body.encoded)
    assert_match(I18n.t('marketing.scipoll.one_liner.observer_html'), mail.body.encoded)
    assert_match(I18n.t('marketing.scipi.one_liner.observer_html'), mail.body.encoded)
    assert_match(I18n.t('marketing.ping.one_liner.observer_html'), mail.body.encoded)
  end

  test 'password_reset' do
    user = FactoryBot.create(:expert, :reset_password_requested)

    mail = UserMailer.password_reset(user)

    assert_equal '[SciPinion] Please reset your password', mail.subject
    assert_equal [user.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match "/password_reset/#{user.reset_password_token}", mail.body.encoded
  end
end

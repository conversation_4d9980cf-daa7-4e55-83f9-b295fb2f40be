# frozen_string_literal: true

require 'test_helper'

module Experts
  class MailerTest < ActionMailer::TestCase
    test 'export' do
      email = Faker::Internet.email
      attachment_name = 'free_form_data.csv'
      csv = file_fixture(attachment_name).read

      mail = Experts::Mailer.export(email, csv)

      assert_equal 'Your export is ready', mail.subject
      assert_equal [email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match(/expert-export-\d{4}-\d{2}-\d{2}-\d{6}\.csv/, mail.attachments[0].filename)
    end
  end
end

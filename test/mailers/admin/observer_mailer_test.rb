# frozen_string_literal: true

require 'test_helper'

module Admin
  class ObserverMailerTest < ActionMailer::TestCase
    setup do
      @survey = FactoryBot.create(:survey)
    end

    test 'notify_new_user' do
      user = FactoryBot.create(:user, :reset_password_requested)

      mail = Admin::ObserverMailer.with(survey: @survey, user:).notify_new_user

      assert_equal "[SciPinion] You have been invited to observe #{@survey.name}", mail.subject
      assert_equal [user.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match "A SciPinion Administrator has invited you to observe #{@survey.name}.", mail.body.encoded
    end

    test 'notify_existing_user' do
      user = FactoryBot.create(:user, :reset_password_requested)

      mail = Admin::ObserverMailer.with(survey: @survey, user:).notify_existing_user

      assert_equal "[SciPinion] You have been invited to observe #{@survey.name}", mail.subject
      assert_equal [user.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match "A SciPinion Administrator has invited you to observe #{@survey.name}.", mail.body.encoded
    end
  end
end

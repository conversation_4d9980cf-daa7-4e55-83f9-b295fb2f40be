# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ContractsMailerTest < ActionMailer::TestCase
    test 'contract_complete' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create(expert:, completed_contract: file_fixture('test_attachment.pdf'))

      mail = ContractsMailer.with(panelist:).contract_complete

      assert_equal "SciPi ##{scipi.id}: Your Signed Contract is Attached", mail.subject
      assert_equal [panelist.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match "Thank you for signing your contract for SciPi #{scipi.display_name}.", mail.body.encoded
      assert_equal 1, mail.attachments.count
      assert_equal 'test_attachment.pdf', mail.attachments[0].filename
    end

    test 'contract_ready' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create(expert:)

      mail = ContractsMailer.with(panelist:).contract_ready

      assert_equal "SciPi ##{scipi.id}: Your Contract is Ready", mail.subject
      assert_equal [panelist.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match 'A SciPinion administrator has requested your signature on your contract ' \
                   "for SciPi #{scipi.display_name}.",
                   mail.body.encoded
    end
  end
end

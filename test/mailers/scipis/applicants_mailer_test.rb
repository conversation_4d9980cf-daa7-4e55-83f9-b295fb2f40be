# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ApplicantsMailerTest < ActionMailer::TestCase
    test 'new message notification with attachments' do
      applicant = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)
      scipi = FactoryBot.create(:scipi)
      message = FactoryBot.create(:message, user: applicant, sent_by: admin, survey: scipi)

      # Create a test attachment
      message.attachments.attach(
        io: Rails.root.join('test/fixtures/files/test_attachment.pdf').open,
        filename: 'test_attachment.pdf',
        content_type: 'application/pdf'
      )

      mail = Scipis::ApplicantsMailer.new_message_notification(scipi:, applicant:, message:)

      assert_equal "New Message for SciPi ##{scipi.id}: #{message.subject}", mail.subject
      assert_equal [applicant.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      html_body = mail.html_part&.body&.to_s || mail.body.to_s
      assert_includes html_body, message.content.to_s
      assert_equal 1, mail.attachments.size
      assert_equal 'test_attachment.pdf', mail.attachments.first.filename
    end

    test 'new reply notification with attachments' do
      applicant = FactoryBot.create(:expert)
      admin = FactoryBot.create(:admin)
      scipi = FactoryBot.create(:scipi)
      parent_message = FactoryBot.create(:message, user: applicant, sent_by: admin, survey: scipi, subject: 'Original Subject')
      reply_message = FactoryBot.create(:message, user: applicant, sent_by: admin, survey: scipi, parent: parent_message, subject: nil)

      # Create a test attachment
      reply_message.attachments.attach(
        io: Rails.root.join('test/fixtures/files/test_attachment.pdf').open,
        filename: 'test_attachment.pdf',
        content_type: 'application/pdf'
      )

      mail = Scipis::ApplicantsMailer.new_reply_notification(scipi:, applicant:, message: reply_message)

      assert_equal "New Reply for SciPi ##{scipi.id}: Original Subject", mail.subject
      assert_equal [applicant.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      html_body = mail.html_part&.body&.to_s || mail.body.to_s
      assert_includes html_body, reply_message.content.to_s
      assert_equal 1, mail.attachments.size
      assert_equal 'test_attachment.pdf', mail.attachments.first.filename
    end
  end
end

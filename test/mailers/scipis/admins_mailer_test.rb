# frozen_string_literal: true

require 'test_helper'

class Scipis::AdminsMailerTest < ActionMailer::TestCase
  test 'question_round_opening_soon' do
    round = FactoryBot.create(:question_round)
    scipi = round.scipi
    admin = scipi.created_by

    mail = Scipis::AdminsMailer.question_round_opening_soon(scipi:, round:, admin:)

    assert_equal "Please Review: SciPi ##{scipi.id} Round #{round.number} is Opening Soon", mail.subject
    assert_equal [admin.email], mail.to
    assert_match "Round #{round.number} of SciPi ##{scipi.id} is opening soon", mail.body.encoded
  end

  test 'debate_round_opening_soon' do
    round = FactoryBot.create(:debate_round)
    scipi = round.scipi
    admin = scipi.created_by

    mail = Scipis::AdminsMailer.debate_round_opening_soon(scipi:, round:, admin:)

    assert_equal "Please Review: SciPi ##{scipi.id} Round #{round.number} is Opening Soon", mail.subject
    assert_equal [admin.email], mail.to
    assert_match "Round #{round.number} of SciPi ##{scipi.id} is opening soon", mail.body.encoded
  end

  test 'new_message_notification with attachment' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)

    message = FactoryBot.create(:message, survey: scipi, user: expert, sent_by: expert)
    message.attachments.attach(
      io: Rails.root.join('test/fixtures/files/test_attachment.pdf').open,
      filename: 'test_attachment.pdf',
      content_type: 'application/pdf'
    )

    mail = Scipis::AdminsMailer.new_message_notification(scipi:, message:, admin:)

    assert_equal "SciPi ##{scipi.id} New Message from #{expert.mail_name}: #{message.subject}", mail.subject
    assert_equal [admin.email], mail.to
    assert_equal 1, mail.attachments.count
    assert_equal 'test_attachment.pdf', mail.attachments.first.filename
    assert_match 'application/pdf', mail.attachments.first.content_type
    assert_match 'This message includes the following attachments:', mail.html_part.body.to_s
    assert_match 'test_attachment.pdf', mail.html_part.body.to_s
  end

  test 'new_reply_notification with attachment' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)

    parent_message = FactoryBot.create(:message, survey: scipi, user: expert, sent_by: expert, subject: 'Original Subject')
    reply_message = FactoryBot.create(:message, survey: scipi, user: expert, sent_by: expert, parent: parent_message, subject: nil)
    reply_message.attachments.attach(
      io: Rails.root.join('test/fixtures/files/test_attachment.pdf').open,
      filename: 'test_attachment.pdf',
      content_type: 'application/pdf'
    )

    mail = Scipis::AdminsMailer.new_reply_notification(scipi:, message: reply_message, admin:)

    assert_equal "SciPi ##{scipi.id} New Reply from #{expert.mail_name}: Original Subject", mail.subject
    assert_equal [admin.email], mail.to
    assert_equal 1, mail.attachments.count
    assert_equal 'test_attachment.pdf', mail.attachments.first.filename
    assert_match 'application/pdf', mail.attachments.first.content_type
    assert_match 'This message includes the following attachments:', mail.html_part.body.to_s
    assert_match 'test_attachment.pdf', mail.html_part.body.to_s
  end

  test 'report_complete with attachment' do
    ActiveStorage::Current.url_options = { host: 'localhost' }

    scipi = FactoryBot.create(:scipi)
    report = FactoryBot.create(:scipi_report, scipi:)
    recipients = scipi.notifiable_owners

    report_file = Rails.root.join('test/fixtures/files/test_attachment.pdf').open

    report.attach_file!(report_file)

    mail = Scipis::AdminsMailer.report_complete(scipi:, report:, admins: recipients)

    assert_equal "SciPi ##{scipi.id} Report Generation Complete", mail.subject
    assert_equal scipi.notifiable_owners.pluck(:email), mail.to
    assert_equal "SciPi ##{scipi.id} Report [INTERIM].pdf", mail.attachments.first.filename
    assert_match 'application/pdf', mail.attachments.first.content_type
  end
end

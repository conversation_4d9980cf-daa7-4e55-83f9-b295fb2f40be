# frozen_string_literal: true

require 'test_helper'

class Scipis::PanelistsMailerTest < ActionMailer::TestCase
  test 'initial question round open' do
    user = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi, :published)
    scipi.panelists.create(user:)
    round = FactoryBot.create(:question_round, survey: scipi)

    mail = Scipis::PanelistsMailer.question_round_open(scipi:, round:, panelist: user).deliver_now

    assert_equal "Round 1 is open for SciPi #{scipi.display_name}", mail.subject
    assert_equal [user.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match 'We are ready to begin this review', mail.body.encoded
    assert_match 'You are one of 1 independent experts', mail.body.encoded
  end

  test 'middle question round open' do
    user = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi, :published, number_of_rounds: 5)
    scipi.panelists.create(user:)
    round = scipi.rounds.third

    mail = Scipis::PanelistsMailer.question_round_open(scipi:, round:, panelist: user).deliver_now

    assert_equal "Round 3 is open for SciPi #{scipi.display_name}", mail.subject
    assert_equal [user.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match 'Thank you for completing Rounds 1 and 2', mail.body.encoded
    assert_match 'We are now ready to begin Round 3', mail.body.encoded
  end

  test 'final question round open' do
    user = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi, :published, number_of_rounds: 3)
    scipi.panelists.create(user:)
    round = scipi.rounds.third

    mail = Scipis::PanelistsMailer.question_round_open(scipi:, round:, panelist: user).deliver_now

    assert_equal "Round 3 is open for SciPi #{scipi.display_name}", mail.subject
    assert_equal [user.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match 'We are now ready to begin Round 3, the final round', mail.body.encoded
    assert_match 'We will initiate your payment upon receipt of your final submission', mail.body.encoded
  end

  test 'debate_open' do
    user = FactoryBot.create(:expert)
    round = FactoryBot.create(:debate_round, position: 2)
    scipi = round.survey

    mail = Scipis::PanelistsMailer.debate_open(scipi:, round:, panelist: user)

    assert_equal "Round 2 is open for SciPi #{scipi.display_name}", mail.subject
    assert_equal [user.email], mail.to
    assert_equal ['<EMAIL>'], mail.from

    assert_match "#{user.first_name},", mail.body.encoded
    assert_match "7 days (from ~#{round.opens_at.strftime('%-m/%-d/%Y')} to ~#{round.closes_at.strftime('%-m/%-d/%Y')})", mail.body.encoded
    assert_match "You are receiving this email because you are a panelist on SciPi #{scipi.display_name}.", mail.body.encoded
  end

  test 'debate summary' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    result_type = FactoryBot.create(:result_type, :diagnostic)
    render_type = FactoryBot.create(:render_type, :diagnostic)
    result = scipi.result_definitions.create!(title: 'Test result', render_type:, result_type:)
    round = FactoryBot.create(:debate_round, survey: scipi, position: 2)
    activity_summaries = [Surveys::ResultActivitySummary.new(result:, new_comment_count: 4)]

    mail = Scipis::PanelistsMailer.debate_summary(scipi:, round:, panelist: expert, activity_summaries:)

    assert_equal "Round 2 summary for SciPi #{scipi.display_name}", mail.subject
    assert_equal [expert.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match 'Test result', mail.body.encoded
    assert_match '4 new comments', mail.body.encoded
    assert_match "You are receiving this email because you are a panelist on SciPi #{scipi.display_name}.", mail.body.encoded
  end

  test 'debate closing reminder when panelist has participated' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi)
    round = FactoryBot.create(:debate_round, survey: scipi, position: 2)

    travel_to round.send_closing_reminder_at do
      mail = Scipis::PanelistsMailer.debate_closing_reminder(scipi:, round:, panelist: expert, participated: true)

      assert_equal "Reminder: Round 2 is closing soon for SciPi #{scipi.display_name}", mail.subject
      assert_equal [expert.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match "This is a reminder that Round 2 of SciPi #{scipi.display_name} " \
                   'will be closing in less than 24 hours',
                   mail.body.encoded
      assert_match "Thank you for your participation in Round 2. If you'd like to share additional thoughts, there is still time.",
                   mail.body.encoded
      assert_no_match 'You have not yet participated in Round 2', mail.body.encoded
      assert_match "You are receiving this email because you are a panelist on SciPi #{scipi.display_name}.", mail.body.encoded
    end
  end

  test 'debate closing reminder when panelist has not participated' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi)
    round = FactoryBot.create(:debate_round, survey: scipi, position: 2)

    travel_to round.send_closing_reminder_at do
      mail = Scipis::PanelistsMailer.debate_closing_reminder(scipi:, round:, panelist: expert, participated: false)

      assert_equal "Reminder: Round 2 is closing soon for SciPi #{scipi.display_name}", mail.subject
      assert_equal [expert.email], mail.to
      assert_equal ['<EMAIL>'], mail.from
      assert_match "This is a reminder that Round 2 of SciPi #{scipi.display_name} " \
                   'will be closing in less than 24 hours',
                   mail.body.encoded
      assert_match 'So far we haven\'t received any comments from you during this round', mail.body.encoded
      assert_match "You are receiving this email because you are a panelist on SciPi #{scipi.display_name}.", mail.body.encoded
    end
  end

  test 'submission received for an earlier round that is not the final round' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi)
    template = 'non_final'

    mail = Scipis::PanelistsMailer.submission_receipt(panelist: expert, scipi:, template:)

    assert_equal "SciPi ##{scipi.id}: Submission Received", mail.subject
    assert_equal [expert.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match 'Thank you for submitting your responses to the charge questions for the current round.',
                 mail.body.encoded
    assert_match 'Please stand by as your fellow panelists finish up.', mail.body.encoded
    assert_match "You are receiving this email because you are a panelist on SciPi #{scipi.display_name}.", mail.body.encoded
  end

  test 'submission received for the final round' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi)
    template = 'final'

    mail = Scipis::PanelistsMailer.submission_receipt(panelist: expert, scipi:, template:)

    assert_equal "SciPi ##{scipi.id}: Submission Received", mail.subject
    assert_equal [expert.email], mail.to
    assert_equal ['<EMAIL>'], mail.from
    assert_match 'Thank you for submitting your final responses to this review. Pending review for completeness and clarity we will initiate your payment.', mail.body.encoded
    assert_match 'We rely upon a payment service provider, <a href=\'https://veem.com\'>Veem</a>, which allows us to provide fast payments to experts all over the world. You will receive an email from Veem to accept the payment.', mail.body.encoded
    assert_match "You are receiving this email because you are a panelist on SciPi #{scipi.display_name}.",
                 mail.body.encoded
  end
end

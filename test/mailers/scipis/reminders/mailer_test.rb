# frozen_string_literal: true

require 'test_helper'

module Scipis
  module Reminders
    class MailerTest < ActionMailer::TestCase
      test 'midpoint reminder when not started' do
        panelist = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        closes_at = 1.week.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: false)

        mail = Mailer.with(reminder:).survey_midpoint

        assert_equal "SciPi ##{scipi.id}: Midpoint Reminder", mail.subject
        assert_equal [panelist.email], mail.to
        assert_equal ['<EMAIL>'], mail.from
        assert_match 'Our records show that you have not yet started your review.', mail.body.encoded
      end

      test 'midpoint reminder when not completed' do
        panelist = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        closes_at = 1.week.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: true)

        mail = Mailer.with(reminder:).survey_midpoint

        assert_equal "SciPi ##{scipi.id}: Midpoint Reminder", mail.subject
        assert_equal [panelist.email], mail.to
        assert_equal ['<EMAIL>'], mail.from
        assert_match 'Our records show that you have begun, but not yet completed your review.', mail.body.encoded
      end

      test 'closing reminder when not started' do
        panelist = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        closes_at = 1.week.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: false)

        mail = Mailer.with(reminder:).survey_closing

        assert_equal "SciPi ##{scipi.id}: Closing Reminder", mail.subject
        assert_equal [panelist.email], mail.to
        assert_equal ['<EMAIL>'], mail.from
        assert_match 'Our records show that you have not yet started your review.', mail.body.encoded
      end

      test 'closing reminder when not completed' do
        panelist = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        closes_at = 1.week.from_now

        reminder = EmailModel.new(panelist:, scipi:, closes_at:, started: true)

        mail = Mailer.with(reminder:).survey_closing

        assert_equal "SciPi ##{scipi.id}: Closing Reminder", mail.subject
        assert_equal [panelist.email], mail.to
        assert_equal ['<EMAIL>'], mail.from
        assert_match 'Our records show that you have begun, but not yet completed your review.', mail.body.encoded
      end
    end
  end
end

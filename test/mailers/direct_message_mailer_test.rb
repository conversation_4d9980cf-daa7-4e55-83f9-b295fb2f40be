# frozen_string_literal: true

require 'test_helper'

class DirectMessageMailerTest < ActionMailer::TestCase
  test 'send message to expert' do
    message = FactoryBot.create(:message)

    email = DirectMessageMailer.send_message(message)

    assert_equal [message.sent_from_address], email.from
    assert_equal [message.user.email], email.to
    assert_equal message.subject, email.subject
    assert_equal [message.sent_by.email], email.reply_to
    assert_match message.content.to_plain_text, email.body.encoded
    assert_no_match 'You are receiving this email because', email.body.encoded
  end
end

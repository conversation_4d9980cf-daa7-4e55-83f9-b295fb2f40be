# frozen_string_literal: true

require 'test_helper'

class DashboardControllerTest < ActionDispatch::IntegrationTest
  # Tests the within_current_time_zone action in ApplicationController
  # can handle invalid time zones
  test 'should get success response with invalid time zone' do
    user = FactoryBot.create(:user, time_zone: 'Invalid/TimeZone')

    sign_in user

    get dashboard_url

    assert_response :success
  end
end

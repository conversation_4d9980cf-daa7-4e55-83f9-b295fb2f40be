# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ContractsControllerTest < ActionDispatch::IntegrationTest
    include ActionCable::TestHelper

    # TODO: ignore non-redirected requests, or requests not from sign now?
    test 'redirect target pushes to redirect channel' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(expert:)

      sign_in(expert)

      get redirect_target_scipi_contract_path(scipi, panelist_id: panelist.id)

      assert_broadcast_on("contract-completion-#{panelist.id}",
                          from: new_scipi_contract_signature_url(scipi),
                          to: scipi_url(scipi))
    end
  end
end

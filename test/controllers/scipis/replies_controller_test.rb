# frozen_string_literal: true

require 'test_helper'

module Scipis
  class RepliesControllerTest < ActionDispatch::IntegrationTest
    test 'should create reply' do
      scipi = FactoryBot.create(:scipi, :published)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)
      message = FactoryBot.create(:message, survey: scipi, user: expert, subject: 'Test Subject')

      sign_in(expert)

      assert_difference -> { Message.count } do
        post scipi_message_replies_path(scipi, message),
             params: {
               message: {
                 content: 'Test reply content',
                 attachments: [fixture_file_upload('test_attachment.pdf', 'application/pdf')]
               }
             }
      end

      assert_redirected_to scipi_message_path(scipi, message)
      assert_match 'Reply sent successfully', flash[:notice]

      reply = Message.last
      assert_equal message, reply.parent
      assert_equal expert, reply.user
      assert_equal expert, reply.sent_by
    end

    test 'should re-render show when reply creation fails' do
      scipi = FactoryBot.create(:scipi, :published)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)
      message = FactoryBot.create(:message, survey: scipi, user: expert, subject: 'Test Subject')

      sign_in(expert)

      assert_no_difference -> { Message.count } do
        post scipi_message_replies_path(scipi, message),
             params: {
               message: {
                 content: ''
               }
             }
      end

      assert_response :unprocessable_content
    end
  end
end

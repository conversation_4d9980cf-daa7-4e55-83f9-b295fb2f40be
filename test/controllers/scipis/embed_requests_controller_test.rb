# frozen_string_literal: true

require 'test_helper'

module Scipis
  class EmbedRequestsControllerTest < ActionDispatch::IntegrationTest
    include ActiveJob::TestHelper
    include ActionCable::TestHelper

    test 'create new embed request' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(expert:, sign_now_contract_id: 'abc123')

      sign_in(expert)

      assert_enqueued_with(job: Contracts::RequestEmbedLinkJob, args: [{ panelist: }]) do
        post scipi_contract_embed_requests_path(scipi_id: scipi, format: :turbo_stream)
      end

      assert_response :success
      assert_select "#contract-status-#{panelist.id} .status-message", 'Loading your contract...'
      assert_not_nil panelist.reload.contract_embed_link_requested_at
      assert_nil panelist.contract_embed_link
    end

    test 'create embed request with existing expired embed request' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'abc123',
        contract_embed_link_requested_at: 1.day.ago,
        contract_embed_link: 'https://example.com/embed_link'
      )

      sign_in(expert)

      assert_changes -> { panelist.reload.contract_embed_link_requested_at } do
        assert_enqueued_with(job: Contracts::RequestEmbedLinkJob, args: [{ panelist: }]) do
          post scipi_contract_embed_requests_path(scipi_id: scipi, format: :turbo_stream)
        end
      end

      assert_response :success
      assert_select "#contract-status-#{panelist.id} .status-message", 'Loading your contract...'
      assert_nil panelist.contract_embed_link
    end

    test 'create embed request with existing un-expired embed request' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'abc123',
        contract_embed_link_requested_at: 1.minute.ago,
        contract_embed_link: 'https://example.com/embed_link'
      )

      sign_in(expert)

      assert_changes -> { panelist.reload.contract_embed_link_requested_at } do
        assert_enqueued_with(job: Contracts::RequestEmbedLinkJob, args: [{ panelist: }]) do
          post scipi_contract_embed_requests_path(scipi_id: scipi, format: :turbo_stream)
        end
      end

      assert_response :success
      assert_select "#contract-status-#{panelist.id} .status-message", 'Loading your contract...'
      assert_nil panelist.contract_embed_link
    end

    test 'create embed request with existing pending (in-flight) embed request' do
      scipi = FactoryBot.create(:scipi)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'abc123',
        contract_embed_link_requested_at: 1.minute.ago,
        contract_embed_link: nil # another request is in flight
      )

      sign_in(expert)

      assert_changes -> { panelist.reload.contract_embed_link_requested_at } do
        assert_enqueued_with(job: Contracts::RequestEmbedLinkJob, args: [{ panelist: }]) do
          post scipi_contract_embed_requests_path(scipi_id: scipi, format: :turbo_stream)
        end
      end

      assert_response :success
      assert_select "#contract-status-#{panelist.id} .status-message", 'Loading your contract...'
      assert_nil panelist.contract_embed_link
    end
  end
end

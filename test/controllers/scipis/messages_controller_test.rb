# frozen_string_literal: true

require 'test_helper'

module Scipis
  class MessagesControllerTest < ActionDispatch::IntegrationTest
    test 'should get new' do
      scipi = FactoryBot.create(:scipi, :published)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)

      sign_in(expert)

      get new_scipi_message_path(scipi)

      assert_response :success
    end

    test 'should create message' do
      scipi = FactoryBot.create(:scipi, :published)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)

      sign_in(expert)

      assert_difference -> { Message.count } do
        post scipi_messages_path(scipi),
             params: {
               message: {
                 subject: 'Test Subject',
                 content: 'Test message content',
                 attachments: [fixture_file_upload('test_attachment.pdf', 'application/pdf')]
               }
             }
      end

      assert_redirected_to scipi_path(scipi)
      assert_match 'Message sent successfully', flash[:notice]
    end

    test 'should show message' do
      scipi = FactoryBot.create(:scipi, :published)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)
      message = FactoryBot.create(:message, survey: scipi, user: expert)

      sign_in(expert)

      get scipi_message_path(scipi, message.id)

      assert_response :success
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ConfidentialityControllerTest < ActionDispatch::IntegrationTest
    test 'accept confidentiality agreement' do
      scipi = FactoryBot.create(:scipi)
      user = FactoryBot.create(:user)
      scipi.auditors << user

      sign_in(user)

      assert_difference -> { user.confidentiality_agreements.where.not(accepted_at: nil).count } do
        post scipi_confidentiality_agreement_path(scipi), params: { confidentiality_agreement: { accepted: '1' } }
      end
    end
  end
end

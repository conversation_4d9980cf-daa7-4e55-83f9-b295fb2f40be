# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ResultChartsControllerTest < ActionDispatch::IntegrationTest
    test 'show renders chart for valid signed result' do
      scipi = FactoryBot.create(:scipi, :published)
      result_definition = FactoryBot.create(:result_definition, question_group: scipi)

      signed_id = result_definition.signed_id(purpose: :chart_image)

      get scipi_result_chart_path(scipi, result_signed_id: signed_id)

      assert_response :success
    end

    test 'show returns 404 for invalid signed_id' do
      scipi = FactoryBot.create(:scipi, :published)

      get scipi_result_chart_path(scipi, result_signed_id: 'invalid_signature')

      assert_response :not_found
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ApplicationsControllerTest < ActionDispatch::IntegrationTest
    # Double clicks cannot be easily tested using a system test so this
    # sets up the situation where someone has applied and reapplies.
    # This happens in the wild either by double clicks, or having more than one
    # tab open on the apply page.
    test 'ignore re-applications' do
      user = users(:expert)
      survey = FactoryBot.create(:survey, :scipi, :published)
      survey.applicants.create!(user:)

      sign_in(user)

      post scipi_applications_path(survey, invite: { applicant_statement: '' })

      assert_redirected_to edit_profile_path(return_scipi: survey)
      assert_match 'Thank you for applying', flash[:warning]
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class VotesControllerTest < ActionController::TestCase
  test 'up vote vote comment' do
    survey = FactoryBot.create(:survey,
                               :general_participation,
                               :published,
                               :results_published)
    result = FactoryBot.create(:result_definition, survey:)
    comment = FactoryBot.create(:comment, debate_topic: result)
    voter = FactoryBot.create(:expert)

    sign_in voter

    assert_difference('Vote.count') do
      post :create,
           params: {
             format: :js,
             comment_id: comment.id,
             yay: 'true'
           }
    end

    assert_response :success
    assert_equal 1, comment.reload.score
  end

  test 'cannot up vote vote comment when comments are closed' do
    closes_at = 2.days.ago

    survey = FactoryBot.create(:survey,
                               :general_participation,
                               :published,
                               :results_published,
                               comments_close_date: closes_at)
    result = FactoryBot.create(:result_definition, survey:)
    comment = FactoryBot.create(:comment, debate_topic: result)
    voter = FactoryBot.create(:expert)

    sign_in voter

    travel_to 1.second.after(closes_at.to_date.in_last_time_zone.end_of_day) do
      assert_no_difference('Vote.count') do
        post :create,
             params: {
               format: :js,
               comment_id: comment.id,
               yay: 'true'
             }
      end
    end

    assert_response :redirect
    assert_equal 0, comment.reload.score
  end

  test 'down vote comment' do
    survey = FactoryBot.create(:survey,
                               :general_participation,
                               :published,
                               :results_published)
    result = FactoryBot.create(:result_definition, survey:)
    comment = FactoryBot.create(:comment, debate_topic: result)
    voter = FactoryBot.create(:expert)

    sign_in voter

    assert_difference('Vote.count') do
      post :create,
           params: {
             format: :js,
             comment_id: comment.id,
             yay: 'false'
           }
    end

    assert_response :success
    assert_equal(-1, comment.reload.score)
  end

  test 'undo vote' do
    survey = FactoryBot.create(:survey,
                               :general_participation,
                               :published,
                               :results_published)
    result = FactoryBot.create(:result_definition, survey:)
    comment = FactoryBot.create(:comment, debate_topic: result)
    voter = FactoryBot.create(:expert)
    vote = Vote.create!(comment:, user: voter, yay: `true`)

    sign_in voter

    assert_difference('Vote.count', -1) do
      delete :destroy, params: { format: :js, id: vote.id }
    end
    assert_equal 0, comment.reload.score
  end

  test 'cannot vote on own comment' do
    survey = FactoryBot.create(:survey,
                               :general_participation,
                               :published,
                               :results_published)
    commenter = FactoryBot.create(:expert)
    result = FactoryBot.create(:result_definition, survey:)
    comment = FactoryBot.create(:comment, user: commenter, debate_topic: result)

    sign_in commenter

    assert_no_difference('Vote.count') do
      post :create,
           params: {
             format: :js,
             comment_id: comment.id,
             yay: 'false'
           }
    end

    assert_response :redirect
    assert_equal 0, comment.reload.score
  end
end

# frozen_string_literal: true

require 'test_helper'

module Pings
  class AcceptancesControllerTest < ActionDispatch::IntegrationTest
    test 'accept answer on paid ping' do
      ping = FactoryBot.create(:ping, :paid, :published)
      answer = FactoryBot.create(:ping_answer, ping: ping)

      sign_in ping.author

      assert_enqueued_with(job: SendAnswerSelectedNotificationsJob, args: [ping]) do
        post pings_answer_acceptance_path(answer)
      end

      assert_redirected_to ping_path(ping)
      assert answer.reload.accepted?
    end

    test 'accept answer on public ping' do
      ping = FactoryBot.create(:ping, :published)
      answer = FactoryBot.create(:ping_answer, ping: ping)

      sign_in ping.author

      assert_enqueued_with(job: CreateAcceptedAnswerNotificationsJob, args: [ping, answer]) do
        post pings_answer_acceptance_path(answer)
      end

      assert_redirected_to ping_path(ping)
      assert answer.reload.accepted?
    end

    test 'undo acceptance' do
      ping = FactoryBot.create(:ping, :published)
      answer = FactoryBot.create(:ping_answer, :accepted, ping: ping)

      sign_in ping.author

      delete pings_answer_acceptance_path(answer)

      assert_redirected_to ping_path(ping)
      assert_not answer.reload.accepted?
    end
  end
end

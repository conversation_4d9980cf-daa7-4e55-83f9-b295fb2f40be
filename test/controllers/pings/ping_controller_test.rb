# frozen_string_literal: true

require 'test_helper'

module Pings
  class PingControllerTest < ActionDispatch::IntegrationTest
    test 'show excludes hidden answers by default for regular users' do
      ping = create(:ping, :published)
      visible_answer = create(:ping_answer, ping:)
      hidden_answer = create(:ping_answer, :hidden, ping:)
      user = create(:user, :confirmed, :with_display_name)

      sign_in user
      get ping_path(ping)

      assert_response :success
      answers = assigns(:answers)
      assert_includes answers, visible_answer
      assert_not_includes answers, hidden_answer
    end

    test 'show includes hidden answers when show_hidden param is present' do
      ping = create(:ping, :published)
      visible_answer = create(:ping_answer, ping:)
      hidden_answer = create(:ping_answer, :hidden, ping:)
      user = create(:user, :confirmed, :with_display_name)

      sign_in user
      get ping_path(ping), params: { show_hidden: 'true' }

      assert_response :success
      answers = assigns(:answers)
      assert_includes answers, visible_answer
      assert_includes answers, hidden_answer
    end

    test 'show includes hidden answers for ping author without show_hidden param' do
      author = create(:user, :confirmed, :with_display_name)
      ping = create(:ping, :published, author: author)
      visible_answer = create(:ping_answer, ping:)
      hidden_answer = create(:ping_answer, :hidden, ping:)

      sign_in author
      get ping_path(ping)

      assert_response :success
      answers = assigns(:answers)
      assert_includes answers, visible_answer
      assert_includes answers, hidden_answer
    end

    test 'show includes hidden answers for pings admin without show_hidden param' do
      admin = create(:pings_admin)
      ping = create(:ping, :published)
      visible_answer = create(:ping_answer, ping:)
      hidden_answer = create(:ping_answer, :hidden, ping:)

      sign_in admin
      get ping_path(ping)

      assert_response :success
      answers = assigns(:answers)
      assert_includes answers, visible_answer
      assert_includes answers, hidden_answer
    end

    test 'show preserves existing ordering with hidden answers included' do
      ping = create(:ping, :published)

      # Create answers with different scores to test accepted_first ordering
      regular_answer = create(:ping_answer, ping:, upvote_count: 2)
      accepted_answer = create(:ping_answer, :accepted, ping:, upvote_count: 1)
      hidden_answer = create(:ping_answer, :hidden, ping:, upvote_count: 3)

      admin = create(:pings_admin)
      sign_in admin
      get ping_path(ping)

      assert_response :success
      answers = assigns(:answers)

      # Should include all answers
      assert_includes answers, regular_answer
      assert_includes answers, accepted_answer
      assert_includes answers, hidden_answer

      # Should maintain accepted_first ordering (accepted answer should be first)
      answers_array = answers.to_a
      assert_equal accepted_answer, answers_array.first
    end

    test 'show preserves newest-first ordering when requested' do
      ping = create(:ping, :published)

      # Create answers at different times
      oldest_answer = create(:ping_answer, ping:, created_at: 3.days.ago)
      newest_answer = create(:ping_answer, ping:, created_at: 1.day.ago)
      hidden_answer = create(:ping_answer, :hidden, ping:, created_at: 2.days.ago)

      admin = create(:pings_admin)
      sign_in admin
      get ping_path(ping), params: { answer_order: 'newest-first' }

      assert_response :success
      answers = assigns(:answers)

      # Should include all answers
      assert_includes answers, oldest_answer
      assert_includes answers, newest_answer
      assert_includes answers, hidden_answer

      # Should maintain newest-first ordering
      answers_array = answers.to_a
      assert_equal newest_answer, answers_array.first
      assert_equal oldest_answer, answers_array.last
    end

    test 'show_hidden param works' do
      ping = create(:ping, :published)
      hidden_answer = create(:ping_answer, :hidden, ping:)
      user = create(:user, :confirmed, :with_display_name)

      sign_in user

      get ping_path(ping), params: { show_hidden: true }

      assert_response :success
      answers = assigns(:answers)
      assert_includes answers, hidden_answer, 'Failed with show_hidden'
    end
  end
end

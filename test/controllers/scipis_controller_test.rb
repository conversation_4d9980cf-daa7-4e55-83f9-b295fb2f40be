# frozen_string_literal: true

require 'test_helper'

class ScipisControllerTest < ActionDispatch::IntegrationTest
  test 'logged out user cannot view a draft scipi landing page' do
    draft = FactoryBot.create(:scipi, :draft)

    post scipi_path(draft)

    assert_response :not_found
  end

  test 'logged in user cannot view a draft scipi landing page' do
    user = FactoryBot.create(:expert)
    draft = FactoryBot.create(:scipi, :draft)

    sign_in(user)

    post scipi_path(draft)

    assert_response :not_found
  end
end

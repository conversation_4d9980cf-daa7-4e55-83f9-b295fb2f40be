# frozen_string_literal: true

require 'test_helper'

class PingsControllerTest < ActionDispatch::IntegrationTest
  test 'index renders successfully with correct instance variables' do
    published_ping = create(:ping, :published)
    hidden_ping = create(:ping, :published, :hidden)

    get pings_path

    assert_response :success
    assert_template :index

    assert_not_nil assigns(:pings)
    assert_not_nil assigns(:pings_filter_navs)
    assert_not_nil assigns(:tags_with_counts)
    assert_not_nil assigns(:current_tags)

    pings = assigns(:pings)
    assert_includes pings, published_ping
    assert_not_includes pings, hidden_ping
  end

  test 'index with my_pings filter redirects to login when not authenticated' do
    get pings_path, params: { filter: 'my_pings' }

    assert_redirected_to login_path
  end

  test "index with my_pings filter shows user's pings when authenticated" do
    user = create(:user, :confirmed, :with_display_name)
    user_ping = create(:ping, :published, author: user)
    published_ping = create(:ping, :published)
    sign_in user

    get pings_path, params: { filter: 'my_pings' }

    assert_response :success

    pings = assigns(:pings)
    assert_includes pings, user_ping
    assert_not_includes pings, published_ping
  end

  test 'index with single tag filter includes only pings with that tag' do
    expertise = create(:expertise)

    ping_with_tag = create(:ping, :published)
    ping_with_tag.expertises << expertise

    untagged_ping = create(:ping, :published)

    get pings_path, params: { tag_id: [expertise.id] }

    assert_response :success

    pings = assigns(:pings)
    assert_includes pings, ping_with_tag
    assert_not_includes pings, untagged_ping
  end

  test 'index with multiple tag filters includes pings with any of those tags (OR condition)' do
    expertise1 = create(:expertise)
    expertise2 = create(:expertise)

    ping_with_expertise1 = create(:ping, :published)
    ping_with_expertise1.expertises << expertise1

    ping_with_expertise2 = create(:ping, :published)
    ping_with_expertise2.expertises << expertise2

    untagged_ping = create(:ping, :published)

    get pings_path, params: { tag_id: [expertise1.id, expertise2.id] }

    assert_response :success

    # Check assigned pings - should include ANY ping with EITHER expertise
    pings = assigns(:pings)
    assert_includes pings, ping_with_expertise1
    assert_includes pings, ping_with_expertise2
    assert_not_includes pings, untagged_ping
  end

  test 'pagination works correctly' do
    # rubocop:disable FactoryBot/ExcessiveCreateList
    create_list(:ping, 15, :published)
    # rubocop:enable FactoryBot/ExcessiveCreateList

    get pings_path, params: { page: 2 }

    assert_response :success

    pings = assigns(:pings)
    assert_equal 15, pings.total_count
    assert_equal 2, pings.current_page
    assert_equal 5, pings.size
    assert pings.total_pages > 1
  end

  test 'handles missing ping gracefully' do
    get ping_path(id: 99_999)

    assert_redirected_to pings_path
    assert_equal I18n.t('pings.show.not_found'), flash[:notice]
  end

  test 'redirects with notice when accessing hidden ping' do
    hidden_ping = create(:ping, :published, :hidden)

    get ping_path(hidden_ping)

    assert_redirected_to pings_path
    assert_equal I18n.t('pings.show.hidden'), flash[:notice]
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  class ContractTemplatesControllerTest < ActionDispatch::IntegrationTest
    setup do
      @admin = FactoryBot.create(:admin)
      @contract_template = FactoryBot.create(:contract_template)
      sign_in @admin
    end

    test 'should get index' do
      get admin_contract_templates_url

      assert_response :success
    end

    test 'should get edit' do
      get edit_admin_contract_template_url(@contract_template)

      assert_response :success
    end

    test 'should create contract template with valid params' do
      template_file = fixture_file_upload('signnow_test_template.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
      signnow_field_definitions = <<~FIELDS.squish
        [{
          "tag_name": "panelist_signature",
          "role": "signer",
          "label": "Expert Panelist Signature",
          "required": true,
          "type": "signature",
          "width": 200,
          "height": 20
        }]
      FIELDS

      assert_difference 'ContractTemplate.count', 1 do
        post admin_contract_templates_url,
             params: {
               contract_template: {
                 template_file:,
                 signnow_field_definitions:
               }
             }
      end

      assert_redirected_to admin_contract_templates_url
      assert_equal 'New template saved', flash[:notice]
    end

    test 'should not create contract template with invalid params' do
      assert_no_difference 'ContractTemplate.count' do
        post admin_contract_templates_url, params: {
          contract_template: {
            template_file: nil,
            signnow_field_definitions: ''
          }
        }
      end

      assert_response :unprocessable_content
    end

    test 'should update contract template with valid params' do
      new_definitions = '[{"tag_name": "new_signature"}]'

      patch admin_contract_template_url(@contract_template), params: {
        contract_template: {
          signnow_field_definitions: new_definitions
        }
      }

      assert_redirected_to admin_contract_templates_url
      assert_equal 'Template updated', flash[:notice]
      assert_equal new_definitions, @contract_template.reload.signnow_field_definitions
    end

    test 'should not update contract template with invalid params' do
      patch admin_contract_template_url(@contract_template), params: {
        contract_template: {
          signnow_field_definitions: nil
        }
      }

      assert_response :unprocessable_content
    end

    test 'should destroy contract template' do
      assert_difference 'ContractTemplate.count', -1 do
        delete admin_contract_template_url(@contract_template)
      end

      assert_redirected_to admin_contract_templates_url
      assert_equal 'Template deleted', flash[:notice]
    end

    test 'should handle destroy failure when contract template is in use' do
      # Create a SciPi that uses this contract template
      FactoryBot.create(:scipi, contract_template: @contract_template)

      assert_no_difference 'ContractTemplate.count' do
        delete admin_contract_template_url(@contract_template)
      end

      assert_response :unprocessable_content
    end
  end
end

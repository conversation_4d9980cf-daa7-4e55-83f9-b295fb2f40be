# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class RoundsControllerTest < ActionDispatch::IntegrationTest
      test 'index' do
        round = FactoryBot.create(:question_round)
        scipi = round.scipi

        sign_in scipi.created_by

        get admin_scipi_rounds_path(scipi)

        assert_response :ok
      end

      test 'new' do
        scipi = FactoryBot.create(:scipi)

        sign_in scipi.created_by

        get new_admin_scipi_round_path(scipi)

        assert_response :ok
      end

      test 'creates a new round' do
        survey = FactoryBot.create(:scipi)
        admin = survey.created_by
        setup_initial_rounds(survey)

        sign_in admin

        assert_difference -> { survey.rounds.count } do
          post admin_scipi_rounds_path(survey),
               params: {
                 round: {
                   name: 'New Roundarino',
                   type: ::Surveys::Rounds::Question.name
                 }
               }
        end

        assert_redirected_to admin_scipi_rounds_path(survey)
      end

      test 'edit' do
        round = FactoryBot.create(:question_round)
        scipi = round.scipi

        sign_in scipi.created_by

        get edit_admin_scipi_round_path(scipi, round.number)

        assert_response :ok
      end

      test 'update round' do
        round = FactoryBot.create(:question_round)
        scipi = round.scipi
        opens_on = 1.week.from_now.to_date
        closes_on = 2.weeks.from_now.to_date

        sign_in scipi.created_by

        patch admin_scipi_round_path(scipi, round.number),
              params: {
                round: {
                  name: 'New name',
                  opens_at_date: opens_on.strftime('%Y-%m-%d'),
                  opens_at_hour: 9,
                  closes_at_date: closes_on.strftime('%Y-%m-%d'),
                  closes_at_hour: 17
                }
              }

        assert_redirected_to admin_scipi_rounds_path(scipi)

        round = round.reload

        assert_equal 'New name', round.name
        assert_equal opens_on, round.opens_at.to_date
        assert_equal 9, round.opens_at.hour
        assert_equal closes_on, round.closes_at.to_date
        assert_equal 16, round.closes_at.hour
        assert_equal 59, round.closes_at.min
        assert_equal 59, round.closes_at.sec
      end

      test 'deletes the last round' do
        scipi = FactoryBot.create(:scipi)
        admin = scipi.created_by
        setup_initial_rounds(scipi)
        last_round = scipi.rounds.last

        sign_in admin

        assert_difference -> { scipi.rounds.count }, -1 do
          delete admin_scipi_round_path(scipi, last_round.number)
        end

        assert_redirected_to admin_scipi_rounds_path(scipi)
        assert_not scipi.rounds.exists?(last_round.id)
      end

      private

      def setup_initial_rounds(scipi)
        scipi.rounds.create!(
          [
            { name: 'Initial Charge Questions', type: ::Surveys::Rounds::Question.name, position: 1 },
            { name: 'Debate', type: ::Surveys::Rounds::Debate.name, position: 2 },
            { name: 'Revisit Charge Questions', type: ::Surveys::Rounds::Question.name, position: 3 }
          ]
        )
      end
    end
  end
end

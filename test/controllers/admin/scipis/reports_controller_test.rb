# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class ReportsControllerTest < ActionDispatch::IntegrationTest
      test 'index' do
        scipi = FactoryBot.create(:scipi)

        sign_in scipi.created_by

        get admin_scipi_reports_path(scipi)

        assert_response :success
      end

      test 'create' do
        scipi = FactoryBot.create(:scipi)

        sign_in scipi.created_by

        assert_difference('::Scipis::Report.count') do
          post admin_scipi_reports_path(scipi),
               params: {
                 report: {
                   prepared_for: 'Test Client',
                   final: true,
                   certified: true
                 }
               }
        end

        assert_redirected_to admin_scipi_reports_path(scipi)
      end

      test 'destroy' do
        report = FactoryBot.create(:scipi_report)
        scipi = report.scipi

        sign_in scipi.created_by

        assert_difference('::Scipis::Report.count', -1) do
          delete admin_scipi_report_path(scipi, report)
        end

        assert_redirected_to admin_scipi_reports_path(scipi)
      end
    end
  end
end

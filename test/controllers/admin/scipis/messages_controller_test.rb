# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class MessagesControllerTest < ActionDispatch::IntegrationTest
      test 'admin can view messages index page' do
        scipi = FactoryBot.create(:scipi, :published)
        admin = FactoryBot.create(:admin)

        sign_in(admin)

        get admin_scipi_messages_path(scipi)

        assert_response :success
      end

      test 'get new message page' do
        scipi = FactoryBot.create(:scipi, :published)
        admin = FactoryBot.create(:admin)

        sign_in(admin)

        get new_admin_scipi_message_path(scipi)

        assert_response :success
      end

      test 'admin can view message show page' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, survey: scipi, user: expert, subject: 'Test Subject')

        sign_in(admin)

        get admin_scipi_message_path(scipi, message)

        assert_response :success
      end

      test 'admin can create bulk message' do
        scipi = FactoryBot.create(:scipi, :published)
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)

        sign_in(admin)

        assert_difference 'Message.count', 2 do
          post admin_scipi_messages_path(scipi), params: {
            bulk_message: {
              subject: 'Test Subject',
              content: 'Test content',
              recipient_ids: [expert1.id, expert2.id],
              attachments: [fixture_file_upload('test_attachment.pdf', 'application/pdf')]
            }
          }
        end

        assert_redirected_to admin_scipi_messages_path(scipi)
        assert_match 'Message(s) sent successfully.', flash[:notice]

        messages = Message.last(2)
        messages.each do |message|
          assert_equal scipi, message.survey
          assert_equal admin, message.sent_by
          assert_equal 'Test Subject', message.subject
          assert_equal 'Test content', message.content.to_plain_text
          assert message.resolved?, 'Admin messages should be resolved by default'
        end
      end

      test 'admin can create bulk message and mark unresolved' do
        scipi = FactoryBot.create(:scipi, :published)
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)

        sign_in(admin)

        assert_difference 'Message.count', 2 do
          post admin_scipi_messages_path(scipi), params: {
            bulk_message: {
              subject: 'Test Subject',
              content: 'Test content',
              recipient_ids: [expert1.id, expert2.id]
            },
            send_unresolved: '1'
          }
        end

        assert_redirected_to admin_scipi_messages_path(scipi)
        assert_match 'Message(s) sent successfully. Messages have been marked as unresolved.', flash[:notice]

        messages = Message.last(2)
        messages.each do |message|
          assert_equal scipi, message.survey
          assert_equal admin, message.sent_by
          assert_equal 'Test Subject', message.subject
          assert_equal 'Test content', message.content.to_plain_text
          assert message.unresolved?, 'Message should be explicitly unresolved'
        end
      end

      test 'admin should resolve message' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, :unresolved, survey: scipi, user: expert, subject: 'Test Subject')

        sign_in(admin)

        post resolve_admin_scipi_message_path(scipi, message)

        assert_redirected_to admin_scipi_message_path(scipi, message)
        assert_match 'Message has been resolved', flash[:notice]
      end

      test 'admin should unresolve message' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        other_admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, :resolved, survey: scipi, user: expert, subject: 'Test Subject', last_resolved_by: other_admin)

        sign_in(admin)

        post unresolve_admin_scipi_message_path(scipi, message)

        assert_redirected_to admin_scipi_message_path(scipi, message)
        assert_match 'Message has been marked as unresolved', flash[:notice]
      end

      test 'scipi admin can resolve message for owned scipi' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        scipi_admin = scipi.created_by # The scipi owner/admin
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, :unresolved, survey: scipi, user: expert, subject: 'Test Subject')

        sign_in(scipi_admin)

        post resolve_admin_scipi_message_path(scipi, message)

        assert_redirected_to admin_scipi_message_path(scipi, message)
        assert_match 'Message has been resolved', flash[:notice]

        message.reload
        assert message.resolved?, 'Message should be resolved after action'
        assert_equal scipi_admin, message.last_resolved_by, 'Message should be resolved by the scipi admin'
      end

      test 'scipi admin cannot resolve message for non-owned scipi' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        other_scipi_admin = FactoryBot.create(:scipi_admin)
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, :unresolved, survey: scipi, user: expert, subject: 'Test Subject')

        sign_in(other_scipi_admin)

        post resolve_admin_scipi_message_path(scipi, message)

        assert_response :redirect
      end

      test 'admin can bulk resolve messages' do
        scipi = FactoryBot.create(:scipi, :published)
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert: expert1)
        scipi.panelists.create!(expert: expert2)
        message1 = FactoryBot.create(:message, :unresolved, survey: scipi, user: expert1)
        message2 = FactoryBot.create(:message, :unresolved, survey: scipi, user: expert2)

        sign_in(admin)

        post bulk_resolve_admin_scipi_messages_path(scipi), params: { message_ids: [message1.id, message2.id] }

        assert_redirected_to admin_scipi_messages_path(scipi)
        assert_match '2 message(s) have been resolved', flash[:notice]

        message1.reload
        message2.reload
        assert message1.resolved?, 'First message should be resolved'
        assert message2.resolved?, 'Second message should be resolved'
        assert_equal admin, message1.last_resolved_by, 'First message should be resolved by admin'
        assert_equal admin, message2.last_resolved_by, 'Second message should be resolved by admin'
      end

      test 'admin can bulk unresolve messages' do
        scipi = FactoryBot.create(:scipi, :published)
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        other_admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert: expert1)
        scipi.panelists.create!(expert: expert2)
        message1 = FactoryBot.create(:message, :resolved, survey: scipi, user: expert1, last_resolved_by: other_admin)
        message2 = FactoryBot.create(:message, :resolved, survey: scipi, user: expert2, last_resolved_by: other_admin)

        sign_in(admin)

        post bulk_unresolve_admin_scipi_messages_path(scipi), params: { message_ids: [message1.id, message2.id] }

        assert_redirected_to admin_scipi_messages_path(scipi)
        assert_match '2 message(s) have been marked as unresolved', flash[:notice]

        message1.reload
        message2.reload
        assert_not message1.resolved?, 'First message should be unresolved'
        assert_not message2.resolved?, 'Second message should be unresolved'
        assert_nil message1.last_resolved_by, 'First message should have no resolved_by'
        assert_nil message2.last_resolved_by, 'Second message should have no resolved_by'
      end
    end
  end
end

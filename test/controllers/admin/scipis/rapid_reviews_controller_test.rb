# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class RapidReviewsControllerTest < ActionDispatch::IntegrationTest
      test 'should get new' do
        admin = FactoryBot.create(:admin)

        sign_in admin

        get new_admin_scipis_rapid_review_path

        assert_response :success
      end

      test 'should create' do
        admin = FactoryBot.create(:admin)

        sign_in admin

        assert_difference('QuestionGroup.count') do
          assert_difference('Tagging.count') do
            assert_difference('::Surveys::ReviewMaterial.count') do
              assert_difference('::Surveys::Round.count', 3) do
                post admin_scipis_rapid_reviews_path,
                     params: {
                       scipi: {
                         name: 'Test Rapid Review',
                         description: 'Test Description',
                         expertise_ids: [expertises(:expertise1).id],
                         review_materials_attributes: [
                           { required: '1', file: fixture_file_upload('test_attachment.pdf') }
                         ],
                         quick_rounds_start_date: 2.days.from_now.strftime('%Y-%m-%d'),
                         quick_rounds_start_hour: '9',
                         quick_rounds_duration_days: '2'
                       }
                     }
              end
            end
          end
        end

        assert_redirected_to admin_survey_path(QuestionGroup.last)
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class RepliesControllerTest < ActionDispatch::IntegrationTest
      test 'admin should create reply' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, survey: scipi, user: expert, subject: 'Test Subject')

        sign_in(admin)

        assert_difference -> { Message.count } do
          post admin_scipi_message_replies_path(scipi, message),
               params: {
                 message: {
                   content: 'Admin reply content',
                   attachments: [fixture_file_upload('test_attachment.pdf', 'application/pdf')]
                 }
               }
        end

        assert_redirected_to admin_scipi_message_path(scipi, message)
        assert_match 'Reply sent successfully', flash[:notice]

        reply = Message.last
        assert_equal message, reply.parent
        assert_equal expert, reply.user # Reply belongs to the original expert
        assert_equal admin, reply.sent_by # But sent by admin
      end

      test 'admin should re-render show when reply creation fails' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        admin = FactoryBot.create(:admin)
        scipi.panelists.create!(expert:)
        message = FactoryBot.create(:message, survey: scipi, user: expert, subject: 'Test Subject')

        sign_in(admin)

        # Send invalid reply (missing content)
        assert_no_difference -> { Message.count } do
          post admin_scipi_message_replies_path(scipi, message),
               params: {
                 message: {
                   content: '' # Invalid: empty content
                 }
               }
        end

        assert_response :unprocessable_content
        assert_template 'admin/scipis/messages/show'
      end
    end
  end
end

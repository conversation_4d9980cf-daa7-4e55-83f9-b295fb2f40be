# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class ObserverAssignmentsControllerTest < ActionDispatch::IntegrationTest
      test 'add existing user as an observer using their name' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        user = FactoryBot.create(:user)
        sign_in admin

        assert_difference(-> { scipi.observers.count }) do
          post admin_survey_observers_path(scipi), params: { observer: { user_id: user.id } }
        end
        assert_response :redirect
        assert_redirected_to admin_survey_path(scipi)
        assert_equal user, scipi.observers.last.user
      end

      test 'add existing user as an observer using their email' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        sign_in admin

        assert_difference(-> { User.count }) do
          assert_difference(-> { scipi.observers.count }) do
            post admin_survey_observers_path(scipi), params: { observer: { email: '<EMAIL>' } }
          end
        end
        assert_response :redirect
        assert_redirected_to admin_survey_path(scipi)
      end

      test 'create a user and add as an observer' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        sign_in admin
        email = Faker::Internet.email

        assert_difference([-> { scipi.observers.count }, -> { User.count }]) do
          post admin_survey_observers_path(scipi), params: { observer: { email: } }
        end
        assert_response :redirect
        assert_redirected_to admin_survey_path(scipi)
        assert_equal email, scipi.observers.last.user.email
      end

      test 'ensure proper re-render on validation error' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        sign_in admin

        assert_no_difference(-> { scipi.observers.count }) do
          post admin_survey_observers_path(scipi), params: { observer: { user_id: nil, email: nil } }
        end
        assert_response :unprocessable_content
        assert_includes response.body, 'You must either select an existing user or provide an email to create a new one'
      end

      test 'delete observer' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        user = FactoryBot.create(:user)
        observer = scipi.observers.create!(user:)
        sign_in admin

        assert_difference(-> { scipi.observers.count }, -1) do
          delete admin_surveys_observer_path(observer)
        end
        assert_response :redirect
        assert_redirected_to admin_survey_path(scipi)
      end
    end
  end
end

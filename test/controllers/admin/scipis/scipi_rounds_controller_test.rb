# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class ScipiRoundsControllerTest < ActionDispatch::IntegrationTest
      test 'creates default SciPi rounds' do
        scipi = FactoryBot.create(:scipi)

        sign_in scipi.created_by

        assert_difference -> { scipi.rounds.count }, 3 do
          post admin_scipi_scipi_rounds_path(scipi)
        end

        assert_redirected_to admin_scipi_rounds_path(scipi)
      end
    end
  end
end

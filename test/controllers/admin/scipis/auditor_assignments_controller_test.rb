# frozen_string_literal: true

require 'test_helper'

module Admin
  module Scipis
    class AuditorAssignmentsControllerTest < ActionDispatch::IntegrationTest
      test 'add auditor to scipi' do
        scipi = FactoryBot.create(:scipi)
        admin = scipi.created_by
        auditor = FactoryBot.create(:user)

        sign_in admin

        post admin_scipi_auditor_assignments_path(scipi), params: { auditor_assignment: { user_id: auditor.id } }

        assert_response :redirect
        assert scipi.auditors.include?(auditor)
      end

      test 'remove auditor from scipi' do
        scipi = FactoryBot.create(:scipi)
        admin = scipi.created_by
        auditor = FactoryBot.create(:user)
        auditor_assigment = scipi.auditor_assignments.create!(user: auditor)

        sign_in admin

        delete admin_scipi_auditor_assignment_path(scipi, auditor_assigment.id)

        assert_response :redirect
        assert_not scipi.auditors.include?(auditor)
      end
    end
  end
end

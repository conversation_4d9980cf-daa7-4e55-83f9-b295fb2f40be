# frozen_string_literal: true

require 'test_helper'

module Admin
  class SurveysControllerTest < ActionDispatch::IntegrationTest
    test 'should get index' do
      _scipi = FactoryBot.create(:scipi) # Add a SciPi so that the full view renders
      admin = FactoryBot.create(:scipi_admin)
      sign_in admin

      get admin_surveys_path

      assert_response :success
    end

    test 'should get show' do
      scipi = FactoryBot.create(:scipi)
      sign_in scipi.created_by

      get admin_survey_path(scipi)

      assert_response :success
    end

    test 'should get new' do
      admin = FactoryBot.create(:admin) # only super-admins can create SciPis
      sign_in admin

      get new_admin_survey_path

      assert_response :success
      assert_select 'form'
    end

    test 'should create survey' do
      scipi_branding = FactoryBot.create(:survey_branding, :scipi)
      expertise = FactoryBot.create(:expertise)
      admin = FactoryBot.create(:admin)
      sign_in admin

      assert_difference('QuestionGroup.count') do
        post admin_surveys_path,
             params: {
               survey: {
                 branding_id: scipi_branding.id,
                 name: 'Test Survey',
                 description: 'Test Description',
                 expertise_ids: [expertise.id],
                 invite_only: true
               }
             }
      end

      survey = QuestionGroup.last
      assert_redirected_to admin_survey_path(survey)
      assert_equal 'Test Survey', survey.name
      assert_equal 'Test Description', survey.description
    end

    test 'should not create survey with invalid params' do
      admin = FactoryBot.create(:admin)
      sign_in admin

      assert_no_difference('QuestionGroup.count') do
        post admin_surveys_path, params: { survey: { name: '', description: 'Test Description' } }
      end

      assert_response :unprocessable_content
    end

    test 'should get edit' do
      scipi = FactoryBot.create(:scipi)
      sign_in scipi.created_by

      get edit_admin_survey_path(scipi)

      assert_response :success
    end

    test 'should update survey' do
      scipi = FactoryBot.create(:scipi)
      expertise = FactoryBot.create(:expertise)
      admin = FactoryBot.create(:admin)
      sign_in admin

      patch admin_survey_path(scipi),
            params: {
              survey: {
                name: 'Updated Survey Name',
                description: 'Updated Description',
                expertise_ids: [expertise.id]
              }
            }

      assert_redirected_to admin_survey_path(scipi)
      scipi.reload
      assert_equal 'Updated Survey Name', scipi.name
      assert_equal 'Updated Description', scipi.description
    end

    test 'should not update survey with invalid params' do
      scipi = FactoryBot.create(:scipi)
      sign_in scipi.created_by

      patch admin_survey_path(scipi), params: { survey: { name: '' } }

      assert_response :unprocessable_content
    end

    test 'should destroy survey' do
      scipi = FactoryBot.create(:scipi)
      admin = FactoryBot.create(:admin) # only super-admins can delete SciPis
      sign_in admin

      assert_difference('QuestionGroup.count', -1) do
        delete admin_survey_path(scipi)
      end

      assert_redirected_to admin_surveys_path
    end
  end
end

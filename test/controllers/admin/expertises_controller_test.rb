# frozen_string_literal: true

require 'test_helper'

module Admin
  class ExpertisesControllerTest < ActionDispatch::IntegrationTest
    test 'should get index' do
      admin = FactoryBot.create(:admin)
      expertise = FactoryBot.create(:expertise)
      sign_in(admin)

      get admin_expertises_url

      assert_response :success
      assert_includes response.body, expertise.name
    end

    test 'should get index sorted by rank' do
      admin = FactoryBot.create(:admin)
      sign_in(admin)

      get admin_expertises_url(sort: 'rank')

      assert_response :success
    end

    test 'should get edit' do
      admin = FactoryBot.create(:admin)
      expertise = FactoryBot.create(:expertise)
      sign_in(admin)

      get edit_admin_expertise_url(expertise)

      assert_response :success
    end

    test 'should create expertise' do
      admin = FactoryBot.create(:admin)
      sign_in(admin)

      assert_difference('Expertise.count') do
        post admin_expertises_url,
             params: {
               expertise: {
                 name: 'New Expertise',
                 profile: true
               }
             }
      end

      assert_redirected_to admin_expertises_url
      assert_equal 'New Expertise', Expertise.order(:created_at).last.name
    end

    test 'should create expertise from suggestion' do
      admin = FactoryBot.create(:admin)
      suggestion = SuggestedExpertise.create!(term: 'Machine Learning', profile_count: 1)
      sign_in(admin)

      assert_difference('Expertise.count') do
        assert_difference('SuggestedExpertise.count', -1) do
          post admin_expertises_url, params: {
            expertise: {
              name: 'Machine Learning',
              profile: true
            },
            suggestion_id: suggestion.id
          }
        end
      end

      assert_redirected_to admin_expertises_url
    end

    test 'should not create expertise with invalid params' do
      admin = FactoryBot.create(:admin)
      sign_in(admin)

      assert_no_difference('Expertise.count') do
        post admin_expertises_url,
             params: {
               expertise: {
                 name: '',
                 profile: true
               }
             }
      end

      assert_response :unprocessable_content
    end

    test 'should update expertise' do
      admin = FactoryBot.create(:admin)
      expertise = FactoryBot.create(:expertise, name: 'Test Expertise')
      sign_in(admin)

      patch admin_expertise_url(expertise),
            params: {
              expertise: {
                name: 'Updated Expertise',
                profile: false
              }
            }

      assert_redirected_to admin_expertises_url
      expertise.reload
      assert_equal 'Updated Expertise', expertise.name
      assert_not expertise.profile
    end

    test 'should not update expertise with invalid params' do
      admin = FactoryBot.create(:admin)
      expertise = FactoryBot.create(:expertise, name: 'Test Expertise')
      sign_in(admin)
      original_name = expertise.name

      patch admin_expertise_url(expertise), params: {
        expertise: {
          name: '',
          profile: true
        }
      }

      assert_response :unprocessable_content
      expertise.reload
      assert_equal original_name, expertise.name
    end

    test 'should destroy expertise' do
      admin = FactoryBot.create(:admin)
      expertise = FactoryBot.create(:expertise, name: 'Test Expertise')
      sign_in(admin)

      assert_difference('Expertise.count', -1) do
        delete admin_expertise_url(expertise)
      end

      assert_redirected_to admin_expertises_url
    end

    test 'should destroy expertise with turbo stream' do
      admin = FactoryBot.create(:admin)
      expertise = FactoryBot.create(:expertise, name: 'Test Expertise')
      sign_in(admin)

      assert_difference('Expertise.count', -1) do
        delete admin_expertise_url(expertise), headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
      end

      assert_response :success
      assert_includes response.body, 'turbo-stream'
      assert_includes response.body, "expertise-#{expertise.id}-container"
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    module Participants
      class ExportsControllerTest < ActionDispatch::IntegrationTest
        test 'download CSV' do
          expert = FactoryBot.create(:expert)
          survey = FactoryBot.create(:survey)
          survey.submissions.create!(submitter: expert)
          filename = "#{survey.display_name} Participants.csv"

          sign_in survey.created_by

          get admin_survey_participants_export_path(survey),
              params: { participants: { expert_ids: [expert.id] }, format: :csv }

          assert_response :success
          assert_content_type 'csv'
          assert_content_disposition(filename:)
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class PanelistsControllerTest < ActionDispatch::IntegrationTest
      test 'add panelist' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        expert = FactoryBot.create(:expert)

        sign_in admin

        assert_difference(-> { scipi.panelists.count }) do
          post admin_survey_panelists_url(scipi.id), params: { panelist: { expert_id: expert.id, status: 'Approved' } }
        end

        assert_response :redirect
      end
    end
  end
end

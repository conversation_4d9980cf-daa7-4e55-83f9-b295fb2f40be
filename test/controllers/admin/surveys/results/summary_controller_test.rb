# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    module Results
      class SummaryControllerTest < ActionDispatch::IntegrationTest
        test 'preview uses default prompt when no custom prompt provided' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:survey, created_by: admin)
          result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses, survey:)

          sign_in admin

          # Mock the SummaryPrompt to return a specific default prompt
          default_prompt_text = "Default prompt for #{result_definition.title}"
          mock_summary_prompt = mock('SummaryPrompt')
          mock_summary_prompt.expects(:default_prompt).returns(default_prompt_text)
          ::Results::SummaryPrompt.expects(:new).with(result: result_definition).returns(mock_summary_prompt)

          # Verify <PERSON> gets called with the default prompt
          Claude.expects(:get_response).with(content: default_prompt_text).returns('<p>Default summary</p>')

          post preview_admin_result_summary_path(result_definition)

          assert_response :success
          assert_includes json_response['summary'], 'Default summary'
        end

        test 'preview uses custom prompt when provided' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:survey, created_by: admin)
          result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses, survey:)

          sign_in admin
          custom_prompt = 'Please summarize this data in bullet points'

          # The default prompt should NOT be called when custom prompt is provided
          ::Results::SummaryPrompt.expects(:new).never

          # Verify Claude gets called with the custom prompt
          Claude.expects(:get_response).with(content: custom_prompt).returns('<p>Custom bullet summary</p>')

          post preview_admin_result_summary_path(result_definition),
               params: { full_prompt: custom_prompt }

          assert_response :success
          assert_includes json_response['summary'], 'Custom bullet summary'
        end

        test 'preview returns error when generation fails' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:survey, created_by: admin)
          result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses, survey:)

          sign_in admin

          mock_generator = mock('SummaryGenerator')
          mock_generator.expects(:preview).raises(StandardError.new('API error'))
          ::Results::SummaryGenerator.expects(:new).with(result_definition).returns(mock_generator)

          post preview_admin_result_summary_path(result_definition)

          assert_response :unprocessable_content
          assert_equal 'API error', json_response['error']
        end

        test 'create generates and saves summary' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:survey, created_by: admin)
          result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses, survey:)

          sign_in admin

          # Mock the Claude module method with HTML response that the generator expects
          Claude.expects(:get_response).with(content: anything).returns('<p>Generated summary from Claude</p>')

          post admin_result_summary_path(result_definition)

          assert_redirected_to admin_survey_results_path(survey)
          assert_includes flash[:notice], 'Summary generated and saved'

          result_definition.reload
          assert_not_nil result_definition.result_summary
          assert_includes result_definition.result_summary.to_plain_text, 'Generated summary from Claude'
        end

        test 'create shows excerpt in flash notice' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:survey, created_by: admin)
          result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses, survey:)

          sign_in admin

          html_content = '<p>summary excerpt text</p>'

          Claude.expects(:get_response).with(content: anything).returns(html_content)

          post admin_result_summary_path(result_definition)

          assert_redirected_to admin_survey_results_path(survey)
          assert_includes flash[:notice], 'summary excerpt text'
        end

        test 'create handles generation errors' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:survey, created_by: admin)
          result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses, survey:)

          sign_in admin

          mock_generator = mock('SummaryGenerator')
          mock_generator.expects(:generate_and_save!).raises(StandardError.new('Generation failed'))
          ::Results::SummaryGenerator.expects(:new).with(result_definition).returns(mock_generator)

          post admin_result_summary_path(result_definition)

          assert_redirected_to admin_survey_results_path(survey)
          assert_includes flash[:alert], 'Failed to generate summary'
        end

        private

        def json_response
          @json_response ||= response.parsed_body
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class ResultsControllerTest < ActionDispatch::IntegrationTest
      test 'generate_summaries broadcasts processing status for each result' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, created_by: admin)
        result1 = FactoryBot.create(:result_definition, :question, survey:)
        result2 = FactoryBot.create(:result_definition, :question, survey:)

        sign_in admin

        # Verify broadcast is called for each result
        CustomResult.any_instance.expects(:broadcast_replace_to).twice.with do |survey_arg, stream, options|
          survey_arg == survey &&
            stream == :results_list &&
            options[:partial] == 'admin/surveys/results/summary_status_indicator' &&
            options[:locals][:processing] == true
        end

        post generate_summaries_admin_survey_results_path(survey),
             params: { result_ids: [result1.id, result2.id] }

        assert_response :ok
      end

      test 'generate_summaries handles empty result_ids' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, created_by: admin)

        sign_in admin

        assert_enqueued_jobs 1, only: ::Surveys::BulkSummaryGenerationJob do
          post generate_summaries_admin_survey_results_path(survey),
               params: { result_ids: [] }
        end

        assert_response :ok
      end

      test 'generate_summaries only processes results belonging to survey' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, created_by: admin)
        other_survey = FactoryBot.create(:survey, created_by: admin)
        result1 = FactoryBot.create(:result_definition, :question, survey:)
        result2 = FactoryBot.create(:result_definition, :question, survey: other_survey)

        sign_in admin

        # Mock the job to verify it only gets result1 (from the correct survey)
        ::Surveys::BulkSummaryGenerationJob.expects(:perform_later).with(survey, [result1.id])

        post generate_summaries_admin_survey_results_path(survey),
             params: { result_ids: [result1.id, result2.id] }

        assert_response :ok
      end

      test 'index sets up summary collection IDs correctly' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, created_by: admin)

        summarizable_type = FactoryBot.create(:result_type, :multiple_choice_responses)

        result_with_summary = FactoryBot.create(:result_definition, :question, survey:, result_type: summarizable_type)
        FactoryBot.create(:summary, custom_result: result_with_summary)

        result_without_summary = FactoryBot.create(:result_definition, :question, survey:, result_type: summarizable_type)

        result_non_summarizable = FactoryBot.create(:result_definition, :question, :diagnostic, survey:)

        sign_in admin

        get admin_survey_results_path(survey)

        assert_response :success

        results = assigns(:results)
        assert_includes results, result_with_summary
        assert_includes results, result_without_summary
        assert_includes results, result_non_summarizable

        assert_kind_of Array, assigns(:missing_summary_ids)
        assert_kind_of Array, assigns(:existing_summary_ids)
        assert_kind_of Array, assigns(:needs_generation_ids)

        assert_includes assigns(:existing_summary_ids), result_with_summary.id
        assert_not_includes assigns(:existing_summary_ids), result_without_summary.id

        assert_includes assigns(:missing_summary_ids), result_without_summary.id
        assert_not_includes assigns(:missing_summary_ids), result_with_summary.id
        assert_not_includes assigns(:missing_summary_ids), result_non_summarizable.id
      end
    end
  end
end

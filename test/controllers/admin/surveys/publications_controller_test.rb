# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class PublicationsControllerTest < ActionDispatch::IntegrationTest
      test 'publish survey' do
        expertise = FactoryBot.create(:expertise)
        survey = FactoryBot.create(:survey, :scipi, :draft, expertises: [expertise])

        sign_in survey.created_by

        assert_difference('QuestionGroup.published.count') do
          post admin_survey_publications_path(survey), params: { question_group: { published_at: 1.day.ago } }
        end
      end

      test 'unpublish survey' do
        survey = FactoryBot.create(:survey, :scipi, :invite_only, :published)

        sign_in survey.created_by

        delete admin_survey_path(survey)

        assert_difference('QuestionGroup.published.count', -1) do
          delete admin_survey_publication_path(survey)
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class SubmissionBackupsControllerTest < ActionDispatch::IntegrationTest
      test 'create backup' do
        survey = FactoryBot.create(:survey)
        _submission = FactoryBot.create(:submission, survey:)

        sign_in survey.created_by

        assert_difference('::Surveys::SubmissionBackup.count') do
          post admin_survey_submission_backups_path(survey), params: { submission_backup: { note: 'Test Note' } }
        end

        assert_response :redirect
      end

      test 'destroy survey' do
        survey = FactoryBot.create(:survey, :scipi, :invite_only, :published)
        # Doesn't matter that this is not a real backup file, just that it's a CSV
        backup_file = file_fixture('free_form_data.csv')
        backup = survey.submission_backups.create!(file: backup_file, requested_by: survey.created_by)

        sign_in survey.created_by

        assert_difference('::Surveys::SubmissionBackup.count', -1) do
          delete admin_survey_submission_backup_path(survey, backup)
        end

        assert_response :redirect
      end
    end
  end
end

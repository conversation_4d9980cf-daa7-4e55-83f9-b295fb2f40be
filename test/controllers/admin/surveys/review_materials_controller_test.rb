# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class ReviewMaterialsControllerTest < ActionDispatch::IntegrationTest
      test 'attach required file resource to new survey' do
        admin = FactoryBot.create(:admin)
        branding = FactoryBot.create(:survey_branding)
        attachment_path = file_fixture('test_attachment.pdf')

        sign_in admin

        assert_difference -> { ::Surveys::ReviewMaterial.count } do
          post admin_surveys_path, params: {
            survey: {
              name: 'Test SciPi',
              branding_id: branding.id,
              ownerships_attributes: {
                '0' => {
                  owner_id: admin.id,
                  notify: '1'
                }
              },
              review_materials_attributes: {
                '0' => {
                  file: fixture_file_upload(attachment_path, 'application/pdf'),
                  required: true
                }
              }
            }
          }
        end

        survey = QuestionGroup.last
        assert_redirected_to admin_survey_path(survey)

        review_material = survey.review_materials.first
        assert_equal 'test_attachment.pdf', review_material.file.filename.to_s
        assert review_material.required?
      end

      test 'attach additional file resource to an existing survey' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(
          :survey,
          review_materials: [::Surveys::ReviewMaterial.new(file: fixture_file_upload(file_fixture('test_attachment.pdf')))],
          created_by: admin
        )
        new_file_path = file_fixture('cv.pdf')

        sign_in admin

        assert_difference -> { ::Surveys::ReviewMaterial.count } do
          patch admin_survey_path(survey), params: {
            survey: {
              review_materials_attributes: {
                '0' => { id: survey.review_materials.first.id }, # Keep existing file
                '1' => { # Add new file
                  file: fixture_file_upload(new_file_path, 'application/pdf'),
                  required: true
                }
              }
            }
          }
        end

        assert_redirected_to admin_survey_path(survey)

        file_names = survey.review_materials.reload.map { |rm| rm.file.filename.to_s }
        assert_includes file_names, 'test_attachment.pdf'
        assert_includes file_names, 'cv.pdf'
      end
    end
  end
end

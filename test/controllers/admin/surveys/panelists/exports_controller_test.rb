# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    module Panelists
      class ExportsControllerTest < ActionDispatch::IntegrationTest
        test 'download CSV' do
          expert = FactoryBot.create(:expert)
          branding = FactoryBot.create(:survey_branding, :scipi)
          scipi = FactoryBot.create(:scipi, :invite_only, branding:)
          scipi.panelists.create!(expert:)
          filename = "SciPi ##{scipi.id} Panel Summary.csv"

          sign_in scipi.created_by

          get admin_survey_panelists_export_path(scipi), params: { format: :csv }

          assert_response :success
          assert_content_type 'csv'
          assert_content_disposition filename:
        end
      end
    end
  end
end

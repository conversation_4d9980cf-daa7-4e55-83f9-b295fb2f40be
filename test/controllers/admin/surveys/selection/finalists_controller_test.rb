# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    module Selection
      class FinalistsControllerTest < ActionDispatch::IntegrationTest
        test 'create makes single applicant finalist' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(expert:)

          sign_in scipi.created_by

          post admin_survey_selection_finalists_path(scipi), params: { applicant_ids: [applicant.id] }

          assert_redirected_to admin_survey_selection_root_path(scipi)
          assert_equal "#{expert.mail_name} was made a finalist", flash[:notice]
          assert applicant.reload.finalist?, 'applicant should be marked as finalist'
        end

        test 'create makes multiple applicants finalist' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert1 = FactoryBot.create(:expert)
          expert2 = FactoryBot.create(:expert)
          applicant1 = scipi.applicants.create!(expert: expert1)
          applicant2 = scipi.applicants.create!(expert: expert2)

          sign_in scipi.created_by

          post admin_survey_selection_finalists_path(scipi), params: { applicant_ids: [applicant1.id, applicant2.id] }

          assert_redirected_to admin_survey_selection_root_path(scipi)
          assert_equal '2 applicants were made a finalist', flash[:notice]
          assert applicant1.reload.finalist?, 'first applicant should be marked as finalist'
          assert applicant2.reload.finalist?, 'second applicant should be marked as finalist'
        end

        test 'create renumbers applicants when survey has no comments' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert1 = FactoryBot.create(:expert)
          expert2 = FactoryBot.create(:expert)
          expert3 = FactoryBot.create(:expert)
          applicant1 = scipi.panelists.create!(expert: expert1, expert_number: 1)
          applicant2 = scipi.panelists.create!(expert: expert2, expert_number: 2)
          applicant3 = scipi.panelists.create!(expert: expert3, expert_number: 3)

          sign_in scipi.created_by

          post admin_survey_selection_finalists_path(scipi), params: { applicant_ids: [applicant2.id] }

          assert_redirected_to admin_survey_selection_root_path(scipi)
          assert applicant2.reload.finalist?, 'applicant should be marked as finalist'
          assert_equal 1, applicant1.reload.expert_number, 'Expert #1 should not be renumbered'
          assert_equal 2, applicant3.reload.expert_number, 'Expert #3 should be renumbered to Expert #2'
        end
      end
    end
  end
end

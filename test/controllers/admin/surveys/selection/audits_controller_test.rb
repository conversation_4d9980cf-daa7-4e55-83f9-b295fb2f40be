# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    module Selection
      class AuditsControllerTest < ActionDispatch::IntegrationTest
        test 'download audit scores CSV' do
          expert = FactoryBot.create(:expert)
          auditor = FactoryBot.create(:expert)
          branding = FactoryBot.create(:survey_branding, :scipi)
          scipi = FactoryBot.create(:scipi, :invite_only, branding:)
          applicant = scipi.applicants.create!(expert:)
          applicant.audits.create!(audited_by: scipi.created_by, score: 85, notes: 'Good candidate')

          sign_in scipi.created_by

          get admin_survey_selection_audits_path(scipi, format: :csv), params: { applicant_ids: [applicant.id] }

          assert_response :success
          assert_content_type 'csv'
          assert_content_disposition filename: "audit_scores_#{scipi.id}_#{Date.current}.csv"

          csv_content = response.body
          assert_includes csv_content, 'Applicant,Auditor,Score,Notes'
          assert_includes csv_content, expert.mail_name
          assert_includes csv_content, auditor.mail_name
          assert_includes csv_content, '85'
          assert_includes csv_content, 'Good candidate'
        end
      end
    end
  end
end

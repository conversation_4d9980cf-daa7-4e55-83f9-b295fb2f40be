# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    module Selection
      class ApprovalsControllerTest < ActionDispatch::IntegrationTest
        test 'create approves single applicant' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(expert:)

          sign_in scipi.created_by

          post admin_survey_selection_approvals_path(scipi), params: { applicant_ids: [applicant.id] }

          assert_redirected_to admin_survey_selection_root_path(scipi)
          assert_equal "#{expert.mail_name} was approved", flash[:notice]
          assert applicant.reload.approved?, 'applicant should be marked as approved'
        end

        test 'create approves multiple applicants' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert1 = FactoryBot.create(:expert)
          expert2 = FactoryBot.create(:expert)
          applicant1 = scipi.applicants.create!(expert: expert1)
          applicant2 = scipi.applicants.create!(expert: expert2)

          sign_in scipi.created_by

          post admin_survey_selection_approvals_path(scipi), params: { applicant_ids: [applicant1.id, applicant2.id] }

          assert_redirected_to admin_survey_selection_root_path(scipi)
          assert_equal '2 applicants were approved', flash[:notice]
          assert applicant1.reload.approved?, 'first applicant should be marked as approved'
          assert applicant2.reload.approved?, 'second applicant should be marked as approved'
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class DOIsControllerTest < ActionDispatch::IntegrationTest
      test 'can create survey with doi' do
        admin = FactoryBot.create(:admin)
        sign_in admin

        assert_difference -> { DOI.count } do
          post admin_surveys_path, params: {
            survey: {
              name: 'Test Survey',
              description: 'Description',
              branding_id: surveys_brandings(:survey).id,
              doi_attributes: {
                identifier: '10.63565/journal.newsurvey'
              },
              ownerships_attributes: {
                '0' => {
                  owner_id: admin.id,
                  notify: '1'
                }
              }
            }
          }
        end

        survey = QuestionGroup.last
        assert_equal '10.63565/journal.newsurvey', survey.doi.identifier
      end

      test 'blank doi is ignored when creating survey without validation errors' do
        admin = FactoryBot.create(:admin)
        sign_in admin

        assert_difference -> { QuestionGroup.count } do
          assert_no_difference -> { DOI.count } do
            post admin_surveys_path, params: {
              survey: {
                name: 'Test Survey',
                description: 'Description',
                branding_id: surveys_brandings(:survey).id,
                doi_attributes: {
                  identifier: ''
                },
                ownerships_attributes: {
                  '0' => {
                    owner_id: admin.id,
                    notify: '1'
                  }
                }
              }
            }
          end
        end

        assert_response :redirect
        assert_redirected_to admin_survey_path(QuestionGroup.last)
        assert_nil QuestionGroup.last.doi
      end

      test 'can update survey with new doi when one already exists' do
        admin = FactoryBot.create(:admin)
        survey = FactoryBot.create(:survey, created_by: admin)
        original_doi = survey.create_doi(identifier: '10.63565/journal.originalsurvey')
        sign_in admin

        assert_no_difference -> { DOI.count } do
          patch admin_survey_path(survey), params: {
            survey: {
              doi_attributes: {
                id: original_doi.id,
                identifier: '10.63565/journal.updatedsurvey'
              }
            }
          }
        end

        assert_equal '10.63565/journal.updatedsurvey', survey.reload.doi.identifier
        assert_response :redirect
        assert_redirected_to admin_survey_path(survey)
      end

      test 'can clear doi from survey' do
        admin = FactoryBot.create(:admin)
        survey = FactoryBot.create(:survey, created_by: admin)
        survey.create_doi(identifier: '10.63565/journal.survey')
        sign_in admin

        assert_difference -> { DOI.count }, -1 do
          patch admin_survey_path(survey), params: {
            survey: {
              doi_attributes: {
                id: survey.doi.id,
                identifier: ''
              }
            }
          }
        end

        assert_nil survey.reload.doi
      end
    end
  end
end

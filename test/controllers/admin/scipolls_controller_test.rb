# frozen_string_literal: true

require 'test_helper'

module Admin
  class ScipollsControllerTest < ActionDispatch::IntegrationTest
    test 'should create scipoll' do
      admin = FactoryBot.create(:scipoll_admin)

      sign_in admin

      assert_difference('QuestionGroup.count') do
        post admin_scipolls_path,
             params: {
               scipoll: {
                 name: 'Test SciPoll',
                 description: 'Test Description',
                 expertise_ids: [expertises(:expertise1).id]
               }
             }
      end

      assert_redirected_to question_group_questions_path(QuestionGroup.last)
    end
  end
end

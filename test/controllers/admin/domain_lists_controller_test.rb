# frozen_string_literal: true

require 'test_helper'

module Admin
  class DomainsListsControllerTest < ActionDispatch::IntegrationTest
    test 'should assign domain to list' do
      # Is assigned to DomainList.default
      domain = FactoryBot.create(:domain)
      another_list = domain_lists(:known_institution)

      admin = FactoryBot.create(:admin)

      sign_in(admin)

      assert_difference(-> { another_list.domains.count }, 1) do
        assert_difference(-> { DomainList.default.domains.count }, -1) do
          patch admin_domain_list_domains_url(another_list), params: { ids: [domain.id] }
        end
      end

      assert_response :redirect
    end
  end
end

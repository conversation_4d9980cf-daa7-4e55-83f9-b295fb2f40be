# frozen_string_literal: true

require 'test_helper'

module Admin
  class DomainsControllerTest < ActionDispatch::IntegrationTest
    test 'should get index' do
      FactoryBot.create(:domain)
      admin = FactoryBot.create(:admin)

      sign_in(admin)

      get admin_domains_url

      assert_response :success
    end

    test 'should filter by list' do
      FactoryBot.create(:domain)
      admin = FactoryBot.create(:admin)

      sign_in(admin)

      get admin_domains_url(params: { list_id: DomainList.default })

      assert_response :success
    end

    test 'should filter by list hostname' do
      FactoryBot.create(:domain)
      admin = FactoryBot.create(:admin)

      sign_in(admin)

      get admin_domains_url(params: { hostname: 'abc' })

      assert_response :success
    end
  end
end

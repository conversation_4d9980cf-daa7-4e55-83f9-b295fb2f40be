# frozen_string_literal: true

require 'test_helper'

class FeedsTest < ActionDispatch::IntegrationTest
  test 'all feeds routes work' do
    brandings = %w[announcements pings scipolls scipis]

    brandings.each do |branding|
      get feeds_path(branding:)
      assert_response :success
    end
  end

  test 'SciPis feed get featured scipis' do
    branding = FactoryBot.create(:survey_branding, :scipi)
    FactoryBot.create_list(:survey, 10, :invite_only, :published, branding:)

    featured_scipis = QuestionGroup.featured_scipis

    get feeds_path('scipis')

    assert_equal response.body, collection_to_feed_json(featured_scipis)
  end

  test 'SciPolls feed get featured scipolls' do
    branding = FactoryBot.create(:survey_branding, :scipoll)
    FactoryBot.create_list(:survey, 10, :published, branding:)

    featured_scipolls = QuestionGroup.featured_scipolls

    get feeds_path('scipolls')

    assert_equal response.body, collection_to_feed_json(featured_scipolls)
  end

  test 'Pings feed get featured pings' do
    FactoryBot.create_list(:ping, 10, :published)

    featured_pings = Ping.featured

    get feeds_path('pings')

    assert_equal response.body, collection_to_feed_json(featured_pings)
  end

  test 'Announcements feed gets published announcement orded by published date' do
    FactoryBot.create(:announcement, :promotable)
    FactoryBot.create_list(:announcement, 10, :unpromotable)

    announcements = Announcement.published.order(published_at: :desc)

    get feeds_path('announcements')

    assert_equal response.body, collection_to_feed_json(announcements)
  end

  private

  def collection_to_feed_json(collection)
    collection.limit(FeedsController::ITEM_LIMIT).map { |l| l.to_feed_listing(base_url: root_url) }.to_json
  end
end

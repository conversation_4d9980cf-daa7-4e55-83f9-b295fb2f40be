# frozen_string_literal: true

require 'test_helper'

class AnswerGroupsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @creator = create(:creator)
    @user2 = create(:expert, first_name: '<PERSON>')
    @user3 = create(:expert, first_name: '<PERSON><PERSON>')
    @survey = FactoryBot.create(:survey, :published, access_code: nil, created_by_id: @creator.id)
    @invite_only_survey = FactoryBot.create(:survey, :scipi, :published, :invite_only, access_code: nil, created_by_id: @creator.id)
    create(:invite, survey: @invite_only_survey, user: @user2)

    @question = create(:long_question, question_group: @survey, validation_rules: { presence: '0' })
  end

  test 'new for open SciPoll' do
    survey = FactoryBot.create(:scipoll, :published, :open)
    user = FactoryBot.create(:expert)

    sign_in user

    get new_question_group_answer_group_url(survey)

    assert_response :success
  end

  test 'new for closed SciPoll' do
    survey = FactoryBot.create(:scipoll, :published, :closed)
    user = FactoryBot.create(:expert)

    sign_in user

    get new_question_group_answer_group_url(survey)

    assert_redirected_to root_path
    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end

  test 'new for open SciPoll with existing draft submission' do
    survey = FactoryBot.create(:scipoll, :published, :open)
    user = FactoryBot.create(:expert)
    submission = FactoryBot.create(:submission, survey:, user:)

    sign_in user

    get new_question_group_answer_group_url(survey)

    assert_redirected_to edit_question_group_answer_group_url(survey, submission)
  end

  test 'new for open SciPi' do
    scipi = FactoryBot.create(:scipi, :published, :open)
    user = FactoryBot.create(:expert)
    scipi.panelists.create!(user:)

    sign_in user

    get new_question_group_answer_group_url(scipi)

    assert_response :success
  end

  test 'new for open SciPi with existing submission' do
    scipi = FactoryBot.create(:scipi, :published, :open)
    user = FactoryBot.create(:expert)
    scipi.panelists.create!(user:)
    submission = FactoryBot.create(:submission, survey: scipi, user:)

    sign_in user

    get new_question_group_answer_group_url(scipi)

    assert_redirected_to edit_question_group_answer_group_url(scipi, submission)
  end

  test 'new for closed SciPi' do
    scipi = FactoryBot.create(:scipi, :published, :closed)
    user = FactoryBot.create(:expert)
    scipi.panelists.create!(user:)

    sign_in user

    get new_question_group_answer_group_url(scipi)

    assert_redirected_to root_path
    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end

  test 'new for closed SciPi with existing draft' do
    scipi = FactoryBot.create(:scipi, :published, :closed)
    user = FactoryBot.create(:expert)
    scipi.panelists.create!(user:)
    submission = FactoryBot.create(:submission, :draft, survey: scipi, user:)

    sign_in user

    get new_question_group_answer_group_url(scipi)

    assert_redirected_to edit_question_group_answer_group_url(scipi, submission)
  end

  test 'new for closed SciPi with final submission' do
    scipi = FactoryBot.create(:scipi, :published, :closed)
    user = FactoryBot.create(:expert)
    scipi.panelists.create!(user:)
    FactoryBot.create(:submission, :final, survey: scipi, user:)

    sign_in user

    get new_question_group_answer_group_url(scipi)

    assert_redirected_to root_path
    assert_equal 'This survey is closed.', flash[:alert]
  end

  test 'preview survey' do
    owner = FactoryBot.create(:scipi_admin)
    survey = FactoryBot.create(:survey, created_by: owner)

    sign_in owner

    get survey_preview_url(survey)

    assert_response :ok
  end

  test 'cannot preview survey' do
    user = FactoryBot.create(:expert)
    survey = FactoryBot.create(:survey)

    sign_in user

    get survey_preview_url(survey)

    assert_redirected_to root_path
    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end

  test 'survey preview does not create submission' do
    owner = create(:scipi_admin)
    survey = FactoryBot.create(:survey, created_by: owner)

    sign_in owner

    get survey_preview_url(survey)

    assert_nil survey.submission_for(owner)
  end

  test 'invite only survey - user accessing new answer group page' do
    survey = FactoryBot.create(:survey, :scipi, :invite_only, :open)
    user = users(:expert)
    survey.panelists.create!(user:)

    sign_in user

    get new_question_group_answer_group_url(survey)

    assert_response :success
  end

  test 'invite only survey - unauthorized user accessing new answer group page' do
    sign_in @user3

    get new_question_group_answer_group_url(@invite_only_survey)

    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end

  test 'user answering a survey' do
    sign_in @user2

    post question_group_answer_groups_url(@survey), params: { answer_group: { @question.id => { text: 'Hello' } } }

    assert_redirected_to survey_url(@survey)
  end

  test 'user cannot answer a closed survey' do
    survey = FactoryBot.create(:survey, :published, :closed)
    question = create(:long_question, question_group: survey, validation_rules: { presence: '0' })
    expert = create(:expert)

    sign_in expert
    post question_group_answer_groups_url(survey), params: { answer_group: { question.id => { text: 'Hello' } } }

    assert_response :redirect
    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end

  test 'invite only survey - user answering a survey' do
    survey = FactoryBot.create(:survey, :scipi, :invite_only, :open)
    user = users(:expert)
    survey.panelists.create!(user:)
    question = survey.questions.long_text.create!(question_text: 'Foo', position: 1)

    sign_in user

    post question_group_answer_groups_url(survey), params: { answer_group: { question.id => { text: 'Hello' } } }

    assert_redirected_to scipi_url(survey)
  end

  test 'invite only survey - unauthorized user answering a survey' do
    sign_in @user3
    post question_group_answer_groups_url(@invite_only_survey), params: { answer_group: { @question.id => { text: 'Hello' } } }
    assert_nil flash[:notice]
    assert_equal 'You are not authorized to access this page.', flash[:alert]
    assert_response :redirect
  end

  test 'GET to edit' do
    user = create(:expert)
    survey = create(:survey, :published)
    submission = create(:submission, survey:, user:)

    sign_in(user)

    get edit_question_group_answer_group_url(survey, submission)

    assert_response :ok
  end

  test 'GET to edit as unauthorized user' do
    user = create(:expert)
    another_user = create(:expert)
    survey = create(:survey, :published)
    submission = create(:submission, survey:, user:)

    sign_in(another_user)

    get edit_question_group_answer_group_url(survey, submission)

    assert_response :redirect
    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end

  test 'PATCH to update to submit' do
    user = create(:expert)
    survey = create(:survey, :published)
    submission = create(:submission, survey:, user:)

    sign_in(user)

    patch question_group_answer_group_url(survey, submission), params: { button: 'submit' }

    assert_redirected_to survey_url(survey)
  end

  test 'PATCH to update to save progress' do
    user = create(:expert)
    survey = create(:survey, :published)
    submission = create(:submission, survey:, user:)

    sign_in(user)

    patch question_group_answer_group_url(survey, submission), params: { button: 'save_progress' }

    assert_redirected_to edit_question_group_answer_group_url(survey, submission)
  end

  # QUESTION: Should saving progress should be allowed on a closed SciPi, or only submitting?
  #
  # Regardless, the permissions do not distinguish between the :save_progress and :submit actions,
  # so they only check :update.
  test 'update existing draft submission for a closed SciPi' do
    scipi = FactoryBot.create(:scipi, :published, :closed)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(expert:)
    draft_submission = FactoryBot.create(:submission, :draft, survey: scipi, submitter: expert)

    sign_in(expert)

    patch question_group_answer_group_url(scipi, draft_submission), params: { button: 'save_progress' }

    assert_redirected_to scipi_url(scipi)
    assert_equal 'Submitted. (LATE)', flash[:success]
  end

  test 'final submission for a closed SciPi' do
    scipi = FactoryBot.create(:scipi, :published, :closed)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(expert:)
    draft_submission = FactoryBot.create(:submission, :draft, survey: scipi, submitter: expert)

    sign_in(expert)

    patch question_group_answer_group_url(scipi, draft_submission), params: { button: 'submit' }

    assert_redirected_to scipi_url(scipi)
    assert_equal 'Submitted. (LATE)', flash[:success]
  end

  test 'PATCH to update as unauthorized user' do
    user = create(:expert)
    another_user = create(:expert)
    survey = create(:survey, :published)
    submission = create(:submission, survey:, user:)

    sign_in(another_user)

    patch question_group_answer_group_url(survey, submission), params: { button: 'submit' }

    assert_response :redirect
    assert_equal 'You are not authorized to access this page.', flash[:alert]
  end
end

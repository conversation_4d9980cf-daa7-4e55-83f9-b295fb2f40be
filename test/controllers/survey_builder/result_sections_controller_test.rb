# frozen_string_literal: true

require 'test_helper'

module SurveyBuilder
  class ResultSectionsControllerTest < ActionDispatch::IntegrationTest
    setup do
      @owner = create(:admin)
      @survey = create(:survey, created_by: @owner)

      sign_in @owner
    end

    test 'new section page' do
      get new_survey_builder_survey_result_section_path(@survey)
      assert_response :ok
    end

    test 'create result section' do
      post survey_builder_survey_result_sections_path(@survey),
           params: {
             section: {
               name: 'New Section'
             }
           }

      assert_redirected_to(
        survey_builder_survey_result_definitions_path(@survey)
      )
      assert_equal 'Section created', flash[:notice]
    end

    test 'cannot create invalid result section' do
      post survey_builder_survey_result_sections_path(@survey),
           params: {
             section: {
               name: ''
             }
           }

      assert_response :unprocessable_content
    end

    test 'edit section page' do
      section = create(:result_section, survey: @survey)

      get edit_survey_builder_result_section_path(section)

      assert_response :ok
    end

    test 'update section' do
      section = create(:result_section, survey: @survey)
      name = 'New name'

      patch survey_builder_result_section_path(section),
            params: {
              section: {
                name:,
                hidden: '0'
              }
            }

      assert_redirected_to(
        survey_builder_survey_result_definitions_path(@survey)
      )
      assert_equal "#{name} updated", flash[:notice]
    end

    test 'cannot update invalid section' do
      section = create(:result_section, survey: @survey)

      patch survey_builder_result_section_path(section),
            params: {
              section: {
                name: '',
                hidden: '0'
              }
            }

      assert_response :unprocessable_content
    end

    test 'delete an empty section' do
      section = create(:result_section, survey: @survey)

      delete survey_builder_result_section_path(section)

      assert_redirected_to(
        survey_builder_survey_result_definitions_path(@survey)
      )
      assert_equal "#{section.name} deleted", flash[:notice]
    end

    test 'move section' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      survey = FactoryBot.create(:survey, created_by: admin)
      section1 = FactoryBot.create(:result_section, survey:, position: 1)
      FactoryBot.create(:result_section, survey:, position: 2)
      FactoryBot.create(:result_section, survey:, position: 3)

      patch move_survey_builder_result_section_path(section1),
            params: {
              new_position: 2
            }

      assert_response :no_content
    end
  end
end

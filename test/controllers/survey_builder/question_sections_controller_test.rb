# frozen_string_literal: true

require 'test_helper'

module SurveyBuilder
  class QuestionSectionsControllerTest < ActionDispatch::IntegrationTest
    setup do
      @survey_admin = create(:admin)
      @survey = create(:survey, created_by: @survey_admin)

      sign_in @survey_admin
    end

    test 'new section page' do
      get new_survey_builder_survey_question_section_path(@survey)

      assert_response :ok
    end

    test 'create new section' do
      post survey_builder_survey_question_sections_path(@survey),
           params: {
             section: {
               name: 'Valid Section Name'
             }
           }

      assert_response :redirect
      assert_equal 'Section was successfully created.', flash[:notice]
    end

    test 'create new section fails with missing name' do
      post survey_builder_survey_question_sections_path(@survey),
           params: {
             section: {
               name: nil
             }
           }

      assert_response :unprocessable_content
    end

    test 'view edit section page' do
      section = create(:question_section, question_group: @survey)

      get edit_survey_builder_question_section_path(section)

      assert_response :ok
    end

    test 'update section' do
      section = create(:question_section, question_group: @survey)

      patch survey_builder_question_section_path(section),
            params: {
              section: { name: 'New name' }
            }

      assert_redirected_to question_group_questions_url(@survey)
      assert_equal 'Section was successfully updated.', flash[:notice]
    end

    test 'cannot update section with invalid data' do
      section = create(:question_section, question_group: @survey)

      patch survey_builder_question_section_path(section),
            params: {
              section: { name: '' }
            }

      assert_response :unprocessable_content
    end

    test 'destroy section' do
      section = create(:question_section, question_group: @survey)

      delete survey_builder_question_section_path(section)

      assert_redirected_to question_group_questions_url(@survey)
      assert_equal 'Section was successfully removed.', flash[:notice]
    end

    test 'clone section to owned survey' do
      source_creator = FactoryBot.create(:scipi_admin)
      source_survey = FactoryBot.create(:survey, owners: [source_creator])
      source_section = create(:question_section,
                              question_group: source_survey,
                              position: 1)
      create(:question,
             position: 1,
             section: source_section,
             question_group: source_survey,
             question_text: 'Question 1')

      destination_survey = create(:survey, created_by: source_creator)
      # target survey (currently) needs to be segmented to accept new section
      create(:question_section,
             question_group: destination_survey,
             position: 1)

      sign_in source_creator
      post clone_survey_builder_question_section_path(source_section),
           params: {
             section: {
               question_group_id: destination_survey.id
             }
           }

      assert_response :redirect
      assert_redirected_to question_group_questions_path(destination_survey)
      assert_equal 2, destination_survey.question_sections.count
      assert_equal 1, destination_survey.questions.count
    end

    test 'clone section to non-owned survey' do
      source_creator = create(:creator)
      source_survey = create(:survey, created_by: source_creator)
      source_section = create(:question_section,
                              question_group: source_survey,
                              position: 1)
      create(:question,
             position: 1,
             section: source_section,
             question_group: source_survey,
             question_text: 'Question 1')

      destination_creator = create(:creator)
      destination_survey = create(:survey, created_by: destination_creator)
      # target survey (currently) needs to be segmented to accept new section
      create(:question_section, question_group: destination_survey, position: 1)

      sign_in source_creator
      post clone_survey_builder_question_section_path(source_section),
           params: {
             section: {
               question_group_id: destination_survey.id
             }
           }

      assert_response :redirect
      assert_equal 'You are not authorized to access this page.', flash[:alert]
      assert_equal 1, destination_survey.question_sections.count
      assert_equal 0, destination_survey.questions.count
    end
  end
end

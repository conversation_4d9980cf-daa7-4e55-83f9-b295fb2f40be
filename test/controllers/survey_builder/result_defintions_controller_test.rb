# frozen_string_literal: true

require 'test_helper'

module SurveyBuilder
  class ResultDefinitionsControllerTest < ActionDispatch::IntegrationTest
    test 'view result builder index' do
      owner = FactoryBot.create(:admin)
      survey = create(:survey, created_by: owner)

      sign_in owner

      get survey_builder_survey_result_definitions_path(survey)

      assert_response :ok
    end

    test 'show new result definition form to survey' do
      load_all_result_types
      owner = FactoryBot.create(:admin)
      survey = create(:survey, created_by: owner)

      sign_in owner

      get new_survey_builder_survey_result_definition_path(survey)

      assert_response :ok
    end

    test 'show new result definition form to section' do
      load_all_result_types
      owner = FactoryBot.create(:admin)
      survey = create(:survey, created_by: owner)
      result_section = create(:result_section, survey:)

      sign_in owner

      get new_survey_builder_result_section_result_definition_path(result_section)

      assert_response :ok
    end

    test 'add invalid result type generates an error' do
      survey = create(:survey)

      survey_admin = survey.created_by
      sign_in survey_admin

      assert_raises(RuntimeError) do
        post survey_builder_survey_result_definitions_path(survey),
             params: {
               result_definition: {
                 result_type_id: -1
               }
             }
      end
    end

    test 'show edit page for question result' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      result_definition = FactoryBot.create(
        :result_definition,
        :question
      )

      get edit_survey_builder_result_definition_path(result_definition),
          params: { id: result_definition.id }

      assert_response :ok
    end

    test 'show edit page for free form result' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      result_definition = FactoryBot.create(:result_definition, :free_form)

      get edit_survey_builder_result_definition_path(result_definition),
          params: { id: result_definition.id }

      assert_response :ok
    end

    test 'update question-based result definition' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      survey = FactoryBot.create(:survey)
      base_question = FactoryBot.create(:radio, survey:)
      groupable_question = FactoryBot.create(:radio, survey:)
      result_definition = FactoryBot.create(:result_definition,
                                            :question,
                                            survey:,
                                            question: base_question)
      render_type = FactoryBot.create(:render_type, :bar_chart)

      patch survey_builder_result_definition_path(result_definition),
            params: {
              result_definition: {
                group_by_question_id: groupable_question.id.to_s,
                render_type_id: render_type.id,
                chart_height: CustomResult::DEFAULT_CHART_HEIGHT
              }
            }

      assert_redirected_to(survey_builder_survey_result_definitions_path(survey))
    end

    test 'update free-form result definition' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      result_definition = FactoryBot.create(:result_definition, :free_form)

      patch survey_builder_result_definition_path(result_definition),
            params: {
              result_definition: {
                title: 'Foobar',
                data_file: fixture_file_upload('free_form_data.csv')
              }
            }

      assert_redirected_to(survey_builder_survey_result_definitions_path(result_definition.survey))
    end

    test 'update failed' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      result_definition = FactoryBot.create(:result_definition, :free_form)

      patch survey_builder_result_definition_path(result_definition),
            params: { result_definition: { title: '' } }

      assert_response :ok
    end

    test 'move result' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      survey = FactoryBot.create(:survey, created_by: admin)
      section = FactoryBot.create(:result_section, survey:)
      result1 = FactoryBot.create(
        :result_definition,
        survey:,
        section:,
        position: 1
      )
      FactoryBot.create(
        :result_definition,
        survey:,
        section:,
        position: 2
      )
      FactoryBot.create(
        :result_definition,
        survey:,
        section:,
        position: 3
      )

      patch move_survey_builder_result_definition_path(result1),
            params: {
              new_parent_id: section.id,
              new_position: 2
            }

      assert_response :no_content
    end

    test 'delete sectioned result' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      survey = FactoryBot.create(:survey, created_by: admin)
      section = FactoryBot.create(:result_section, survey:)
      result = FactoryBot.create(
        :result_definition,
        survey:,
        section:,
        position: 1
      )

      delete survey_builder_result_definition_path(result)

      assert_redirected_to survey_builder_survey_result_definitions_path(survey)
    end

    test 'delete un-sectioned result' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      survey = FactoryBot.create(:survey, created_by: admin)
      result = FactoryBot.create(
        :result_definition,
        survey:,
        position: 1
      )

      delete survey_builder_result_definition_path(result)

      assert_redirected_to survey_builder_survey_result_definitions_path(survey)
    end

    test 'sync result definitions' do
      admin = FactoryBot.create(:admin)

      sign_in admin

      survey = FactoryBot.create(:survey, created_by: admin)

      post sync_survey_builder_survey_result_definitions_path(survey)

      assert_redirected_to survey_builder_survey_result_definitions_path(survey)
      assert_equal '✨ Results synced! ✨', flash[:notice]
    end
  end
end

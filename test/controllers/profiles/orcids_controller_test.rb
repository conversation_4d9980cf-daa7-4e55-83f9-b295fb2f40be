# frozen_string_literal: true

require 'test_helper'

module Profiles
  class OrcidsControllerTest < ActionDispatch::IntegrationTest
    include ActiveJob::TestHelper

    test 'new' do
      expert = FactoryBot.create(:expert)

      sign_in expert

      get new_profile_orcid_path

      assert_redirected_to ORCID.authorization_url(redirect_uri: orcid_redirect_url)
    end

    test 'authentication_complete' do
      expert = FactoryBot.create(:expert)

      sign_in expert

      ::ORCID::OAuthGateway.any_instance.stubs(:access_token_and_orcid_id).returns(
        {
          'access_token' => 'a1a1a1a1a1-b2b2-c3c3-d4d4-e5e5e5e5e5e5',
          'token_type' => 'bearer',
          'refresh_token' => 'b2b2b2b2b2-c3c3-d4d4-e5e5-f6f6f6f6f6f6',
          'expires_in' => 631_138_518,
          'scope' => '/authenticate',
          'name' => '<PERSON>',
          'orcid' => '0000-0001-2345-6789'
        }
      )

      assert_difference -> { expert.oauth_tokens.count } do
        get orcid_redirect_path, params: { code: 'ABC123' }
      end

      assert_redirected_to edit_profile_path
      assert_not_nil expert.profile.reload.orcid_record
    end
  end
end

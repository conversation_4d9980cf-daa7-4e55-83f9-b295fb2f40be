# frozen_string_literal: true

require 'application_system_test_case'

class PublicResultsTest < ApplicationSystemTestCase
  test 'show multiple-choice question responses (with question_details)' do
    survey = FactoryBot.create(:survey, :legacy, :published, :results_public)
    result_position = rand(1..10)
    question_details = Faker::Lorem.sentence
    question = FactoryBot.create(:radio, survey:, question_details:)

    selected_choice = question.answer_choices[0]
    answer_question(survey, question, selected_choice)

    result_definition = create_multiple_choice_result(
      survey,
      question,
      result_position,
      :pie_chart,
      true # show_question_details
    )

    login_as any_user

    visit survey_result_path(survey_id: survey.id, id: result_definition.id)

    assert_selected_nav_item(result_definition)
    assert_header_content(result_definition)
    within('#result_title') do
      assert_text(question_details)
    end
  end

  test 'question details NOT visible if show_question_details is false' do
    survey = FactoryBot.create(:survey, :legacy, :published, :results_public)
    question_details = Faker::Lorem.sentence
    question = FactoryBot.create(:radio, survey:, question_details:)

    result_definition = create_multiple_choice_result(
      survey,
      question,
      rand(1..10),
      :pie_chart,
      false # show_question_details
    )

    login_as any_user

    visit survey_result_path(survey_id: survey.id, id: result_definition.id)

    within('#result_title') do
      assert_no_text(question_details)
    end
  end

  test 'show group by responses result' do
    survey = FactoryBot.create(:survey, :legacy, :published, :results_public)

    result_position = rand(1..10)
    base_question = FactoryBot.create(:radio, survey:)
    group_by_question = FactoryBot.create(:radio, survey:)

    submission = survey.submissions.create!(
      submitter: FactoryBot.create(:expert),
      submitted_at: Time.zone.now
    )
    base_selected_choice = base_question.answer_choices[0]
    selected_choice = SelectedChoice.new(
      answer_choice: base_selected_choice
    )
    submission.answers.create!(
      question: base_question,
      selected_choices: [selected_choice]
    )
    group_by_selected_choice = group_by_question.answer_choices[0]
    selected_choice = SelectedChoice.new(
      answer_choice: group_by_selected_choice
    )
    submission.answers.create!(
      question: group_by_question,
      selected_choices: [selected_choice]
    )

    result_type = FactoryBot.create(
      :result_type,
      :grouped_multiple_choice_responses
    )
    result_definition = FactoryBot.create(
      :result_definition,
      render_type: FactoryBot.create(:render_type, :bar_chart),
      result_type:,
      position: result_position,
      question: base_question,
      filter_by_question: group_by_question.id,
      survey:
    )

    login_as any_user

    visit survey_result_path(survey_id: survey.id, id: result_definition.id)

    assert_selected_nav_item(result_definition)
    assert_header_content(result_definition)
  end

  test 'show multiple-question scores result' do
    survey = FactoryBot.create(:survey, :legacy, :published, :results_public)

    result_position = rand(1..10)

    render_type = FactoryBot.create(:render_type, :ranked_order_plot)
    result_type = FactoryBot.create(
      :result_type,
      :multi_question_score,
      render_types: [render_type]
    )

    choice1 = FactoryBot.create(:answer_choice, value: 0.3)
    choice2 = FactoryBot.create(:answer_choice, value: 0.7)

    question = FactoryBot.create(
      :radio,
      answer_choices: [choice1, choice2],
      survey:,
      weight: 0.5,
      position: 1
    )

    selected_choice = question.answer_choices[0]
    answer_question(survey, question, selected_choice)

    result_definition = FactoryBot.create(
      :result_definition,
      render_type:,
      result_type:,
      title: Faker::Lorem.sentence,
      position: result_position,
      questions: [question],
      survey:
    )

    login_as any_user

    visit survey_result_path(survey_id: survey.id, id: result_definition.id)

    assert_selected_nav_item(result_definition)
    assert_header_content(result_definition)
    assert_descriptive_statistics_legend(
      max: 0.15,
      mean: 0.15,
      min: 0.15,
      std_dev: 0.0
    )
  end

  test 'show free form result' do
    survey = FactoryBot.create(:survey, :legacy, :published, :results_public)

    value1 = rand
    value2 = rand
    data_set = {
      'label1' => value1,
      'label2' => value2
    }
    result_position = rand(1..10)

    result_definition = FactoryBot.create(
      :result_definition,
      :free_form,
      position: result_position,
      survey:
    )

    data_set.each do |label, value|
      result_definition.data_points.create!(label:, value:)
    end

    login_as any_user

    visit survey_result_path(survey_id: survey.id, id: result_definition.id)

    assert_selected_nav_item(result_definition)
    assert_header_content(result_definition)

    data_values = data_set.values
    assert_descriptive_statistics_legend(
      max: data_values.max,
      mean: DescriptiveStatistics.mean(data_values),
      min: data_values.min,
      std_dev: DescriptiveStatistics.standard_deviation(data_values)
    )
  end

  test 'can view results page for survey with public results' do
    survey = FactoryBot.create(:survey, :legacy, :published, :results_public)

    visit survey_results_path(survey_id: survey.id)

    assert_current_path survey_results_path(survey_id: survey.id)
  end

  test 'Cannot view  published results page for scipi as logged out user' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published)

    visit survey_results_path(survey_id: scipi.id)

    assert_current_path login_path
  end

  test 'Can view public results page for as logged out user' do
    scipi = FactoryBot.create(:survey, :scipi, :results_public)

    visit survey_results_path(survey_id: scipi.id)

    assert_current_path survey_results_path(survey_id: scipi.id)
  end

  test 'can view results page for scipi as logged in user' do
    scipi = FactoryBot.create(:survey, :scipi, :results_public)

    login_as any_user

    visit survey_results_path(survey_id: scipi.id)

    assert_current_path survey_results_path(survey_id: scipi.id)
  end

  private

  def answer_question(survey, question, selected_choice)
    submission = survey.submissions.create!(
      submitter: FactoryBot.create(:expert),
      submitted_at: Time.zone.now
    )
    selected_choice = SelectedChoice.new(answer_choice: selected_choice)
    submission.answers.create!(
      question:,
      selected_choices: [selected_choice]
    )
  end

  def assert_descriptive_statistics_legend(max:, mean:, min:, std_dev:)
    within('.data-summary') do
      find('li', text: 'Max')
        .assert_text("Max: #{Formats.scientific(max)}")
      find('li', text: 'Mean')
        .assert_text("Mean: #{Formats.scientific(mean)}")
      find('li', text: 'Min')
        .assert_text("Min: #{Formats.scientific(min)}")
      find('li', text: 'stdDev')
        .assert_text("stdDev: #{Formats.scientific(std_dev)}")
    end
  end

  def assert_header_content(result_definition)
    within('.result-number') do
      assert_text(result_definition.position)
    end

    within('#result_title') do
      assert_text(result_definition.title)
    end
  end

  def assert_selected_nav_item(result_definition)
    within('#sidebar_nav') do
      within('.result.active') do
        within('i') do
          assert_text(result_definition.position)
        end
        assert_text(result_definition.title)
      end
    end
  end

  def create_multiple_choice_result(survey, question, result_position, chart_type, show_question_details)
    FactoryBot.create(
      :result_definition,
      :multiple_choice_responses,
      chart_type,
      position: result_position,
      question:,
      survey:,
      show_question_details:
    )
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

class ProfilesTest < ApplicationSystemTestCase
  test 'expert adds CV' do
    user = FactoryBot.create(:expert)

    cv_path = file_fixture('cv.pdf')

    login_as(user)

    visit edit_profile_path

    within('.profile-form') do
      attach_file('profile_profile_attributes_cv', cv_path)

      click_button('Update Profile')
    end

    assert_on_profile_page
    within('#cv_view .cv-link') { assert_content 'My Curriculum Vitae' }
  end

  test 'attempt to upload CV of wrong type' do
    user = FactoryBot.create(:expert)

    cv_path = file_fixture('not_a_pdf.docx')

    login_as(user)

    visit edit_profile_path

    within('.profile-form') do
      attach_file('profile_profile_attributes_cv', cv_path)

      click_button('Update Profile')
    end

    sleep(0.2)

    assert_text 'Your CV can only be a PDF file'
  end

  test 'delete CV' do
    user = FactoryBot.create(:expert, :with_cv)
    login_as(user)

    visit edit_profile_path

    within('#cv_view') do
      accept_confirm do
        click_link('Delete')
      end
    end

    assert_on_profile_page
    within('.flash-notice') { assert_content 'Your CV was deleted.' }
  end

  test 'update employment history' do
    user = FactoryBot.create(:expert)
    sector = FactoryBot.create(:employment_sector)
    years_of_experience = rand(0..12)

    login_as user

    visit edit_profile_path

    sector_locator = 'profile[employment_history_items_attributes][0][employment_sector_id]'
    years_locator = 'profile[employment_history_items_attributes][0][years]'

    within('.profile-form .emp-history-table') do
      find(:field, sector_locator).select(sector.name)
      find(:field, years_locator).fill_in(with: years_of_experience)
    end

    click_button('Update')

    assert_on_profile_page
    scroll_to(find(:field, sector_locator)) # it's a long form
    assert_equal sector.id.to_s,
                 find(:field, sector_locator).value,
                 'Years of experience was not correctly set'
    assert_equal years_of_experience.to_s,
                 find(:field, years_locator).value,
                 'Years of experience was not correctly set'
  end

  test 'update education history' do
    user = FactoryBot.create(:expert)
    degree_type = FactoryBot.create(:degree_type)
    subject_area = TestData::Degree.subject_area
    year = TestData::Degree.grad_year

    login_as user

    visit edit_profile_path

    degree_select_xpath = '//*[@class="degree-row"]/td[@class="degree__type"]/select'
    subject_area_input_xpath = '//*[@class="degree-row"]/td[@class="degree__subject-area"]/input'
    grad_year_select_xpath = '//*[@class="degree-row"]/td[@class="degree__grad-year"]/select'

    within('.profile-form .education-table') do
      first(:xpath, degree_select_xpath)
        .select(degree_type.name)
      first(:xpath, subject_area_input_xpath)
        .fill_in(with: subject_area)
      first(:xpath, grad_year_select_xpath)
        .select(year)
    end

    click_button('Update')

    assert_on_profile_page
    assert_equal degree_type.id.to_s,
                 first(:xpath, degree_select_xpath).value,
                 'Degree was not correctly set'
    assert_equal subject_area.to_s,
                 first(:xpath, subject_area_input_xpath).value,
                 'Degree was not correctly set'
    assert_equal year.to_s,
                 first(:xpath, grad_year_select_xpath).value,
                 'Degree was not correctly set'
  end

  test 'delete degree' do
    user = FactoryBot.create(:expert)
    degree = FactoryBot.create(:degree, expert: user)

    login_as user

    visit edit_profile_path

    degree_tr_xpath = "//tr[@data-row-id=\"#{degree.id}\"]"
    degree_delete_link_xpath = "#{degree_tr_xpath}" \
                               '/td[contains(@class, "row__delete-action")]/a'

    within('.profile-form .education-table') do
      accept_confirm do
        first(:xpath, degree_delete_link_xpath).click
      end
    end

    assert_on_profile_page
    assert_no_selector :xpath, degree_tr_xpath
  end

  test 'add certification' do
    user = FactoryBot.create(:expert)
    cert_name = Faker::Lorem.sentence(word_count: 4)

    login_as user

    visit edit_profile_path

    cert_xpath = '//tr[@class="certification-row"]/td[@class="certification__name"]/input[@type="text"]'

    within('.profile-form .education-table') do
      first(:xpath, cert_xpath)
        .fill_in(with: cert_name)
    end

    click_button('Update')

    assert_on_profile_page
    assert_equal cert_name,
                 first(:xpath, cert_xpath).value,
                 'Certification name was not correctly set'
  end

  test 'delete certification' do
    user = FactoryBot.create(:expert)
    certification = FactoryBot.create(:certification, expert: user)

    login_as user

    visit edit_profile_path

    certification_tr_xpath = "//tr[@data-row-id=\"#{certification.id}\"]"
    certification_delete_link_xpath = "#{certification_tr_xpath}" \
                                      '/td[contains(@class, "row__delete-action")]/a'

    within('.profile-form .certifications-table') do
      accept_confirm do
        first(:xpath, certification_delete_link_xpath).click
      end
    end

    assert_on_profile_page
    assert_no_selector :xpath, certification_tr_xpath
  end

  private

  def assert_on_profile_page
    assert_current_path edit_profile_path
  end
end

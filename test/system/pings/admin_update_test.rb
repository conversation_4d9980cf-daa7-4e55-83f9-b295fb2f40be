# frozen_string_literal: true

require 'application_system_test_case'

module Pings
  class AdminUpdateTest < ApplicationSystemTestCase
    include ApplicationHelper

    test 'update paid draft Ping' do
      ping = FactoryBot.create(:ping, :paid, :draft)
      login_as FactoryBot.create(:pings_admin)

      title = Faker::Lorem.question
      content = Faker::Lorem.paragraphs.join(' ')
      voting_opens_at = 3.weeks.from_now.change(min: 0, sec: 0, nsec: 0)
      voting_closes_at = 4.weeks.from_now.change(min: 0, sec: 0, nsec: 0)

      visit edit_ping_path(ping)

      within('.ping-form') do
        fill_in('Your Question', with: title)
        fill_in_rich_text_area 'ping_content', with: content

        fill_in('ping[voting_opens_at_date]', with: voting_opens_at)
        select(voting_opens_at.strftime('%I %p'), from: 'ping[voting_opens_at_hour]')

        fill_in('ping[voting_closes_at_date]', with: voting_closes_at)
        select(voting_closes_at.strftime('%I %p'), from: 'ping[voting_closes_at_hour]')

        click_button('Save')
      end

      assert_current_path ping_path(ping.reload)

      within('.ping-content') do
        assert_text title
        assert_text content
      end

      within('.ping-metadata') do
        assert_text format_date(voting_opens_at)
        assert_text format_date(voting_closes_at)
      end
    end

    private

    def assert_ping_created
      super
      assert_ping_is_draft
    end

    def assert_ping_is_draft
      within('.content-main') do
        assert_text('This Ping is a draft')
      end
    end

    def assert_type_required
      assert_field_required 'Type', message: 'Type must exist'
    end

    def click_create_button = click_button('Save as Draft')

    def creator(*)
      @creator ||= FactoryBot.create(:pings_admin)
    end

    def default_values
      super.merge(type: -> { pings_ping_types(:paid) }, voting_opens_at: -> { 1.week.from_now })
    end

    def fill_in_form
      select(@submitted_values[:type].name, from: 'Type') if @submitted_values.key?(:type)
      fill_in('Voting opens at', with: @submitted_values[:voting_opens_at])

      super
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'pings_system_test_case'

module Pings
  class AnswersTest < ApplicationSystemTestCase
    include PingsSystemTestCase

    test 'post an answer to a ping' do
      expert = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping)

      login_as(expert)

      visit ping_path(ping)

      submit_answer_form

      sleep(0.1)

      assert_current_path ping_path(ping)

      within_answer_list do
        assert_text(@submitted_values[:content])
      end
    end

    test 'answering to a Ping auto-subscribes the author' do
      expert = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping)

      login_as(expert)

      visit ping_path(ping)

      within('.ping-answer-form') do
        fill_in_rich_text_area 'answer_content', with: Faker::Lorem.paragraph

        click_button('Submit Answer')
      end

      sleep(0.2)

      assert_current_path ping_path(ping)

      within('.ping-subscribe') do
        assert_text('You are receiving notifications because you answered this Ping.')
      end
    end

    test 'answering to a <PERSON> does not auto-subscribe when auto-subscribe is disabled' do
      expert = FactoryBot.create(:expert, :autosubscribe_disabled, :with_display_name)
      ping = FactoryBot.create(:ping)

      login_as(expert)

      visit ping_path(ping)

      within('.ping-answer-form') do
        fill_in_rich_text_area 'answer_content', with: Faker::Lorem.paragraph

        click_button('Submit Answer')
      end

      sleep(0.1)

      assert_current_path ping_path(ping)

      within('.ping-subscribe') do
        assert_no_text('You are receiving notifications because you answered this Ping.')
      end
    end

    test 'post answer with display name' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping)

      login_as(expert)

      visit ping_path(ping)

      submit_answer_form(include_optional: :display_name)

      assert_current_path ping_path(ping)

      sleep(0.1)

      answer = ping.answers.last
      within_answer(answer) do
        assert_text("#{@submitted_values[:display_name]} (me)")
      end
    end

    test 'cannot create an Answer w/o a display_name when one is required' do
      expert = FactoryBot.create(:expert, ping_credits: 1)
      ping = FactoryBot.create(:ping)

      login_as(expert)

      visit ping_path(ping)

      submit_answer_form

      sleep(0.2)

      within_form_group(label: 'Display name') do
        assert_content('Display name can\'t be blank')
      end
    end

    test 'cannot post an answer when unconfirmed' do
      expert = FactoryBot.create(:expert, :unconfirmed, :with_display_name)
      ping = FactoryBot.create(:ping)

      login_as(expert)

      visit ping_path(ping)

      assert_text 'You cannot post, answer or vote on a Ping until you have confirmed your email address.'
      assert_no_text 'Your account is registered as an observer account. Only expert can answer and vote on Pings.'
      assert_no_link 'Sign In to Answer'

      assert find('form fieldset')[:disabled],
             'there should be disabled teaser fields'
    end

    test 'experts can see their own answer when voting is closed' do
      ping = FactoryBot.create(:ping, :paid, :published, :voting_closed)
      expert = FactoryBot.create(:expert, :with_display_name)
      answer = FactoryBot.create(:ping_answer, ping:, author: expert)

      login_as(expert)

      visit ping_path(ping)

      within_answer(answer) do
        assert_text answer.content.to_plain_text
        assert_no_button('Up vote this answer')
        assert_no_button('Down vote this answer')
      end
    end

    test 'experts can edit their own answer when voting is unopened' do
      ping = FactoryBot.create(:ping, :paid, :published, :voting_unopened)
      expert = FactoryBot.create(:expert, :with_display_name)
      answer = FactoryBot.create(:ping_answer, ping:, author: expert)
      content = Faker::Lorem.paragraphs.join

      login_as(expert)

      visit ping_path(ping)

      sleep(0.1)

      within_answer(answer) do
        click_link 'Edit'
      end

      sleep(0.1)

      within('.ping-answer-form') do
        fill_in_rich_text_area 'answer_content', with: content

        click_button('Update Answer')
      end

      sleep(0.1)

      assert_current_path ping_path(ping)

      within_answer(answer) do
        assert_text content
      end
    end

    test 'subscribes answerer when notifications are supported' do
      ping = FactoryBot.create(:ping, :published, :paid)

      login_as FactoryBot.create(:expert, :with_display_name)

      visit ping_path(ping)

      submit_answer_form

      within('.content-sidebar') do
        assert_text 'You are receiving notifications because you answered'
      end
    end

    test 'non-experts cannot answer a Ping' do
      observer = FactoryBot.create(:observer)
      ping = FactoryBot.create(:ping)

      login_as observer

      visit ping_path(ping)

      assert_text 'Your account is registered as an observer account. Only experts can post, answer and vote on Pings.'

      assert_no_link 'Sign In to Answer'
    end

    test 'logged out users are prompted to login to answer' do
      ping = FactoryBot.create(:ping)

      visit ping_path(ping)

      assert_link 'Sign In to Answer'
      assert_no_text 'Your account is registered as an observer account. Only experts can answer and vote on Pings.'
    end

    private

    def submit_answer_form(without: [], include_optional: [], **overrides)
      default_values = { content: -> { Faker::Lorem.paragraphs.join } }
      optional_values = { display_name: -> { Faker::Internet.username } }

      values = default_values.except(*without)
      values = values.merge(optional_values.slice(*include_optional))

      @submitted_values = values.transform_values(&:call).merge(overrides)

      within('.ping-answer-form') do
        fill_in_rich_text_area 'answer_content', with: @submitted_values[:content]
        fill_in('Display name', with: @submitted_values[:display_name]) if @submitted_values.key?(:display_name)

        click_button('Submit Answer')
      end
    end
  end
end

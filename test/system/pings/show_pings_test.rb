# frozen_string_literal: true

require 'application_system_test_case'

module Pings
  class ShowPingTest < ApplicationSystemTestCase
    test 'view ping on index page' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping)

      login_as expert

      visit pings_path

      within('.list-group-pings') do
        assert_content(ping.title)
      end
    end

    test 'expertise tags appear on pings listing' do
      author = users(:expert)
      tag = expertises(:expertise1)

      ping = author.pings.create!(
        title: 'Some ping title',
        content: 'Generic Body',
        type: pings_ping_types(:public),
        expertises: [tag]
      )

      visit pings_path

      within('.list-group-pings') do
        assert_content(ping.title)
        assert_content(tag.name)
      end
    end

    test 'view ping show page' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping)

      login_as expert

      visit ping_path(ping)

      assert_content(ping.title)
    end

    test 'a missing ping takes you back to pings index' do
      FactoryBot.create(:ping)
      unknown_ping_id = Ping.maximum(:id) + 1

      visit ping_path(unknown_ping_id)

      assert_current_path pings_path
      assert_content 'Unable to locate the Ping you requested'
    end

    test 'a hidden ping takes you back to pings index' do
      ping = FactoryBot.create(:ping, :hidden)

      visit ping_path(ping.id)

      assert_current_path pings_path
      assert_content 'This Ping has been removed by SciPinion moderators'
    end

    test 'regular users cannot view draft ping on index page' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping, :paid, :draft)

      login_as expert

      visit pings_path

      within('#pings_list_card') do
        assert_no_content(ping.title)
      end
    end

    test 'regular users cannot view draft ping on show page' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping, :paid, :draft)

      login_as expert

      visit pings_path

      within('#pings_list_card') do
        assert_no_content(ping.title)
      end
    end

    test 'Pings Admins can view draft pings on show page' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :paid, :draft)

      login_as admin

      visit ping_path(ping)

      within('h1') do
        assert_content(ping.title)
      end
    end

    test 'default order is highest score first' do
      user = FactoryBot.create(:user)
      ping = FactoryBot.create(:ping)
      most_upvotes = FactoryBot.create(:ping_answer, ping:, upvote_count: 2)
      FactoryBot.create(:ping_answer, ping:, upvote_count: 0)

      login_as user

      visit ping_path(ping)

      within('.answer-list') do
        ping_element = first(:css, '.ping-answer')
        assert ping_element.has_text?(most_upvotes.content.to_plain_text)
      end
    end

    test 'sort answers by most recent' do
      user = FactoryBot.create(:user)
      ping = FactoryBot.create(:ping)
      most_recent = FactoryBot.create(:ping_answer, ping:, upvote_count: 0, created_at: Time.current)
      least_recent = FactoryBot.create(:ping_answer, ping:, upvote_count: 2, created_at: 1.day.ago)

      login_as user

      visit ping_path(ping)

      click_button('Sort')
      sleep(0.2)

      within('.dropdown-menu') do
        click_link('Newest first')
        sleep(0.2)
      end

      within('.answer-list') do
        ping_elements = all(:css, '.ping-answer')

        assert ping_elements.first.has_text?(most_recent.content.to_plain_text)
        assert ping_elements.last.has_text?(least_recent.content.to_plain_text)
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'pings_system_test_case'

module Pings
  class ModerationTest < ApplicationSystemTestCase
    include PingsSystemTestCase

    test 'flag ping for moderation' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping)
      reason = FactoryBot.create(:moderation_reason, :user_facing)

      login_as(expert)

      visit ping_path(ping)

      within('.ping-content') do
        click_link('Flag')
      end

      within('.moderation-request-form') do
        choose(reason.name)
        fill_in('Comment', with: Faker::Lorem.sentences.join("\n"))

        click_button('Submit')
      end

      assert_current_path ping_path(ping)

      assert_content 'Thank you. Your request for moderation has been received.'
    end

    test 'requires comment' do
      expert = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping)
      reason = FactoryBot.create(:moderation_reason, :user_facing, :require_comment)

      login_as(expert)

      visit ping_path(ping)

      within('.ping-content') do
        click_link('Flag')
      end

      within('.moderation-request-form') do
        choose(reason.name)

        click_button('Submit')
      end
      sleep(0.2)

      assert_content 'A comment is required'
    end

    test 'flag ping answer for moderation' do
      expert = FactoryBot.create(:expert)
      answer = FactoryBot.create(:ping_answer)
      reason = FactoryBot.create(:moderation_reason, :user_facing)

      login_as(expert)

      visit ping_path(answer.ping)

      within_answer(answer) do
        click_link('Flag')
      end

      within('.moderation-request-form') do
        choose(reason.name)
        fill_in('Comment', with: Faker::Lorem.sentences.join("\n"))

        click_button('Submit')
      end

      assert_current_path ping_path(answer.ping)

      assert_content 'Thank you. Your request for moderation has been received.'
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Pings
  class EditPingTest < ApplicationSystemTestCase
    test 'edit a ping' do
      inside_edit_window = (Ping::EDIT_WINDOW_DURATION - 1.minute).ago
      ping = FactoryBot.create(:ping, created_at: inside_edit_window)
      author = ping.author

      login_as(author)

      visit ping_path(ping)

      within('.ping-content') do
        click_link 'Edit'
      end

      title = Faker::Lorem.question
      content = Faker::Lorem.paragraphs.join

      sleep(0.2)

      within('.ping-form') do
        fill_in('Your Question', with: title)
        fill_in_rich_text_area 'ping_content', with: content

        click_button('Save')
      end
      sleep(0.2)

      assert_selector('.ping-content', text: title)
    end

    test 'no link visible after window is closed' do
      outside_edit_window = (Ping::EDIT_WINDOW_DURATION + 1.minute).ago
      ping = FactoryBot.create(:ping, created_at: outside_edit_window)
      author = ping.author

      login_as(author)

      visit ping_path(ping)

      within('.ping-content') do
        assert_no_content('Edit')
      end
    end

    test 'non-author cannot edit' do
      inside_edit_window = (Ping::EDIT_WINDOW_DURATION - 1.minute).ago
      ping = FactoryBot.create(:ping, created_at: inside_edit_window)

      login_as(FactoryBot.create(:expert))

      visit ping_path(ping)

      within('.ping-content') do
        assert_no_content('Edit')
      end
    end

    test 'ping admins can edit any ping' do
      ping = FactoryBot.create(:ping, :outside_change_window)

      login_as(FactoryBot.create(:pings_admin))

      visit ping_path(ping)

      within('.ping-admin') do
        click_link 'Edit Ping'
      end

      sleep(0.2)

      assert_current_path edit_ping_path(ping)
    end
  end
end

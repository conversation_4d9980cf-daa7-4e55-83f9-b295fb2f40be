# frozen_string_literal: true

require 'application_system_test_case'

module Pings
  module CreationTest
    def test_default_creation_ping
      login_and_visit_new_path

      submit_create_ping_form

      assert_ping_created
    end

    def test_cannot_create_a_ping_without_a_title
      login_and_visit_new_path

      submit_create_ping_form(without: :title)

      assert_title_required
    end

    def test_cannot_create_a_ping_without_a_question_content
      login_and_visit_new_path

      submit_create_ping_form(without: :content)

      assert_content_required
    end

    private

    def assert_content_required
      assert_field_required 'Question Details'
    end

    def assert_ping_created
      within('.ping-content') do
        assert_text(@submitted_values[:title])
        assert_text(@submitted_values[:content])
        assert_text(@submitted_values[:expertise])
      end

      within('.ping-metadata') do
        assert_text("#{@submitted_values[:display_name]} (me)")
      end
    end

    def assert_title_required
      assert_field_required 'Your Question'
    end

    def default_values
      {
        title: -> { Faker::Lorem.question },
        content: -> { Faker::Lorem.paragraphs.join },
        expertise: -> { expertises(:expertise1).name }
      }
    end

    def fill_in_form
      fill_in('Your Question', with: @submitted_values[:title]) if @submitted_values[:title].present?
      fill_in_rich_text_area 'ping_content', with: @submitted_values[:content] if @submitted_values[:content].present?

      select_from_slim_select(@submitted_values[:expertise], from: 'Expertise') if @submitted_values.key?(:expertise)
    end

    def login_and_visit_new_path(*, **)
      login_as(creator(*, **))

      visit new_ping_path
    end

    def optional_values = {}

    def submit_create_ping_form(without: [], include_optional: [], **overrides)
      values = default_values.except(*without)
      values = values.merge(optional_values.slice(*include_optional))

      @submitted_values = values.transform_values(&:call).merge(overrides)

      fill_in_form

      click_create_button

      sleep(0.2)
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Pings
  class SubscriptionsTest < ApplicationSystemTestCase
    test 'non-participants are not subscribed by default' do
      ping = FactoryBot.create(:ping, :published)
      login_as FactoryBot.create(:expert)

      visit ping_path(ping)

      within('.content-sidebar') do
        assert_text(/Notifications/i)
        assert_button 'Subscribe'
        assert_subscribe_text
      end
    end

    test 'subscribe to <PERSON> as non-participant' do
      ping = FactoryBot.create(:ping, :published)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit ping_path(ping)

      within('.content-sidebar') do
        click_button 'Subscribe'
      end

      within('.content-sidebar') do
        assert_text 'You are receiving notifications because you subscribed'
      end
    end

    test 'subscriber unsubscribes from Ping' do
      ping = FactoryBot.create(:ping, :published)
      expert = FactoryBot.create(:expert)
      ping.subscribers << expert

      login_as expert

      visit ping_path(ping)

      within('.content-sidebar') do
        click_button 'Unsubscribe'
      end

      sleep(0.2)

      within('.content-sidebar') do
        assert_subscribe_text
      end
    end

    test 'unsubscribe while logged out' do
      ping = FactoryBot.create(:ping, :published)
      subscriber = FactoryBot.create(:user)
      subscription = ping.subscriptions.create!(subscriber:)

      visit pings_subscription_path(token: subscription.token)

      click_button 'Unsubscribe'

      assert_alert(text: 'You have been successfully unsubscribed!')

      assert_button text: 'Resubscribe'
    end

    test 'resubscribe while logged out' do
      ping = FactoryBot.create(:ping, :published)
      subscriber = FactoryBot.create(:user)
      subscription = ping.subscriptions.create!(subscriber:, unsubscribed_at: 1.day.ago)

      visit pings_subscription_path(token: subscription.token)

      click_button 'Resubscribe'

      assert_alert(text: 'You have been successfully resubscribed!')

      assert_button text: 'Unsubscribe'
    end

    private

    def assert_subscribe_text
      assert_text 'Subscribe to receive notifications when new answers are posted, or when an answer is accepted.'
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'pings_system_test_case'

module Pings
  class PingAdministrationTest < ApplicationSystemTestCase
    include ApplicationHelper
    include PingsSystemTestCase

    test 'save and publish Ping from edit page' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :paid, :draft, author: admin)

      login_as admin

      visit edit_ping_path(ping)

      sleep(0.3)

      title = Faker::Lorem.sentence

      within('.ping-form') do
        fill_in('Your Question', with: title)
        fill_in('Reward', with: 5)

        accept_confirm do
          click_button 'Save & Publish'

          sleep(0.1)
        end
      end

      sleep(0.3)

      within('.content-main') do
        assert_no_content('This Ping is a draft')
        assert_text title
      end
    end

    test 'blacklist a ping from admin dash' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :paid, :published, author: admin)

      login_as admin

      visit admin_pings_path

      sleep(0.3)

      within(".pings-list #ping-#{ping.id}") do
        accept_confirm do
          click_button('Blacklist')
        end
      end

      sleep(0.3)

      assert_text("'#{ping.name}' is now Blacklisted")
    end

    test 'open voting on a paid Ping' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :paid, :published, author: admin)

      login_as admin

      visit ping_path(ping)

      sleep(0.3)

      within('.content-sidebar') do
        accept_confirm do
          click_button('Open Voting')
        end
      end

      sleep(0.3)

      assert_text('Voting is now open!')
    end

    test 'future schedule voting open date' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :paid, :published, author: admin)
      voting_opens_at = 1.week.from_now.change(min: 0, sec: 0, nsec: 0)

      login_as admin

      visit edit_ping_path(ping)

      sleep(0.3)

      within('.ping-form') do
        fill_in('ping[voting_opens_at_date]', with: voting_opens_at)
        select(voting_opens_at.strftime('%I %p'), from: 'ping[voting_opens_at_hour]')

        click_button('Save')
      end

      sleep(0.3)

      assert_current_path ping_path(ping.reload)

      within('.ping-metadata') do
        assert_text format_date(voting_opens_at)
      end
    end

    test 'future schedule voting close date' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :paid, :published, :voting_open, author: admin)
      voting_closes_at = 1.week.from_now.change(min: 0, sec: 0, nsec: 0)

      login_as admin

      visit edit_ping_path(ping)

      sleep(0.3)

      within('.ping-form') do
        fill_in('ping[voting_closes_at_date]', with: voting_closes_at)
        select(voting_closes_at.strftime('%I %p'), from: 'ping[voting_closes_at_hour]')

        click_button('Save')
      end

      sleep(0.3)

      assert_current_path ping_path(ping.reload)

      within('.ping-metadata') do
        assert_text format_date(voting_closes_at)
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'creation_test'

module Pings
  class AdminCreationTest < ApplicationSystemTestCase
    include CreationTest

    test 'cannot create a ping without a type' do
      login_and_visit_new_path

      submit_create_ping_form(without: :type)

      assert_type_required
    end

    test 'create public ping from admin account' do
      title = Faker::Lorem.question

      login_and_visit_new_path

      select(pings_ping_types(:public).name, from: 'Type')
      fill_in('Your Question', with: title)
      fill_in_rich_text_area 'ping_content', with: Faker::Lorem.paragraphs.join

      select_from_slim_select(expertises(:expertise1).name, from: 'Expertise')

      click_button('Save & Publish')

      within('.ping-content') do
        assert_text title
      end
    end

    test 'create public promoted ping from admin account' do
      title = Faker::Lorem.question

      login_and_visit_new_path

      select(pings_ping_types(:public).name, from: 'Type')
      fill_in('Your Question', with: title)
      fill_in_rich_text_area 'ping_content', with: Faker::Lorem.paragraphs.join
      select_from_slim_select(expertises(:expertise1).name, from: 'Expertise')
      fill_in('ping[promote_until]', with: 1.week.from_now)

      click_button('Save & Publish')

      within('.ping-content') do
        assert_text title
        assert_text('Featured')
      end
    end

    private

    def assert_ping_created
      super
      assert_ping_is_draft
    end

    def assert_ping_is_draft
      within('.content-main') do
        assert_text('This Ping is a draft')
      end
    end

    def assert_type_required
      assert_field_required 'Type', message: 'Type must exist'
    end

    def click_create_button = click_button('Save as Draft')

    def creator(*)
      @creator ||= FactoryBot.create(:pings_admin)
    end

    def default_values
      super.merge(
        reward_amount: -> { rand(100..1000) },
        type: -> { pings_ping_types(:paid) },
        voting_opens_at: -> { 1.week.from_now }
      )
    end

    def fill_in_form
      select(@submitted_values[:type].name, from: 'Type') if @submitted_values.key?(:type)

      fill_in('ping[voting_opens_at_date]', with: @submitted_values[:voting_opens_at_date]) if @submitted_values[:voting_opens_at_date]
      select(@submitted_values[:voting_opens_at_hour], from: 'ping[voting_opens_at_hour]') if @submitted_values[:voting_opens_at_hour]

      fill_in('Reward', with: @submitted_values[:reward_amount]) if @submitted_values.key?(:reward_amount) && @submitted_values.key?(:type)

      super
    end

    def optional_values
      {
        voting_opens_at_date: -> { 1.week.from_now },
        voting_opens_at_hour: -> { rand(0..23) }
      }
    end
  end
end

# frozen_string_literal: true

# Add these tests to test/system/pings/show_pings_test.rb (or create a new test file)

require 'application_system_test_case'

module Pings
  class HiddenAnswersTest < ApplicationSystemTestCase
    test 'public users see hidden answer placeholders when show_hidden param is present' do
      ping = FactoryBot.create(:ping, :published)
      FactoryBot.create(:ping_answer, ping: ping, content: 'This is a visible answer')
      FactoryBot.create(:ping_answer, :hidden, ping: ping, content: 'This is a hidden answer')

      visit ping_path(ping, show_hidden: 'true')

      assert_content 'This is a visible answer'
      assert_content 'Answer has been removed by the moderator'
      assert_no_content 'This is a hidden answer'
    end

    test 'admins see both visible answers and full hidden answer content' do
      admin = FactoryBot.create(:pings_admin)
      ping = FactoryBot.create(:ping, :published)
      FactoryBot.create(:ping_answer, ping: ping, content: 'This is a visible answer')
      FactoryBot.create(:ping_answer, :hidden, ping: ping, content: 'This is a hidden answer')

      login_as admin
      visit ping_path(ping)

      assert_content 'This is a visible answer'
      assert_content 'This is a hidden answer'
      assert_selector '.opacity-25'
    end
  end
end

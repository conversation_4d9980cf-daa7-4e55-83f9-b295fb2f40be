# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'pings_system_test_case'

module Pings
  class VotingTest < ApplicationSystemTestCase
    include PingsSystemTestCase

    test 'upvote answer' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)

      login_as(expert)

      visit ping_path(answer.ping)

      click_upvote(answer)

      assert_score_equals(1, answer:)
    end

    test 'upvote answer when score is hidden' do
      ping = FactoryBot.create(:ping, :paid, :voting_open)
      answer = FactoryBot.create(:ping_answer, ping:)
      expert = FactoryBot.create(:expert)

      login_as(expert)

      visit ping_path(answer.ping)

      click_upvote(answer)

      assert_no_selector("#{answer_selector(answer)} .score")
      assert_upvote_button_selected(answer)
    end

    test 'undo upvote' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.upvote!(expert)

      login_as(expert)

      visit ping_path(answer.ping)

      click_undo_upvote(answer)

      assert_score_equals(0, answer:)
    end

    test 'down vote answer' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)

      login_as(expert)

      visit ping_path(answer.ping)

      click_downvote(answer)

      assert_downvote_button_selected(answer)
      assert_score_equals(-1, answer:)
    end

    test 'undo down vote' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.downvote!(expert)

      login_as(expert)

      visit ping_path(answer.ping)

      click_undo_downvote(answer)

      assert_score_equals(0, answer:)
    end

    test 'down-voting disabled' do
      ping = FactoryBot.create(:ping, :downvoting_disabled, :voting_open)
      answer = FactoryBot.create(:ping_answer, ping:)
      expert = FactoryBot.create(:expert)

      login_as(expert)

      visit ping_path(answer.ping)

      assert_no_selector("#{answer_selector(answer)} .downvote-button")
    end

    test 'cannot vote on own answer' do
      expert = FactoryBot.create(:expert, :with_display_name)
      answer = FactoryBot.create(:ping_answer, author: expert)

      login_as(expert)

      visit ping_path(answer.ping)

      assert_voting_disabled(answer)
    end

    test 'unconfirmed users cannot vote on answers' do
      expert = FactoryBot.create(:expert, :unconfirmed)
      answer = FactoryBot.create(:ping_answer)

      login_as(expert)

      visit ping_path(answer.ping)

      assert_voting_disabled(answer)
    end

    test 'scores are not visible until paid Pings have accepted an answer' do
      expert = FactoryBot.create(:expert, :confirmed)
      ping = FactoryBot.create(:ping, :paid, :voting_open)
      answer = FactoryBot.create(:ping_answer, ping:)

      login_as(expert)

      visit ping_path(answer.ping)

      assert_score_not_visible(answer)
    end

    test 'show scores paid Pings with an accepted answer' do
      expert = FactoryBot.create(:expert, :confirmed)
      ping = FactoryBot.create(:ping, :paid, :voting_open)
      answer = FactoryBot.create(:ping_answer, :accepted, ping:)

      login_as(expert)

      visit ping_path(answer.ping)

      assert_selector("#{answer_selector(answer)} .score")
    end

    test 'voting not yet open' do
      ping = FactoryBot.create(:ping, :paid, :published)
      FactoryBot.create(:ping_answer, ping:)

      expert = FactoryBot.create(:expert)

      login_as(expert)

      visit ping_path(ping)

      assert_answers_not_yet_visible
    end

    test 'voting open' do
      expert = FactoryBot.create(:expert, :confirmed)
      ping = FactoryBot.create(:ping, :paid, :voting_open, :voting_not_yet_closed)
      answer = FactoryBot.create(:ping_answer, ping:)

      login_as(expert)

      visit ping_path(ping)

      assert_answers_visible
      assert_voting_enabled(answer)
    end

    test 'voting closed' do
      expert = FactoryBot.create(:expert, :confirmed)
      ping = FactoryBot.create(:ping, :paid, :published, :voting_closed)
      FactoryBot.create(:ping_answer, ping:)

      login_as(expert)

      visit ping_path(ping)

      assert_answers_visible
      assert_voting_closed
    end

    private

    def assert_answers_not_yet_visible
      within('.answers-content') do
        assert_answers_not_visible
      end
      assert_text('Accepting Answers')
    end

    def assert_answers_not_visible
      assert_no_selector('.answer-list')
    end

    def assert_answers_visible
      assert_selector('.answer-list')
    end

    def assert_downvote_button_selected(answer)
      answer_selector = answer_selector(answer)
      assert_selector("#{answer_selector} .downvote-button.vote-button--voted")
    end

    def assert_score_equals(score, answer:)
      answer_selector = answer_selector(answer)
      within("#{answer_selector} .score") do
        assert_content(score)
      end
    end

    def assert_score_not_visible(answer)
      assert_no_selector("#{answer_selector(answer)} .score")
    end

    def assert_upvote_button_selected(answer)
      answer_selector = answer_selector(answer)
      assert_selector("#{answer_selector} .upvote-button.vote-button--voted")
    end

    def assert_voting_closed
      assert_text 'Reviewing Results'
      assert_no_selector('.upvote-button')
      assert_no_selector('.downvote-button')
    end

    def assert_voting_disabled(answer)
      within_answer(answer) do
        assert find('.upvote-button').disabled?
        assert find('.downvote-button').disabled?
      end
    end

    def assert_voting_enabled(answer)
      within_answer(answer) do
        assert_not find('.upvote-button').disabled?
      end
    end

    def click_downvote(answer)
      within_answer(answer) { click_button('Downvote this answer') }
    end

    def click_upvote(answer)
      within_answer(answer) { click_button('Upvote this answer') }
    end

    def click_undo_downvote(answer)
      within_answer(answer) { click_button('Click to undo') }
    end

    def click_undo_upvote(answer)
      within_answer(answer) { click_button('Click to undo') }
    end
  end
end

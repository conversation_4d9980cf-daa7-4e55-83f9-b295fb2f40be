# frozen_string_literal: true

require 'application_system_test_case'

module Pings
  class PublicTest < ApplicationSystemTestCase
    test 'view ping on index page' do
      ping = FactoryBot.create(:ping)

      visit pings_path

      within('.list-group-pings') do
        assert_content(ping.title)
      end
    end

    test 'public visit of my pings url redirects to login' do
      visit pings_path(filter: :my_pings)

      assert_current_path login_path
    end

    test 'answered ping url does not redirect and loads correct pings' do
      unanswered_ping = FactoryBot.create(:ping)

      answered_ping = FactoryBot.create(:ping)
      FactoryBot.create(:ping_answer, ping: answered_ping)

      visit pings_path(filter: :answered)

      assert_current_path pings_path(filter: :answered)
      assert_content(answered_ping.title)
      assert_no_content(unanswered_ping.title)
    end

    test 'view ping show page' do
      ping = FactoryBot.create(:ping)
      FactoryBot.create(:ping_answer, ping:)

      visit ping_path(ping)

      assert_content(ping.title)
    end

    test 'cannot view hidden ping on index page' do
      ping = FactoryBot.create(:ping, :hidden)

      visit pings_path

      within('#pings_list_card') do
        assert_no_content(ping.title)
      end
    end

    test 'view ping meta image' do
      ping = FactoryBot.create(:ping)

      visit ping_path(ping)

      assert_selector("meta[property='og:image'][content*='scipinion-ping-']", visible: false)
    end

    test 'promoted ping gets featured badge on show page' do
      ping = FactoryBot.create(:ping, :promoted)

      visit ping_path(ping)

      within('div.content-main') do
        assert_selector('span.badge', text: 'Featured')
      end
    end

    test 'promoted ping gets featured badge on index page' do
      FactoryBot.create(:ping, :promoted)

      visit pings_path

      within('.list-group-pings') do
        assert_selector('span.badge', text: 'Featured')
      end
    end

    test 'past promoted ping has no featured flag' do
      ping = FactoryBot.create(:ping, promote_until: 1.week.ago)

      visit ping_path(ping)

      within('div.content-main') do
        assert_content(ping.title)
        assert_no_content('Featured')
      end
    end

    test 'JSON-LD for ping with accepted and unaccepted answers' do
      ping = FactoryBot.create(:ping)
      accepted_answer = FactoryBot.create(:ping_answer, :accepted, ping:, upvote_count: 5)
      unaccepted_answer = FactoryBot.create(:ping_answer, ping:, upvote_count: 3)

      visit ping_path(ping)

      script_tag_content = page.find('script[type="application/ld+json"]', visible: false)['innerHTML']
      json_ld_data = JSON.parse(script_tag_content)

      assert_equal 'Question', json_ld_data['mainEntity']['@type']
      assert_equal ping.title, json_ld_data['mainEntity']['name']
      assert_equal 2, json_ld_data['mainEntity']['answerCount']

      assert json_ld_data['mainEntity']['acceptedAnswer'].present?
      assert_equal accepted_answer.content.to_plain_text, json_ld_data['mainEntity']['acceptedAnswer']['text']
      assert_equal 5, json_ld_data['mainEntity']['acceptedAnswer']['upvoteCount']

      assert_equal 1, json_ld_data['mainEntity']['suggestedAnswer'].size
      suggested_answer = json_ld_data['mainEntity']['suggestedAnswer'].first
      assert_equal unaccepted_answer.content.to_plain_text, suggested_answer['text']
      assert_equal 3, suggested_answer['upvoteCount']
    end
  end
end

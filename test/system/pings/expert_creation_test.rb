# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'creation_test'

module Pings
  class ExpertCreationTest < ApplicationSystemTestCase
    test 'create a Ping' do
      expert = FactoryBot.create(:expert, ping_credits: 1, profile_attributes: { display_name: 'Pinger' })
      title = Faker::Lorem.question
      content = Faker::Lorem.paragraph
      expertise = FactoryBot.create(:expertise).name

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: title)
      fill_in_rich_text_area 'ping_content', with: content

      select_from_slim_select(expertise, from: 'Expertise')

      click_button('Post Question')

      within('.ping-content') do
        assert_text(title)
        assert_text(content)
        assert_text(expertise)
      end

      within('.ping-metadata') do
        assert_text("#{expert.display_name} (me)")
      end

      within('.ping-subscribe') do
        assert_text('You are receiving notifications because you posted this Ping')
      end
    end

    test 'expert creates a Ping with a display name' do
      expert = FactoryBot.create(:expert, ping_credits: 1)
      display_name = Faker::Internet.username
      title = Faker::Lorem.question
      content = Faker::Lorem.paragraph
      expertise = FactoryBot.create(:expertise).name

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: title)
      fill_in_rich_text_area 'ping_content', with: content

      select_from_slim_select(expertise, from: 'Expertise')

      fill_in('Display name', with: display_name)

      click_button('Post Question')

      sleep(0.5) # Wait for action to complete

      within('.ping-content') do
        assert_text(title)
        assert_text(content)
        assert_text(expertise)
      end

      within('.ping-metadata') do
        assert_text("#{expert.display_name} (me)")
      end

      within('.ping-subscribe') do
        assert_text('You are receiving notifications because you posted this Ping')
      end
    end

    test 'cannot create a ping without a title' do
      expert = FactoryBot.create(:expert, ping_credits: 1, profile_attributes: { display_name: 'Pinger' })
      expertise = FactoryBot.create(:expertise).name

      login_as expert

      visit new_ping_path

      fill_in_rich_text_area 'ping_content', with: 'My content'
      select_from_slim_select(expertise, from: 'Expertise')

      click_button('Post Question')

      assert_field_required 'Your Question'
    end

    test 'cannot create a ping without a question content' do
      expert = FactoryBot.create(:expert, ping_credits: 1, profile_attributes: { display_name: 'Pinger' })
      expertise = FactoryBot.create(:expertise).name

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: 'My question?')
      select_from_slim_select(expertise, from: 'Expertise')

      sleep(0.2)

      click_button('Post Question')

      assert_field_required 'Question Details'
    end

    test 'cannot create a ping without at least one expertise tag' do
      expert = FactoryBot.create(:expert, ping_credits: 1, profile_attributes: { display_name: 'Pinger' })
      title = Faker::Lorem.question
      content = Faker::Lorem.paragraph

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: title)
      fill_in_rich_text_area 'ping_content', with: content

      click_button('Post Question')

      sleep(0.2)

      assert_text 'should have between 1 and 5 expertise tags'
    end

    test 'cannot create a Ping w/o a display_name when one is required' do
      expert = FactoryBot.create(:expert, ping_credits: 1)
      expertise = FactoryBot.create(:expertise).name

      login_as expert

      visit new_ping_path

      sleep(0.2) # Let JS load, for slim select

      fill_in('Your Question', with: 'My question?')
      fill_in_rich_text_area 'ping_content', with: 'My content'
      select_from_slim_select(expertise, from: 'Expertise')
      fill_in('Display name', with: '')

      click_button('Post Question')

      assert_field_required 'Display name'
    end

    test 'cannot create a Ping with a display_name that is already taken' do
      another_expert = FactoryBot.create(:expert, ping_credits: 1, profile_attributes: { display_name: 'Pinger' })
      expert = FactoryBot.create(:expert, ping_credits: 1)
      expertise = FactoryBot.create(:expertise).name

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: 'My question?')
      fill_in_rich_text_area 'ping_content', with: 'My content'
      select_from_slim_select(expertise, from: 'Expertise')
      fill_in('Display name', with: another_expert.display_name)

      click_button('Post Question')

      within_form_group(label: 'Display name') do
        assert_text("Display name '#{another_expert.display_name}' is already taken")
      end
    end

    test 'cannot create a ping - logged out' do
      visit new_ping_path

      assert_current_path welcome_path
    end

    test 'cannot create a ping - not enough credit' do
      expert = FactoryBot.create(:expert, ping_credits: 0)

      login_as expert

      visit new_ping_path

      assert_text 'Insufficient Ping Credits'
    end

    test 'show warning if the title not a question' do
      expert = FactoryBot.create(:expert, ping_credits: 1)

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: 'Not a question.')
      send_keys(:tab)

      assert_text 'It does not look like your Ping is phrased as a question.'
    end

    test 'do not show a warning if the title is a question' do
      expert = FactoryBot.create(:expert, ping_credits: 1)

      login_as expert

      visit new_ping_path

      fill_in('Your Question', with: 'Yes, a question?')
      send_keys(:tab)

      assert_no_text 'It does not look like your Ping is phrased as a question.'
    end
  end
end

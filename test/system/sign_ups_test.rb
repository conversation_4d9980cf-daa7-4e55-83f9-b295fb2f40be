# frozen_string_literal: true

require 'application_system_test_case'

class SignUpsTest < ApplicationSystemTestCase
  test 'cold sign up' do
    visit new_sign_up_path

    submit_sign_up_form(email: valid_email, password: valid_password)

    assert_current_path dashboard_path
    assert_selector '.global-alert', text: I18n.t('sign_ups.create.success')
  end

  test 'claim account sign up seeds email, first_name and last_name' do
    email = valid_email

    visit claim_account_path(email:, first_name: 'Firstname', last_name: 'Lastname')

    assert_content I18n.t('claim_account.intro')

    submit_sign_up_form(password: valid_password, role: nil)

    assert_current_path dashboard_path

    user = User.find_by(email:)

    assert_not_nil user
    assert_equal user.first_name, 'Firstname'
    assert_equal user.last_name, 'Lastname'
  end

  test 'sign_up filters invalid first_name' do
    email = valid_email
    invalid_first_name = 'Fellow Scientist'

    visit claim_account_path(email:, first_name: invalid_first_name)

    assert_content 'Claim your account'

    submit_sign_up_form(password: valid_password, role: nil)

    sleep(0.2)

    user = User.find_by(email:)

    assert_empty user.first_name
  end

  test 'claim account landing page handles empty firstname' do
    email = valid_email

    visit claim_account_path(email:, first_name: '', last_name: 'Lastname')

    assert_content 'Claim your account'
  end

  test 'claim account redirects to login if email already registered' do
    expert = FactoryBot.create(:expert)

    email = expert.email

    visit claim_account_path(email:, first_name: 'Firstname', last_name: 'Lastname')

    assert_current_path login_path(email:)
  end

  test 'cannot sign up with an invalid email' do
    visit new_sign_up_path

    submit_sign_up_form(email: 'not an email', password: 'not_an_email')

    assert_content I18n.t('activemodel.errors.models.sign_up.attributes.email.invalid')
  end

  test 'cannot sign up with a duplicate email' do
    email = valid_email
    User.create!(email:, password: valid_password)

    visit new_sign_up_path

    within('.sign-up-form') do
      choose_expert_role
      fill_in('Email Address', with: email)
      fill_in('Password', with: valid_password)

      click_button('Sign Up')
    end

    assert_current_path sign_ups_path
    assert_content I18n.t('activemodel.errors.models.sign_up.attributes.email.taken')
  end

  test 'cannot sign up with a missing role' do
    skip 'We set a role by default now. Someone would have to clear it in the source code to produce this scenario.'
    visit new_sign_up_path

    submit_sign_up_form(email: valid_email, password: valid_password, role: nil)

    assert_current_path sign_ups_path
    assert_content 'can\'t be blank and Invalid role selected' # I18n.t('activemodel.errors.models.sign_up.attributes.role_id.missing')
  end

  test 'cannot sign up with an missing email' do
    visit new_sign_up_path

    within('.sign-up-form') do
      choose_expert_role
      fill_in('Email Address', with: '')
      fill_in('Password', with: valid_password)

      click_button('Sign Up')
    end

    assert_current_path sign_ups_path
    assert_content 'An email address is required to sign up'
  end

  test 'cannot sign up with an missing password' do
    visit new_sign_up_path

    within('.sign-up-form') do
      choose_expert_role
      fill_in('Email Address', with: valid_email)
      fill_in('Password', with: '')

      click_button('Sign Up')
    end

    assert_current_path sign_ups_path
    assert_content 'A password is required to sign up'
  end

  test 'cannot sign up with a short password' do
    visit new_sign_up_path

    within('.sign-up-form') do
      choose_expert_role
      fill_in('Email Address', with: valid_email)
      fill_in('Password', with: 'passwor')

      click_button('Sign Up')
    end

    assert_current_path sign_ups_path
    assert_content I18n.t('activemodel.errors.models.sign_up.attributes.password.too_short')
  end

  private

  def choose_expert_role
    choose("sign_up_role_id_#{Role.expert.id}", allow_label_click: true)
  end

  def submit_sign_up_form(password:, email: nil, role: Role.expert)
    within('.sign-up-form') do
      choose("sign_up_role_id_#{role.id}", allow_label_click: true) if role.present?
      fill_in('Email Address', with: email) if email
      fill_in('Password', with: password)

      click_button('Sign Up')
    end
  end

  def valid_email
    Faker::Internet.email
  end

  def valid_password
    Faker::Internet.password
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

# NOTE: This test should be concerned with the behavior of the access token specifically,
#       not the additional behavior of the scipolls, or anywhere else access tokens are used.
class AccessTokensTest < ApplicationSystemTestCase
  test 'guest user accesses un-accessed token' do
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(:scipoll,
                                :access_token_required,
                                :allow_guest_participation,
                                :published,
                                submit_instructions: 'SciPoll Instructions')
    access_token = scipoll.access_tokens.create!

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    assert_text 'SciPoll Instructions'
  end

  test 'guest user re-accesses own accessed token from current session' do
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(:scipoll,
                                :access_token_required,
                                :allow_guest_participation,
                                :published,
                                submit_instructions: 'SciPoll Instructions')
    access_token = scipoll.access_tokens.create!

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    new_window = open_new_window
    switch_to_window new_window

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    assert_text 'SciPoll Instructions'
  end

  test 'guest user re-accesses accessed token from new session' do
    role = FactoryBot.create(:role, :guest)
    guest = role.users.create!
    scipoll = FactoryBot.create(:scipoll,
                                :access_token_required,
                                :allow_guest_participation,
                                :published,
                                submit_instructions: 'SciPoll Instructions')
    access_token = scipoll.access_tokens.create!(user: guest)

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    assert_text 'SciPoll Instructions'
  end

  test 'registered user accesses token as logged in expert' do
    expert = FactoryBot.create(:expert)
    scipoll = FactoryBot.create(:scipoll,
                                :access_token_required,
                                :allow_guest_participation,
                                :published,
                                submit_instructions: 'SciPoll Instructions')
    access_token = scipoll.access_tokens.create!

    login_as expert

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    assert_text 'SciPoll Instructions'

    click_link("SciPoll #{scipoll.id}")

    within('#sidebar') do
      assert_text 'MY ACCOUNT'
    end
  end

  test 'cannot reuse an already used access token' do
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(:scipoll,
                                :access_token_required,
                                :allow_guest_participation,
                                :published,
                                submit_instructions: 'SciPoll Instructions')
    submitter = FactoryBot.create(:user)
    FactoryBot.create(:submission, :final, question_group: scipoll, user: submitter)
    access_token = scipoll.access_tokens.create!(user: submitter)

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    assert_current_path new_sign_up_path
    assert_text 'This invitation has already been used.'
  end

  test 'invalid access token' do
    scipoll = FactoryBot.create(:scipoll, :access_token_required, :allow_guest_participation, :published)
    role = FactoryBot.create(:role, :guest)
    access_token = scipoll.access_tokens.create!
    access_token.create_user!(roles: [role])

    visit access_tokens_path(access_token: 'invalid')

    assert_current_path new_sign_up_path
    assert_text 'Your invitation is not valid. ' \
                'If you copy and pasted this link, please make sure you copied the entire link.'
  end
end

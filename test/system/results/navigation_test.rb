# frozen_string_literal: true

require 'application_system_test_case'
module Results
  class ResultsNavigationTest < ApplicationSystemTestCase
    test 'view results conclusions (a.k.a. next steps) page' do
      survey = FactoryBot.create(:survey, :results_published, :results_public, :results_conclusion)

      visit survey_results_path(survey, conclusion: true)

      within('#sidebar_nav') do
        assert_text('NEXT STEPS')
      end

      within('#content') do
        assert_text('Next Steps')
        assert_text(survey.results_conclusion.to_plain_text)
      end
    end

    test 'exit survey nav link exists when results conclusion not present' do
      survey = FactoryBot.create(:survey, :results_published, :results_public)

      visit survey_results_path(survey)

      within('#sidebar_nav') do
        assert_text('EXIT')
      end
    end
  end
end

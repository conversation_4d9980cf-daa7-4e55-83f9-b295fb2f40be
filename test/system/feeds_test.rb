# frozen_string_literal: true

require 'application_system_test_case'

class FeedsTest < ApplicationSystemTestCase
  test 'scipis feed' do
    scipi = find_first_or_create_scipi

    visit feeds_path(branding: 'scipis')

    assert_content scipi.title
  end

  test 'scipolls feed' do
    scipoll = find_first_or_create_scipoll

    visit feeds_path(branding: 'scipolls')

    assert_content scipoll.title
  end

  test 'pings feed' do
    ping = FactoryBot.create(:ping)

    visit feeds_path(branding: 'pings')

    assert_content ping.title
  end

  test 'announcements feed' do
    announcement = FactoryBot.create(:announcement, :promotable)

    visit feeds_path(branding: 'announcements')

    assert_content announcement.title
  end

  test 'featured feed has both scipis and scipolls' do
    scipi = find_first_or_create_scipi
    scipoll = find_first_or_create_scipoll

    visit feeds_path(branding: 'featured')

    assert_content scipi.title
    assert_content scipoll.title
  end

  private

  # Apparently surveys can already exist at this point in the tests
  def find_first_or_create_scipoll
    QuestionGroup.published&.scipoll&.first || FactoryBot.create(:scipoll, :published)
  end

  def find_first_or_create_scipi
    QuestionGroup.published&.scipi&.first || FactoryBot.create(:survey, :scipi, :published)
  end
end

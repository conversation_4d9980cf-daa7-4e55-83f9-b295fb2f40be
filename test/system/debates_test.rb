# frozen_string_literal: true

require 'application_system_test_case'

class DebatesTest < ApplicationSystemTestCase
  test 'view a comment' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published)
    result_definition = create_result_for(scipi)
    comment = create_comment_for(result_definition)

    expert = FactoryBot.create(:expert)
    FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

    login_as(expert)

    visit survey_result_path(survey_id: scipi, id: result_definition.id)

    assert_comment_exists(comment)
  end

  test 'create a new comment (log in first)' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published)
    result_definition = create_result_for(scipi)
    expert = FactoryBot.create(:expert)
    invite = FactoryBot.create(:invite, :approved, survey: scipi, user: expert)
    comment_text = create_comment_text

    login_as(expert)

    visit survey_result_path(survey_id: scipi, id: result_definition.id)

    fill_in_comment_form(comment_text)

    assert_comment_content(display_id: invite.expert_display_id, text: comment_text)
  end

  test 'delete own comment' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published)
    result_definition = create_result_for(scipi)

    expert = FactoryBot.create(:expert)
    FactoryBot.create(:invite, :approved, survey: scipi, user: expert)
    comment = FactoryBot.create(
      :comment,
      debate_topic: result_definition,
      user: expert
    )

    login_as(expert)

    visit survey_result_path(survey_id: scipi, id: result_definition.id)

    within("#comment_#{comment.id}") do
      click_link('Delete Comment')
    end

    assert_no_selector("#comment_#{comment.id}")
  end

  test 'cannot delete another expert\'s comment' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published)
    result_definition = create_result_for(scipi)
    comment = create_comment_for(result_definition)

    expert = FactoryBot.create(:expert)
    FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

    login_as(expert)

    visit survey_result_path(survey_id: scipi, id: result_definition.id)

    within("#comment_#{comment.id}") do
      assert_no_link 'Delete Comment'
    end
  end

  test 'comments not visible if are not allowed' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published)
    result_definition = create_result_for(scipi)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

    login_as(expert)

    visit survey_result_path(survey_id: scipi, id: result_definition.id)

    assert_no_selector('.comment-old .comment-contents')
  end

  test 'cannot see comment form when comments are closed' do
    scipi = FactoryBot.create(:survey, :scipi, :results_published, :comments_closed)
    result_definition = create_result_for(scipi)

    expert = FactoryBot.create(:expert)
    FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

    login_as(expert)

    visit survey_result_path(survey_id: scipi, id: result_definition.id)

    # assert_text('Comments are closed for this page.') # only appears if there are other comments
    assert_no_selector('#comment_new')
  end

  private

  def assert_comment_content(display_id:, text:)
    assert_text(display_id)
    assert_text(text)
  end

  def assert_comment_exists(comment)
    within("#comment_#{comment.id} .comment-contents") do
      display_id = comment.user.display_id
      text = comment.content.to_plain_text

      assert_comment_content(display_id:, text:)
    end
  end

  def create_comment_for(result_definition)
    FactoryBot.create(:comment, debate_topic: result_definition)
  end

  def create_comment_text
    Faker::Lorem.sentences.join(' ')
  end

  def create_result_for(scipi)
    FactoryBot.create(
      :result_definition,
      survey: scipi,
      result_type:,
      render_type:
    )
  end

  def fill_in_comment_form(text)
    within('#comment_new') do
      # TODO: Figure out why this doesn't work
      # fill_in_rich_text_area('comment_content', with: text)

      find('trix-editor').click.set(text)

      sleep(0.2)

      click_button('Add Comment')
    end
  end

  def render_type
    @render_type ||= result_type.default_render_type
  end

  def result_type
    @result_type ||= FactoryBot.create(:result_type, :diagnostic)
  end
end

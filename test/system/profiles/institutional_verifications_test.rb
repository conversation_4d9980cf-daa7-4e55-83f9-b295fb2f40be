# frozen_string_literal: true

require 'application_system_test_case'

module Profiles
  class InstitutionalVerificationsTest < ApplicationSystemTestCase
    test 'do not show institutional verification when expert\'s login email is unconfirmed' do
      expert = FactoryBot.create(:expert, :unconfirmed)

      login_as(expert)

      visit edit_profile_path

      assert_no_selector '.card.card-header', text: 'Institutional Verification'
    end

    test 'verification needed' do
      list = domain_lists(:personal_email)
      personal_domain = FactoryBot.create(:domain, list:)
      expert = FactoryBot.create(:expert, :confirmed, email: "user@#{personal_domain.hostname}")

      login_as(expert)

      visit edit_profile_path

      assert_text 'Institutional verification is needed'
    end

    test 'awaiting review' do
      personal_list = domain_lists(:personal_email)
      unknown_list = domain_lists(:unknown)
      personal_domain = FactoryBot.create(:domain, list: personal_list)
      unknown_domain = FactoryBot.create(:domain, list: unknown_list)
      expert = FactoryBot.create(:expert, :confirmed, email: "user@#{personal_domain.hostname}")
      FactoryBot.create(:email, :additional, :confirmed, profile: expert.profile, address: "user@#{unknown_domain.hostname}")

      login_as(expert)

      visit edit_profile_path

      assert_text 'Your institutional verification is awaiting review'
    end

    test 'pending completion' do
      personal_list = domain_lists(:personal_email)
      known_list = domain_lists(:known_institution)
      personal_domain = FactoryBot.create(:domain, list: personal_list)
      known_domain = FactoryBot.create(:domain, list: known_list)
      expert = FactoryBot.create(:expert, :confirmed, email: "user@#{personal_domain.hostname}")
      FactoryBot.create(:email, :additional, :unconfirmed, profile: expert.profile, address: "user@#{known_domain.hostname}")

      login_as(expert)

      visit edit_profile_path

      assert_text 'Your institutional email verification is pending completion'
    end

    test 'complete' do
      personal_list = domain_lists(:personal_email)
      known_list = domain_lists(:known_institution)
      personal_domain = FactoryBot.create(:domain, list: personal_list)
      known_domain = FactoryBot.create(:domain, list: known_list)
      expert = FactoryBot.create(:expert, :confirmed, email: "user@#{personal_domain.hostname}")
      FactoryBot.create(:email, :additional, :confirmed, profile: expert.profile, address: "user@#{known_domain.hostname}")

      login_as(expert)

      visit edit_profile_path

      assert_text 'Your institutional email verification is complete'
    end

    test 'resend confirmation email' do
      list = domain_lists(:personal_email)
      personal_domain = FactoryBot.create(:domain, list:)
      expert = FactoryBot.create(:expert, email: "user@#{personal_domain.hostname}")
      profile = expert.profile
      FactoryBot.create(:email, :additional, :unconfirmed, profile:)

      login_as(expert)

      visit edit_profile_path

      click_link 'Resend confirmation'

      assert_text 'Sent!'
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Sc<PERSON>olls
  class ResultsTest < ApplicationSystemTestCase
    test 'scipoll results index redirects to first result' do
      scipoll = FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)

      result_definition = create_result(scipoll:)

      visit survey_results_path(scipoll)

      assert_current_path survey_result_path(survey_id: scipoll.id, id: result_definition.id)
    end

    test 'public scipoll results redirected while scipoll is open' do
      scipoll = FactoryBot.create(:scipoll, :published, results_published: true, closes_at: 1.week.from_now, results_public: true)

      result_definition = create_result(scipoll:)

      visit survey_result_path(survey_id: scipoll.id, id: result_definition.id)

      assert_current_path login_path
    end

    test 'public scipoll results available after close' do
      scipoll = FactoryBot.create(:scipoll, :published, results_published: true, closes_at: 1.week.ago, results_public: true)

      result_definition = create_result(scipoll:)

      visit survey_result_path(survey_id: scipoll.id, id: result_definition.id)

      assert_current_path survey_result_path(survey_id: scipoll.id, id: result_definition.id)
    end

    test 'scipoll results and debate home link goes to landing page' do
      scipoll = FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)

      result_definition = create_result(scipoll:)

      visit survey_result_path(survey_id: scipoll.id, id: result_definition.id)

      click_link "SciPoll #{scipoll.id} Results & Debate"

      # We have survey_path_for helper to handle this path that is unavailbe in tests, so hardcoding it
      assert_current_path "/scipolls/#{scipoll.id}"
    end

    test 'meta image for scipoll results' do
      scipoll = FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)

      result_definition = create_result(scipoll:)

      visit survey_result_path(survey_id: scipoll.id, id: result_definition.id)

      assert_selector("meta[property='og:image'][content*='scipinion-scipoll-results-']", visible: false)
    end

    private

    def create_result(scipoll:)
      FactoryBot.create(
        :result_definition,
        :multiple_choice_responses,
        :pie_chart,
        position: rand(1..10),
        question: FactoryBot.create(:radio, survey: scipoll),
        survey: scipoll
      )
    end
  end
end

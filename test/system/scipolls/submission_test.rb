# frozen_string_literal: true

require 'application_system_test_case'

module Sc<PERSON>olls
  module Participation
    class SubmissionTest < ApplicationSystemTestCase
      test 'expert can submit answer_group' do
        scipoll = FactoryBot.create(:scipoll, :published)

        login_as FactoryBot.create(:expert)

        visit new_question_group_answer_group_path(scipoll)

        within('.main-content form') do
          click_button('Finish')
        end

        assert_current_path scipoll_path(scipoll)
        assert_link('Retake SciPoll')
        assert_alert text: 'Submission Saved'
      end

      test 'expert cannot update submission after close date' do
        scipoll = FactoryBot.create(:scipoll, :published, closes_at: 2.days.ago)
        expert = FactoryBot.create(:expert)
        submission = FactoryBot.create(:submission, :final, question_group: scipoll, user: expert)

        login_as expert

        visit scipoll_path(scipoll)

        assert_no_link('Retake SciPoll')

        visit edit_question_group_answer_group_path(scipoll, submission)

        assert_current_path dashboard_path
        assert_alert text: 'You are not authorized to access this page.'
      end

      test 'expert can save draft' do
        scipoll = FactoryBot.create(:scipoll, :published)
        expert = FactoryBot.create(:expert)

        login_as expert

        visit new_question_group_answer_group_path(scipoll)

        within('.main-content form') do
          click_button('Save Progress')
        end

        edit_submission_path = edit_question_group_answer_group_path(scipoll, scipoll.submission_for(expert))

        assert_current_path edit_submission_path
        assert_alert text: 'Progress Saved'
      end

      test 'save progress button is no longer available after submitting answer_group' do
        scipoll = FactoryBot.create(:scipoll, :published)
        expert = FactoryBot.create(:expert)

        login_as expert

        visit new_question_group_answer_group_path(scipoll)

        within('.main-content form') do
          click_button('Finish')
        end

        assert_current_path scipoll_path(scipoll) # Force a pause here to fix flakey test

        edit_submission_path = edit_question_group_answer_group_path(scipoll, scipoll.submission_for(expert))

        visit edit_submission_path

        assert_button('Finish')
        assert_no_button('Save Progress')
      end

      test 'answer_group#new routes to answer_group#edit path if submission already exists' do
        survey = FactoryBot.create(:scipoll, :published)
        expert = FactoryBot.create(:expert)
        submission = FactoryBot.create(:submission, :final, question_group: survey, user: expert)

        login_as expert

        visit new_question_group_answer_group_path(survey)

        assert_current_path edit_question_group_answer_group_path(survey, submission)
      end

      test 'new submission when previous submission already exists and survey is closed' do
        survey = FactoryBot.create(:scipoll, :closed, :published)
        expert = FactoryBot.create(:expert)
        FactoryBot.create(:submission, :final, question_group: survey, user: expert)

        login_as expert

        visit new_question_group_answer_group_path(survey)

        assert_current_path dashboard_path
        assert_alert text: 'This survey is closed'
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module <PERSON><PERSON>olls
  class PrefillTest < ApplicationSystemTestCase
    test 'prefill_answer_choice param on new submission will check appropriate answer' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey, prefill_answer_choice: question.answer_choices.first)

      assert_checked_field(question.answer_choices.first.label)
    end

    test 'prefill_answer_choice param will overwrite existing answer on existing submission' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      create(:answer, answer_choices: [question.answer_choices.second], answer_group: submission, question:) # second answer selected

      login_as user

      visit edit_question_group_answer_group_path(survey, submission, prefill_answer_choice: question.answer_choices.first) # prefill first answer

      assert_checked_field(question.answer_choices.first.label)
    end

    test 'prefill route for logged in user WITHOUT a preexisitng submission' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)

      login_as FactoryBot.create(:expert)

      visit scipoll_prefill_url(survey, question.answer_choices.first)

      assert_current_path new_question_group_answer_group_path(survey, prefill_answer_choice: question.answer_choices.first)
      assert_alert text: finish_reminder
    end

    test 'prefill route for logged in user WITH a preexisitng submission' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)

      login_as user

      visit scipoll_prefill_url(survey, question.answer_choices.second)

      assert_current_path edit_question_group_answer_group_path(survey, submission, prefill_answer_choice: question.answer_choices.second)
      assert_alert text: finish_reminder
    end

    test 'prefill route for logged out users WITH account gets you to answer_group#new' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)
      user = FactoryBot.create(:expert)

      visit scipoll_prefill_url(survey, question.answer_choices.first, email: user.email)

      assert_current_path login_path(email: user.email)
      assert_alert text: login_encouragement
      assert_field(id: 'session_email', with: user.email)

      complete_login_form

      assert_current_path new_question_group_answer_group_path(survey, prefill_answer_choice: question.answer_choices.first)
      assert_alert text: finish_reminder
    end

    test 'prefill route for logged out users WITHOUT account gets you to answer_group#new' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)
      email = Faker::Internet.email

      visit scipoll_prefill_url(survey, question.answer_choices.first, email:)

      assert_current_path new_sign_up_path(email:)
      assert_alert text: login_encouragement
      assert_field(id: 'sign_up_email', with: email)

      within('.sign-up-form') do
        choose("sign_up_role_id_#{Role.expert.id}", allow_label_click: true)
        fill_in 'sign_up_password', with: 'secret123'
        click_button('Sign Up')
      end

      assert_current_path new_question_group_answer_group_path(survey, prefill_answer_choice: question.answer_choices.first)
      assert_alert text: finish_reminder
    end

    test 'prefill route for without email param goes to signup page' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)

      visit scipoll_prefill_url(survey, question.answer_choices.first)

      assert_current_path new_sign_up_path
    end

    test 'prefill route handles email addresses with spaces' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)
      fixable_email = 'test <EMAIL>' # Malformed from MailChimp
      fixed_email = '<EMAIL>'

      visit scipoll_prefill_url(survey, question.answer_choices.first, email: fixable_email)

      assert_current_path new_sign_up_path(email: fixed_email)
      assert_field(id: 'sign_up_email', with: fixed_email)
    end

    test 'prefill route for with invalid email param goes to signup page and clears email' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      question = FactoryBot.create(:radio, survey:)
      email = Emails::MAILCHIMP_EMAIL_MERGE_TAG # This might be a MailChimp merge tag error scenario

      visit scipoll_prefill_url(survey, question.answer_choices.first, email:)

      assert_current_path new_sign_up_path
      assert_field(id: 'sign_up_email', with: nil)
    end

    test 'prefill route with invalid answer_choice still leads to correct survey without flash' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      FactoryBot.create(:radio, survey:)
      invalid_answer_choice_id = 666

      login_as FactoryBot.create(:expert)

      visit scipoll_prefill_url(survey, invalid_answer_choice_id)

      assert_current_path new_question_group_answer_group_path(survey, prefill_answer_choice: invalid_answer_choice_id)
      assert_no_content finish_reminder
    end

    test 'prefill route with invalid answer_choice and preexisitng submission' do
      survey = FactoryBot.create(:survey, :general_participation, :open)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      invalid_answer_choice_id = 666

      visit scipoll_prefill_url(survey, invalid_answer_choice_id, email: user.email)

      complete_login_form

      assert_current_path edit_question_group_answer_group_path(survey, submission) # Cleared prefill param
      assert_no_content finish_reminder
    end

    test 'prefill answer choice with an access token for guest user' do
      FactoryBot.create(:role, :guest)
      scipoll = FactoryBot.create(:scipoll, :access_token_required, :allow_guest_participation, :open, :published)
      question = FactoryBot.create(:radio, survey: scipoll)
      answer_choice = question.answer_choices.first
      access_token = scipoll.access_tokens.create!

      visit scipoll_prefill_path(scipoll, answer_choice, access_token: access_token.generate_token_for(:authentication))

      assert_checked_field answer_choice.label
    end

    test 'prefill answer choice with an access token for logged in user' do
      expert = FactoryBot.create(:expert)
      scipoll = FactoryBot.create(:scipoll, :access_token_required, :allow_guest_participation, :open, :published)
      question = FactoryBot.create(:radio, survey: scipoll)
      answer_choice = question.answer_choices.first
      access_token = scipoll.access_tokens.create!

      login_as expert

      visit scipoll_prefill_path(scipoll, answer_choice, access_token: access_token.generate_token_for(:authentication))

      assert_checked_field answer_choice.label
    end

    test 'prefill answer choice with an access token, then register goes to dashboard' do
      FactoryBot.create(:role, :guest)
      scipoll = FactoryBot.create(:scipoll, :access_token_required, :allow_guest_participation, :open, :published)
      question = FactoryBot.create(:radio, survey: scipoll)
      answer_choice = question.answer_choices.first
      access_token = scipoll.access_tokens.create!

      visit scipoll_prefill_path(scipoll, answer_choice, access_token: access_token.generate_token_for(:authentication))

      click_button 'Finish'

      within('.sign-up-form') do
        fill_in 'sign_up[email]', with: Faker::Internet.email
        fill_in 'sign_up[password]', with: 'secret123'
        click_button('Sign Up')
      end

      assert_text 'Welcome! Your account is currently pending.'
      assert_text 'Dashboard'
    end

    test 'prefill answer choice with an access token, then login goes to dashboard' do
      FactoryBot.create(:role, :guest)
      scipoll = FactoryBot.create(:scipoll, :access_token_required, :allow_guest_participation, :open, :published)
      question = FactoryBot.create(:radio, survey: scipoll)
      expert = FactoryBot.create(:expert)
      answer_choice = question.answer_choices.first
      access_token = scipoll.access_tokens.create!

      visit scipoll_prefill_path(scipoll, answer_choice, access_token: access_token.generate_token_for(:authentication))

      click_button 'Finish'

      click_link 'Log In'

      within('.login-form') do
        fill_in 'session[email]', with: expert.email
        fill_in 'session[password]', with: 'secret123'
        click_button('Log In')
      end

      assert_text 'Dashboard'
    end

    private

    def complete_login_form
      within('.login-form') do
        fill_in 'password', with: 'secret123'
        click_button('Log In')
      end
    end

    def login_encouragement
      I18n.t('scipoll.prefill.login_encouragement')
    end

    def finish_reminder
      I18n.t('scipoll.prefill.finish_reminder')
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Sc<PERSON>olls
  class GuestAccessTest < ApplicationSystemTestCase
    test 'new guest user can take a scipoll with an access token' do
      FactoryBot.create(:role, :guest)
      introduction = 'Submit me!'
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation, introduction:)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      access_token = scipoll.access_tokens.create!

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      assert_text scipoll.name
      assert_text introduction

      choose 'Red'
      click_button 'Finish'

      assert_current_path submitted_question_group_answer_group_path(scipoll, scipoll.submissions.last)
      assert_text 'Your submission has been recorded!'
      assert_field 'Email'
      assert_field 'Password'
      assert_button 'Sign Up'
      assert_no_text 'Dashboard'
    end

    test 'log in or sign up forms not shown on new submission page' do
      FactoryBot.create(:role, :guest)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      access_token = scipoll.access_tokens.create!

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      within('#sidebar') do
        assert_link scipoll.display_id
        assert_no_text 'Sign Up'
        assert_no_text 'Log In'
      end
    end

    test 'guest users cannot take a draft scipoll' do
      FactoryBot.create(:role, :guest)
      introduction = 'This is a draft scipoll, guest users should not see me'

      scipoll = FactoryBot.create(:scipoll, :draft, :allow_guest_participation, introduction:)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      access_token = scipoll.access_tokens.create!

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      assert_no_text scipoll.name
      assert_no_text introduction
      assert_button 'Sign Up'
    end

    test 'users cannot take a closed scipoll' do
      FactoryBot.create(:role, :guest)
      scipoll = FactoryBot.create(:scipoll, :published, :closed, :allow_guest_participation)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      access_token = scipoll.access_tokens.create!

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      assert_current_path scipoll_path(scipoll)
      assert_no_button 'Take SciPoll'
    end

    test 'existing guest user can take a scipoll with an access token' do
      FactoryBot.create(:role, :guest)
      introduction = 'Submit me!'
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation, introduction:)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      access_token = scipoll.access_tokens.create!

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      # To simulate a second visit, we need to navigate away first to avoid multiple tab alert.
      # We also cannot close the initial window so all we can do it navigate away, and switch.
      visit root_path

      new_window = open_new_window
      switch_to_window new_window

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      assert_text scipoll.name
      assert_text introduction

      choose 'Red'
      click_button 'Finish'

      assert_text 'Your submission has been recorded!'
      assert_field 'Email'
      assert_field 'Password'
      assert_button 'Sign Up'
      assert_no_text 'Dashboard'
    end

    test 'new guest user cannot reuse an already user access token' do
      FactoryBot.create(:role, :guest)
      introduction = 'Submit me!'
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation, introduction:)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      submitter = FactoryBot.create(:user)
      FactoryBot.create(:submission, :final, question_group: scipoll, user: submitter)
      access_token = scipoll.access_tokens.create!(user: submitter)

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      using_session('New Session') do
        visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

        assert_text 'This invitation has already been used.'
      end
    end

    test 'cannot access a scipoll submission page without an access token when an access token required' do
      scipoll = FactoryBot.create(:scipoll, :published, :access_token_required)

      expert = FactoryBot.create(:expert)

      login_as expert

      visit new_question_group_answer_group_path(scipoll)

      assert_text 'You are not authorized to access this page.'
    end

    test 'guest users\' scipolls are shown after logging in as a registered user' do
      expert = FactoryBot.create(:expert)
      FactoryBot.create(:role, :guest)
      scipoll = FactoryBot.create(:scipoll, :published, :access_token_required, :allow_guest_participation, :unlisted)
      scipoll.questions.radio.create!(
        question_text: 'What is your favorite color?',
        position: 1,
        answer_choices: [
          AnswerChoice.new(label: 'Red', position: 1),
          AnswerChoice.new(label: 'Blue', position: 2)
        ]
      )
      access_token = scipoll.access_tokens.create!

      ENV['SCIPOLL_BRANDING_ID'] = scipoll.branding_id.to_s
      Rails.application.reload_routes!

      visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

      choose 'Red'
      click_button 'Finish'

      sleep 0.5

      login_as expert

      visit scipolls_path(mine: true)

      within('#scipolls_table') { assert_text scipoll.name }
    end
  end
end

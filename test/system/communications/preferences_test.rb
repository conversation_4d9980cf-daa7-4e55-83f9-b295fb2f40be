# frozen_string_literal: true

require 'application_system_test_case'

module Communications
  class PreferencesTest < ApplicationSystemTestCase
    test 'no subscriptions are available' do
      user = FactoryBot.create(:user)

      login_as user

      visit communication_preferences_path

      assert_text 'No subscriptions are currently available for you'
      assert_text 'Once subscriptions are available they will appears here.'
    end

    test 'unsubscribe from list' do
      user, subscription = create_user_with_subscription!

      login_as user

      visit communication_preferences_path

      uncheck subscription.subscribable_name

      within("form[action='#{communication_preferences_path}']") do
        click_button save_button_label
      end

      assert_unchecked_field subscription.subscribable_name
    end

    test 'subscribe to list' do
      user, subscription = create_user_with_subscription!(unsubscribed_at: 1.day.ago)

      login_as user

      visit communication_preferences_path

      check subscription.subscribable_name

      within("form[action='#{communication_preferences_path}']") do
        click_button save_button_label
      end

      assert_checked_field subscription.subscribable_name
    end

    test 'disable auto-subscribe' do
      user = FactoryBot.create(:user, disabled_autosubscribe_at: nil)

      login_as user

      visit communication_preferences_path

      uncheck 'Auto-subscribe'

      click_button 'Save preferences'

      assert_no_checked_field 'Auto-subscribe'
    end

    test 're-enable auto-subscribe' do
      user = FactoryBot.create(:user, disabled_autosubscribe_at: Time.current)

      login_as user

      visit communication_preferences_path

      check 'Auto-subscribe'

      click_button 'Save preferences'

      assert_checked_field 'Auto-subscribe'
    end

    private

    def account_nav_label
      I18n.t('layouts.user_nav.account_nav.comms_prefs')
    end

    def create_user!
      FactoryBot.create(:user)
    end

    def create_user_with_subscription!(unsubscribed_at: nil)
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, roles: [role])
      list = FactoryBot.create(:distribution_list, roles: [role])
      subscription = user.subscriptions.create!(subscribable: list, unsubscribed_at:)

      [user, subscription]
    end

    def save_button_label
      I18n.t('communications.preferences.show.save_button')
    end

    def subnav_label
      I18n.t('communications.preferences.show.nav_item.comms_prefs')
    end
  end
end

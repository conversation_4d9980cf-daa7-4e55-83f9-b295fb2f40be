# frozen_string_literal: true

require 'application_system_test_case'

class PublicTest < ApplicationSystemTestCase
  test 'public homepage contains scipis scipolls pings and announcements' do
    # Apparently surveys already exist here
    scipi = find_first_or_create_scipi
    scipoll = find_first_or_create_scipoll

    ping = FactoryBot.create(:ping)
    announcement = FactoryBot.create(:announcement, :promotable)

    visit root_path

    within('#scipis_table_card') do
      assert_content scipi.name
    end
    within('#scipolls_table_card') do
      assert_content scipoll.name
    end
    within('#pings_table_card') do
      assert_content ping.title
    end

    within('#announcements_card') do
      assert_content announcement.title
    end
  end

  test 'do not redirect guest users to dashboard' do
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)
    access_token = scipoll.access_tokens.create!

    # Creates user and session
    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    visit root_path

    assert_current_path root_path
  end

  test 'public home page meta' do
    visit root_path

    assert_selector("meta[property='og:image'][content*='scipinion-']", visible: false)
  end

  private

  def find_first_or_create_scipoll
    QuestionGroup.published&.scipoll&.first || FactoryBot.create(:scipoll, :published)
  end

  def find_first_or_create_scipi
    QuestionGroup.published&.scipi&.first || FactoryBot.create(:survey, :scipi, :published)
  end
end

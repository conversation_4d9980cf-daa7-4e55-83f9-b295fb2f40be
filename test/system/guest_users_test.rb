# frozen_string_literal: true

require 'application_system_test_case'

# NOTE: The only method for creating a guest user and session is via an access token to a SciPoll.
#       If another method is added, it can be used here instead.
class GuestUsersTest < ApplicationSystemTestCase
  test 'guest signs up after SciPoll' do
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(
      :scipoll,
      :allow_guest_participation,
      :published,
      :access_token_required,
      :unlisted # make unlisted so the SciPoll does not show up in the teaser list on the Dashboard
    )
    access_token = scipoll.access_tokens.create!

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    click_button 'Finish'

    within('.sign-up-form') do
      fill_in('Email Address', with: TestData.email)
      fill_in('Password', with: TestData.password)

      click_button('Sign Up')
    end

    assert_selector '.global-alert', text: I18n.t('sign_ups.create.success')

    within('#scipolls_table_card') do
      assert_text scipoll.name
    end
  end

  test 'guest users are sent to the signup page when they try to access the dashboard' do
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(:scipoll, :allow_guest_participation, :published, :access_token_required)
    access_token = scipoll.access_tokens.create!

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    visit dashboard_path

    assert_button 'Sign Up'
  end

  test 'guest user logs in as an expert' do
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(
      :scipoll,
      :allow_guest_participation,
      :published,
      :access_token_required,
      :unlisted # make unlisted so the SciPoll does not show up in the teaser list on the Dashboard
    )
    access_token = scipoll.access_tokens.create!

    visit access_tokens_path(access_token: access_token.generate_token_for(:authentication))

    login_as expert

    assert_selector '.header-title', text: 'Dashboard'
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

class EditSettingsTest < ApplicationSystemTestCase
  test 'change email' do
    current_password = TestData.password
    new_email = TestData.email
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      fill_in('Email', with: new_email)
      fill_in('Current password', with: current_password)

      click_button('Update Email')
    end

    assert_text I18n.t('settings.update_email.success', email: new_email)

    within('.email-settings-form') do
      assert_field 'Email', with: new_email
    end
  end

  test 'cannot update with blank email' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      fill_in('Email', with: '')
      fill_in('Current password', with: current_password)

      click_button('Update Email')
    end

    assert_current_path update_email_settings_path

    within('.email-settings-form', text: 'Email') do
      assert_text(/please provide a new email address/i)
    end
  end

  test 'cannot update to the same email' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      fill_in('Email', with: user.email)
      fill_in('Current password', with: current_password)

      click_button('Update Email')
    end

    assert_current_path update_email_settings_path

    within('.email-settings-form', text: 'Email') do
      assert_text(/is the same as your current email/i)
    end
  end

  test 'cannot update to an email that already exists in the system' do
    existing_email = FactoryBot.create(:expert).email
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      fill_in('Email', with: existing_email)
      fill_in('Current password', with: current_password)

      click_button('Update Email')
    end

    assert_current_path update_email_settings_path

    within('.email-settings-form', text: 'Email') do
      assert_text(/this email is already in use by another account/i)
    end
  end

  test 'cannot update with malformed email' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      # the @ is needed to bypass the browser's validation
      fill_in('Email', with: 'not_an_email@domain')
      fill_in('Current password', with: current_password)

      click_button('Update Email')
    end

    assert_current_path update_email_settings_path

    within('.email-settings-form', text: 'Email') do
      assert_text(/should be in the format '<EMAIL>'/i)
    end
  end

  test 'cannot update email without a current password' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      fill_in('Email', with: TestData.email)
      fill_in('Current password', with: '')

      click_button('Update Email')
    end

    assert_current_path update_email_settings_path

    within('.email-settings-form', text: 'Current password') do
      assert_text(/for your security, your password is required to change your email/i)
    end
  end

  test 'cannot update email with an incorrect password' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.email-settings-form') do
      fill_in('Email', with: TestData.email)
      fill_in('Current password', with: 'not_the_password')

      click_button('Update Email')
    end

    assert_current_path update_email_settings_path

    within('.email-settings-form', text: 'Current password') do
      assert_text(/does not match your current password/i)
    end
  end

  test 'change password' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)
    new_password = TestData.password

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.password-settings-form') do
      fill_in('New password', with: new_password)
      fill_in('Current password', with: current_password)

      click_button('Update Password')
    end

    assert_current_path edit_settings_path

    within('.global-alert') do
      assert_text "Success! You've updated your password."
    end
  end

  test 'cannot change password when new password blank' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.password-settings-form') do
      fill_in('New password', with: '')
      fill_in('Current password', with: current_password)

      click_button('Update Password')
    end

    assert_current_path update_password_settings_path

    within('.password-settings-form .form-group', text: 'New password') do
      assert_text(/can't be blank/i)
    end
  end

  test 'cannot change password when new password too short' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)
    new_password = TestData.short_password

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.password-settings-form') do
      fill_in('New password', with: new_password)
      fill_in('Current password', with: current_password)

      click_button('Update Password')
    end

    assert_current_path update_password_settings_path

    within('.password-settings-form .form-group', text: 'New password') do
      assert_text(/is too short \(minimum is 8 characters\)/i)
    end
  end

  test 'cannot change password when current password is missing' do
    current_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)

    login_as(user, password: current_password)

    visit edit_settings_path

    within('.password-settings-form') do
      fill_in('New password', with: TestData.password)
      fill_in('Current password', with: '')

      click_button('Update Password')
    end

    assert_current_path update_password_settings_path

    within('.password-settings-form .form-group', text: 'Current password') do
      assert_text 'For your security, your current password is required to make this change'
    end
  end

  test 'cannot update password with an incorrect current password' do
    user = FactoryBot.create(:expert)

    login_as(user)

    visit edit_settings_path

    within('.password-settings-form') do
      fill_in('New password', with: TestData.password)
      fill_in('Current password', with: 'not_the_password')

      click_button('Update Password')
    end

    sleep(0.2)

    assert_current_path update_password_settings_path

    within('.password-settings-form .form-group', text: 'Current password') do
      assert_text 'does not match your current password'
    end
  end

  test 'expert sets time zone' do
    time_zone = ActiveSupport::TimeZone.all.sample.to_s

    login_as(FactoryBot.create(:expert))

    visit edit_settings_path

    within('.time-zone-settings-form') do
      select(time_zone, from: 'Time zone')

      click_button('Update Time Zone')
    end

    assert_current_path edit_settings_path

    within('.time-zone-settings-form') do
      assert_select 'Time zone', selected: time_zone
    end
  end
end

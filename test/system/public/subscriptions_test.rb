# frozen_string_literal: true

require 'application_system_test_case'

module Public
  class SubscriptionsTest < ApplicationSystemTestCase
    test 'unsubscribe from Ping' do
      ping = FactoryBot.create(:ping)
      subscriber = FactoryBot.create(:user, :confirmed)
      subscription = ping.subscriptions.create!(subscriber:)

      visit public_subscription_url(list_token: ping.token, subscription_token: subscription.token)

      click_button 'Unsubscribe'

      assert_text "You have successfully unsubscribed from '#{ping.title}'"
      assert_button 'Resubscribe'
    end

    test 'unsubscribe from DistributionList' do
      role = FactoryBot.create(:role)
      subscriber = FactoryBot.create(:user, :confirmed, roles: [role])
      list = FactoryBot.create(:distribution_list, roles: [role])
      subscription = list.subscriptions.create!(subscriber:)

      visit public_subscription_url(list_token: list.token, subscription_token: subscription.token)

      click_button 'Unsubscribe'

      assert_text "You have successfully unsubscribed from '#{list.name}'"
      assert_button 'Resubscribe'
    end

    test 'resubscribe to Ping' do
      ping = FactoryBot.create(:ping)
      subscriber = FactoryBot.create(:user, :confirmed)
      subscription = ping.subscriptions.create!(subscriber:, unsubscribed_at: Time.current)

      visit public_subscription_url(list_token: ping.token, subscription_token: subscription.token)

      click_button 'Resubscribe'

      assert_text "You have successfully resubscribed to '#{ping.title}'"
      assert_button 'Unsubscribe'
    end

    test 'resubscribe to DistributionList' do
      role = FactoryBot.create(:role)
      subscriber = FactoryBot.create(:user, :confirmed, roles: [role])
      list = FactoryBot.create(:distribution_list, roles: [role])
      subscription = list.subscriptions.create!(subscriber:, unsubscribed_at: Time.current)

      visit public_subscription_url(list_token: list.token, subscription_token: subscription.token)

      click_button 'Resubscribe'

      assert_text "You have successfully resubscribed to '#{list.name}'"
      assert_button 'Unsubscribe'
    end
  end
end

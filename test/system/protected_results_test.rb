# frozen_string_literal: true

require 'application_system_test_case'

class ProtectedResultsTest < ApplicationSystemTestCase
  test 'good faith visitors to protected results pages are routed back to where they started' do
    user = create(:expert)
    survey = create(:survey,
                    :invite_only,
                    :published,
                    :results_published)
    create(:invite, :approved, survey:, user:)

    visit survey_results_path(survey)

    assert_current_path login_path

    after_login_path = survey_results_path(survey)

    login_as(user, after_login_path:)

    assert_current_path after_login_path
  end

  test 'logged-in good faith and authorized visitors get to results directly' do
    user = create(:expert)
    survey = create(:survey,
                    :invite_only,
                    :published,
                    :results_published)
    create(:invite, :approved, survey:, user:)

    login_as user

    visit survey_results_path(survey)

    assert_current_path survey_results_path(survey)
  end

  test 'good faith visitors routing also works for single results' do
    user = create(:expert)
    survey = create(:survey,
                    :invite_only,
                    :published,
                    :results_published)
    create(:invite, :approved, survey:, user:)
    result_position = rand(1..10)
    question = FactoryBot.create(:radio, survey:)

    selected_choice = question.answer_choices[0]
    answer_question(survey, question, selected_choice)

    result_definition = create_multiple_choice_result(
      survey,
      question,
      result_position,
      :pie_chart
    )

    visit survey_result_path(survey_id: survey.id, id: result_definition.id)

    assert_current_path login_path

    after_login_path = survey_result_path(survey_id: survey.id, id: result_definition.id)

    login_as(user, after_login_path:)

    assert_current_path after_login_path
  end

  test 'good faith BUT unauthorized visitors to protected results pages are routed back to dashboard' do
    user = create(:expert)
    survey = create(:survey,
                    :invite_only,
                    :published,
                    :results_published)

    visit survey_results_path(survey)

    assert_current_path login_path

    login_as user

    assert_current_path dashboard_path
  end

  test 'bad faith visitor are routed back to root (and logging in will not get them there either)' do
    user = create(:expert)
    survey = create(:survey,
                    :invite_only,
                    results_published: false)

    visit survey_results_path(survey)

    assert_current_path root_path

    login_as user

    assert_current_path dashboard_path
  end

  private

  def answer_question(survey, question, selected_choice)
    submission = survey.submissions.create!(
      submitter: FactoryBot.create(:expert),
      submitted_at: Time.zone.now
    )
    selected_choice = SelectedChoice.new(answer_choice: selected_choice)
    submission.answers.create!(
      question:,
      selected_choices: [selected_choice]
    )
  end

  def create_multiple_choice_result(survey, question, result_position, chart_type)
    FactoryBot.create(
      :result_definition,
      :multiple_choice_responses,
      chart_type,
      position: result_position,
      question:,
      survey:
    )
  end
end

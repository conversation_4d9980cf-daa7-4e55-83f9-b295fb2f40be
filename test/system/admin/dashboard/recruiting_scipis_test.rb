# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Dashboard
    class RecruitingScipisTest < ApplicationSystemTestCase
      setup do
        skip 'Not in use'
      end

      test 'empty state when no Scipis are recruiting' do
        admin = FactoryBot.create(:scipi_admin)

        login_as(admin)

        visit admin_root_path

        within('.recruiting-scipis') do
          assert_text 'You have no actively recruiting SciPis'
        end
      end

      test 'no incomplete applications' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, :scipi, :published, created_by: admin)
        expert = FactoryBot.create(:expert, :complete_profile)

        survey.applicants.create!(expert:)

        login_as(admin)

        visit admin_root_path

        within('.recruiting-scipis') do
          assert_text 'There are no incomplete applications'
        end
      end

      test 'no applications' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, :scipi, :published, created_by: admin)

        login_as(admin)

        visit admin_root_path

        within('.recruiting-scipis') do
          click_link 'All'

          assert_text survey.name
          assert_text 'There are no applications yet'
        end
      end

      test 'should see experts without YOE' do
        db_admin = users(:db_admin)
        expert = create_expert!(profile_attributes: { publications_count: 0 }, employment_history_items: [])
        scipi = FactoryBot.create(:survey, :scipi, :published)
        scipi.applicants.create!(expert:)

        login_as(db_admin)

        visit admin_root_path

        within('.recruiting-scipis') do
          assert_text scipi.display_name
          assert_text expert.mail_name
        end
      end
    end
  end
end

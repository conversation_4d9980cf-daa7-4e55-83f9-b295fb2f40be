# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Experts
    class PendingVerificationTest < ApplicationSystemTestCase
      test 'view unverified user' do
        admin = FactoryBot.create(:admin)
        user = FactoryBot.create(:expert, :unverified)

        login_as(admin)

        visit admin_experts_unverified_path(status_id: VerificationStatus.pending)

        within("#user_#{user.id}") do
          assert_text user.email
          assert_text user.full_name
        end
      end
    end
  end
end

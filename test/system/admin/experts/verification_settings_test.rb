# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Experts
    class VerificationSettingTest < ApplicationSystemTestCase
      test 'turn auto verification off' do
        admin = FactoryBot.create(:admin)

        login_as(admin)

        visit admin_experts_unverified_path(status_id: VerificationStatus.pending)

        accept_confirm { click_button 'Turn off auto-verification' }

        assert_text 'Auto-verification is off'
      end

      test 'turn auto verification on' do
        VerificationStatus.change_default!(VerificationStatus.pending)

        admin = FactoryBot.create(:admin)

        login_as(admin)

        visit admin_experts_unverified_path(status_id: VerificationStatus.pending)

        accept_confirm { click_button 'Turn on auto-verification' }

        assert_text 'Auto-verification is on'
      end
    end
  end
end

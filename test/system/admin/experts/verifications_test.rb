# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Experts
    class VerificationsTest < ApplicationSystemTestCase
      test 'verify expert' do
        admin = FactoryBot.create(:admin)
        expert = FactoryBot.create(:expert, :unverified)

        login_as(admin)

        visit admin_expert_path(expert)

        freeze_time do
          choose 'Verified'
          fill_in 'Add a note (Optional)', with: 'This person checks out'

          accept_confirm { click_button 'Update verification' }

          assert_checked_field 'Verified'
          assert_text "Last updated by #{admin.name} on #{Time.current.strftime('%B %d, %Y')}"
          within('.notes-list ') do
            assert_text 'This person checks out'
          end
        end
      end
    end
  end
end

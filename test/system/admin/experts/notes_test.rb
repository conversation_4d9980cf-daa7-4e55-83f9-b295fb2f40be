# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Experts
    class NotesTest < ApplicationSystemTestCase
      test 'create note without context' do
        admin = users(:db_admin)
        expert = users(:expert)

        login_as(admin)

        visit admin_expert_path(expert)

        within('.new-note-form') do
          fill_in('Content', with: 'I am note content')
          choose('Positive')

          click_button('Add Note')
        end

        within('.notes-list') do
          assert_text 'I am note content'
          # `visible: false` because icons are sometimes slow to load
          assert_selector 'i[title="A positive note"]', visible: false
        end
      end

      test 'create note with SciPi as context' do
        admin = FactoryBot.create(:db_admin)
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)

        login_as(admin)

        visit admin_expert_path(expert)

        within('.new-note-form') do
          select(scipi.name, from: 'Context')
          fill_in('Content', with: 'I am note content')

          click_button('Add Note')
        end

        within('.notes-list') do
          assert_text scipi.name
          assert_text 'I am note content'
        end
      end
    end
  end
end

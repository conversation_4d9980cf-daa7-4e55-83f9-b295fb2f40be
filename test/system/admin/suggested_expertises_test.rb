# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class SuggestedExpertisesTest < ApplicationSystemTestCase
    test 'show suggested expertise' do
      suggestion = SuggestedExpertise.create!(term: Faker::Science.science, profile_count: 10)

      login_as TestData.any_db_admin

      visit admin_suggested_expertises_path

      within("#suggested_expertise_#{suggestion.id}") do
        assert_text suggestion.term
        assert_text suggestion.profile_count
      end
    end

    test 'add suggested expertise' do
      suggestion = SuggestedExpertise.create!(term: 'Add me', profile_count: 10)

      login_as TestData.any_db_admin

      visit admin_suggested_expertises_path

      within("#suggested_expertise_#{suggestion.id}") do
        click_button('Add')
      end

      assert_text "\"#{suggestion.term}\" was added"
      assert_no_selector("#suggested_expertise_#{suggestion.id}")

      visit admin_expertises_path

      assert_text suggestion.term
    end

    test 'remove suggested expertise' do
      suggestion = SuggestedExpertise.create!(term: 'Remove me', profile_count: 10)

      login_as TestData.any_db_admin

      visit admin_suggested_expertises_path

      within("#suggested_expertise_#{suggestion.id}") do
        click_button('Remove')
      end

      assert_text "\"#{suggestion.term}\" was removed and will not re-appear"
      assert_no_selector("#suggested_expertise_#{suggestion.id}")
    end
  end
end

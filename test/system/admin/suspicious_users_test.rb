# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class SuspiciousUsersTest < ApplicationSystemTestCase
    test 'trust suspicious users' do
      admin = FactoryBot.create(:admin)

      login_as(admin)

      user = create_expert!
      user.mark_suspicious!(reason: 'This is a test')

      visit admin_suspicious_users_path

      within("#user_#{user.id}") do
        accept_confirm do
          click_button 'Mark not suspicious'
        end
      end

      assert_no_selector "#user_#{user.id}"
      assert_text "#{user.email} is no longer marked as not suspicious"
    end
  end
end

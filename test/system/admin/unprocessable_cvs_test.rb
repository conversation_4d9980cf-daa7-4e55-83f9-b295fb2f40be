# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class UnprocessableCvsTest < ApplicationSystemTestCase
    test 'display malformed cv' do
      expert = FactoryBot.create(:expert)
      expert.profile.cv.attach(io: load_test_file('cv.pdf'), filename: 'test.pdf')
      expert.profile.update!(
        cv_text: nil,
        last_text_extraction_error: 'Badness occurred'
      )

      admin = FactoryBot.create(:db_admin)
      login_as admin

      visit admin_unprocessable_cvs_path

      assert_text expert.mail_name

      assert_text 'Badness occurred'
    end

    test 'add cv text' do
      expert = FactoryBot.create(:expert)
      expert.profile.cv.attach(io: load_test_file('cv.pdf'), filename: 'test.pdf')
      expert.profile.update!(
        cv_text: nil,
        last_text_extraction_error: 'Fix me!'
      )

      admin = FactoryBot.create(:db_admin)
      login_as admin

      visit admin_unprocessable_cvs_path

      within("#profile_#{expert.profile.id}") do
        fill_in('CV Text', with: 'This is the CV text')

        click_button 'Save'
      end

      assert_no_text expert.mail_name
      assert_no_text 'Fix me!'
    end
  end
end

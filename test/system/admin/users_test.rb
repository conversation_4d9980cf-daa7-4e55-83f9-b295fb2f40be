# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class UsersTest < ApplicationSystemTestCase
    test 'show user subscriptions' do
      distro_role = FactoryBot.create(:role)
      subscriber = FactoryBot.create(:user, roles: [distro_role])
      list = FactoryBot.create(:distribution_list, roles: [distro_role])
      list.subscribers << subscriber

      distro_manager = FactoryBot.create(:user, :distribution_list_manager)
      login_as(distro_manager)

      visit admin_user_path(subscriber)

      within('.subscriptions-list') do
        assert_text list.name
      end
    end

    test 'unsubscribe user from list' do
      distro_role = FactoryBot.create(:role)
      subscriber = FactoryBot.create(:user, roles: [distro_role])
      list = FactoryBot.create(:distribution_list, roles: [distro_role])
      list.subscribers << subscriber

      distro_manager = FactoryBot.create(:user, :distribution_list_manager)
      login_as(distro_manager)

      visit admin_user_path(subscriber)

      accept_confirm do
        click_button('Unsubscribe', title: "Unsubscribe this user from '#{list.name}'")
      end

      assert_text 'User successfully unsubscribed!'
      assert_button('Resubscribe', title: "Resubscribe this user to '#{list.name}'")
    end

    test 'resubscribe user to list' do
      distro_role = FactoryBot.create(:role)
      subscriber = FactoryBot.create(:user, roles: [distro_role])
      list = FactoryBot.create(:distribution_list, roles: [distro_role])
      list.subscriptions.create(subscriber:, unsubscribed_at: 1.day.ago)

      distro_manager = FactoryBot.create(:user, :distribution_list_manager)
      login_as(distro_manager)

      visit admin_user_path(subscriber)

      accept_confirm do
        click_button('Resubscribe', title: "Resubscribe this user to '#{list.name}'")
      end

      assert_text 'User successfully resubscribed!'
      assert_button('Unsubscribe', title: "Unsubscribe this user from '#{list.name}'")
    end

    test 'no NeverBounce information available' do
      user = FactoryBot.create(:user, last_neverbounce_status: nil, last_neverbounce_check_at: nil)

      distro_manager = FactoryBot.create(:user, :distribution_list_manager)
      login_as(distro_manager)

      visit admin_user_path(user)

      assert_text "This user's email has not been checked with NeverBounce."
    end

    test 'show NeverBounce status' do
      last_checked_at = 1.day.ago
      user = FactoryBot.create(:user, last_neverbounce_status: 'valid', last_neverbounce_check_at: last_checked_at)

      distro_manager = FactoryBot.create(:user, :distribution_list_manager)
      login_as(distro_manager)

      visit admin_user_path(user)

      assert_neverbounce_field('NeverBounce Status', 'valid')
      assert_neverbounce_field('Last Checked At', last_checked_at.strftime('%m/%d/%Y at %I:%M %p %Z'))
    end

    test 'Danger zone allows destruction of destroyable accounts' do
      admin = FactoryBot.create(:admin)

      destructable_user = FactoryBot.create(:expert)

      login_as(admin)

      visit admin_root_path

      within('#dashboard-signups') do
        assert_text destructable_user.email.to_s
      end

      visit edit_admin_user_path(destructable_user)

      within('#account_destruction') do
        accept_confirm do
          click_button 'Delete'
        end
      end

      assert_current_path admin_root_path
      assert_nil User.find_by(email: destructable_user.email)

      within('#dashboard-signups') do
        assert_no_text destructable_user.email.to_s
      end
    end

    test 'Danger zone blocks destruction of non-destructable user' do
      admin = FactoryBot.create(:admin)

      non_destructable_user = FactoryBot.create(:expert)
      survey = FactoryBot.create(:survey)
      FactoryBot.create(:submission, user: non_destructable_user, survey:)

      login_as(admin)

      visit edit_admin_user_path(non_destructable_user)

      within('#account_destruction') do
        assert_button 'Delete', disabled: true
      end
    end

    test 'Danger has some related records stats' do
      admin = FactoryBot.create(:admin)

      user = FactoryBot.create(:expert, :with_display_name)
      FactoryBot.create_list(:ping, 3, author: user)

      login_as(admin)

      visit edit_admin_user_path(user)

      within('#account_destruction') do
        assert_text 'profile: 1'
        assert_text 'submissions: 0'
        assert_text 'pings: 3'
      end
    end

    private

    def assert_neverbounce_field(label, value)
      assert_selector(
        :xpath,
        "//table//th[contains(text(), '#{label}')]/following-sibling::td[contains(text(), '#{value}')]"
      )
    end
  end
end

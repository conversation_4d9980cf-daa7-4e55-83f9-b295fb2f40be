# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Communications
    class DistributionListsTest < ApplicationSystemTestCase
      test 'show distribution lists' do
        admin = FactoryBot.create(:admin)
        role = FactoryBot.create(:role)
        list = FactoryBot.create(:distribution_list, roles: [role])

        login_as(admin)

        visit admin_communications_distribution_lists_path

        assert_text list.name
        assert_text "Available to roles #{role.name}"
      end

      test 'show distribution list with suspended delivery' do
        admin = FactoryBot.create(:admin)
        role = FactoryBot.create(:role)
        list = FactoryBot.create(
          :distribution_list,
          roles: [role],
          delivery_suspended_at: Time.current,
          delivery_suspended_by: admin
        )

        login_as(admin)

        visit admin_communications_distribution_lists_path

        assert_text "Delivery suspended #{list.name}", normalize_ws: true
        assert_text "Available to roles #{role.name}"
      end

      test 'add a distribution list' do
        list_manager_role = FactoryBot.create(:role, :manage_distribution_lists)
        admin = FactoryBot.create(:admin, roles: [list_manager_role])
        included_role = FactoryBot.create(:role, :available_for_signup)
        FactoryBot.create(:user, :confirmed, roles: [included_role])

        login_as(admin)

        visit admin_communications_distribution_lists_path

        fill_in 'Name', with: 'My new list'
        fill_in 'Description', with: 'I am the public description'
        fill_in 'Postmark Stream ID', with: 'test-stream'
        fill_in 'Position', with: '1'
        fill_in 'Admin notes', with: 'I am important stuff that admins should need to know'
        check included_role.name

        click_button 'Create list'

        assert_text I18n.t('admin.communications.distribution_lists.create.success')
        assert_text 'My new list'
        assert_text 'Stream ID test-stream'
        assert_text "Available to roles #{included_role.name}"
      end

      test 'edit a distribution list' do
        list_manager_role = FactoryBot.create(:role, :manage_distribution_lists)
        admin = FactoryBot.create(:admin, roles: [list_manager_role])
        list = FactoryBot.create(:distribution_list)

        login_as(admin)

        visit admin_communications_distribution_lists_path

        click_link('Settings', title: "Edit #{list.name}'s Settings")

        within('.edit-form') do
          fill_in 'Name', with: 'Updated name'
          fill_in 'Position', with: '20'
          fill_in 'Postmark Stream ID', with: 'updated-test-stream'
        end

        click_button I18n.t('admin.communications.distribution_lists.edit.buttons.update')

        assert_text I18n.t('admin.communications.distribution_lists.update.success')
        assert_text 'Updated name'
        assert_text 'Stream ID updated-test-stream'
        assert_text 'Position 20'
      end

      test 'suspend delivery for a distribution list' do
        list_manager_role = FactoryBot.create(:role, :manage_distribution_lists)
        admin = FactoryBot.create(:admin, roles: [list_manager_role])
        list = FactoryBot.create(:distribution_list)

        login_as(admin)

        visit admin_communications_distribution_lists_path

        click_link('Settings', title: "Edit #{list.name}'s Settings")

        accept_confirm do
          click_button I18n.t('admin.communications.distribution_lists.edit.buttons.suspend_delivery')
        end

        assert_text I18n.t('admin.communications.distribution_lists.suspend.success')
        assert_text 'Delivery suspended'
        assert_button I18n.t('admin.communications.distribution_lists.edit.buttons.resume_delivery')
      end

      test 'resume delivery for a suspended distribution list' do
        list_manager_role = FactoryBot.create(:role, :manage_distribution_lists)
        admin = FactoryBot.create(:admin, roles: [list_manager_role])
        list = FactoryBot.create(:distribution_list, delivery_suspended_at: Time.current, delivery_suspended_by: admin)

        login_as(admin)

        visit admin_communications_distribution_lists_path

        click_link('Settings', title: "Edit #{list.name}'s Settings")

        accept_confirm do
          click_button I18n.t('admin.communications.distribution_lists.edit.buttons.resume_delivery')
        end

        assert_text I18n.t('admin.communications.distribution_lists.resume.success')
        assert_no_text 'Delivery suspended'
        assert_button I18n.t('admin.communications.distribution_lists.edit.buttons.suspend_delivery')
      end

      test 'delete a distribution list' do
        list_manager_role = FactoryBot.create(:role, :manage_distribution_lists)
        admin = FactoryBot.create(:admin, roles: [list_manager_role])
        list = FactoryBot.create(:distribution_list)

        login_as(admin)

        visit admin_communications_distribution_lists_path

        click_link('Settings', title: "Edit #{list.name}'s Settings")

        accept_confirm do
          click_button I18n.t('admin.communications.distribution_lists.edit.buttons.destroy')
        end

        assert_text I18n.t('admin.communications.distribution_lists.destroy.success')
        assert_no_text list.name
      end
    end
  end
end

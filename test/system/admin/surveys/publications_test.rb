# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    class PublicationsTest < ApplicationSystemTestCase
      test 'view draft Scipi' do
        scipi = FactoryBot.create(:scipi, :invite_only, :draft)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        assert_text 'Draft'
      end

      test 'warn when SciPi does not have questions' do
        expertise = FactoryBot.create(:expertise)
        scipi = FactoryBot.create(:scipi, :invite_only, :draft, expertises: [expertise])

        login_as scipi.created_by

        visit new_admin_survey_publication_path(scipi)

        assert_text "This #{scipi.branding.name} has no questions. You should add them before approving any panelists."
        assert_button 'Publish', disabled: false
      end

      test 'disallow publishing when general participation surveys do not have questions' do
        scipoll = FactoryBot.create(:scipoll, :draft)

        login_as scipoll.created_by

        visit new_admin_survey_publication_path(scipoll)

        assert_text "This #{scipoll.branding.name} has no questions. You should not publish this #{scipoll.branding.name} " \
                    'until you add at least one question.'
        assert_button 'Publish', disabled: true
      end

      test 'disallow publishing SciPoll with results published and no results' do
        scipoll = FactoryBot.create(:scipoll, :draft, results_published: true)
        FactoryBot.create(:question, :text, survey: scipoll) # make sure we get past question warning

        login_as scipoll.created_by

        visit new_admin_survey_publication_path(scipoll)

        assert_text "This #{scipoll.branding.name} has \"Results Published\" checked, but no results are defined"
        assert_button 'Publish', disabled: true
      end

      test 'warn when Scipi allow public access' do
        scipi_branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, :general_participation, :draft, branding: scipi_branding)

        login_as scipi.created_by

        visit new_admin_survey_publication_path(scipi)

        assert_text 'This Scipi has public access. SciPi access is generally restricted to "Requires Selection"'
      end

      test 'warn when Scipolls require selection' do
        scipoll_branding = FactoryBot.create(:survey_branding, :scipoll)
        scipoll = FactoryBot.create(:scipoll, :draft, branding: scipoll_branding, invite_only: true)

        login_as scipoll.created_by

        visit new_admin_survey_publication_path(scipoll)

        assert_text "This Scipoll's access is set to \"Requires Selection\". Scipoll access is generally set to \"General Participation\""
      end

      test 'show notice when SciPis allow guest access' do
        scipi_branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, :invite_only, :draft, allow_guest_participation: true, branding: scipi_branding)

        login_as scipi.created_by

        visit new_admin_survey_publication_path(scipi)

        assert_text 'This SciPi allows guest participation. This means anonymous people (i.e. not logged in) can participate.'
      end

      test 'warn when SciPis do not have a recruitment close date' do
        scipi_branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, :invite_only, :draft, branding: scipi_branding)

        login_as scipi.created_by

        visit new_admin_survey_publication_path(scipi)

        assert_text 'This SciPi "Requires Selection", but does not have a recruitment close date. It is recommended that you set one.'
      end

      test 'warn when scipis disallow drafts' do
        scipi_branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, :invite_only, :draft, branding: scipi_branding, disallow_draft_submissions: true)

        login_as scipi.created_by

        visit new_admin_survey_publication_path(scipi)

        assert_text 'SciPis usually allow draft submissions. Please make sure this is not a mistake.'
      end

      test 'show a notice when scipolls allow drafts' do
        scipoll_branding = FactoryBot.create(:survey_branding, :scipoll)
        scipoll = FactoryBot.create(:scipoll, :draft, branding: scipoll_branding, disallow_draft_submissions: false)

        login_as scipoll.created_by

        visit new_admin_survey_publication_path(scipoll)

        assert_text "This SciPoll allows draft submissions. It's a good idea to disallow drafts so participants don't leave behind unfinished submissions."
      end
    end
  end
end

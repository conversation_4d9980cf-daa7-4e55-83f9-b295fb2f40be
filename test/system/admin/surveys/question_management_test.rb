# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    class QuestionManagementTest < ApplicationSystemTestCase
      setup do
        FactoryBot.rewind_sequences
      end

      test 'create profile country question' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, owners: [admin])

        login_as(admin)

        visit new_admin_survey_question_path(survey)

        select 'Country (Profile)', from: 'Question type'

        click_button 'Create'

        assert_current_path question_group_questions_path(survey)
        assert_text Questions::ProfileCountry.default_question_text
        assert_text 'Country (Profile)'
      end

      test 'update profile country question' do
        admin = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, owners: [admin])
        question = FactoryBot.create(:profile_country_question, survey:)

        login_as(admin)

        visit edit_admin_question_path(question)

        fill_in 'Question text', with: 'New question text'

        click_button 'Update'

        assert_selector 'td.question_text', text: 'New question text'
      end

      test 'delete question in un-sectioned survey' do
        admin = FactoryBot.create(:admin)
        survey = FactoryBot.create(:survey, created_by: admin)
        question1 = FactoryBot.create(:question, :text, survey:)
        question2 = FactoryBot.create(:question, :text, survey:)
        question3 = FactoryBot.create(:question, :text, survey:)

        login_as(admin)

        visit question_group_questions_path(survey)

        sleep(0.2)

        within_question_row(question2) do
          accept_confirm do
            click_link('Delete')
          end
        end

        assert_current_path question_group_questions_path(survey)

        deleted_row = question_row_selector(question2)
        assert_no_selector(deleted_row)

        assert_question_position(question1, 1)
        assert_question_position(question3, 2)
      end

      private

      def assert_question_position(question, position)
        within_question_row(question) do
          within('.number') do
            assert_content position.to_s
          end
        end
      end

      def question_row_selector(question)
        "#question_#{question.id}"
      end

      def within_question_row(question, &)
        selector = question_row_selector(question)

        within(selector, &)
      end
    end
  end
end

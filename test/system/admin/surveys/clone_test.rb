# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    class CloneTest < ApplicationSystemTestCase
      test 'only super admins can clone a survey' do
        user = FactoryBot.create(:super_admin)
        survey = FactoryBot.create(:survey, created_by: user)

        login_as(user)

        visit admin_survey_path(survey)

        accept_confirm do
          click_button 'Clone'
        end

        assert_text "#{survey.name} (copy)"
      end

      test 'scipi admins cannot clone survey' do
        user = FactoryBot.create(:scipi_admin)
        survey = FactoryBot.create(:survey, created_by: user)

        login_as(user)

        visit admin_survey_path(survey)

        assert_no_button 'Clone'
      end
    end
  end
end

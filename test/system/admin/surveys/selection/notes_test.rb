# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    module Selection
      class NotesTest < ApplicationSystemTestCase
        test 'adding a note returns the admin to the selection page' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          admin = scipi.created_by
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(user: expert)

          login_as(admin)

          visit admin_survey_selection_root_path(scipi)

          within(".applicant-#{applicant.id}") do
            click_button 'Actions'

            click_link 'Add note'
          end

          within('.new-note-form') do
            fill_in('Content', with: 'I am note content')

            click_button('Add Note')
          end

          assert_current_path admin_survey_selection_root_path(scipi)
          assert_text 'Note added.'
        end
      end
    end
  end
end

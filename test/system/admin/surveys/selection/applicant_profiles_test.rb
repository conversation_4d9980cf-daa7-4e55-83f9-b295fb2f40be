# frozen_string_literal: true

require 'application_system_test_case'

##
# This test should test different states of applicants profile data, to ensure
# that all states are handled correctly. e.g. empty profile, no CV, no keywords,
# no pubs, etc.

module Admin
  module Surveys
    module Selection
      class ApplicantProfilesTest < ApplicationSystemTestCase
        test 'new applicant without a CV' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(expert:)

          login_as(scipi.created_by)

          visit admin_survey_selection_root_path(scipi)

          assert_selector :xpath,
                          "//table[@id=\"applicant-table\"]/tbody/tr[@id=\"applicant-row-#{applicant.id}\"]/td[count(//table[@id=\"applicant-table\"]/thead/tr/th[text()=\"CV\"]/preceding-sibling::th)+1]/i[contains(@class, \"fa-times-circle\")]",
                          visible: :all
        end

        test 'new applicant without GE data' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(expert:, total_publications: 0, years_experience: 0)

          login_as(scipi.created_by)

          visit admin_survey_selection_root_path(scipi)

          assert_selector :xpath,
                          "//table[@id=\"applicant-table\"]/tbody/tr[@id=\"applicant-row-#{applicant.id}\"]/td[count(//table[@id=\"applicant-table\"]/thead/tr/th[descendant-or-self::*/text()=\"GE Score\"]/preceding-sibling::th)+1]",
                          text: 'N/A'
        end

        test 'scipi admins can view applicant profiles' do
          role = FactoryBot.create(:role, :scipi_admin)
          user = FactoryBot.create(:user, roles: [role])
          scipi = FactoryBot.create(:scipi, :invite_only, created_by: user)
          expert = FactoryBot.create(:expert)
          applicant = FactoryBot.create(:applicant, scipi:, expert:)

          login_as(user)

          visit admin_survey_selection_root_path(scipi)

          within("#applicant-row-#{applicant.id}") do
            click_link applicant.display_name
          end

          assert_current_path admin_expert_path(expert)
        end
      end
    end
  end
end

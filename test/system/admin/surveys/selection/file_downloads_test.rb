# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'test_helper'

module Admin
  module Surveys
    module Selection
      class FileDownloadsTest < ApplicationSystemTestCase
        include TestHelper

        test 'download profile data' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:scipi, :invite_only, owners: [admin])
          expert = FactoryBot.create(:expert)
          applicant = survey.applicants.create!(expert:)

          export = ApplicantExport.new(applicant_ids: [applicant.id], survey:)

          login_as admin

          visit admin_survey_selection_root_path(survey)

          select_applicant(applicant)

          click_button('Export')
          click_button('Profile Data')

          assert_downloaded(export.filename) do |file_contents|
            assert_equal export.to_csv, file_contents
          end
        end

        test 'download CVs' do
          admin = FactoryBot.create(:scipi_admin)
          survey = FactoryBot.create(:scipi, :invite_only, owners: [admin])
          expert = FactoryBot.create(:expert, :with_cv)
          applicant = survey.applicants.create!(expert:)

          login_as admin

          visit admin_survey_selection_root_path(survey)

          select_applicant(applicant)

          click_button('Export')
          click_button('CVs (Zip file)')

          assert_downloaded("#{survey.display_name} Applicant CVs.zip")
        end
      end
    end
  end
end

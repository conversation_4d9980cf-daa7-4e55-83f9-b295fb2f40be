# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    module Selection
      class AuditsTest < ApplicationSystemTestCase
        test 'view audit page for applicant' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(user: expert)
          applicant.audits.create!(audited_by: scipi.created_by, score: 100, notes: 'These are audit notes')

          login_as scipi.created_by

          visit admin_surveys_selection_applicant_audit_path(applicant)

          assert_field 'Score', with: '100'
          assert_field 'Notes', with: 'These are audit notes'
        end

        test 'add audit score to applicant' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(user: expert)

          login_as scipi.created_by

          visit admin_survey_selection_root_path(scipi)

          within(".applicant-#{applicant.id} .audit-score") do
            click_link '0.0' # safe bet since there's no audits
          end

          fill_in 'Score', with: '10'
          fill_in 'Notes', with: 'These are audit notes'

          click_button 'Submit Audit'

          assert_text 'Audit saved'
          assert_field 'Score', with: '10'

          visit admin_survey_selection_root_path(scipi)

          assert_selector ".applicant-#{applicant.id} .audit-score", text: '1.0'
        end
      end
    end
  end
end

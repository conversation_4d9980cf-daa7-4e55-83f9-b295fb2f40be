# frozen_string_literal: true

require 'application_system_test_case'
require_relative 'test_helper'

module Admin
  module Surveys
    module Selection
      class KeywordScoreTest < ApplicationSystemTestCase
        test 'view scaled score values' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          keyword = scipi.selection_keywords.create!(terms: ['Foo'])

          expert1 = FactoryBot.create(:expert)
          expert2 = FactoryBot.create(:expert)
          expert3 = FactoryBot.create(:expert)
          applicant1 = scipi.applicants.create!(expert: expert1)
          applicant2 = scipi.applicants.create!(expert: expert2)
          applicant3 = scipi.applicants.create!(expert: expert3)
          applicant1.keyword_counts.create!(keyword:, count: 123)
          applicant2.keyword_counts.create!(keyword:, count: 57)
          applicant3.keyword_counts.create!(keyword:, count: 23)

          login_as(scipi.created_by)

          visit admin_survey_selection_root_path(scipi)

          assert_selector ".applicant-#{applicant1.id} .keyword-score-#{keyword.id}", text: '1.00'
          assert_selector ".applicant-#{applicant2.id} .keyword-score-#{keyword.id}", text: '0.46'
          assert_selector ".applicant-#{applicant3.id} .keyword-score-#{keyword.id}", text: '0.19'
        end

        test 'view scaled score tootip' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          keyword = scipi.selection_keywords.create!(terms: ['Foo'])

          expert = FactoryBot.create(:expert)
          applicant = scipi.applicants.create!(expert:)
          applicant.keyword_counts.create!(keyword:, count: 100)

          login_as(scipi.created_by)

          visit admin_survey_selection_root_path(scipi)

          scaled_score = find(".applicant-#{applicant.id} .keyword-score-#{keyword.id} .scaled-score")
          scaled_score.hover

          assert_text 'Raw count: 100'
          assert_text 'Max count: 100'
          assert_text 'Rank: 1st'
        end
      end
    end
  end
end

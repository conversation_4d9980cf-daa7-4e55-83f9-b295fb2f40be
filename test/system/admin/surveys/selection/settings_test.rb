# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    module Selection
      class SettingsTest < ApplicationSystemTestCase
        test 'update keyword terms' do
          scipi = FactoryBot.create(:scipi, :invite_only)
          expert = FactoryBot.create(:expert)
          # Must add an applicant so that the table (and terms_label) shows up
          scipi.applicants.create!(expert:)

          login_as(scipi.created_by)

          visit admin_survey_selection_root_path(scipi)

          click_button 'Settings'

          fill_in term_field_name(index: 0), with: 'PFAS, PFOA'

          click_button 'Save Settings'

          within('#applicant-table thead') do
            assert_text 'PFAS'
            assert_no_text 'PFOA'
          end
        end

        private

        def term_field_name(index:)
          "selection[selection_keywords_attributes][#{index}][terms_field]"
        end
      end
    end
  end
end

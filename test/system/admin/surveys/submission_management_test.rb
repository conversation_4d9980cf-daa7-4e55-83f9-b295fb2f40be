# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    class SubmissionManagementTest < ApplicationSystemTestCase
      test 'clean up submissions' do
        survey = FactoryBot.create(:survey)
        FactoryBot.create(:long_question, survey:)
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        expert3 = FactoryBot.create(:expert)
        FactoryBot.create(:submission_with_text_answers, survey:, user: expert1)
        FactoryBot.create(:submission, :draft, survey:, user: expert2)
        FactoryBot.create(:submission_with_text_answers, submitted_at: nil, survey:, user: expert3)

        admin = survey.created_by

        login_as(admin)

        visit admin_survey_path(survey)

        find_by_id('clean_up_button').click

        accept_confirm("Are you sure you want to cleanup these #{survey.branding_name} submissions?") do
          click_button 'Draft Submissions'
        end

        assert_current_path admin_survey_path(survey)
        assert_text 'Purged 1 blanks. Submitted 1 drafts.'

        assert_selector "#participant-row-#{expert1.id}", text: 'Submitted'
        assert_no_selector "#participant-row-#{expert2.id}"
        assert_selector "#participant-row-#{expert3.id}", text: 'Submitted'
      end

      test 'submit selected drafts' do
        survey = FactoryBot.create(:survey)
        expert = FactoryBot.create(:expert)
        survey.submissions.create!(submitter: expert, submitted_at: nil)

        login_as survey.created_by

        visit admin_survey_path(survey)

        find("#participant_#{expert.id}").check
        click_button('Submissions')
        click_button('Submit')

        assert_selector "#participant-row-#{expert.id}", text: 'Submitted'
        assert_text 'One draft was submitted'
      end

      test 'set selected completed submissions as drafts' do
        survey = FactoryBot.create(:survey)
        expert = FactoryBot.create(:expert)
        survey.submissions.create!(submitter: expert, submitted_at: Time.current)

        login_as survey.created_by

        visit admin_survey_path(survey)

        find("#participant_#{expert.id}").check
        click_button('Submissions')
        sleep(0.2)
        click_button('Revert to draft')

        assert_selector "#participant-row-#{expert.id}", text: 'Draft'
        assert_text 'One completed submission was reverted to a draft'
      end

      test 'click into view a submission' do
        survey = FactoryBot.create(:survey)
        expert = FactoryBot.create(:expert)
        submission = survey.submissions.create!(submitter: expert, submitted_at: Time.current)

        login_as survey.created_by

        visit admin_survey_path(survey)

        find("#submission_#{submission.id}").click

        assert_current_path admin_surveys_submission_path(submission)
        assert_text "Submission for #{submission.submitter.internal_display_name}"
      end

      test 'submission view shows questions and answers' do
        survey = FactoryBot.create(:survey)
        FactoryBot.create(:long_question, survey:)
        expert = FactoryBot.create(:expert)
        submission = FactoryBot.create(:submission_with_text_answers, submitted_at: Time.current, survey:, user: expert)

        admin = survey.created_by

        login_as(admin)

        visit admin_surveys_submission_path(submission)

        assert_text "Submission for #{submission.submitter.internal_display_name}"
        assert_text survey.questions.first.title_with_number
        assert_text submission.answers.first.text_answer_content.to_plain_text # TRIX causing issues, just check text
      end

      test 'delete scipoll submission' do
        survey = FactoryBot.create(:survey, :scipoll)
        expert = FactoryBot.create(:expert)
        submission = FactoryBot.create(:submission_with_text_answers, submitted_at: Time.current, survey:, user: expert)

        admin = survey.created_by

        login_as(admin)

        visit admin_surveys_submission_path(submission)

        sleep(0.1)

        accept_confirm do
          click_button('Delete Submission')
        end

        assert_text "Submission ##{submission.id} was successfully deleted."
        assert_no_selector "#participant-row-#{expert.id}"
      end

      test 'cannot delete scipi submission' do
        survey = FactoryBot.create(:survey, :scipi)
        expert = FactoryBot.create(:expert)
        submission = FactoryBot.create(:submission_with_text_answers, submitted_at: Time.current, survey:, user: expert)

        admin = survey.created_by

        login_as(admin)

        visit admin_surveys_submission_path(submission)

        assert_no_selector 'Delete Submission'
      end
    end
  end
end

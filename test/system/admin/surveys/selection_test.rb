# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Surveys
    class SelectionTest < ApplicationSystemTestCase
      test 'no applicants state' do
        scipi = FactoryBot.create(:scipi, :invite_only)

        login_as(scipi.created_by)

        visit admin_survey_selection_root_path(scipi)

        assert_text 'No candidates have applied yet'
      end

      test 'view selection page' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        login_as(scipi.created_by)

        visit admin_survey_selection_root_path(scipi)

        assert_text applicant.display_name
      end

      test 'selection page hide rejected applicants by default' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:, rejected_at: Time.current)

        login_as(scipi.created_by)

        visit admin_survey_selection_root_path(scipi)

        assert_no_text applicant.display_name
      end

      test 'show rejected applicants' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:, rejected_at: Time.current)

        login_as(scipi.created_by)

        visit admin_survey_selection_root_path(scipi)

        click_button 'Active only' # dropdown toggle
        click_link 'All Applicants'

        assert_text applicant.display_name
      end

      test 'delete applicant' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        login_as(scipi.created_by)

        visit admin_survey_selection_root_path(scipi)

        within(".applicant-#{applicant.id}") do
          click_button 'Actions'

          accept_confirm("Are you sure you want to remove #{applicant.mail_name} from this SciPi?") do
            click_link 'Remove'
          end
        end

        assert_current_path admin_survey_selection_root_path(scipi)

        assert_no_selector "#applicant-#{applicant.id}"
        assert_text "#{expert.mail_name} was removed from this SciPi"
      end

      test 'selection closed' do
        scipi = FactoryBot.create(:scipi, :invite_only, selection_closes_on: 1.week.ago)

        login_as(scipi.created_by)

        visit admin_survey_selection_root_path(scipi)

        assert_text "Selection is closed for this #{scipi.branding.name}"
      end
    end
  end
end

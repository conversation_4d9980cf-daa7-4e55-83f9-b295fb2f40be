# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class PingsTest < ApplicationSystemTestCase
    setup do
      @admin = users(:pings_admin)

      login_as @admin
    end

    test 'view draft paid ping' do
      ping = create_paid_ping

      visit admin_pings_path(status: 'draft')

      assert_text ping.title
    end

    test 'view published paid Ping' do
      ping = create_paid_ping(published_at: Time.current)

      visit admin_pings_path

      within("#ping-#{ping.id}") do
        assert_text ping.title
        assert_no_content 'Draft'
      end
    end

    test 'view user-created public ping' do
      expert = users(:expert)
      ping = create_public_ping(author: expert)

      visit admin_pings_path

      within("#ping-#{ping.id}") do
        assert_text ping.title
      end
    end

    private

    def create_paid_ping(**)
      create_ping(author: @admin, type: :paid, reward_amount: 100, **)
    end

    def create_ping(author:, type:, **)
      author.pings.create!(
        content: 'This is a question.',
        title: "I am a #{type} ping",
        type: pings_ping_types(type),
        expertises: [expertises(:expertise1)],
        **
      )
    end

    def create_public_ping(author:)
      create_ping(author:, type: :public)
    end
  end
end

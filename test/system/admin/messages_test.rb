# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class MessagesTest < ApplicationSystemTestCase
    # setup do
    #   Elasticsearch::Extensions::Test::Cluster.start
    # end
    #
    # teardown do
    #   Elasticsearch::Extensions::Test::Cluster.stop
    # end

    test 'message expert' do
      skip('Un-skip once we can run ES in system tests, or whatever search tool we end up on')
      admin = FactoryBot.create(:db_admin)
      expert = FactoryBot.create(:expert)

      login_as admin

      visit admin_experts_path

      find_field('experts[expert_ids][]', with: expert.id).click
      click_button('Message Users')
      fill_in('Subject', with: 'Message sub')
      fill_in('Content', with: 'A message')
      click_button('Send Message')

      # There is no flash on the screen so test that the modal is closed,
      # and at least we're exercising the code.
      assert_no_text 'Compose Message'
    end
  end
end

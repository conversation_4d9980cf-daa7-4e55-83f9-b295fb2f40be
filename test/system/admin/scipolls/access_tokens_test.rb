# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Sc<PERSON>olls
    class AccessTokensTest < ApplicationSystemTestCase
      test 'download access tokens' do
        admin = FactoryBot.create(:scipoll_admin)
        scipoll = FactoryBot.create(:scipoll, :access_token_required, access_token_count: 1, created_by: admin)
        access_token = scipoll.access_tokens.create!

        csv = CSV.generate(headers: true, force_quotes: true) do |row|
          access_token_value = access_token.generate_token_for(:authentication)

          row << ['ID', 'Status', 'Access Token', 'Access Token URL']
          row << [access_token.id, 'Unused', access_token_value, access_tokens_url(access_token: access_token_value)]
        end

        login_as scipoll.created_by

        visit admin_survey_path(scipoll)

        within('#scipi-actions') do
          click_button 'Export'
          click_link 'Access Tokens'
        end

        assert_downloaded("scipoll_#{scipoll.id}_access_tokens.csv") do |file_contents|
          assert_equal "﻿#{csv}", file_contents
        end
      end

      test 'reset access tokens' do
        admin = FactoryBot.create(:scipoll_admin)
        scipoll = FactoryBot.create(:scipoll, :access_token_required, access_token_count: 1, created_by: admin)
        expert = FactoryBot.create(:expert)
        scipoll.submissions.create!(user: expert, submitted_at: Time.current)
        scipoll.access_tokens.create!(user: expert)

        login_as scipoll.created_by

        visit admin_survey_path(scipoll)

        within('#scipi-actions') do
          find_by_id('clean_up_button').click
          accept_confirm { click_button 'Reset 1 access token' }
        end

        assert_alert text: 'Access tokens have been reset.'
        assert_text 'This survey does not have participants yet.'
        assert_text 'Experts will appear here once they submit their responses.'
      end
    end
  end
end

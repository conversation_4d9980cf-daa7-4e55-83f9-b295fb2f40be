# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Sc<PERSON>olls
    class PublicationsTest < ApplicationSystemTestCase
      test 'require expertise tags for publication' do
        scipoll = FactoryBot.create(:scipoll)
        scipoll.questions.create!(question_text: 'What is your favorite color?', position: 1)

        login_as scipoll.created_by

        assert_no_scipoll_publication_route

        visit new_admin_survey_publication_url(scipoll)

        within('.expertise') do
          assert_text 'Not set!'
          assert_text 'At least one expertise tag is required to publish'
        end

        assert_button 'Publish', disabled: true
      end

      test 'required expertise tags are set' do
        expertise = FactoryBot.create(:expertise)
        scipoll = FactoryBot.create(:scipoll, expertises: [expertise])

        login_as scipoll.created_by

        assert_no_scipoll_publication_route

        visit new_admin_survey_publication_url(scipoll)

        within('.expertise') do
          assert_text expertise.name
          assert_no_text 'Not set!'
          assert_no_text 'At least one expertise tag is required to publish'
        end
      end

      test 'scheduled but missing critical info warning' do
        expertise = FactoryBot.create(:expertise)
        published_at = 2.days.from_now
        scipoll = FactoryBot.create(:scipoll, :results_published, expertises: [expertise], published_at:)

        login_as scipoll.created_by

        assert_no_scipoll_publication_route

        visit new_admin_survey_publication_url(scipoll)

        pub_at = published_at.strftime('%A, %B %-d, %Y at %I:%M %p (%Z)')
        assert_text "This SciPoll is scheduled for publication on #{pub_at} but is missing critical information below."
        assert_text 'Oops! No Questions'
        assert_text 'Oops! No Results'
      end

      test 'do not show results published warning when set, but still a draft' do
        # If the results_published flag is set, but the survey has not yet been published,
        # calling results_published? will return false, since it also checks published?
        # This is checking that we're checking the setting directly.
        scipoll = FactoryBot.create(:scipoll, :draft, :results_published)

        login_as scipoll.created_by

        visit new_admin_survey_publication_url(scipoll)

        assert_text 'Results published'
        assert_no_text 'The default setting of publishing results published was turned off for this SciPoll'
      end

      test 'show results published warning' do
        scipoll = FactoryBot.create(:scipoll, results_published: false)

        login_as scipoll.created_by

        visit new_admin_survey_publication_url(scipoll)

        assert_text 'Results not published'
        assert_text 'The default setting of publishing results published was turned off for this SciPoll'
      end

      test 'show results public setting when not yet published' do
        # If the results_public flag is set, but the survey has not yet been published,
        # calling results_public? will return false, since it also checks published?.
        # This is checking that we're checking the setting directly.
        scipoll = FactoryBot.create(:scipoll, :draft, results_published: true, results_public: true)

        login_as scipoll.created_by

        visit new_admin_survey_publication_url(scipoll)

        assert_text 'Results public'
      end

      test 'show results public setting when not yet closed' do
        # If the results_public flag is set, but the survey still open, calling
        # results_public? will return false, since it also checks closed?.
        # This is checking that we're checking the setting directly.
        scipoll = FactoryBot.create(:scipoll, :open, :published, :results_published, results_public: true)

        login_as scipoll.created_by

        visit new_admin_survey_publication_url(scipoll)

        assert_text 'Results public'
      end

      test 'show results public published warning when disabled' do
        scipoll = FactoryBot.create(:scipoll, results_public: false)

        login_as scipoll.created_by

        visit new_admin_survey_publication_url(scipoll)

        assert_text 'Results not public, they will only visible to participants when published'
        assert_text 'The default setting of making results public was turned off for this SciPoll.'
      end

      private

      # Once there is a scipoll publication route, it should be used instead of
      # the survey publication route.
      def assert_no_scipoll_publication_route
        assert_no_url_helper :new_admin_scipoll_publication_url,
                             message: "Replace 'scipoll' with 'scipoll' in the URL helper below"
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class ScipollsTest < ApplicationSystemTestCase
    test 'scipolls can see SciPolls' do
      admin = FactoryBot.create(:scipoll_admin)
      scipoll = FactoryBot.create(:scipoll, created_by: admin)

      login_as(admin)

      click_link 'SciPolls'

      assert_text scipoll.name
    end

    test 'new SciPoll defaults' do
      admin = FactoryBot.create(:scipoll_admin)

      login_as(admin)

      visit new_admin_scipoll_path

      within('#access') do
        assert_checked_field 'Public'
        assert_unchecked_field 'Allow guest participation'
        assert_unchecked_field 'Unlisted'
      end

      within('#participation') do
        assert_field 'Closes on', with: 1.month.from_now.strftime('%Y-%m-%d')
        assert_checked_field 'Disallow draft submissions'
      end

      within('#results') do
        assert_checked_field 'Results Published (for authorized participants)'
        # assert_field 'Minimum results required', with: '0'
        assert_checked_field 'Results Public on Close?'
      end

      within('#debate') do
        assert_checked_field 'Allow/Show Comments'
        assert_field 'Debate closes on', with: ''
      end
    end

    test 'create scipoll and redirect to question builder' do
      admin = FactoryBot.create(:scipoll_admin)

      login_as(admin)

      visit new_admin_scipoll_path

      fill_in 'Name', with: 'My SciPoll'

      click_button 'Create SciPoll'

      assert_text 'SciPoll was successfully created.'
      assert_current_path question_group_questions_path(QuestionGroup.last)
    end

    test 'update scipoll' do
      scipoll = FactoryBot.create(:scipoll)

      login_as(scipoll.created_by)

      visit edit_admin_scipoll_path(scipoll)

      fill_in 'Name', with: 'Updated SciPoll'

      click_button 'Save SciPoll'

      assert_text 'SciPoll was successfully updated.'
    end
  end
end

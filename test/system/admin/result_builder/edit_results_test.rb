# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module ResultBuilder
    class EditResultsTest < ApplicationSystemTestCase
      test 'update a multi-question score result' do
        admin = FactoryBot.create(:admin)
        survey = FactoryBot.create(:survey, created_by: admin)

        updated_title = 'Updated Title'
        render_type = FactoryBot.create(:render_type, :ranked_order_plot)
        result_type = FactoryBot.create(
          :result_type,
          :multi_question_score,
          render_types: [render_type]
        )

        question1 = FactoryBot.create(
          :radio,
          :scorable,
          survey:,
          position: 1
        )
        question2 = FactoryBot.create(
          :radio,
          :scorable,
          survey:,
          position: 2
        )
        question_to_add = FactoryBot.create(
          :radio,
          :scorable,
          survey:,
          position: 3
        )

        result_def = FactoryBot.create(
          :result_definition,
          render_type:,
          result_type:,
          title: 'Original Title',
          questions: [question1, question2],
          survey:
        )

        login_as(admin)

        visit edit_survey_builder_result_definition_path(result_def)

        within('#edit_result_definition') do
          fill_in 'Title', with: updated_title
          check question_to_add.question_text

          click_button('Save')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)

        assert_result_exists updated_title
        assert_confirmation updated_title

        assert_content question1.question_text
        assert_content question2.question_text
        assert_content question_to_add.question_text
      end

      test 'edit a file download result' do
        admin = FactoryBot.create(:admin)

        login_as(admin)

        result = FactoryBot.create(:result_definition, :file_attachment)

        visit edit_survey_builder_result_definition_path(result)

        new_title = 'New File Download Title'
        file_name = 'cv.pdf' # alternate to test_attachment.pdf

        within('#edit_result_definition') do
          fill_in 'Title', with: new_title
          attach_file 'New file', File.join(file_fixture_path, file_name)

          click_button('Save')
        end

        sleep(0.2)

        assert_current_path survey_builder_survey_result_definitions_path(result.survey)

        within('.result-builder__result-list') do
          assert_content(new_title)
        end

        result_row_path = "//li[@class=\"result-builder__result-list-item\" and @data-result-id = '#{result.id}']"

        within(:xpath, result_row_path) do
          click_link('View Result')
        end

        assert_current_path survey_result_path(result.survey, result)

        assert_link file_name
      end

      private

      def assert_confirmation(title)
        assert_alert type: 'info', text: "Result Definition '#{title}' updated."
      end

      def assert_result_exists(title)
        assert_selector '.result-builder__result-name', text: title
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module ResultBuilder
    class AddResultsTest < ApplicationSystemTestCase
      attr_reader :admin, :survey

      setup do
        @admin = FactoryBot.create(:admin)
        @survey = FactoryBot.create(:survey, created_by: admin)
        # Once the new result definition form no longer has references
        # to the result types this can be removed.
        Rails.application.load_seed
      end

      test 'add a multiple choice question result' do
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::MULTIPLE_CHOICE_RESPONSES_NAME
        )
        render_type = result_type.default_render_type
        question = FactoryBot.create(:radio, survey:)

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)
        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          select question.question_text, from: 'Question'
          select render_type.name, from: 'Render type'

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(question.question_text)
        assert_creation_confirmation(question.question_text)
      end

      test 'add a grid question result' do
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::GRID_RESPONSES_NAME
        )
        question = FactoryBot.create(:grid_question, :radio, survey:)

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          select question.question_text, from: 'Question'

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(question.question_text)
        assert_creation_confirmation(question.question_text)
      end

      test 'add a text question result' do
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::TEXT_RESPONSES_NAME
        )
        question = FactoryBot.create(:question, :text, survey:)

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          select question.question_text, from: 'Question'

          click_button('Create Result definition')
        end

        sleep(0.3)

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(question.question_text)
        assert_creation_confirmation(question.question_text)
      end

      test 'add a grouped question result' do
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::GROUPED_MULTIPLE_CHOICE_RESPONSES_NAME
        )
        question = FactoryBot.create(:radio, survey:)
        group_by_question = FactoryBot.create(:radio, survey:)
        title = "#{question.question_text} Grouped By #{group_by_question.question_text}"

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          select question.question_text, from: 'Question'
          select group_by_question.question_text, from: 'Group by question'

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(title)
        assert_creation_confirmation(title)
      end

      test 'add a custom data set result' do
        title = 'A Free Form Data Set'
        attachment_path = file_fixture('free_form_data.csv')
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::FREE_FORM_NAME
        )

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          fill_in 'Title', with: title
          attach_file('Data file', attachment_path)

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(title)
        assert_creation_confirmation(title)
      end

      test 'add a submission score result' do
        title = 'Submission score'
        login_as(admin)
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::SUBMISSION_SCORE_NAME
        )

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          fill_in 'Title', with: title

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(title)
        assert_creation_confirmation(title)
      end

      test 'add a file attachment result' do
        title = 'File attachment'
        attachment_path = file_fixture('test_attachment.pdf')
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::FILE_ATTACHMENT_NAME
        )

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'

          sleep(0.3) # it might take a moment for the fields to show up

          fill_in 'Title', with: title
          attach_file('File', attachment_path)

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(title)
        assert_creation_confirmation(title)
      end

      test 'add a participant file list result' do
        question = FactoryBot.create(:file_upload_question, survey:)
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::PARTICIPANT_UPLOAD_LIST_NAME
        )

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          select question.question_text, from: 'Question'

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(question.question_text)
        assert_creation_confirmation(question.question_text)
      end

      test 'add a weighted grid sum result' do
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::WEIGHTED_SUM_SCORES_NAME
        )
        question = FactoryBot.create(:grid_question, survey:)
        FactoryBot.create(:grid_structure, :weight_sum_grid, question:)

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'
          select question.question_text, from: 'Question'

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)
        assert_result_exists(question.question_text)
        assert_creation_confirmation(question.question_text)
      end

      test 'add a multi question score result' do
        title = 'Multi Question score Result'
        result_type = ResultDefinitionType.find_by!(
          system_name: ResultDefinitionType::MULTI_QUESTION_SCORE_NAME
        )
        included1 = FactoryBot.create(:radio, :scorable, survey:)
        included2 = FactoryBot.create(:radio, :scorable, survey:)
        excluded = FactoryBot.create(:radio, :scorable, survey:)

        login_as(admin)

        visit new_survey_builder_survey_result_definition_path(survey)

        within('#new_result_definition') do
          select result_type.name, from: 'Result type'

          fill_in 'Title', with: title
          check included1.question_text
          check included2.question_text

          click_button('Create Result definition')
        end

        assert_current_path survey_builder_survey_result_definitions_path(survey)

        assert_result_exists title
        assert_creation_confirmation title

        assert_content included1.question_text
        assert_content included2.question_text
        assert_no_content excluded.question_text
      end

      private

      def assert_creation_confirmation(title)
        assert_alert type: 'info', text: "New result '#{title}' created"
      end

      def assert_result_exists(title)
        assert_selector '.result-builder__result-name', text: title
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module ResultBuilder
    class AppendResultsTest < ApplicationSystemTestCase
      setup do
        @admin = FactoryBot.create(:admin)

        load_all_result_types
        load_all_render_types
      end

      test 'append with missing results' do
        survey = FactoryBot.create(:survey, created_by: @admin)
        question_section = FactoryBot.create(:question_section, survey:)
        question1 = FactoryBot.create(:radio, survey:, section: question_section)
        question2 = FactoryBot.create(:radio, survey:, section: question_section)
        question3 = FactoryBot.create(:radio, survey:, section: question_section)
        result_section = FactoryBot.create(:result_section, survey:)
        FactoryBot.create(:result_definition, question: question1, survey:, section: result_section)

        login_as(@admin)

        visit survey_builder_survey_result_definitions_path(survey)

        assert_text(question1.question_text)
        assert_no_text(question2.question_text)
        assert_no_text(question3.question_text)

        accept_confirm { click_link('Append (2)') }

        assert_text('2 Results Appended!')
        assert_text(question1.question_text)
        assert_text(question2.question_text)
        assert_text(question3.question_text)
      end

      test 'cannot append if all results are present' do
        survey = FactoryBot.create(:survey, created_by: @admin)
        question_section = FactoryBot.create(:question_section, survey:)
        question1 = FactoryBot.create(:radio, survey:, section: question_section)
        question2 = FactoryBot.create(:radio, survey:, section: question_section)
        result_section = FactoryBot.create(:result_section, survey:)
        FactoryBot.create(:result_definition, question: question1, survey:, section: result_section)
        FactoryBot.create(:result_definition, question: question2, survey:, section: result_section)

        login_as(@admin)

        visit survey_builder_survey_result_definitions_path(survey)

        assert_no_text('Append')
      end
    end
  end
end

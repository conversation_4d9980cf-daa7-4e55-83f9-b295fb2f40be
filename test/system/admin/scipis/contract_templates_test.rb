# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Scipis
    class ContractTemplatesTest < ApplicationSystemTestCase
      test 'create SciPi with contract template' do
        admin = FactoryBot.create(:admin)
        template = FactoryBot.create(:contract_template)

        login_as admin

        visit new_admin_survey_path

        choose 'SciPi'
        fill_in 'Name', with: 'Contract template test'
        check 'Contracts required?'
        select template.filename, from: 'Contract template'
        click_button 'Create Survey'

        # Toggle on contracts dropdown
        assert_button 'Contracts'
      end

      test 'can edit contract template without sent contracts' do
        contract_template = FactoryBot.create(:contract_template)
        scipi = FactoryBot.create(:scipi, :contract_required, contract_template:)

        login_as scipi.created_by

        visit edit_admin_survey_path(scipi)

        scroll_to(find_by_id('participation'))

        assert_field 'Contracts required?', disabled: false
        assert_field 'Contract template', disabled: false
      end

      test 'cannot edit contract template with sent contracts' do
        contract_template = FactoryBot.create(:contract_template)
        scipi = FactoryBot.create(:scipi, :contract_required, contract_template:)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_last_sent_at: Time.current)

        login_as scipi.created_by

        visit edit_admin_survey_path(scipi)

        scroll_to(find_by_id('participation'))

        assert_text 'The contract settings cannot be changed since 1 contract has already been sent to panelists'
        assert_field 'Contracts required?', disabled: true
        assert_field 'Contract template', disabled: true
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Scipis
    class PublicationsTest < ApplicationSystemTestCase
      include ::Scipis::EngagementInfoTestHelper

      test 'require expertise tags for publication' do
        scipi = FactoryBot.create(:scipi)

        login_as scipi.created_by

        visit new_admin_survey_publication_url(scipi)

        within('.expertise') do
          assert_text 'Not set!'
          assert_text 'At least one expertise tag is required to publish'
        end

        assert_button 'Publish', disabled: true
      end

      test 'required expertise tags are set' do
        expertise = FactoryBot.create(:expertise)
        scipi = FactoryBot.create(:scipi, expertises: [expertise])

        login_as scipi.created_by

        visit new_admin_survey_publication_url(scipi)

        within('.expertise') do
          assert_text expertise.name
          assert_no_text 'Not set!'
          assert_no_text 'At least one expertise tag is required to publish'
        end
      end

      test 'show warning when paid and without a default pay rate' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, branding:, engagement_info_attributes: { default_pay_rate: 0 })

        login_as scipi.created_by

        visit new_admin_survey_publication_url(scipi)

        scroll_to_engagement_details

        assert_text 'This SciPi is paid, but has no default pay rate.'
      end

      test 'show engagement details' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(
          :scipi,
          branding:,
          engagement_info_attributes: {
            blinding: 'Double blind',
            default_pay_rate: 1000,
            estimated_start_date: 1.week.from_now,
            estimated_end_date: 2.weeks.from_now,
            follow_up_work: 'Lots of follow up work',
            level_of_effort: 'Approximately 5-10 hours',
            number_of_panelists: 33,
            travel: 'None',
            review_format: 'Three rounds',
            work_description: 'Review stuff and opine'
          }
        )

        login_as scipi.created_by

        visit new_admin_survey_publication_url(scipi)

        scroll_to_engagement_details

        within('#engagement-info') do
          assert_engagement_details(label: 'Work Description', details: 'Review stuff and opine')
          assert_engagement_details(label: 'Review Format', details: 'Three rounds')
          assert_engagement_details(label: 'Blinding', details: 'Double blind')
          assert_engagement_details(label: 'Level of effort', details: 'Approximately 5-10 hours')
          assert_engagement_details(label: 'Review Period', details: "From #{1.week.from_now.strftime('%B %-d, %Y')} " \
                                                                     "until #{2.weeks.from_now.strftime('%B %-d, %Y')}")
          assert_engagement_details(label: 'Compensation', details: '$1,000')
          assert_engagement_details(label: 'Number of panelists', details: '33')
          assert_engagement_details(label: 'Travel', details: 'None')
          assert_engagement_details(label: 'Follow-up work', details: 'Lots of follow up work')
        end
      end

      private

      # So this part of the page appears in screenshots
      def scroll_to_engagement_details
        scroll_to(find_by_id('engagement-info'))
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Scipis
    module Contracts
      class SettingsTest < ApplicationSystemTestCase
        test 'show unsent contract status' do
          template = FactoryBot.create(:contract_template)
          scipi = FactoryBot.create(:scipi, :invite_only)
          login_as scipi.created_by

          visit admin_scipi_contracts_path(scipi)

          check 'Contracts required?'
          select template.filename, from: 'Contract template'

          click_button 'Save Settings'

          assert_text 'Settings updated'
          assert_checked_field 'Contracts required?'
          assert_select 'Contract template', selected: template.filename
        end

        test 'show warnings when contracts are required and no template is set' do
          scipi = FactoryBot.create(:scipi, :invite_only, :contract_required)
          login_as scipi.created_by

          visit admin_scipi_contracts_path(scipi)

          assert_text 'A contract template must be set in order to send contracts to panelists'
        end
      end
    end
  end
end

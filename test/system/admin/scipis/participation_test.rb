# frozen_string_literal: true

require 'application_system_test_case'

require_relative 'show_page_test_helper'

module Admin
  module Scipis
    class ParticipationTest < ApplicationSystemTestCase
      include ShowPageTestHelper

      # This is mainly to handle regressions where applicants were showing up here accidentally
      test 'applicants do not show up on participation page' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)

        login_as(admin)

        visit admin_survey_path(scipi)

        assert_no_selector('.scipi-admin_participant-list', text: expert.mail_name)
      end

      test 'suspended panelist' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as(scipi.created_by)

        visit admin_survey_path(scipi, expert.id)

        check("participant_#{expert.id}")

        click_button('Status')
        click_button('Suspend')

        within_panelist_row(panelist: expert) do
          assert_text 'Suspended'
        end
      end

      test 'reactivate panelist' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, suspended_at: Time.current, suspended_by: scipi.created_by)

        login_as(scipi.created_by)

        visit admin_survey_path(scipi, expert.id)

        check("participant_#{expert.id}")

        click_button('Status')
        click_button('Reactivate')

        within_panelist_row(panelist: expert) do
          assert_no_text 'Suspended'
        end
      end

      test 'only super admins can delete a survey' do
        user = FactoryBot.create(:super_admin)
        scipi = FactoryBot.create(:scipi, :draft, created_by: user)

        login_as(user)

        visit admin_survey_path(scipi)

        accept_confirm do
          click_button 'Delete'
        end

        assert_text "Survey #{scipi.display_name} was deleted."
      end

      test 'SciPi admins cannot delete a SciPi' do
        user = FactoryBot.create(:scipi_admin)
        scipi = FactoryBot.create(:scipi, :draft, created_by: user)

        login_as(user)

        visit admin_survey_path(scipi)

        assert_no_button 'Delete'
      end

      private

      def select_from_autocomplete(expert)
        element = find('.autocomplete__item', text: expert.email)
        # Make sure we're clicking in the element
        element.click(x: 5, y: 5)
        send_keys(:escape)
        sleep(0.3)
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

require_relative 'show_page_test_helper'

module Admin
  module Scipis
    class ContractsTest < ApplicationSystemTestCase
      include ShowPageTestHelper

      test 'show unsent contract status' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        within_panelist_row(panelist: expert) do
          assert_selector '.contract-status', text: 'Unsent'
        end
      end

      test 'mark contract as sent' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        within_panelist_row(panelist: expert) do
          check("participant_#{expert.id}")
        end

        click_button 'Contracts' # dropdown toggle
        sleep(0.2)
        click_button 'Mark as sent (unsigned)'
        sleep(0.2)

        within_panelist_row(panelist: expert) do
          assert_selector '.contract-status', text: 'Sent'
        end
      end

      test 'mark contract as unsent' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_last_sent_at: Time.current)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        within_panelist_row(panelist: expert) do
          check("participant_#{expert.id}")
        end

        click_button 'Contracts' # dropdown toggle
        click_button 'Mark as unsent'

        within_panelist_row(panelist: expert) do
          assert_selector '.contract-status', text: 'Unsent'
        end
      end

      test 'mark contract as signed' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_last_sent_at: Time.current)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        within_panelist_row(panelist: expert) do
          check("participant_#{expert.id}")
        end

        click_button 'Contracts' # dropdown toggle
        click_button 'Mark as signed'

        within_panelist_row(panelist: expert) do
          assert_selector '.contract-status', text: 'Signed'
        end
      end

      test 'create contract previews' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_last_sent_at: nil)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        within_panelist_row(panelist: expert) do
          check("participant_#{expert.id}")
        end

        click_button 'Contracts' # dropdown toggle

        accept_confirm { click_button 'Create preview(s) in Sign Now' }

        assert_text 'Contracts preview(s) were sent to SignNow.'
      end

      test 'send contracts' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only, :published)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_last_sent_at: nil)

        login_as scipi.created_by

        visit admin_survey_path(scipi)

        within_panelist_row(panelist: expert) do
          check("participant_#{expert.id}")
        end

        click_button 'Contracts' # dropdown toggle

        accept_confirm { click_button 'Request signature(s)' }

        assert_text 'Contracts were sent to selected panelists.'
        within_panelist_row(panelist: expert) do
          assert_selector '.contract-status', text: 'Sent'
        end
      end
    end
  end
end

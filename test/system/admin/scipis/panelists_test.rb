# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  module Scipis
    class PanelistsTest < ApplicationSystemTestCase
      test 'view panelist' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        admin = scipi.created_by
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        note = expert.notes.create!(content: 'Foo', context: scipi, created_by: admin)

        login_as(admin)

        visit admin_survey_path(scipi)

        within("#participant-row-#{expert.id}") do
          click_link 'Participant Details'
        end

        assert_text expert.mail_name
        assert_note_present note
      end

      test 'show default pay rate' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi,
                                  :contract_required,
                                  :invite_only,
                                  branding:,
                                  engagement_info_attributes: { default_pay_rate: 1234.56 })
        admin = scipi.created_by
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as(admin)

        visit admin_survey_path(scipi)

        within("#participant-row-#{expert.id}") do
          assert_text '$1,234.56'
        end
      end

      test 'set custom pay rate' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi,
                                  :contract_required,
                                  :invite_only,
                                  branding:,
                                  engagement_info_attributes: { default_pay_rate: 1234.56 })
        admin = scipi.created_by
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as(admin)

        visit admin_survey_path(scipi)

        check("participant_#{expert.id}")

        click_button 'Contracts' # dropdown toggle
        click_button 'Set Rates...'

        fill_in('New Rate', with: '9876.54')
        click_button 'Save rate'

        within("#participant-row-#{expert.id}") do
          assert_text '$9,876.54'
        end
      end

      test 'custom pay rate dialog re-displays on validation failure' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi,
                                  :contract_required,
                                  :invite_only,
                                  branding:,
                                  engagement_info_attributes: { default_pay_rate: 1234.56 })
        admin = scipi.created_by
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as(admin)

        visit admin_survey_path(scipi)

        check("participant_#{expert.id}")

        click_button 'Contracts' # dropdown toggle
        click_button 'Set Rates...'

        fill_in('New Rate', with: '')
        click_button 'Save rate'

        within('dialog[data-dialog-target="dialog"]') do
          assert_text 'can\'t be blank'
        end
      end

      private

      def assert_note_present(note = nil, **attrs)
        if note.present? # assume preexisting note
          within_note(note) do
            assert_text note.content
            assert_selector('i.fas.fa-star.me-2.text-warning', count: 1) if note.positive?
            assert_selector('i.fas.fa-flag.me-2.text-danger', count: 1) if note.negative?
          end
        else
          # assume note just created
          within_note_list do
            assert_text attrs[:content]
          end
        end
      end

      def within_note_list(&)
        within('.notes-list', &)
      end

      def within_note(note, &)
        within("#note-#{note.id}", &)
      end
    end
  end
end

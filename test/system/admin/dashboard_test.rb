# frozen_string_literal: true

require 'application_system_test_case'

module Admin
  class DashboardTest < ApplicationSystemTestCase
    test 'regular users cannot get to the Dashboard' do
      user = FactoryBot.create(:expert)

      login_as user

      visit admin_root_path

      assert_current_path dashboard_path
    end

    test 'admins can get to the Dashboard' do
      user = FactoryBot.create(:admin)

      login_as user

      visit admin_root_path

      assert_current_path admin_root_path
      assert_content 'Recent Sign-Ups'
    end
  end
end

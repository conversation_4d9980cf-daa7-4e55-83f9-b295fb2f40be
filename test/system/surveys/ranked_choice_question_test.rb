# frozen_string_literal: true

require 'application_system_test_case'

module Surveys
  class RankedChoiceQuestionTest < ApplicationSystemTestCase
    test 'Simplest drag-to-rank scenario (1,2,3 -> 1,2,3) works and saves correctly' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:ranked_choice, survey:)

      login_as FactoryBot.create(:expert)
      visit new_question_group_answer_group_path(survey)

      all_choices = question.answer_choices.order(:position).to_a
      ranked_container = find('[data-ranked-choice-question-target="rankedContainer"]')

      # Drag items to container in reverse order to achieve 1,2,3
      all_choices.reverse_each do |answer_choice|
        unranked_item = find("[data-ranked-choice-question-target='unrankedContainer'] div[data-choice-id='#{answer_choice.id}']")
        unranked_item.drag_to(ranked_container)
      end

      click_button 'Finish'
      click_link 'View Submission'

      # Verify 1,2,3 on view submission page
      within("[data-question-id='#{question.id}']") do
        all_choices.each_with_index do |ac, index|
          assert_selector('strong', text: /#{index + 1}.*#{ac.label}/)
        end
      end
    end

    test 'Edit page loads with previously-ranked choices in correct order' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:ranked_choice, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      answer = FactoryBot.create(:answer, answer_group: submission, question:)
      add_ranked_choices(answer) # Create initial ranking (1,2,3)

      login_as user
      visit edit_question_group_answer_group_path(survey, submission)

      # Verify 1,2,3 on edit page
      within('[data-ranked-choice-question-target="rankedContainer"]') do
        expected_ids = question.answer_choices.order(:position).pluck(:id).map(&:to_s)
        choice_ids = all('div[data-choice-id]').pluck('data-choice-id')
        assert_equal expected_ids, choice_ids
      end
    end

    test 'Can reorder ranked choices and save new order' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:ranked_choice, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      answer = FactoryBot.create(:answer, answer_group: submission, question:)
      add_ranked_choices(answer) # Create initial ranking (1,2,3)

      login_as user
      visit edit_question_group_answer_group_path(survey, submission)

      # Move the second ranked item to the top
      second_item = first("div[data-choice-id=\"#{question.answer_choices.second.id}\"]")
      target = first('div[data-choice-id]')

      second_item.drag_to(target)

      click_button 'Finish'
      click_link 'View Submission'

      # Verify the new order on view submission page (2,1,3)
      within("[data-question-id='#{question.id}']") do
        assert_selector('strong', text: /1.*#{question.answer_choices.second.label}/)
        assert_selector('strong', text: /2.*#{question.answer_choices.first.label}/)
        assert_selector('strong', text: /3.*#{question.answer_choices.third.label}/)
      end
    end

    test 'Drag an unranked item into the middle of ranked items list' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:ranked_choice, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      answer = FactoryBot.create(:answer, answer_group: submission, question:)
      add_ranked_choices(answer, 2) # Create initial ranking (1,2)

      login_as user
      visit edit_question_group_answer_group_path(survey, submission)

      first_choice, second_choice, third_choice = question.answer_choices.order(:position).to_a

      # Find the unranked item (choice 3) from the unranked container
      unranked_item = find("[data-ranked-choice-question-target='unrankedContainer'] div[data-choice-id='#{third_choice.id}']")

      # Find the target position (after the first ranked item)
      target = find("[data-ranked-choice-question-target='rankedContainer'] div[data-choice-id='#{first_choice.id}']")

      unranked_item.drag_to(target)

      sleep(0.3)

      # Verify the final order 1, 3, 2
      within('[data-ranked-choice-question-target="rankedContainer"]') do
        expected_ids = [first_choice.id, third_choice.id, second_choice.id].map(&:to_s)
        choice_ids = all('div[data-choice-id]').pluck('data-choice-id')
        assert_equal expected_ids, choice_ids
      end
    end

    test 'Unrank items by dragging back to unranked container' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:ranked_choice, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      answer = FactoryBot.create(:answer, answer_group: submission, question:)
      add_ranked_choices(answer) # Create initial ranking (1,2,3)

      login_as user
      visit edit_question_group_answer_group_path(survey, submission)

      first_choice, second_choice, third_choice = question.answer_choices.order(:position).to_a

      ranked_item = find("[data-ranked-choice-question-target='rankedContainer'] div[data-choice-id='#{first_choice.id}']")
      unranked_container = find('[data-ranked-choice-question-target="unrankedContainer"]')

      ranked_item.drag_to(unranked_container)

      # Assert choice 1 moved to unranked container
      within('[data-ranked-choice-question-target="unrankedContainer"]') do
        assert_selector("div[data-choice-id='#{first_choice.id}']")
      end

      # Assert choice 2 & 3 remain ranked in order
      within('[data-ranked-choice-question-target="rankedContainer"]') do
        expected_ids = [second_choice.id, third_choice.id].map(&:to_s)
        choice_ids = all('div[data-choice-id]').pluck('data-choice-id')
        assert_equal expected_ids, choice_ids
      end
    end

    test 'Unranking and saving updates ranking order correctly' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:ranked_choice, survey:)
      user = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, question_group: survey, user:)
      answer = FactoryBot.create(:answer, answer_group: submission, question:)
      add_ranked_choices(answer) # Create initial ranking (1,2,3)

      login_as user
      visit edit_question_group_answer_group_path(survey, submission)

      first_choice, second_choice, third_choice = question.answer_choices.order(:position).to_a

      ranked_item = find("[data-ranked-choice-question-target='rankedContainer'] div[data-choice-id='#{first_choice.id}']")
      unranked_container = find('[data-ranked-choice-question-target="unrankedContainer"]')

      ranked_item.drag_to(unranked_container)

      click_button 'Finish'
      click_link 'View Submission'

      # Verify ranks updated correctly - choice 2 with rank 1, choice 3 with rank 2
      within("[data-question-id='#{question.id}']") do
        assert_selector('strong', text: /1.*#{second_choice.label}/)
        assert_selector('strong', text: /2.*#{third_choice.label}/)
      end
    end

    test 'Required ranked choice question requires one items' do
      survey = FactoryBot.create(:survey, :published, disallow_draft_submissions: true)
      FactoryBot.create(:ranked_choice, survey:, required: true)

      login_as FactoryBot.create(:expert)
      visit new_question_group_answer_group_path(survey)

      click_button 'Finish'

      sleep(0.3)

      assert_text 'Answer(s) required for'
    end

    test 'Required ranked choice question can be saved in a partially-ranked state' do
      survey = FactoryBot.create(:survey, :published, disallow_draft_submissions: false)
      question = FactoryBot.create(:ranked_choice, survey:, required: true)

      login_as FactoryBot.create(:expert)
      visit new_question_group_answer_group_path(survey)

      # Rank two of three choices
      container = find('[data-ranked-choice-question-target="rankedContainer"]')
      question.answer_choices.limit(2).each do |answer_choice|
        draggable = find("[data-ranked-choice-question-target=\"unrankedContainer\"] div[data-choice-id=\"#{answer_choice.id}\"]")
        draggable.drag_to(container)
      end

      click_button 'Save Progress'
      assert_no_text 'Answer(s) required for'
    end

    private

    def add_ranked_choices(answer, limit = nil)
      choices = answer.question.answer_choices.order(:position)
      choices = choices.limit(limit) if limit

      choices.each_with_index do |choice, index|
        answer.selected_choices.create!(
          answer_choice: choice,
          rank: index + 1
        )
      end
    end
  end
end

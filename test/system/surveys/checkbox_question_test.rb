# frozen_string_literal: true

require 'application_system_test_case'

module Surveys
  class CheckboxQuestionTest < ApplicationSystemTestCase
    test 'Limited checkbox question' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:, max_selected_choices: 2)

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      # Check the first two of three checkboxes
      question.answer_choices.limit(2).each do |ac|
        check ac.label
      end

      third_checkbox = find_field(question.answer_choices[2].label, disabled: true)

      assert third_checkbox.disabled?
    end

    test 'Setting the limit to max possible choices causes no problems' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:, max_selected_choices: 3)

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      click_all(question.answer_choices)

      question.answer_choices.each do |ac|
        assert find_field(ac.label).checked?
      end
    end

    test 'Setting the limit to 0 choices causes no problems' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:, max_selected_choices: 0)

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      click_all(question.answer_choices)

      question.answer_choices.each do |ac|
        assert find_field(ac.label).checked?
      end
    end

    test 'Unlimited checkbox questions work as per usual' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:)

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      click_all(question.answer_choices)

      question.answer_choices.each do |ac|
        assert find_field(ac.label).checked?
      end
    end

    private

    def click_all(answer_choices)
      answer_choices.each do |ac|
        check ac.label
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

# This test should be used to verify that questions scores,
# like the WSG are calculated correctly.
module Surveys
  class QuestionScoreTest < ApplicationSystemTestCase
    test 'default score for an empty WSG grid' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(
        :grid_question,
        survey:,
        answer_choices: [
          AnswerChoice.new(label: 1, value: 1, position: 1),
          AnswerChoice.new(label: 2, value: 2, position: 2),
          AnswerChoice.new(label: 3, value: 3, position: 3)
        ]
      )

      _grid = FactoryBot.create(:grid_structure, :weight_sum_grid, question:)

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      within('.weighted-sum-results') do
        within('.wss-holder') do
          assert_text '0.0'
        end
      end
    end

    test 'incomplete WSG score' do
      row_headers = %w[row1::0.5 row2::0.5]
      col_headers = %w[col1::0.7 col2::0.3]

      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(
        :grid_question,
        answer_choices: [
          AnswerChoice.new(label: 1, value: 1, position: 1),
          AnswerChoice.new(label: 2, value: 2, position: 2),
          AnswerChoice.new(label: 3, value: 3, position: 3)
        ],
        survey:
      )
      _grid = FactoryBot.create(
        :grid_structure,
        :weight_sum_grid,
        question:,
        row_headers:,
        column_headers: col_headers
      )

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      select(2, from: cell_selector(question_id: question.id, row: 1, column: 1))
      select(3, from: cell_selector(question_id: question.id, row: 1, column: 2))
      select(2, from: cell_selector(question_id: question.id, row: 2, column: 1))
      # Skip row 2, column 2
      send_keys(:escape)

      within('.weighted-sum-results') do
        within('.wss-holder') do
          assert_text '61.7'
        end
      end
    end

    test 'complete WSG score' do
      row_headers = %w[row1::0.5 row2::0.5]
      col_headers = %w[col1::0.7 col2::0.3]

      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(
        :grid_question,
        answer_choices: [
          AnswerChoice.new(label: 1, value: 1, position: 1),
          AnswerChoice.new(label: 2, value: 2, position: 2),
          AnswerChoice.new(label: 3, value: 3, position: 3)
        ],
        survey:
      )
      _grid = FactoryBot.create(
        :grid_structure,
        :weight_sum_grid,
        question:,
        row_headers:,
        column_headers: col_headers
      )

      login_as FactoryBot.create(:expert)

      visit new_question_group_answer_group_path(survey)

      select(2, from: cell_selector(question_id: question.id, row: 1, column: 1))
      select(3, from: cell_selector(question_id: question.id, row: 1, column: 2))
      select(2, from: cell_selector(question_id: question.id, row: 2, column: 1))
      select(3, from: cell_selector(question_id: question.id, row: 2, column: 2))
      send_keys(:escape)

      within('.weighted-sum-results') do
        within('.wss-holder') do
          assert_text '76.7'
        end
      end
    end

    private

    def cell_selector(question_id:, row:, column:)
      "answer_group_#{question_id}_cell_#{row}_#{column}_answer"
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  module Participation
    class ReviewMaterialsTest < ApplicationSystemTestCase
      test 'show required document in introduction' do
        survey = FactoryBot.create(:survey, :scipi, :published, introduction: 'This is an introduction.')
        survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'), required: true)

        expert = FactoryBot.create(:expert)
        survey.applicants.create!(expert:, approved_at: Time.zone.now)

        login_as expert

        visit scipi_path(survey)

        click_link "Take #{survey.branding_name}"

        within('.introduction') do
          assert_text 'Review Materials'
          assert_text 'test_attachment.pdf'
          assert_text 'You have not downloaded this file yet'
          # Since no optional materials are present
          assert_no_text 'Files marked "optional" are not required, but may provide additional, useful context.'
        end
      end

      test 'show optional document in introduction' do
        survey = FactoryBot.create(:survey, :scipi, :published, introduction: 'This is an introduction.')
        survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'), required: false)

        expert = FactoryBot.create(:expert)
        survey.applicants.create!(expert:, approved_at: Time.zone.now)

        login_as expert

        visit scipi_path(survey)

        click_link "Take #{survey.branding_name}"

        within('.introduction') do
          assert_text 'Review Materials'
          assert_text 'test_attachment.pdf'
          assert_text 'Optional'
          assert_text 'Files marked "optional" are not required, but may provide additional, useful context.'
        end
      end

      test 'download file' do
        survey = FactoryBot.create(:survey, :scipi, :published, introduction: 'This is an introduction.')
        survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'), required: false)

        expert = FactoryBot.create(:expert)
        survey.applicants.create!(expert:, approved_at: Time.zone.now)

        login_as expert

        visit scipi_path(survey)

        click_link "Take #{survey.branding_name}"

        within('.introduction') do
          click_link 'test_attachment.pdf'
        end

        assert_downloaded('test_attachment.pdf')

        within('.introduction') do
          assert_text 'Downloaded!'
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  module Participation
    class FileUploadQuestionTest < ApplicationSystemTestCase
      test 'answer a file upload question' do
        skip('I do not think this test can work a present since the file field is not visible')
        # Finish this once better styling is in place.
        survey = FactoryBot.create(:survey, :general_participation, :results_published)
        question = FactoryBot.create(:file_upload_question, survey:)
        file_name = 'test_attachment.pdf'
        file_path = file_fixture(file_name)

        login_as FactoryBot.create(:expert)

        visit new_question_group_answer_group_path(survey)

        within('#new_answer_group') do
          attach_file("#answer_group[#{question.id}_attachment", file_path, make_visible: true)

          click_button('Save Progress')
        end

        assert_content("Current attachment: #{file_name}")
      end
    end
  end
end

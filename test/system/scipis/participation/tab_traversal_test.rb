# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  module Participation
    class TabTraversalTest < ApplicationSystemTestCase
      test 'traverse through a full scipi using pager' do
        scipi = segmented_scipi
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as expert

        visit new_question_group_answer_group_path(scipi)

        # Intro Tab
        assert_button 'Prev', disabled: true
        click_button('Next')

        # Section 1 Tab
        assert_button 'Prev', disabled: false
        check scipi.questions.first.answer_choices.first.label # Fail if not visible
        click_button('Next')

        # Finish Tab
        assert_button 'Prev', disabled: false
        assert_button 'Next', disabled: true
        click_button('Finish')

        assert_alert text: 'Submission Saved'
      end

      test 'traverse through a full scipi using tab links' do
        scipi = segmented_scipi
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as expert

        visit new_question_group_answer_group_path(scipi)

        # Intro Tab
        assert_button 'Prev', disabled: true
        assert_button 'Next', disabled: false
        click_link scipi.question_sections.first.name

        # Section 1 Tab
        assert_button 'Prev', disabled: false
        assert_button 'Next', disabled: false
        check scipi.questions.first.answer_choices.first.label # Fail if not visible
        click_link 'Finish'

        # Finish Tab
        assert_button 'Prev', disabled: false
        assert_button 'Next', disabled: true
        click_button('Finish')

        assert_alert text: 'Submission Saved'
      end

      private

      def segmented_scipi
        scipi = FactoryBot.create(:scipi, :invite_only, :published, branding: Surveys::Branding.scipi, introduction: 'hello world')
        section = FactoryBot.create(:question_section, question_group: scipi, position: 1)
        FactoryBot.create(:checkbox, position: 1, section:, question_group: scipi, answer_choice_count: 2)

        scipi
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  module Participation
    class SubmissionTest < ApplicationSystemTestCase
      test 'panelist without a contract can take a survey when contracts are not required' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, branding: Surveys::Branding.scipi)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as expert

        visit scipi_path(scipi)

        click_link('Take SciPi')

        within('.main-content form') do
          click_button('Finish')
        end

        assert_link('Retake SciPi')
        assert_alert text: 'Submission Saved'
      end

      test 'panelist without a contract cannot take a survey when contracts are required' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only, :published, branding: Surveys::Branding.scipi)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_signed_at: nil)

        login_as expert

        visit scipi_path(scipi)

        assert_no_link('Take SciPi')
      end

      test 'panelist with a contract can take a survey when contracts are required' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only, :published, branding: Surveys::Branding.scipi)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:, contract_signed_at: Time.current, contract_last_sent_at: Time.current)

        login_as expert

        visit scipi_path(scipi)

        click_link('Take SciPi')

        within('.main-content form') do
          click_button('Finish')
        end

        assert_link('Retake SciPi')
        assert_alert text: 'Submission Saved'
      end

      test 'panelist can save draft' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, branding: Surveys::Branding.scipi)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as expert

        visit scipi_path(scipi)

        click_link('Take SciPi')

        within('.main-content form') do
          click_button('Save Progress')
        end

        assert_button 'Save Progress'
        assert_button 'Finish'
        assert_alert text: 'Progress Saved'
      end

      test 'save progress button is no longer available after submitting answer_group' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, branding: Surveys::Branding.scipi)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)

        login_as expert

        visit scipi_path(scipi)

        click_link('Take SciPi')

        within('.main-content form') do
          click_button('Finish')
        end

        click_link('Retake SciPi')

        assert_button('Finish')
        assert_no_button('Save Progress')
      end
    end
  end
end

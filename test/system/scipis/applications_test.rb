# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class ApplicationsTest < ApplicationSystemTestCase
    test 'application page shows default landing page content' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open, landing_content: nil)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipi_path(scipi)

      assert_text 'Welcome to this SciPi'
      assert_text 'If you are able to participate in the survey or view the results, those options will appear'
    end

    test 'application page shows custom landing page content' do
      landing_content = Faker::Lorem.paragraphs.join
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open, landing_content:)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipi_path(scipi)

      assert_text landing_content
      assert_no_text "Welcome to this #{scipi.branding.name}"
      assert_no_text 'If you are able to participate in the survey or view the results, those options will appear'
    end

    test 'successful application with incomplete profile' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipi_path(scipi)

      within('.introduction') do
        assert_content scipi.landing_content.to_plain_text
      end

      within('form[data-controller="scipi-application"]') do
        fill_in_rich_text_area 'invite_applicant_statement', with: Faker::Lorem.paragraph
        click_button('Apply')
      end

      assert_current_path edit_profile_path(return_scipi: scipi)
      assert_content 'Thank you for applying'
    end

    test 'successful application with complete profile' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)
      expert = FactoryBot.create(:expert, :complete_profile)
      applicant_statement = Faker::Lorem.paragraph

      login_as expert

      visit scipi_path(scipi)

      within('.introduction') do
        assert_content scipi.landing_content.to_plain_text
      end

      within('form[data-controller="scipi-application"]') do
        fill_in_rich_text_area 'invite_applicant_statement', with: applicant_statement
        click_button('Apply')
      end

      sleep(0.2)

      assert_current_path scipi_path(scipi)
      assert_content 'Thank you for applying'

      within('.content-sidebar') do
        assert_text 'Under Review'
        click_link 'My Application Statement'
        assert_content applicant_statement
      end
    end

    test 'can edit applicant_statement while selection is open' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open, :selection_open)
      expert = FactoryBot.create(:expert, :complete_profile)
      FactoryBot.create(:invite, survey: scipi, user: expert)
      new_applicant_statement =  Faker::Lorem.paragraph

      login_as expert

      visit scipi_path(scipi)

      click_link 'My Application Statement'

      within('form[data-controller="scipi-application"]') do
        fill_in_rich_text_area 'invite_applicant_statement', with: new_applicant_statement
        click_button('Update')
      end

      assert_current_path scipi_path(scipi)

      assert_content 'Applicant Statement Updated'

      click_link 'My Application Statement'

      within('#edit_application_modal') do
        assert_content new_applicant_statement
      end
    end

    test 'cannot edit applicant_statement while selection is closed' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open, selection_closes_on: 1.week.ago)
      expert = FactoryBot.create(:expert, :complete_profile)
      invite = FactoryBot.create(:invite, survey: scipi, user: expert)

      login_as expert

      visit scipi_path(scipi)

      click_link 'My Application Statement'

      sleep(0.2)

      within('#edit_application_modal') do
        assert_no_selector('form[data-controller="scipi-application"]')
        assert_text invite.applicant_statement.to_plain_text
      end
    end

    test 'warning for application with empty applicant statement' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)
      expert = FactoryBot.create(:expert, :complete_profile)

      login_as expert

      visit scipi_path(scipi)

      within('form[data-controller="scipi-application"]') do
        click_button('Apply')
      end

      sleep(0.2)

      page.driver.browser.switch_to.alert.accept

      assert_current_path scipi_path(scipi)
      assert_content 'Thank you for applying'
    end

    test 'submit apply after recruitment has closed' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipi_path(scipi)

      scipi.update!(recruitment_closes_on: 2.days.ago)

      within('form[data-controller="scipi-application"]') do
        fill_in_rich_text_area 'invite_applicant_statement', with: 'Better luck next time'
        click_button('Apply')
      end

      sleep(0.2)

      assert_text("We're sorry, but the application period for this SciPi is closed.")
    end

    test 'cannot apply to a closed scipi' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_closed)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipi_path(scipi)

      assert_text 'Recruiting Closed'
      assert_no_button 'Apply'

      within('.introduction') do
        assert_content scipi.landing_content.to_plain_text
      end

      assert_no_selector('form[data-controller="scipi-application"]')
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class PublicViewTest < ApplicationSystemTestCase
    setup do
      ENV['LEGACY_BRANDING_ID'] = Surveys::Branding.legacy.id.to_s
      ENV['SCIPOLL_BRANDING_ID'] = Surveys::Branding.scipoll.id.to_s
      Rails.application.reload_routes!
    end

    test 'published scipis are visible' do
      published = FactoryBot.create(:survey, :scipi, :published)

      visit scipis_path

      within_card_table do
        assert_text published.name
      end
    end

    test 'draft scipis are not visible' do
      draft = FactoryBot.create(:scipi, :draft)

      visit scipis_path

      within_card_table do
        assert_no_text draft.name
      end
    end

    test 'published, unlisted scipis are not visible' do
      unlisted = FactoryBot.create(:survey, :scipi, :published, :unlisted)

      visit scipis_path

      within_card_table do
        assert_no_text unlisted.name
      end
    end

    test 'recruiting scipis show up on Recruiting tab' do
      recruiting = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)

      visit scipis_path(recruiting: true)

      within_card_table do
        assert_text recruiting.name
      end
    end

    test 'open scipis show up on Active tab' do
      open = FactoryBot.create(:survey, :scipi, :open)

      visit scipis_path(active: true)

      within_card_table do
        assert_text open.name
      end
    end

    test 'scipis without public results do not show up on the Results tab' do
      no_results = FactoryBot.create(:survey, :scipi, :published, results_public: false)

      visit scipis_path(results: true)

      within_card_table do
        assert_no_text no_results.name
      end
    end

    test 'scipis with public results show up on the Results tab' do
      public_results = FactoryBot.create(:survey, :scipi, :published, :results_public)

      visit scipis_path(results: true)

      within_card_table do
        assert_text public_results.name
      end
    end

    test 'search for scipi' do
      name = Faker::Lorem.words.join(' ')
      scipi = FactoryBot.create(:survey, :scipi, :published, name:)

      visit scipis_path

      search_term = scipi.name.split.sample

      within('#scipis_table_card') do
        fill_in('Search SciPis', with: search_term)
        assert_text scipi.name
      end
    end

    test 'cannot find scipi with missing term' do
      scipi = FactoryBot.create(:survey, :scipi, :published, name: 'foo')

      visit scipis_path

      within('#scipis_table_card') do
        fill_in('Search SciPis', with: 'bar')
        assert_no_text scipi.name
      end
    end

    test 'complete scipolls show up on complete tabs' do
      complete_scipoll = FactoryBot.create(:scipoll, :published, :closed)
      active_scipoll = FactoryBot.create(:scipoll, :published, :open)

      visit scipolls_path(complete: true)

      assert_current_path scipolls_path(complete: true)

      within_card_table do
        assert_text complete_scipoll.name
        assert_no_text active_scipoll.name
      end
    end

    test 'scipolls and legacy do NOT show up on scipis path' do
      scipi = FactoryBot.create(:survey, :scipi, :published)
      scipoll = FactoryBot.create(:scipoll, :published)
      legacy_survey = FactoryBot.create(:survey, :legacy, :published)

      visit scipis_path

      within_card_table do
        assert_text scipi.name
        assert_no_text scipoll.name
        assert_no_text legacy_survey.name
      end
    end

    test 'legacy_index_path works and holds only legacy surveys' do
      scipi = FactoryBot.create(:survey, :scipi, :published)
      scipoll = FactoryBot.create(:scipoll, :published)
      legacy_survey = FactoryBot.create(:survey, :legacy, :published)

      visit surveys_path

      assert_current_path surveys_path

      within_card_table do
        assert_text legacy_survey.name
        assert_no_text scipi.name
        assert_no_text scipoll.name
      end
    end

    private

    def within_card_table(&)
      within('.card-table', &)
    end
  end
end

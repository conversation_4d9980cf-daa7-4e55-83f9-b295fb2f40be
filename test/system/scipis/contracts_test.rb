# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class ContractsTest < ApplicationSystemTestCase
    test 'contract not requestable to sign' do
      scipi = FactoryBot.create(:scipi, :published, :contract_required)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(
        expert:,
        sign_now_contract_id: nil,
        contract_embed_link: nil,
        contract_embed_link_requested_at: nil,
        contract_signed_at: nil
      )

      login_as expert

      visit scipi_path(scipi)

      assert_text 'We will notify you when your contract is ready to sign'
    end

    test 'request contract' do
      scipi = FactoryBot.create(:scipi, :published, :contract_required)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'dummy_document_id',
        contract_embed_link: nil,
        contract_embed_link_requested_at: nil,
        contract_signed_at: nil
      )

      login_as expert

      visit scipi_path(scipi)

      click_button 'Start Signing'

      assert_text 'Contract Loading'
      assert_text 'Your contract will be ready shortly.'
      assert_text 'Loading your contract...'
    end

    test 'contract ready to sign' do
      scipi = FactoryBot.create(:scipi, :published, :contract_required)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(
        expert:,
        contract_embed_link: 'http://example.com/embed_link',
        contract_embed_link_requested_at: Time.current,
        contract_signed_at: nil
      )

      login_as expert

      visit scipi_path(scipi)

      assert_text 'View & Sign Your Contract'
      assert_link 'Sign Now!'
    end

    test 'contract request expired' do
      scipi = FactoryBot.create(:scipi, :published, :contract_required)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(
        expert:,
        sign_now_contract_id: 'dummy_document_id',
        contract_embed_link: 'http://example.com/embed_link',
        contract_embed_link_requested_at: 46.minutes.ago,
        contract_signed_at: nil
      )

      login_as expert

      visit scipi_path(scipi)

      assert_text 'Your Previous Contract Expired'
      assert_button 'Start Signing'
    end

    test 'completed contract awaiting file' do
      scipi = FactoryBot.create(:scipi, :published, :contract_required)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(
        expert:,
        contract_last_sent_at: Time.current,
        contract_embed_link: 'http://example.com/embed_link',
        contract_signed_at: Time.current
      )

      login_as expert

      visit scipi_path(scipi)

      assert_text 'Contract Complete'
      assert_text 'We will email you a copy of your signed contract once it is available.'
      assert_no_link 'Download'
    end

    test 'download completed contract' do
      scipi = FactoryBot.create(:scipi, :published, :contract_required)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        contract_last_sent_at: Time.current,
        contract_embed_link: 'http://example.com/embed_link',
        contract_signed_at: Time.current
      )
      panelist.completed_contract.attach(
        io: file_fixture('test_attachment.pdf').open,
        filename: 'test_contract.pdf',
        content_type: 'application/pdf'
      )

      login_as expert

      visit scipi_path(scipi)

      assert_text 'Contract Complete'
      assert_link 'Download'
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module <PERSON>ipis
  module Results
    class FileUploadListTest < ApplicationSystemTestCase
      test 'view a file upload list result' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :results_published)
        FactoryBot.create(:invite, :approved, survey: scipi, expert:)

        question = FactoryBot.create(:file_upload_question, scipi:)
        result = FactoryBot.create(:result_definition, :upload_list, scipi:, question:)

        submission = FactoryBot.create(:submission, :final, scipi:, submitter: expert)
        file_name = 'test_attachment.pdf'
        file = uploaded_fixture_file(file_name)
        answer = FactoryBot.create(:answer, question:, submission:, attachment: file)

        login_as expert

        visit survey_result_path(scipi, result)

        assert_link answer.attachment_filename
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  module Results
    class FileDownloadTest < ApplicationSystemTestCase
      test 'view download result' do
        scipi = FactoryBot.create(:survey, :legacy, :results_public)

        file = uploaded_fixture_file('test_attachment.pdf')
        result = FactoryBot.create(:result_definition,
                                   :file_attachment,
                                   :file_download_render_type,
                                   scipi:,
                                   position: 1,
                                   attachment: file)

        visit survey_result_path(scipi, result)

        assert_content result.attachment_filename
      end
    end
  end
end

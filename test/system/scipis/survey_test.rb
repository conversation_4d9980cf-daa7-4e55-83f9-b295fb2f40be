# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class SurveyTest < ApplicationSystemTestCase
    setup do
      ENV['SCIPOLL_BRANDING_ID'] = Surveys::Branding.scipoll.id.to_s
      Rails.application.reload_routes!
    end

    test 'survey path autocorrects to scipi path' do
      scipi = FactoryBot.create(:survey, :scipi, :published)

      visit survey_path(scipi)

      assert_current_path scipi_path(scipi)
    end

    test 'survey path autocorrects to scipoll path' do
      scipoll = FactoryBot.create(:scipoll, :published)

      visit survey_path(scipoll)

      assert_current_path scipoll_path(scipoll)
    end

    test 'scipi path autocorrects to scipoll path' do
      scipoll = FactoryBot.create(:scipoll, :published)

      visit scipi_path(scipoll)

      assert_current_path scipoll_path(scipoll)
    end

    test 'legacy survey path falls back to survey_path' do
      legacy_survey = FactoryBot.create(:survey, :legacy, :published)

      visit survey_path(legacy_survey)

      assert_current_path survey_path(legacy_survey)
    end

    test 'expertise tags appear on survey listing' do
      scipi = FactoryBot.create(:survey, :scipi, :published)
      tag = expertises(:expertise1)
      scipi.expertises = [tag]

      visit scipis_path

      within('#scipis_table') do
        assert_text scipi.name
        assert_text tag.name
      end
    end

    test 'expertise tags appear on survey landing page' do
      scipi = FactoryBot.create(:survey, :scipi, :published)
      tag = expertises(:expertise1)
      scipi.expertises = [tag]

      visit scipi_path(scipi)

      within('.content-main') do
        assert_text tag.name
      end
    end

    test 'scipolls and legacy do not appear in scipis index' do
      published_scipoll = FactoryBot.create(:scipoll, :published)
      legacy_survey = question_groups(:legacy)

      visit scipis_path

      within_table do
        assert_no_text published_scipoll.name
        assert_no_text legacy_survey.name
      end
    end

    test 'scipolls do appear on scipolls index' do
      published_scipoll = FactoryBot.create(:scipoll, :published)

      visit scipolls_path

      within_table do
        assert_text published_scipoll.name
      end
    end

    test 'scipolls index shows scipoll headings' do
      visit scipolls_path

      within('.header h1') do
        assert_text 'SciPolls'
      end
    end

    test 'scipoll menu expanded when viewing scipoll index' do
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipolls_path

      within('.navbar-nav-main-nav') do
        assert_selector('.nav-link.active', text: 'SciPolls')
        assert_selector('#scipolls_menu.collapse.show')
      end
    end

    private

    def within_table(&)
      within('.card-table', &)
    end
  end
end

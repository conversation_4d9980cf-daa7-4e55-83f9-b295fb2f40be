# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class LandingPageTest < ApplicationSystemTestCase
    include EngagementInfoTestHelper

    test 'logged out user can see published Scipi landing page' do
      published = FactoryBot.create(:survey, :scipi, :published)

      visit scipi_path(published)

      assert_text published.name
    end

    test 'any logged in users can see published Scipi landing page' do
      user = FactoryBot.create(:expert)
      published = FactoryBot.create(:survey, :scipi, :published)

      login_as(user)

      visit scipi_path(published)

      assert_text published.name
    end

    test 'admins can see draft Scipi landing page' do
      admin = FactoryBot.create(:admin)
      draft = FactoryBot.create(:scipi, :draft)

      login_as(admin)

      visit scipi_path(draft)

      assert_text draft.name
    end

    test 'take survey button appears for approved participant' do
      scipi = FactoryBot.create(:survey, :scipi, :open)
      expert = FactoryBot.create(:expert)
      FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

      login_as expert

      visit scipi_path(scipi)

      within('.block-actions') do
        assert_selector('a', text: "Take #{scipi.branding_name}")
      end
    end

    test 'approved participant sees default instructions' do
      survey = FactoryBot.create(:survey, :scipi, :open, participant_instructions: nil)
      expert = FactoryBot.create(:expert)
      survey.panelists.create!(expert:)

      login_as expert

      visit scipi_path(survey)

      assert_text 'Congratulations! You have been selected to participate in this survey'
      assert_text 'The survey administrator will contact you with next steps'
    end

    test 'approved participant sees custom instructions' do
      survey = FactoryBot.create(:survey, :scipi, :open, participant_instructions: 'I am custom instructions!')
      expert = FactoryBot.create(:expert)
      survey.panelists.create!(expert:)

      login_as expert

      visit scipi_path(survey)

      assert_no_text 'Congratulations! You have been selected to participate in this survey'
      assert_no_text 'The survey administrator will contact you with next steps'
      assert_text 'I am custom instructions!'
    end

    test 'debate results button appears for approved participant in debate stage' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :results_published)
      expert = FactoryBot.create(:expert)
      FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

      login_as expert

      visit scipi_path(scipi)

      within('.block-actions') do
        assert_selector('a', text: 'Debate Results')
      end
    end

    test 'debate results button appears for approved participant in debate stage ROUND' do
      scipi = FactoryBot.create(:scipi, :published, :results_published, number_of_rounds: 1)
      FactoryBot.create(:debate_round, :active, survey: scipi, position: 1)

      expert = FactoryBot.create(:expert)
      FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

      login_as expert

      visit scipi_path(scipi)

      within('.block-actions') do
        assert_selector('a', text: 'Debate Results')
      end
    end

    test 'public login/register to apply appears for requiting scipi' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)

      visit scipi_path(scipi)

      within('.block-actions') do
        assert_selector('a', text: 'Login/Register to Apply')
      end
    end

    test 'no actions buttons appear for applicant' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)
      expert = FactoryBot.create(:expert)
      FactoryBot.create(:invite, survey: scipi, user: expert)

      login_as expert

      visit scipi_path(scipi)

      within('.block-actions') do
        assert_no_selector('a')
      end
    end

    test 'view complete scipoll shows complete badge' do
      scipoll = FactoryBot.create(:scipoll, :published, :closed)

      visit scipoll_path(scipoll)

      within('.header') do
        assert_selector('.badge', text: 'Complete')
      end
    end

    test 'scipoll menu expanded when viewing scipoll' do
      scipoll = FactoryBot.create(:scipoll, :published)
      expert = FactoryBot.create(:expert)

      login_as expert

      visit scipi_path(scipoll)

      within('.navbar-nav-main-nav') do
        assert_selector('.nav-link.active', text: 'SciPolls')
        assert_selector('#scipolls_menu.collapse.show')
      end
    end

    test 'meta image for stock scipi' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_closed)

      visit survey_path(scipi)

      assert_selector("meta[property='og:image'][content*='scipinion-scipi-']", visible: false)
      refute_selector("meta[property='og:image'][content*='scipinion-scipi-recruiting-']", visible: false)
    end

    test 'meta image for recruiting scipi' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :recruitment_open)

      visit survey_path(scipi)

      assert_selector("meta[property='og:image'][content*='scipinion-scipi-recruiting-']", visible: false)
    end

    test 'view engagement info' do
      scipi = FactoryBot.create(
        :survey,
        :scipi,
        :published,
        engagement_info_attributes: {
          blinding: 'Double blind',
          default_pay_rate: 1000,
          estimated_start_date: 1.week.from_now,
          estimated_end_date: 2.weeks.from_now,
          follow_up_work: 'Lots of follow up work',
          level_of_effort: 'Approximately 5 to 10 hours',
          max_pay_rate: 2000,
          number_of_panelists: 33,
          travel: 'None',
          review_format: 'Three rounds',
          work_description: 'Review stuff and opine'
        }
      )

      visit scipi_url(scipi)

      within('.engagement-info') do
        assert_engagement_details(label: 'Work Description', details: 'Review stuff and opine')
        assert_engagement_details(label: 'Review Format', details: 'Three rounds')
        assert_engagement_details(label: 'Blinding', details: 'Double blind')
        assert_engagement_details(label: 'Level of effort', details: 'Approximately 5 to 10 hours')
        assert_engagement_details(label: 'Review Period Dates are approximate and subject to change',
                                  details: "From #{1.week.from_now.strftime('%B %-d, %Y')} " \
                                           "until #{2.weeks.from_now.strftime('%B %-d, %Y')}")
        assert_engagement_details(label: 'Compensation', details: '$1,000 - $2,000')
        assert_engagement_details(label: 'Number of panelists', details: '33')
        assert_engagement_details(label: 'Travel', details: 'None')
        assert_engagement_details(label: 'Follow-up work', details: 'Lots of follow up work')
      end
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class ResultsTest < ApplicationSystemTestCase
    test 'legacy survey results accessible to participant when results are published' do
      legacy_survey = FactoryBot.create(:survey, :legacy, :published, :results_published)
      expert = users(:expert)
      legacy_survey.answer_groups.create!(user: expert, submitted_at: Time.zone.now)

      login_as expert

      visit survey_path(legacy_survey)

      within('.block-actions') do
        click_link('Debate Results')
      end

      assert_current_path survey_results_path(legacy_survey)
    end

    test 'legacy survey results inaccessible to nonparticipant even when results are published' do
      legacy_survey = FactoryBot.create(:survey, :legacy, :published, :results_published)
      expert = users(:expert)

      login_as expert

      visit survey_path(legacy_survey)

      within('.block-actions') do
        assert_no_selector('a', text: 'View Results')
        assert_no_selector('a', text: 'Debate Results')
      end

      visit survey_results_path(legacy_survey)

      assert_current_path dashboard_path
    end

    test 'meta image for scipi results' do
      scipi = FactoryBot.create(:survey, :scipi, :published, :results_public)

      result_definition = create_result(scipi:)

      visit survey_result_path(survey_id: scipi.id, id: result_definition.id)

      assert_selector("meta[property='og:image'][content*='scipinion-scipi-results-']", visible: false)
    end

    private

    def create_result(scipi:)
      FactoryBot.create(
        :result_definition,
        :multiple_choice_responses,
        :pie_chart,
        position: rand(1..10),
        question: FactoryBot.create(:radio, survey: scipi),
        survey: scipi
      )
    end
  end
end

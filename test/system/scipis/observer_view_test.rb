# frozen_string_literal: true

require 'application_system_test_case'

module Scipis
  class ObserverViewTest < ApplicationSystemTestCase
    test 'observer gets routed to observe route' do
      scipi = FactoryBot.create(:scipi, :invite_only)
      observer = FactoryBot.create(:expert)
      scipi.observers.create!(user: observer)

      login_as observer

      visit scipi_path(scipi)
      assert_current_path observe_scipi_path(scipi)
    end

    test 'preview Survey' do
      scipi = FactoryBot.create(:scipi, :invite_only)
      observer = FactoryBot.create(:expert)
      scipi.observers.create!(user: observer)

      login_as observer

      visit scipi_path(scipi)

      click_link 'Preview Survey'

      assert_current_path survey_preview_path(scipi)
    end
  end
end

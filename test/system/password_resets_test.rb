# frozen_string_literal: true

require 'application_system_test_case'

class PasswordResetsTest < ApplicationSystemTestCase
  test 'logged out  user, with existing email requests a password reset' do
    email = Faker::Internet.email
    FactoryBot.create(:expert, email:)

    visit reset_password_path

    within('.password-reset-request-form') do
      fill_in('Email', with: email)

      click_button('Request Password Reset Email')
    end

    assert_current_path confirm_password_reset_request_path
    assert_content 'An email has been sent to you with instructions on how to reset your password.'
  end

  test 'logged out user, with non-existent email receives an error' do
    email = Faker::Internet.email

    visit reset_password_path

    within('.password-reset-request-form') do
      fill_in('Email', with: email)

      click_button('Request Password Reset Email')
    end

    assert_current_path password_reset_request_path
    assert_content 'This email is not associated with an account on our system'
  end

  test 'reset password' do
    new_password = Faker::Internet.password
    user = FactoryBot.create(:expert, :reset_password_requested)

    visit password_reset_path(token: user.reset_password_token)

    within('.password-reset-form') do
      fill_in('New password', with: new_password)
      fill_in('Confirm password', with: new_password)

      click_button('Reset Password')
    end

    assert_current_path login_path
    assert_content 'Success! Your password has been reset. You may log in.'
  end

  test 'cannot view expired reset' do
    user = FactoryBot.create(:expert, :reset_password_expired)

    visit password_reset_path(token: user.reset_password_token)

    assert_current_path password_reset_expired_path
    assert_content 'We\'re sorry, but your password reset request has expired.'
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

class AuthenticationTest < ApplicationSystemTestCase
  test 'logged out users are sent to the welcome path to login' do
    visit dashboard_path

    assert_current_path welcome_path
  end

  test 'login from root path' do
    password = Faker::Internet.password

    user = FactoryBot.create(:expert, password:)

    visit root_path

    within('.login-form') do
      fill_in('email address', with: user.email)
      fill_in('password', with: password)

      click_button('Log In')
    end

    assert_current_path dashboard_path
  end

  test 'login from standalone login page' do
    password = Faker::Internet.password

    user = FactoryBot.create(:expert, password:)

    visit login_path

    within('.login-form') do
      fill_in('email address', with: user.email)
      fill_in('password', with: password)

      click_button('Log In')
    end

    assert_current_path dashboard_path
  end

  test 'cannot login with bad email' do
    visit root_path

    within('.login-form') do
      fill_in('email address', with: Faker::Internet.email)
      fill_in('password', with: Faker::Internet.password)

      click_button('Log In')
    end

    assert_current_path sessions_path

    within('.login-form') do
      assert_content I18n.t('activemodel.errors.models.session.cannot_authenticate')
    end
  end

  test 'cannot login with bad password' do
    user = FactoryBot.create(:expert, password: Faker::Internet.password)

    visit root_path

    within('.login-form') do
      fill_in('email address', with: user.email)
      fill_in('password', with: Faker::Internet.password)

      click_button('Log In')
    end

    assert_current_path sessions_path

    within('.login-form') do
      assert_content I18n.t('activemodel.errors.models.session.cannot_authenticate')
    end
  end

  test 'log out user' do
    user = FactoryBot.create(:expert)

    login_as(user)

    visit root_path

    within('#sidebar') do
      click_link(user.first_name)
      click_link('Log out')
    end

    assert_current_path root_path
    within('.alert') do
      assert_content 'You have been logged out'
    end
  end

  test 'logged in users are sent to the dashboard' do
    user = FactoryBot.create(:expert)

    login_as(user)

    visit login_path

    assert_current_path dashboard_path
  end

  test 'bad login from public scipis page' do
    visit scipis_path

    within('.login-form') do
      fill_in('email address', with: Faker::Internet.email)
      fill_in('password', with: Faker::Internet.password)

      click_button 'Log In'
    end

    assert_current_path sessions_path
    assert_content I18n.t('activemodel.errors.models.session.cannot_authenticate')
  end

  test 'redirects to previous path after login' do
    password = Faker::Internet.password
    admin = FactoryBot.create(:expert, password:)

    desired_path = edit_profile_path

    visit desired_path

    within('.login-form') do
      fill_in('email address', with: admin.email)
      fill_in('password', with: password)

      click_button 'Log In'
    end

    assert_current_path desired_path
  end

  test 'internal users are redirected to the admin dashboard after login' do
    password = Faker::Internet.password
    role = FactoryBot.create(:role, :internal)
    user = FactoryBot.create(:user, password:, roles: [role])

    visit root_path

    within('.login-form') do
      fill_in('email address', with: user.email)
      fill_in('password', with: password)

      click_button 'Log In'
    end

    assert_current_path admin_root_path
  end
end

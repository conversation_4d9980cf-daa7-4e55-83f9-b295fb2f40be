# frozen_string_literal: true

require 'application_system_test_case'

class NotificationsTest < ApplicationSystemTestCase
  test 'no notifications' do
    user = FactoryBot.create(:user)

    login_as user

    visit notifications_path

    assert_text 'You are up to date!'
    assert_text 'You have no unread notifications.'
  end

  test 'new notification as Ping author' do
    ping = FactoryBot.create(:ping)
    author = ping.author
    message = Faker::Lorem.sentence
    author.notifications.create!(source: ping, message:, notification_type: 'test-notification')

    login_as author

    visit notifications_path

    assert_text 'My Pings'
    assert_text "Ping ##{ping.id}: #{ping.title}"
    assert_link message, href: ping_path(ping)
  end

  test 'new notification as Ping subscriber' do
    ping = FactoryBot.create(:ping)
    subscriber = FactoryBot.create(:user)
    ping.subscribe(subscriber)
    message = Faker::Lorem.sentence
    subscriber.notifications.create!(source: ping, message:, notification_type: 'test-notification')

    login_as subscriber

    visit notifications_path

    assert_text 'Subscribed Pings'
    assert_text "Ping ##{ping.id}: #{ping.title}"
    assert_link message, href: ping_path(ping)
  end

  test 'unread and undelivered notifications are deliverable' do
    user = FactoryBot.create(:user)
    subscribable = FactoryBot.create(:ping)
    notification = user.notifications.create!(
      source: subscribable,
      message: Faker::Lorem.sentence,
      notification_type: 'test-notification'
    )

    notifications = Notification.deliverable

    assert_includes notifications, notification
  end

  test 'read notifications are not deliverable' do
    user = FactoryBot.create(:user)
    subscribable = FactoryBot.create(:ping)
    notification = user.notifications.create!(
      source: subscribable,
      message: Faker::Lorem.sentence,
      notification_type: 'test-notification'
    )
    notification.update!(read_at: Time.current)

    notifications = Notification.deliverable

    assert_not_includes notifications, notification
  end

  test 'delivered notifications are not deliverable' do
    user = FactoryBot.create(:user)
    subscribable = FactoryBot.create(:ping)
    notification = user.notifications.create!(
      source: subscribable,
      message: Faker::Lorem.sentence,
      notification_type: 'test-notification'
    )
    notification.update!(delivery_attempted_at: Time.current)

    notifications = Notification.deliverable

    assert_not_includes notifications, notification
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

module Notifications
  class SubscriptionsTest < ApplicationSystemTestCase
    test 'show subscribed Ping' do
      user = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping, :published)
      subscription = ping.subscribe(user)

      login_as user

      visit notifications_subscriptions_path

      within("#subscription_#{subscription.id}") do
        assert_text ping.title
        assert_text "You subscribed to this #{subscription.subscribable_type} on #{subscription.created_at.strftime('%B %-d, %Y')}"
        assert_button 'Unsubscribe'
      end
    end

    test 'show auto-subscribed, posted Ping' do
      user = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping, :published, author: user)
      subscription = user.subscriptions.find_by!(subscribable: ping)

      login_as user

      visit notifications_subscriptions_path

      within("#subscription_#{subscription.id}") do
        assert_text ping.title
        assert_text "You're subscribed because you posted this Ping on #{ping.created_at.strftime('%B %-d, %Y')}"
        assert_button 'Unsubscribe'
      end
    end

    test 'show auto-subscribed, answered Ping' do
      user = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping, :published)
      ping.add_answer(author: user, content: "<p>#{Faker::Lorem.paragraph}</p>")
      subscription = user.subscriptions.find_by!(subscribable: ping)

      login_as user

      visit notifications_subscriptions_path

      within("#subscription_#{subscription.id}") do
        assert_text ping.title
        assert_text "You're subscribed because you answered this Ping on #{ping.created_at.strftime('%B %-d, %Y')}"
        assert_button 'Unsubscribe'
      end
    end

    test 'no subscriptions' do
      user = FactoryBot.create(:expert)

      login_as user

      visit notifications_subscriptions_path

      assert_text 'You have no subscriptions'
      assert_text 'Subscriptions will appear here as you subscribe to notifications, or engage with other experts.'
    end

    test 'unsubscribe' do
      user = FactoryBot.create(:expert)
      ping = FactoryBot.create(:ping, :published)
      subscription = ping.subscribe(user)

      login_as user

      visit notifications_subscriptions_path

      within("#subscription_#{subscription.id}") do
        click_button 'Unsubscribe'
      end

      assert_no_selector "#subscription_#{subscription.id}"
      assert_text 'You have successfully unsubscribed.'
    end

    test 'unconfirmed email alert' do
      user = FactoryBot.create(:expert, :unconfirmed)

      login_as user

      visit notifications_subscriptions_path

      assert_text 'Your email address has not yet been confirmed'
      assert_text 'Your email address is not confirmed. You may still subscribe to notifications, but we cannot send ' \
                  'you notifications until you confirm your email address.'
      assert_text 'If you no longer can find your confirmation email, ' \
                  'you may request another one by clicking the button below.'
      assert_button 'Resend confirmation'
    end
  end
end

# frozen_string_literal: true

require 'application_system_test_case'

class EmailConfirmationsTest < ApplicationSystemTestCase
  test 'confirm email' do
    expert = FactoryBot.create(:expert)
    email = FactoryBot.create(:email, profile: expert.profile)
    confirmation = FactoryBot.create(:email_confirmation, email:)

    visit email_confirmation_path(token: confirmation.token)

    click_button('Confirm my email')

    assert_current_path root_path
    assert_text 'Thank you! Your email address has been confirmed.'
  end

  test 'already confirmed' do
    expert = FactoryBot.create(:expert)
    email = FactoryBot.create(:email, :confirmed, profile: expert.profile)
    confirmation = email.confirmations.first

    visit email_confirmation_path(token: confirmation.token)

    assert_text 'This email has already been confirmed.'
  end

  test 'expired token' do
    expert = FactoryBot.create(:expert)
    email = FactoryBot.create(:email, :unconfirmed, profile: expert.profile)
    confirmation = email.confirmations.first

    travel_to(8.days.from_now) do
      visit email_confirmation_path(token: confirmation.token)

      assert_text 'The confirmation link has expired. Please request a new one from your profile.'
    end
  end
end

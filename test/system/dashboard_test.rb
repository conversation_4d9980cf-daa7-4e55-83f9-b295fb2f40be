# frozen_string_literal: true

require 'application_system_test_case'

class DashboardTest < ApplicationSystemTestCase
  include ActionView::Helpers::SanitizeHelper

  test 'expert login redirects to dashboard_path' do
    expert = FactoryBot.create(:expert, :unconfirmed)

    login_as(expert)

    assert_current_path dashboard_path
  end

  test 'unconfirmed users see an alert' do
    expert = FactoryBot.create(:expert, :unconfirmed)

    login_as(expert)

    within('.card-notifications') do
      assert_content strip_tags(I18n.t('dashboard.card_notifications.unconfirmed_email_html'))
    end
  end

  test 'expert with late draft submission sees alert' do
    scipi = FactoryBot.create(:scipi, :general_participation, :results_published, closes_at: 2.days.ago)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :draft, question_group: scipi, user: expert)

    login_as(expert)

    within('.card-notifications') do
      assert_content "Your submission for SciPi ##{scipi.id} is late."
    end
  end

  test 'expert sees announcement and can click for more' do
    FactoryBot.create_list(:announcement, 3, :promotable)
    announcements = Announcement.published.order(published_at: :desc)

    expert = FactoryBot.create(:expert)

    login_as(expert)

    within('#announcements_card') do
      assert_content announcements.first.title

      click_link 'View All'
    end

    within('#announcements_offcanvas') do
      announcements.each do |announcement|
        assert_content announcement.title
      end
    end
  end

  test "expert with scipis sees 'My SciPis' on Dashboard" do
    scipi = FactoryBot.create(:scipi, :invite_only, :published, branding: Surveys::Branding.scipi)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:invite, :approved, survey: scipi, user: expert)

    login_as(expert)

    within('#scipis_table_card') do
      assert_content scipi.name
    end
  end
end

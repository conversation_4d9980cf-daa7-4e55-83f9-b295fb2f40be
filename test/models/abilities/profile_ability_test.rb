# frozen_string_literal: true

require 'test_helper'

module Abilities
  class ProfileAbilityTest < ActiveSupport::TestCase
    test 'admins can delete anyone\'s degree' do
      admin = FactoryBot.create(:admin)
      user = FactoryBot.create(:expert)
      degree = FactoryBot.create(:degree, expert: user)

      ability = ProfileAbility.new(admin)

      assert ability.can?(:destroy, degree),
             'admins should be able to delete any degree'
    end

    test 'cannot delete another user\'s degree' do
      user = FactoryBot.create(:expert)

      another_user = FactoryBot.create(:expert)
      degree = FactoryBot.create(:degree, expert: another_user)

      ability = ProfileAbility.new(user)

      assert ability.cannot?(:destroy, degree),
             'should not be able to delete another user\'s degree'
    end

    test 'can delete own degree' do
      user = FactoryBot.create(:expert)
      degree = FactoryBot.create(:degree, expert: user)

      ability = ProfileAbility.new(user)

      assert ability.can?(:destroy, degree),
             'should be able to delete own degree'
    end

    test 'admins can delete anyone\'s certification' do
      admin = FactoryBot.create(:admin)
      user = FactoryBot.create(:expert)
      certification = FactoryBot.create(:certification, expert: user)

      ability = ProfileAbility.new(admin)

      assert ability.can?(:destroy, certification),
             'admins should be able to delete any certification'
    end

    test 'cannot delete another user\'s certification' do
      user = FactoryBot.create(:expert)

      another_user = FactoryBot.create(:expert)
      certification = FactoryBot.create(:certification, expert: another_user)

      ability = ProfileAbility.new(user)

      assert ability.cannot?(:destroy, certification),
             'should not be able to delete another user\'s certification'
    end

    test 'can delete own certification' do
      user = FactoryBot.create(:expert)
      certification = FactoryBot.create(:certification, expert: user)

      ability = ProfileAbility.new(user)

      assert ability.can?(:destroy, certification),
             'should be able to delete own certification'
    end

    test 'experts can :edit their own profile' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit, profile), 'should be able to :edit their own profile'
    end

    test 'experts can :edit their another profile' do
      user = FactoryBot.create(:expert)
      other_user = FactoryBot.create(:expert)

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit, other_user.profile), 'should be able to :edit their own profile'
    end

    test 'observers can :edit their own profile' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit, profile), 'should be able to :edit their own profile'
    end

    test 'observers can :edit their another profile' do
      user = FactoryBot.create(:observer)
      other_user = FactoryBot.create(:observer)

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit, other_user.profile), 'should be able to :edit their own profile'
    end

    test 'experts can :edit_display_name' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_display_name, profile), 'should be able to :edit_display_name'
    end

    test 'observers cannot :edit_display_name' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit_display_name, profile), 'should not be able to :edit_display_name'
    end

    test 'experts can :edit_expertise' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_expertise, profile), 'should be able to :edit_expertise'
    end

    test 'observers cannot :edit_expertise' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit_expertise, profile), 'should not be able to :edit_expertise'
    end

    test 'experts can :edit_education' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_education, profile), 'should be able to :edit_education'
    end

    test 'observers cannot :edit_education' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit_education, profile), 'should not be able to :edit_education'
    end

    test 'experts can :edit_employment_history' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_employment_history, profile), 'should be able to :edit_employment_history'
    end

    test 'observers cannot :edit_employment_history' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit_employment_history, profile), 'should not be able to :edit_employment_history'
    end

    test 'observers can :edit_interests' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_interests, profile), 'should be able to :edit_interests'
    end

    test 'experts cannot :edit_interests' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert_not ability.can?(:edit_interests, profile), 'should not be able to :edit_interests'
    end

    test 'observers can :edit_linkedin_profile' do
      user = FactoryBot.create(:observer)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_linkedin_profile, profile), 'should be able to :edit_linkedin_profile'
    end

    test 'experts can :edit_third_party_profiles' do
      user = FactoryBot.create(:expert)
      profile = user.profile

      ability = ProfileAbility.new(user)

      assert ability.can?(:edit_third_party_profiles, profile), 'should be able to :edit_third_party_profiles'
    end
  end
end

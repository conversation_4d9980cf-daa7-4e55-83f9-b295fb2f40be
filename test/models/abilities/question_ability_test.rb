# frozen_string_literal: true

require 'test_helper'

module Abilities
  class QuestionAbilityTest < ActiveSupport::TestCase
    test 'survey participants can answer questions' do
      user = create(:expert)
      survey = create(:survey, :general_participation, :published)
      question = create(:question, survey:)
      ability = QuestionAbility.new(user)

      assert ability.can?(:answer, question), 'survey participants should be able to answer on questions'
    end

    test 'users that cannot participate cannot answer questions' do
      user = create(:expert)
      survey = create(:survey, :published, :invite_only)
      question = create(:question, survey:)
      ability = QuestionAbility.new(user)

      assert ability.cannot?(:answer, question), 'survey non-participants should not be able to answer questions'
    end
  end
end

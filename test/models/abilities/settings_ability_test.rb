# frozen_string_literal: true

require 'test_helper'

module Abilities
  class SettingsAbilityTest < ActiveSupport::TestCase
    test 'nil users cannot :edit_settings' do
      user = nil

      ability = Abilities::SettingsAbility.new(user)

      assert ability.cannot?(:edit_settings, user), 'nil users can edit settings'
    end

    test 'guest users cannot :edit_settings' do
      guest = FactoryBot.create(:guest)

      ability = Abilities::SettingsAbility.new(guest)

      assert ability.cannot?(:edit_settings, guest), 'guests cannot edit settings'
    end

    test 'registered users can :edit_settings on own account' do
      user = FactoryBot.create(:user)

      ability = Abilities::SettingsAbility.new(user)

      assert ability.can?(:edit_settings, user), 'users can edit own settings'
    end

    test 'registered users cannot :edit_settings on other accounts' do
      user = FactoryBot.create(:user)
      other_user = FactoryBot.create(:user)

      ability = Abilities::SettingsAbility.new(user)

      assert ability.cannot?(:edit_settings, other_user), 'expert can edit other user\'s settings'
    end
  end
end

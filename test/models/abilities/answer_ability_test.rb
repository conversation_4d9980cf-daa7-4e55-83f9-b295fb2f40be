# frozen_string_literal: true

require 'test_helper'

module Abilities
  class AnswerAbilityTest < ActiveSupport::TestCase
    include ::AbilityTestHelper

    test 'non-survey participant cannot download attachment' do
      scipi = create(:survey, :invite_only, :published, :results_published)
      question = create(:question, survey: scipi)
      answer_group1 = create(:submission, survey: scipi)
      answer = create(:answer, answer_group: answer_group1, question:)

      @user = users(:expert)

      assert_cannot :download_attachment, answer, message: 'non-participants should not be able to download attachments'
    end

    test 'same-survey participant can download attachment' do
      survey = create(:survey, :published, :results_published)
      user = users(:expert)
      create(:invite, :approved, user:, survey:)
      question = create(:question, survey:)
      answer_group1 = create(:submission, survey:)
      answer = create(:answer, answer_group: answer_group1, question:)

      @user = users(:expert)
      create(:invite, :approved, user: @user, survey:)

      assert_can :download_attachment, answer, message: 'participants should be able to download published attachments'
    end

    test 'answer submitter can download attachment' do
      survey = create(:survey, :published, :results_published)
      question = create(:question, survey:)
      answer_group1 = create(:submission, survey:)
      answer = create(:answer, answer_group: answer_group1, question:)
      @user = answer.submitter
      create(:invite, :approved, user: @user, survey:)

      assert_can :download_attachment, answer, message: 'users should be able to download their published attachment'
    end

    test 'answer submitter can download attachment with unpublished results' do
      survey = create(:survey, :published)
      question = create(:question, survey:)
      answer_group1 = create(:submission, survey:)
      answer = create(:answer, answer_group: answer_group1, question:)
      @user = answer.submitter
      create(:invite, :approved, user: @user, survey:)

      assert_can :download_attachment, answer, message: 'users should be able to download their unpublished attachment'
    end

    test 'logged out user can view public, published files' do
      survey = create(:survey, :general_participation, :published, :results_published)
      question = create(:question, survey:)
      answer_group1 = create(:submission, survey:)
      answer = create(:answer, answer_group: answer_group1, question:)

      @user = nil

      assert_can :download_attachment,
                 answer,
                 message: 'logged out users should be able to download published attachments'
    end

    private

    def ability
      @ability ||= AnswerAbility.new(@user)
    end
  end
end

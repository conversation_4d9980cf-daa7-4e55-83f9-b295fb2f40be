# frozen_string_literal: true

require 'test_helper'

class GridResultsTest < ActiveSupport::TestCase
  setup do
    setup_grid_question_test_data
  end

  test 'initialization - radio question' do
    grid_radio_results = GridRadioResults.new(question: @grid_radio_question)

    assert_equal %w[Yes No], grid_radio_results.row_headers
    assert_equal %w[Up Down], grid_radio_results.column_headers
  end

  test 'initialization - checkbox question' do
    grid_checkbox_results = GridRadioResults.new(question: @grid_checkbox_question)

    assert_equal %w[Chocolate Vanilla Strawberry], grid_checkbox_results.row_headers
    assert_equal %w[Delicious Pricey], grid_checkbox_results.column_headers
  end

  test 'initialization - select question' do
    grid_select_results = GridSelectResults.new(question: @grid_select_question)

    assert_equal %w[Apple Google Tesla], grid_select_results.row_headers
    assert_equal ['Prestige (n)', 'Pay (n)', 'Convenience (n)'], grid_select_results.column_headers
  end

  test 'empty results - radio question' do
    grid_radio_results = GridRadioResults.new(question: @grid_radio_question)

    expected_empty_result = "<table class='table table-bordered'><thead><tr><th>First Cell</th><th>Up</th><th>Down</th><th><span class='total'>Total</span></th></tr></thead><tr><td class='grid-row-header'>Yes</td><td></td><td></td><td><span class='total'>0</span></td></tr><tr><td class='grid-row-header'>No</td><td></td><td></td><td><span class='total'>0</span></td></tr></table>"
    assert_equal expected_empty_result, grid_radio_results.results_html
  end

  test 'empty results - checkbox question' do
    grid_checkbox_results = GridRadioResults.new(question: @grid_checkbox_question)

    expected_empty_result = "<table class='table table-bordered'><thead><tr><th>Top Cell</th><th>Delicious</th><th>Pricey</th><th><span class='total'>Total</span></th></tr></thead><tr><td class='grid-row-header'>Chocolate</td><td></td><td></td><td><span class='total'>0</span></td></tr><tr><td class='grid-row-header'>Vanilla</td><td></td><td></td><td><span class='total'>0</span></td></tr><tr><td class='grid-row-header'>Strawberry</td><td></td><td></td><td><span class='total'>0</span></td></tr></table>"
    assert_equal expected_empty_result, grid_checkbox_results.results_html
  end

  test 'empty results - select question' do
    grid_select_results = GridRadioResults.new(question: @grid_select_question)

    expected_empty_result = "<table class='table table-bordered'><thead><tr><th>Top Left Cell</th><th>Prestige</th><th>Pay</th><th>Convenience</th><th><span class='total'>Total</span></th></tr></thead><tr><td class='grid-row-header'>Apple</td><td></td><td></td><td></td><td><span class='total'>0</span></td></tr><tr><td class='grid-row-header'>Google</td><td></td><td></td><td></td><td><span class='total'>0</span></td></tr><tr><td class='grid-row-header'>Tesla</td><td></td><td></td><td></td><td><span class='total'>0</span></td></tr></table>"
    assert_equal expected_empty_result, grid_select_results.results_html
  end

  test 'results with single answer - radio question' do
    response = create(:submission, question_group: @survey)
    create(:answer, answer_group: response, question: @grid_radio_question, answer_text_on_1st_row: '0-1', answer_text_on_2nd_row: '1-1')
    grid_radio_results = GridRadioResults.new(question: @grid_radio_question)

    expected_result = "<table class='table table-bordered'><thead><tr><th>First Cell</th><th>Up</th><th>Down</th><th><span class='total'>Total</span></th></tr></thead><tr><td class='grid-row-header'>Yes</td><td><span class='percentage'>0.00%</span><span class='count'>0</span></td><td><span class='percentage'>100.00%</span><span class='count'>1</span></td><td><span class='total'>1</span></td></tr><tr><td class='grid-row-header'>No</td><td><span class='percentage'>0.00%</span><span class='count'>0</span></td><td><span class='percentage'>100.00%</span><span class='count'>1</span></td><td><span class='total'>1</span></td></tr></table>"

    assert_equal expected_result, grid_radio_results.results_html
  end

  test 'results with single answer - checkbox question' do
    response = create(:submission, question_group: @survey)
    create(:answer, answer_group: response, question: @grid_checkbox_question, answer_text_on_1st_row: "1\r\n0", answer_text_on_2nd_row: "0\r\n1", answer_text_on_3rd_row: "0\r\n1")
    grid_checkbox_results = GridCheckboxResults.new(question: @grid_checkbox_question)

    expected_result = "<table class='table table-bordered'><thead><tr><th>Top Cell</th><th>Delicious</th><th>Pricey</th><th><span class='total'>Total</span></th></tr></thead><tr><td class='grid-row-header'>Chocolate</td><td><span class='percentage'>100.00%</span><span class='count'>1</span></td><td><span class='percentage'>0.00%</span><span class='count'>0</span></td><td><span class='total'>1</span></td></tr><tr><td class='grid-row-header'>Vanilla</td><td><span class='percentage'>0.00%</span><span class='count'>0</span></td><td><span class='percentage'>100.00%</span><span class='count'>1</span></td><td><span class='total'>1</span></td></tr><tr><td class='grid-row-header'>Strawberry</td><td><span class='percentage'>0.00%</span><span class='count'>0</span></td><td><span class='percentage'>100.00%</span><span class='count'>1</span></td><td><span class='total'>1</span></td></tr></table>"

    assert_equal expected_result, grid_checkbox_results.results_html
  end

  test 'grid:checkbox frequency data (with multiple answers)' do
    grid_checkbox_question = @grid_checkbox_question
    response1 = create(:submission, question_group: @survey)
    response2 = create(:submission, question_group: @survey)
    create(:answer, answer_group: response1, question: grid_checkbox_question, answer_text_on_1st_row: "1\r\n1", answer_text_on_2nd_row: "0\r\n1", answer_text_on_3rd_row: "0\r\n1")
    create(:answer, answer_group: response2, question: grid_checkbox_question, answer_text_on_1st_row: "1\r\n0", answer_text_on_2nd_row: "1\r\n0", answer_text_on_3rd_row: "0\r\n1")
    grid_checkbox_results = GridCheckboxResults.new(question: grid_checkbox_question)

    expected_result = [[2, 1], [1, 1], [0, 2]]

    assert_equal expected_result, grid_checkbox_results.frequency_data
  end

  test 'grid:radio frequency data (with multiple answers)' do
    grid_radio_question = @grid_radio_question
    response1 = create(:submission, question_group: @survey)
    response2 = create(:submission, question_group: @survey)
    create(:answer, answer_group: response1, question: grid_radio_question, answer_text_on_1st_row: '0-1', answer_text_on_2nd_row: '1-0')
    create(:answer, answer_group: response2, question: grid_radio_question, answer_text_on_1st_row: '0-1', answer_text_on_2nd_row: '0-1')
    grid_radio_results = GridRadioResults.new(question: grid_radio_question)

    expected_result = [[0, 2], [1, 1]]

    assert_equal expected_result, grid_radio_results.frequency_data
  end

  test 'grid:select frequency data (with multiple answers)' do
    grid_select_question = create(:grid_question, question_group: @survey, question_text: 'Select question text')
    create(:grid_structure, input_type: 'Select', question: grid_select_question, row_headers: %w[Apple Google Tesla], column_headers: %w[Prestige Pay Convenience], first_cell_text: 'Top Left Cell')

    value1 = grid_select_question.answer_choices.first.label
    value2 = grid_select_question.answer_choices.second.label

    # random answer combos
    answer_text_set1 = "#{value1}\r\n#{value2}\r\n#{value1}"
    answer_text_set2 = "#{value2}\r\n#{value2}\r\n#{value1}"

    response1 = create(:submission, question_group: @survey)
    response2 = create(:submission, question_group: @survey)

    # random application of answer combos
    create(:answer, answer_group: response1, question: grid_select_question, answer_text_on_1st_row: answer_text_set1, answer_text_on_2nd_row: answer_text_set2, answer_text_on_3rd_row: answer_text_set1)
    create(:answer, answer_group: response2, question: grid_select_question, answer_text_on_1st_row: answer_text_set2, answer_text_on_2nd_row: answer_text_set1, answer_text_on_3rd_row: answer_text_set2)

    grid_select_results = GridSelectResults.new(question: grid_select_question)

    expected_result = [[[1, 1], [0, 2], [2, 0]], [[1, 1], [0, 2], [2, 0]], [[1, 1], [0, 2], [2, 0]]]

    assert_equal expected_result, grid_select_results.frequency_data
  end

  # rubocop:disable Layout/LineLength
  test 'results with single answer - select question' do
    response = create(:submission, question_group: @survey)
    create(:answer, answer_group: response, question: @grid_select_question, answer_text_on_1st_row: 'label5', answer_text_on_2nd_row: 'label5', answer_text_on_3rd_row: 'label6')
    grid_select_results = GridSelectResults.new(question: @grid_select_question)
    expected_result = "<table class='table table-bordered'><thead><tr><th>Top Left Cell</th><th>Prestige (n)</th><th>Pay (n)</th><th>Convenience (n)</th><th><span class='total'>Total</span></th></tr></thead><tr><td class='grid-row-header'>Apple</td><td><div class='rating text-start'>label5 (1)</div><div class='rating text-start text-gray-light'>label6</div></td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start text-gray-light'>label6</div></td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start text-gray-light'>label6</div></td><td><span class='total'>1</span></td></tr><tr><td class='grid-row-header'>Google</td><td><div class='rating text-start'>label5 (1)</div><div class='rating text-start text-gray-light'>label6</div></td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start text-gray-light'>label6</div></td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start text-gray-light'>label6</div></td><td><span class='total'>1</span></td></tr><tr><td class='grid-row-header'>Tesla</td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start'>label6 (1)</div></td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start text-gray-light'>label6</div></td><td><div class='rating text-start text-gray-light'>label5</div><div class='rating text-start text-gray-light'>label6</div></td><td><span class='total'>1</span></td></tr></table>"

    assert_equal expected_result, grid_select_results.results_html
  end
  # rubocop:enable Layout/LineLength

  test 'empty answer text array - radio question' do
    values = []
    grid_radio_results = GridRadioResults.new(question: @grid_radio_question, values:)
    assert_equal [], grid_radio_results.grid_answer_text_array
  end

  test 'answer text array - radio question' do
    map = {
      [] => [],
      [nil, nil] => [nil, nil],
      %w[0-1 1-0] => %w[Down Up],
      %w[0-0 1-1] => %w[Up Down],
      %w[0-0 1-0] => %w[Up Up],
      %w[0-1 1-1] => %w[Down Down]
    }

    map.each do |key, value|
      assert_equal value, GridRadioResults.new(
        question: @grid_radio_question,
        values: key
      ).grid_answer_text_array
    end
  end

  test 'answer text array - checkbox question' do
    map = {
      [] => [],
      [nil, nil, nil] => [nil, nil, nil],
      ["0\r\n0", "0\r\n0", "0\r\n0"] => ['', '', ''],
      ["1\r\n0", "1\r\n1", "0\r\n1"] => ['Delicious', 'Delicious,Pricey', 'Pricey'],
      ["0\r\n1", "0\r\n0", "1\r\n1"] => ['Pricey', '', 'Delicious,Pricey'],
      ["1\r\n1", "1\r\n0", "0\r\n1"] => ['Delicious,Pricey', 'Delicious', 'Pricey']
    }

    map.each do |key, value|
      assert_equal value, GridCheckboxResults.new(
        question: @grid_checkbox_question,
        values: key
      ).grid_answer_text_array
    end
  end

  test 'answer text array - select question' do
    map = {
      [] => [],
      [nil, nil, nil] => [[nil, nil, nil], [nil, nil, nil], [nil, nil, nil]],
      ["5\r\n4\r\n", "\r\n2\r\n5", "1\r\n3\r\n4"] => [['5', '4', ''], ['', '2', '5'], %w[1 3 4]],
      ["2\r\n4\r\n5", "2\r\n4\r\n1", "\r\n4\r\n2"] => [%w[2 4 5], %w[2 4 1], ['', '4', '2']]
    }

    map.each do |key, value|
      assert_equal value, GridSelectResults.new(
        question: @grid_select_question,
        values: key
      ).grid_answer_text_array
    end
  end
end

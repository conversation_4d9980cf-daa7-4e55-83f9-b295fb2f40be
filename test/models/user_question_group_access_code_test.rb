# frozen_string_literal: true

require 'test_helper'

class UserQuestionGroupAccessCodeTest < ActiveSupport::TestCase
  setup do
    @user1 = create(:expert)
    @user2 = create(:expert, first_name: 'Two')
    admin = create(:admin)
    @question_group = create(:question_group, created_by_id: admin.id)
    @access_code1 = create(:user_question_group_access_code,
                           user: @user1,
                           question_group: @question_group)
    @access_code2 = create(:user_question_group_access_code,
                           user: @user1,
                           question_group: @question_group,
                           revoked_at: Time.zone.now)
    @access_code3 = create(:user_question_group_access_code,
                           user: @user2,
                           question_group: @question_group)
  end

  test 'active access codes' do
    assert_same_elements [@access_code1, @access_code3], UserQuestionGroupAccessCode.active
  end

  test 'question_group_access_for_user' do
    assert_same_elements [@access_code1],
                         UserQuestionGroupAccessCode.question_group_access_for_user(@question_group, @user1)
  end
end

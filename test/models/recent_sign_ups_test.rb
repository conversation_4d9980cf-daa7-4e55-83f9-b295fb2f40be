# frozen_string_literal: true

require 'test_helper'

class RecentSignupsTest < ActiveSupport::TestCase
  test 'defaults to today' do
    skip 'Get back to this'
    signups = RecentSignUps.new

    assert signups.selected_timeframe.key == :today
  end

  test "today's sign ups" do
    role = FactoryBot.create(:role, :available_for_signup)
    created_today = FactoryBot.create(:user, created_at: Time.current, roles: [role])
    not_created_today = FactoryBot.create(:user, created_at: 1.day.ago, roles: [role])

    signups = RecentSignUps.new(timeframe: :today)
    users = signups.users

    assert_includes users, created_today
    assert_not_includes users, not_created_today
  end

  test "last 48 hour's sign ups" do
    role = FactoryBot.create(:role, :available_for_signup)
    created_today = FactoryBot.create(:user, created_at: Time.current, roles: [role])
    created_yesterday = FactoryBot.create(:user, created_at: 40.hours.ago, roles: [role])
    more_than_48_hours_ago = FactoryBot.create(:user, created_at: 49.hours.ago, roles: [role])

    signups = RecentSignUps.new(timeframe: :last_48_hours)
    users = signups.users

    assert_includes users, created_today
    assert_includes users, created_yesterday
    assert_not_includes users, more_than_48_hours_ago
  end

  test "past week's sign ups" do
    role = FactoryBot.create(:role, :available_for_signup)
    created_today = FactoryBot.create(:user, created_at: Time.current, roles: [role])
    created_this_week = FactoryBot.create(:user, created_at: 6.days.ago, roles: [role])
    more_than_a_week_ago = FactoryBot.create(:user, created_at: 8.days.ago, roles: [role])

    signups = RecentSignUps.new(timeframe: :week)
    users = signups.users

    assert_includes users, created_today
    assert_includes users, created_this_week
    assert_not_includes users, more_than_a_week_ago
  end
end

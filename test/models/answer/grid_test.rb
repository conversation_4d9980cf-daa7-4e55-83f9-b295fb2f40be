# frozen_string_literal: true

require 'test_helper'

class Answer::GridTest < ActiveSupport::TestCase
  test 'grid radio answer skipped' do
    answer = create(:answer, question: create(:grid_question, :radio))

    assert answer.skipped?, 'should be skipped'
  end

  test 'grid select answer skipped' do
    question = create(:grid_question, :select)
    answer = create(:answer, question:,
                             answer_text_on_1st_row: "1\r\n1\r\n1",
                             answer_text_on_2nd_row: "1\r\n1\r\n1",
                             answer_text_on_3rd_row: "1\r\n1\r\n")

    assert answer.skipped?, 'should be skipped (all cells must have data to be complete)'
  end

  # Not correct in storing legit values, but does test the skipped method correctly
  test 'grid select complete (data in each cell)' do
    question = create(:grid_question, :select)
    answer = create(:answer,
                    question:,
                    answer_text_on_1st_row: "1\r\n1\r\n1",
                    answer_text_on_2nd_row: "1\r\n1\r\n1",
                    answer_text_on_3rd_row: "1\r\n1\r\n1")

    assert_not answer.skipped?, 'should not be skipped'
  end

  # Any data counts as enough data
  test 'grid radio answer not skipped' do
    answer = create(:answer,
                    question: create(:grid_question, :radio),
                    answer_text_on_1st_row: '0-1')

    assert_not answer.skipped?, 'should be skipped'
  end

  test 'set value at for checkbox grid question' do
    question = create(:grid_question)
    question.create_grid_structure(
      input_type: 'Checkbox',
      first_cell_text: 'foo',
      row_headers: %w[aaa bbb ccc],
      column_headers: %w[xxx yyy xxx]
    )
    answer = create(:answer,
                    question:,
                    answer_text_on_1st_row: "1\r\n0\r\n0",
                    answer_text_on_2nd_row: "0\r\n1\r\n0",
                    answer_text_on_3rd_row: "0\r\n0\r\n0")

    answer.set_value_at!(0, 1, 1)

    assert_equal answer.answer_text_on_1st_row, "1\r\n1\r\n0"
  end

  test 'row_at for checkbox' do
    question = create(:grid_question, :checkbox)
    answer = create(:grid_answer,
                    question:,
                    answer_text_on_1st_row: "0\r\n1\r\n1",
                    answer_text_on_2nd_row: "1\r\n0\r\n1")

    assert_equal %w[1 0 1], answer.row_at(1)
  end

  test 'row_at for select' do
    question = create(:grid_question, :select)
    # There are two answer choices in the factory
    choice1, choice2 = *question.answer_choices
    answer = create(:grid_answer, question:, answer_text_on_1st_row: "#{choice1.label}\r\n#{choice2.label}\r\n#{choice1.label}")

    assert_equal [choice1.label, choice2.label, choice1.label], answer.row_at(0)
  end

  test 'row_at with training blanks' do
    question = create(:grid_question)
    create(:grid_structure,
           :checkbox,
           row_headers: %w[one two three],
           column_headers: %w[four five six],
           question:)
    answer = create(:grid_answer,
                    question:,
                    answer_text_on_1st_row: "1\r\n2\r\n3",
                    answer_text_on_2nd_row: "4\r\n5\r\n")
    assert_equal ['4', '5', nil], answer.row_at(1)
  end

  test 'row_at with nil row' do
    question = create(:grid_question)
    create(:grid_structure,
           :checkbox,
           row_headers: %w[one two three],
           column_headers: %w[four five six],
           question:)
    answer = create(:grid_answer,
                    question:,
                    answer_text_on_1st_row: "1\r\n2\r\n3",
                    answer_text_on_2nd_row: "4\r\n5\r\n6",
                    answer_text_on_3rd_row: nil)
    assert_equal [nil, nil, nil], answer.row_at(2)
  end

  test 'scitrust score' do
    grid_structure = create(:grid_structure, :select)
    question = create(:grid_question, grid_structure:)
    (0..10).times { |i| question.answer_choices.create!(label: i, value: i.to_f, position: i + 1) }

    create(:answer, question:)
  end
end

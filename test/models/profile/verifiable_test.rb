# frozen_string_literal: true

require 'test_helper'

class Profile::VerifiableTest < ActiveSupport::TestCase
  test 'default verification status is verified' do
    user = FactoryBot.create(:user)

    assert user.profile.verified?, 'should be verified by default'
  end

  test 'verify expert' do
    admin = FactoryBot.create(:admin)
    unverified = verification_statuses(:pending)
    profile = FactoryBot.create(:profile, verification_status: unverified)
    original_timestamp = profile.verification_status_updated_at

    profile.verify!(admin)

    assert profile.verified?
    assert profile.verification_status_updated_at > original_timestamp
    assert_equal admin, profile.verified_by
  end

  test 'cannot verify expert' do
    admin = FactoryBot.create(:admin)
    unverified = verification_statuses(:pending)
    profile = FactoryBot.create(:profile, verification_status: unverified)
    original_timestamp = profile.verification_status_updated_at

    profile.cannot_verify!(admin)

    assert profile.cannot_verify?
    assert profile.verification_status_updated_at > original_timestamp
    assert_equal admin, profile.verified_by
  end
end

# frozen_string_literal: true

require 'test_helper'

class MessageTest < ActiveSupport::TestCase
  include ActionMailer::TestHelper

  test 'send from default address' do
    address = '<EMAIL>'

    message = Message.new(sent_from_address: '<EMAIL>')

    assert_equal address, message.from_email, 'from address should be the default'
  end

  test 'sent by admin' do
    expert = FactoryBot.create(:expert)
    message = Message.new(user: expert, sent_by: FactoryBot.create(:admin))

    assert message.sent_by_admin?, 'should be sent by admin'
  end

  test 'not sent by admin' do
    expert = FactoryBot.create(:expert)
    message = Message.new(user: expert, sent_by: expert)

    assert_not message.sent_by_admin?, 'should be sent by admin'
  end

  test 'non-SciPi message do not send an email' do
    admin = FactoryBot.create(:admin)
    expert = FactoryBot.create(:expert)

    assert_no_enqueued_emails do
      Message.create!(user: expert, sent_by: admin, subject: 'foo', content: 'bar')
    end
  end

  test 'total message count with no replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')

    assert_equal 1, message.total_message_count
  end

  test 'total message count with replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    parent_message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')

    Message.create!(survey: scipi, user: expert, sent_by: admin, content: 'reply 1', parent: parent_message)
    Message.create!(survey: scipi, user: expert, sent_by: expert, content: 'reply 2', parent: parent_message)

    assert_equal 3, parent_message.total_message_count
  end

  test 'last active at with no replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')

    assert_equal message.created_at, message.last_active_at
  end

  test 'last active at with replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    parent_message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')

    reply = travel_to 1.day.from_now do
      parent_message.replies.create!(survey: scipi, user: expert, sent_by: admin, content: 'reply')
    end

    assert_equal reply.created_at, parent_message.last_active_at
    assert reply.created_at > parent_message.created_at
  end

  test 'new panelist messages send a new-message notification to admins' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')

    assert_enqueued_email_with(Scipis::AdminsMailer, :new_message_notification, args: [{ scipi:, message:, admin: scipi.notifiable_owners.first }])
  end

  test 'panelist reply messages send a new-reply notification to admins' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    parent_message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')
    reply_message = Message.create!(survey: scipi, user: expert, sent_by: expert, content: 'reply', parent: parent_message)

    assert_enqueued_email_with(Scipis::AdminsMailer, :new_reply_notification, args: [{ scipi:, message: reply_message, admin: scipi.notifiable_owners.first }])
  end

  test 'new admin messages send a new-message notification to panelist' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: admin, subject: 'foo', content: 'bar')

    assert_enqueued_email_with(Scipis::ApplicantsMailer, :new_message_notification, args: [{ scipi:, applicant: expert, message: }])
  end

  test 'admin reply messages send a new-reply notification to panelist' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    parent_message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')
    reply_message = Message.create!(survey: scipi, user: expert, sent_by: admin, content: 'reply', parent: parent_message)

    assert_enqueued_email_with(Scipis::ApplicantsMailer, :new_reply_notification, args: [{ scipi:, applicant: expert, message: reply_message }])
  end

  test 'unread admin replies count with no last_read_by_user_at' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')
    message.replies.create!(survey: scipi, user: expert, sent_by: admin, content: 'admin reply')

    assert_equal 0, message.unread_admin_replies_count
  end

  test 'unread admin replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    message = Message.create!(
      survey: scipi,
      user: expert,
      sent_by: expert,
      subject: 'foo',
      content: 'bar',
      last_read_by_user_at: 1.hour.ago
    )
    message.replies.create!(survey: scipi, user: expert, sent_by: admin, content: 'admin reply 1')
    message.replies.create!(survey: scipi, user: expert, sent_by: expert, content: 'expert reply')
    message.replies.create!(survey: scipi, user: expert, sent_by: admin, content: 'admin reply 2')

    assert_equal 2, message.unread_admin_replies_count
  end

  test 'no unread admin replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')
    reply = message.replies.create!(survey: scipi, user: expert, sent_by: admin, content: 'admin reply 1')
    message.update!(last_read_by_user_at: reply.created_at + 1.hour)

    assert_equal 0, message.unread_admin_replies_count
  end

  test 'unread_admin_replies_count ignores expert replies' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    message = Message.create!(survey: scipi, user: expert, sent_by: expert, subject: 'foo', content: 'bar')
    reply = message.replies.create!(survey: scipi, user: expert, sent_by: expert, content: 'expert reply')
    reply.update!(created_at: 1.hour.ago)
    message.update!(last_read_by_user_at: reply.created_at - 1.hour)

    assert_equal 0, message.unread_admin_replies_count
  end
end

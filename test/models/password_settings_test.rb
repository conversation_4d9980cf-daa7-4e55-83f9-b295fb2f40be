# frozen_string_literal: true

require 'test_helper'

class PasswordSettingsTest < ActiveSupport::TestCase
  include ActionMailer::TestHelper

  test 'update password settings' do
    current_password = TestData.password
    new_password = TestData.password
    user = FactoryBot.create(:expert, password: current_password)
    password_settings = PasswordSettings.find(user.id)

    assert password_settings.update(password: new_password, current_password:)
    assert_enqueued_email_with SettingsMailer, :notify_password_change, args: [user]
    assert user.reload.authenticate(new_password), 'should authenticate with new password'
  end
end

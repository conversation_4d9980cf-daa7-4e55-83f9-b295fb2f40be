# frozen_string_literal: true

require 'test_helper'

class SectionTest < ActiveSupport::TestCase
  test 'clone section to different survey' do
    source_survey = FactoryBot.create(:survey)
    source_section = source_survey.question_sections.create!(name: 'Copy me!', position: 1)
    source_survey.questions.create!(position: 1, section: source_section, question_text: 'Question 1')
    source_survey.questions.create!(position: 2, section: source_section, question_text: 'Question 2')
    source_survey.questions.create!(position: 3, section: source_section, question_text: 'Question 3')

    target_survey = FactoryBot.create(:survey, name: 'Destination')
    # Target survey (currently) needs have existing sections to accept new ones
    existing_target_section = target_survey.question_sections.create!(name: 'Existing section', position: 1)
    target_survey.questions.create!(position: 1, section: existing_target_section, question_text: 'Existing question')

    assert_difference(-> { target_survey.question_sections.count }) do
      assert_difference(-> { target_survey.questions.count }, source_section.questions.count) do
        source_section.copy_to(target_survey)
      end
    end

    target_section = target_survey.question_sections.find_by!(position: 2)
    (1..3).each do |question_pos|
      source_question = source_section.questions.find_by!(position: question_pos)
      target_question = target_section.questions.find_by!(position: question_pos)

      assert_equal source_question.position, target_question.position
      assert_equal source_question.question_text, target_question.question_text
    end
  end

  test 'delete after first' do
    survey = FactoryBot.create(:survey)
    first = survey.question_sections.create!(name: 'First', position: 1)
    second = survey.question_sections.create!(name: 'Second', position: 2)
    third = survey.question_sections.create!(name: 'Third', position: 3)

    sections = survey.question_sections
    sections.delete!(first)

    assert first.destroyed?, 'first should be destroyed'
    assert_equal 1, second.reload.position, 'the second section should now be the first'
    assert_equal 2, third.reload.position, 'the third section should now be the second'
  end

  test 'delete middle section' do
    survey = FactoryBot.create(:survey)
    first = survey.question_sections.create!(name: 'First', position: 1)
    second = survey.question_sections.create!(name: 'Second', position: 2)
    third = survey.question_sections.create!(name: 'Third', position: 3)

    sections = survey.question_sections
    sections.delete!(second)

    assert_equal 1, first.reload.position, 'the first section should still be the first'
    assert second.destroyed?, 'second should be destroyed'
    assert_equal 2, third.reload.position, 'the third section should now be the second'
  end

  test 'delete last ' do
    survey = FactoryBot.create(:survey)
    first = survey.question_sections.create!(name: 'First', position: 1)
    second = survey.question_sections.create!(name: 'Second', position: 2)
    third = survey.question_sections.create!(name: 'Third', position: 3)

    sections = survey.question_sections
    sections.delete!(third)

    assert_equal 1, first.reload.position, 'the first section should still be the first'
    assert_equal 2, second.reload.position, 'the second section should still be the second'
    assert third.destroyed?, 'third should be destroyed'
  end

  test 'cannot delete section with answered questions' do
    survey = FactoryBot.create(:survey)
    expert = users(:expert)
    section = survey.question_sections.create!(name: 'Has answers', position: 1)
    question = survey.questions.create!(section:, position: 1, question_text: 'Has answer?', type: 'Questions::Long')
    submission = survey.submissions.create!(submitted_at: Time.current, submitter: expert)
    submission.answers.create!(question:, text_answer_content: 'Foobar')

    sections = survey.question_sections

    assert_raises(Section::NotDeleteableError) do
      sections.delete!(section)
    end
  end
end

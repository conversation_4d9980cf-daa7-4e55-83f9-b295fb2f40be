# frozen_string_literal: true

require 'test_helper'

class BulkMessageTest < ActiveSupport::TestCase
  include ActionMailer::TestHelper

  test 'create BulkMessage enqueues SendMessageJob' do
    sent_by = FactoryBot.create(:admin)
    expert = FactoryBot.create(:expert)
    content = 'Message content'
    subject = 'Message subject'

    bulk_message = BulkMessage.new(content:, recipient_ids: [expert.id], subject:, sent_by:)

    assert_difference -> { Message.count } do
      bulk_message.save
    end

    message = Message.order(:created_at).last

    assert_enqueued_email_with DirectMessageMailer, :send_message, args: [message]
  end

  test 'default from address' do
    sent_by = FactoryBot.create(:admin)
    expert = FactoryBot.create(:expert)
    content = 'Message content'
    subject = 'Message subject'

    bulk_message = BulkMessage.new(content:, recipient_ids: [expert.id], subject:, sent_by:)

    assert_equal 'SciPinon <<EMAIL>>', bulk_message.sent_from_address
  end

  test 'sent message to SciPi applicant' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    sent_by = scipi.created_by
    sent_from_address = ActionMailer::Base.email_address_with_name(sent_by.email, sent_by.full_name)
    expert = FactoryBot.create(:expert)
    content = 'Message content'
    subject = 'Message subject'

    bulk_message = BulkMessage.new(
      content:,
      recipient_ids: [expert.id],
      subject:,
      sent_by:,
      sent_from_address:,
      survey: scipi
    )

    assert_equal sent_from_address, bulk_message.sent_from_address
  end
end

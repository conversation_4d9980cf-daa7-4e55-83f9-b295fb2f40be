# frozen_string_literal: true

require 'test_helper'

module Reputation
  class MetricTest < ActiveSupport::TestCase
    test 'identity_email_confirmations - confirmed email' do
      user = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Confirmed Email', category: 'Identity').first
      assert_equal 10, metric.value
    end

    test 'identity_email_confirmations - unconfirmed email' do
      user = FactoryBot.create(:expert, :unconfirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Confirmed Email', category: 'Identity').first

      assert_equal 0, metric.value
    end

    test 'identity_known_institutions - with known institution' do
      domain = FactoryBot.create(:domain, :known_institution)
      user = FactoryBot.create(:expert, :confirmed, email: "user@#{domain.hostname}")

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Confirmed Trusted Institution', category: 'Identity').first

      assert_equal 40, metric.value
    end

    test 'identity_known_institutions - without known institution' do
      domain = FactoryBot.create(:domain, :unknown_institution)
      user = FactoryBot.create(:expert, :confirmed, email: "user@#{domain.hostname}")

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Confirmed Trusted Institution', category: 'Identity').first

      assert_equal 0, metric.value
    end

    test 'identity_unknown_institutions - with unknown institution' do
      domain = FactoryBot.create(:domain, :unknown_institution)
      user = FactoryBot.create(:expert, :confirmed, email: "user@#{domain.hostname}")

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Confirmed Unknown Institution', category: 'Identity').first

      assert_equal 20, metric.value
    end

    test 'identity_unknown_institutions - without unknown institution' do
      domain = FactoryBot.create(:domain, :known_institution)
      user = FactoryBot.create(:expert, :confirmed, email: "user@#{domain.hostname}")

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Confirmed Unknown Institution', category: 'Identity').first

      assert_equal 0, metric.value
    end

    test 'identity_cv_score - with CV uploaded' do
      user = FactoryBot.create(:expert, :with_cv)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'CV Uploaded', category: 'Identity').first

      assert_equal 20, metric.value
    end

    test 'identity_cv_score - without CV uploaded' do
      user = FactoryBot.create(:expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'CV Uploaded', category: 'Identity').first

      assert_equal 0, metric.value
    end

    test 'identity_scipi_applicants - as participant' do
      user = FactoryBot.create(:expert, :confirmed)
      FactoryBot.create(:invite, :approved, user: user)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi Fin/Alt/Part', category: 'Identity').first

      assert_equal 90, metric.value
    end

    test 'identity_scipi_applicants - as finalist' do
      user = FactoryBot.create(:expert, :confirmed)
      invite = FactoryBot.create(:invite, user: user)
      invite.update!(selection_finalist_flagged_at: Time.current)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi Fin/Alt/Part', category: 'Identity').first

      assert_equal 60, metric.value
    end

    test 'identity_scipi_applicants - as alternate' do
      user = FactoryBot.create(:expert, :confirmed)
      invite = FactoryBot.create(:invite, user: user)
      invite.update!(marked_alternate_at: Time.current)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi Fin/Alt/Part', category: 'Identity').first

      assert_equal 60, metric.value
    end

    test 'identity_scipi_applicants - as non-applicant' do
      user = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi Fin/Alt/Part', category: 'Identity').first

      assert_equal 0, metric.value
    end

    test 'expertise_present - with expertise categories' do
      expert = FactoryBot.create(:expert, :expertise_complete)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Added Expertise Categories', category: 'Expertise').first

      assert_equal 1, metric.value
    end

    test 'expertise_present - without expertise categories' do
      expert = FactoryBot.create(:expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Added Expertise Categories', category: 'Expertise').first

      assert_equal 0, metric.value
    end

    test 'expertise_cv_score - with CV uploaded' do
      expert = FactoryBot.create(:expert, :with_cv)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'CV Uploaded', category: 'Expertise').first

      assert_equal 10, metric.value
    end

    test 'expertise_cv_score - without CV uploaded' do
      expert = FactoryBot.create(:expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'CV Uploaded', category: 'Expertise').first

      assert_equal 0, metric.value
    end

    test 'expertise_scipi_history - as participant' do
      user = FactoryBot.create(:expert, :confirmed)
      FactoryBot.create(:invite, :approved, user: user)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi History', category: 'Expertise').first

      assert_equal 95, metric.value
    end

    test 'expertise_scipi_history - as alternate' do
      user = FactoryBot.create(:expert, :confirmed)
      invite = FactoryBot.create(:invite, user: user)
      invite.update!(marked_alternate_at: Time.current)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi History', category: 'Expertise').first

      assert_equal 65, metric.value
    end

    test 'expertise_scipi_history - as finalist' do
      user = FactoryBot.create(:expert, :confirmed)
      invite = FactoryBot.create(:invite, user: user)
      invite.update!(selection_finalist_flagged_at: Time.current)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi History', category: 'Expertise').first

      assert_equal 35, metric.value
    end

    test 'expertise_scipi_history - as non-applicant' do
      user = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi History', category: 'Expertise').first

      assert_equal 0, metric.value
    end

    test 'expertise_scipi_vote_score - with votes' do
      user = FactoryBot.create(:expert, :confirmed)
      comment = FactoryBot.create(:comment, user: user)
      FactoryBot.create_list(:vote, 5, comment: comment, yay: true)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Result Comment', category: 'Expertise').first

      assert_equal 5, metric.value
    end

    test 'expertise_scipi_vote_score - without votes' do
      user = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'Result Comment', category: 'Expertise').first

      assert_equal 0, metric.value
    end

    test 'expertise_accepted_answers - with accepted answers' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name)
      ping = FactoryBot.create(:ping)
      answer = FactoryBot.create(:ping_answer, ping: ping, author: expert)
      answer.update!(accepted_at: Time.current)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Accepted Answer Count', category: 'Expertise').first

      assert_equal 1, metric.value
    end

    test 'expertise_accepted_answers - without accepted answers' do
      expert = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Accepted Answer Count', category: 'Expertise').first

      assert_equal 0, metric.value
    end

    test 'participation_scipoll_answers - with scipoll answers' do
      user = FactoryBot.create(:expert, :confirmed)
      branding = FactoryBot.create(:survey_branding, :scipoll)
      scipoll = FactoryBot.create(:question_group, branding: branding)
      FactoryBot.create(:submission, user: user, question_group: scipoll, submitted_at: Time.current)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPoll Answers ', category: 'Participation').first

      assert_equal 1, metric.value
    end

    test 'participation_scipoll_answers - without scipoll answers' do
      user = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPoll Answers ', category: 'Participation').first

      assert_equal 0, metric.value
    end

    test 'participation_pings_answer_score - with ping answer score' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name)
      ping = FactoryBot.create(:ping)
      answer = FactoryBot.create(:ping_answer, ping: ping, author: expert)
      FactoryBot.create(:ping_vote, :upvote, answer: answer)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Answer Score', category: 'Expertise').first

      assert_equal 1, metric.value
    end

    test 'participation_pings_answer_score - without ping answer score' do
      expert = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Answer Score', category: 'Expertise').first

      assert_equal 0, metric.value
    end

    test 'participation_note_scores - with note impact' do
      expert = FactoryBot.create(:expert, :confirmed)
      FactoryBot.create(:note, :positive, expert: expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Note Impact', category: 'Participation').first

      assert_equal 1, metric.value
    end

    test 'participation_note_scores - without note impact' do
      expert = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Note Impact', category: 'Participation').first

      assert_equal 0, metric.value
    end

    test 'participation_cv_score - with CV uploaded' do
      expert = FactoryBot.create(:expert, :with_cv)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'CV Uploaded', category: 'Participation').first

      assert_equal 10, metric.value
    end

    test 'participation_cv_score - without CV uploaded' do
      expert = FactoryBot.create(:expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'CV Uploaded', category: 'Participation').first

      assert_equal 0, metric.value
    end

    test 'participation_ping_questions - with ping questions' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name)
      FactoryBot.create(:ping, author: expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Questions', category: 'Participation').first

      assert_equal 1, metric.value
    end

    test 'participation_ping_questions - without ping questions' do
      expert = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Questions', category: 'Participation').first

      assert_equal 0, metric.value
    end

    test 'participation_ping_answers - with ping answers' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name)
      ping = FactoryBot.create(:ping)
      FactoryBot.create(:ping_answer, ping: ping, author: expert)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Answers', category: 'Participation').first

      assert_equal 1, metric.value
    end

    test 'participation_ping_answers - without ping answers' do
      expert = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Answers', category: 'Participation').first

      assert_equal 0, metric.value
    end

    test 'participation_pings_accepted_answers - with accepted answers' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name)
      ping = FactoryBot.create(:ping)
      answer = FactoryBot.create(:ping_answer, ping: ping, author: expert)
      answer.update!(accepted_at: Time.current)
      Reputation::Metric.refresh

      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Accepted Answer Count', category: 'Participation').first
      assert_equal 1, metric.value
    end

    test 'participation_pings_accepted_answers - without accepted answers' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: expert.id, name: 'Pings Accepted Answer Count', category: 'Participation').first

      assert_equal 0, metric.value
    end

    test 'participation_scipi_applicants - with applications' do
      user = FactoryBot.create(:expert, :confirmed)
      FactoryBot.create_list(:invite, 2, user: user)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi Applications', category: 'Participation').first

      assert_equal 2, metric.value
    end

    test 'participation_scipi_applicants - without applications' do
      user = FactoryBot.create(:expert, :confirmed)

      Reputation::Metric.refresh
      metric = Reputation::Metric.where(user_id: user.id, name: 'SciPi Applications', category: 'Participation').first

      assert_equal 0, metric.value
    end
  end
end

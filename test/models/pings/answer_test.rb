# frozen_string_literal: true

require 'test_helper'

module Pings
  class AnswerTest < ActiveSupport::TestCase
    test 'answer not accepted' do
      answer = Answer.new(accepted_at: nil)

      assert_not answer.accepted?
    end

    test 'answer accepted' do
      answer = Answer.new(accepted_at: Time.current)

      assert answer.accepted?
    end

    test 'mark accepted' do
      answer = FactoryBot.create(:ping_answer)

      answer.mark_accepted!

      assert answer.accepted?
    end

    test 'undo acceptance' do
      answer = FactoryBot.create(:ping_answer, :accepted)

      answer.undo_acceptance!

      assert_not answer.accepted?
    end

    test 'answer scores start at zero' do
      answer = FactoryBot.create(:ping_answer)

      assert_equal 0, answer.score
    end

    test 'downvote' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.downvote!(expert)

      assert_equal(-1, answer.score)
    end

    test 'undo downvote' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      FactoryBot.create(:ping_vote, :downvote, answer:, voter: expert)
      answer.downvote!(expert)

      assert_equal 0, answer.score
    end

    test 'upvote' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.upvote!(expert)

      assert_equal 1, answer.score
    end

    test 'undo upvote' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      FactoryBot.create(:ping_vote, :upvote, answer:, voter: expert)
      answer.upvote!(expert)

      assert_equal 0, answer.score
    end

    test 'positive vote score' do
      answer = FactoryBot.create(:ping_answer)
      FactoryBot.create(:ping_vote, :upvote, answer:)
      FactoryBot.create(:ping_vote, :upvote, answer:)
      FactoryBot.create(:ping_vote, :downvote, answer:)

      assert_equal 1, answer.score
    end

    test 'negative vote score' do
      answer = FactoryBot.create(:ping_answer)
      FactoryBot.create(:ping_vote, :downvote, answer:)
      FactoryBot.create(:ping_vote, :downvote, answer:)
      FactoryBot.create(:ping_vote, :upvote, answer:)

      assert_equal(-1, answer.score)
    end

    test 'expert voted?' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.upvote!(expert)

      assert answer.voted?(expert)
    end

    test 'upvoted_by?' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.upvote!(expert)

      assert answer.upvoted_by?(expert)
    end

    test 'downvoted_by?' do
      answer = FactoryBot.create(:ping_answer)
      expert = FactoryBot.create(:expert)
      answer.downvote!(expert)

      assert answer.downvoted_by?(expert)
    end

    test 'author credited after answer' do
      ping = FactoryBot.create(:ping)
      author = FactoryBot.create(:expert, :with_display_name)

      assert_difference(-> { author.reload.ping_credits }) do
        ping.answers.create!(
          author:,
          content: Faker::Lorem.paragraphs.join(' ')
        )
      end
    end

    test 'an expert cannot create a ping without a display name' do
      ping = FactoryBot.create(:ping)
      author = FactoryBot.create(:expert)

      answer_attrs = FactoryBot.attributes_for(:ping_answer)

      answer = author.ping_answers.create(answer_attrs.merge(ping_id: ping.id))

      assert answer.errors.added?(:display_name, :blank)
    end

    test 'hiding an answer cancels unsent notifications for that answer (and nothing else)' do
      ping = FactoryBot.create(:ping)
      subscriber = FactoryBot.create(:user)
      ping.subscribe(subscriber)

      older_answer = FactoryBot.create(:ping_answer, ping:, created_at: 1.hour.ago)
      target_answer = FactoryBot.create(:ping_answer, ping:)

      # This happens in the controller
      Pings::CreateNewAnswerNotificationsJob.perform_now(ping, older_answer)
      Pings::CreateNewAnswerNotificationsJob.perform_now(ping, target_answer)

      assert_equal 4, Notification.count

      assert_difference(-> { Notification.count }, -2) do
        target_answer.update!(hidden_at: Time.current)
      end

      assert_equal 2, Notification.count
    end
  end
end

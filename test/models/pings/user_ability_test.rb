# frozen_string_literal: true

require 'test_helper'

module Pings
  class UserAbilityTest < ActiveSupport::TestCase
    include ::AbilityTestHelper

    setup do
      @current_user = FactoryBot.create(:expert, :with_display_name, ping_credits: 10)
    end

    #
    # Unverified user cases
    #
    # NOTE: In addition to not being able to :create, :answer, and :vote on Ping and answers, we also need to
    # block actions on Pings and answers in the case where we retroactively change a user to unverified status.
    #

    test 'unverified users cannot :create a Ping' do
      unverify_user!

      ping = Ping.new(type: PingType.default)

      assert_cannot :create, ping, message: 'unverified users should not be able to :create a Ping'
    end

    test 'unverified users cannot :answer a Ping' do
      unverify_user!

      ping = FactoryBot.create(:ping, :published)

      assert_cannot :answer, ping, message: 'unverified users should not be able to :answer a Ping'
    end

    test 'unverified users cannot :edit a Ping' do
      unverify_user!

      ping = FactoryBot.create(:ping, :published, author: @current_user)

      assert_cannot :edit, ping, message: 'unverified users should not be able to :edit a Ping'
    end

    test 'unverified users cannot :accept answers' do
      unverify_user!

      ping = FactoryBot.create(:ping, :published, author: @current_user)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_cannot :accept, answer, message: 'unverified users should not be able to :accept answers'
    end

    test 'unverified users cannot :edit their answers' do
      unverify_user!

      # This case should likely not happen since it currently only pertains
      # to paid Pings, but it's here for completeness sake.
      ping = FactoryBot.create(:ping, :published, :allows_voting_deadlines, :voting_unopened)
      answer = FactoryBot.create(:ping_answer, ping:, author: @current_user)

      assert_cannot :edit, answer, message: 'unverified users should not be able to :edit their answer'
    end

    test 'unverified users cannot :vote on answers' do
      unverify_user!

      ping = FactoryBot.create(:ping, :published)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_cannot :vote, answer, message: 'unverified users should be able to :vote on an answer'
    end

    test 'unverified users cannot :undo_acceptance on an answer' do
      unverify_user!

      ping = FactoryBot.create(:ping, :published, author: @current_user)
      answer = FactoryBot.create(:ping_answer, :accepted, ping:)

      assert_cannot :undo_acceptance, answer, message: 'unverified users should not be able :undo_acceptance on an answer'
    end

    test 'should not be able to view draft Pings' do
      ping = FactoryBot.create(:ping, :draft)

      assert_cannot :view, ping, message: 'should not be able to view draft Pings'
    end

    test 'scores are always visible on public pings' do
      logged_out!

      ping = FactoryBot.create(:ping)

      assert_can :view_score, ping, message: 'should be able to view the score'
    end

    test 'scores are not visible on paid pings without an accepted answer' do
      logged_out!

      ping = FactoryBot.create(:ping, :paid, :published)

      assert_cannot :view_score, ping, message: 'should not be able to view the score without an accepted answer'
    end

    test 'scores are visible on paid pings with an accepted answer' do
      logged_out!

      ping = FactoryBot.create(:ping, :paid, :published, :answer_accepted)

      assert_can :view_score, ping, message: 'should be able to view the score'
    end

    test 'can create with attributes' do
      ping = FactoryBot.build(:ping)

      assert_can :create, ping, message: 'should be able to create Ping'

      assert_attribute_permitted(Ping, :create, :title)
      assert_attribute_permitted(Ping, :create, :content)
      assert_attribute_permitted(Ping, :create, :display_name)

      assert_attribute_not_permitted(Ping, :create, :type_id)
      assert_attribute_not_permitted(Ping, :create, :reward_amount)
      assert_attribute_not_permitted(Ping, :create, :voting_opens_at_date)
      assert_attribute_not_permitted(Ping, :create, :voting_opens_at_hour)
      assert_attribute_not_permitted(Ping, :create, :voting_closes_at_date)
      assert_attribute_not_permitted(Ping, :create, :voting_closes_at_hour)
    end

    test 'logged out users cannot create a Ping' do
      logged_out!

      ping = FactoryBot.build(:ping)

      assert_cannot :create, ping, message: 'logged out users cannot create a Ping'
    end

    test 'unconfirmed users cannot create a Ping' do
      @current_user = FactoryBot.create(:expert, :unconfirmed)

      ping = FactoryBot.build(:ping)

      assert_cannot :create, ping, message: 'unconfirmed users cannot create with attributes'
    end

    test 'users without enough credit cannot create a Ping' do
      @current_user.update!(ping_credits: 0)

      ping = FactoryBot.build(:ping)

      assert_cannot :create, ping, message: 'users without enough credit cannot create a Ping'
    end

    test 'authors can edit within change window' do
      ping = FactoryBot.create(:ping, author: @current_user)

      assert_can :edit, ping, message: 'authors should be able to edit pings within change window'

      assert_attribute_permitted(ping, :edit, :title)
      assert_attribute_permitted(ping, :edit, :content)

      assert_attribute_not_permitted(ping, :edit, :display_name)
      assert_attribute_not_permitted(ping, :edit, :type_id)
      assert_attribute_not_permitted(ping, :edit, :reward_amount)
      assert_attribute_not_permitted(ping, :edit, :voting_opens_at_date)
      assert_attribute_not_permitted(ping, :edit, :voting_opens_at_hour)
      assert_attribute_not_permitted(ping, :edit, :voting_closes_at_date)
      assert_attribute_not_permitted(ping, :edit, :voting_closes_at_hour)
    end

    test 'authors cannot edit outside change window' do
      ping = FactoryBot.create(:ping, :outside_change_window, author: @current_user)

      assert_cannot :edit, ping, message: 'should not be able to edit pings within change window'
    end

    test 'users edit another\'s ping' do
      ping = FactoryBot.create(:ping, :outside_change_window)

      assert_cannot :edit, ping, message: 'should not be able to another user\'s Ping'
    end

    test 'users can view answers on public pings' do
      ping = FactoryBot.create(:ping)

      assert_can :view_answers, ping, message: 'users can view answers on public pings'
    end

    test 'users cannot view answers when voting is unopened' do
      ping = FactoryBot.create(:ping)

      assert_can :view_answers, ping, message: 'users can view answers on public pings'
    end

    test 'user can view answers when voting once voting is opened' do
      ping = FactoryBot.create(:ping, :paid, :voting_open)

      assert_can :view_answers, ping, message: 'user can view answers when voting once voting is opened'
    end

    test 'logged out users can view answers when voting is open' do
      logged_out!

      ping = FactoryBot.create(:ping, :paid, :voting_open)

      assert_can :view_answers, ping, message: 'logged out users can view answers when voting is open'
    end

    test 'anonymous users cannot answer Ping' do
      logged_out!

      ping = FactoryBot.create(:ping)

      assert_cannot :answer, ping, message: 'anonymous users should not be able to answer a Ping'
    end

    test 'unconfirmed user users cannot answer a Ping' do
      @current_user = FactoryBot.create(:expert, :unconfirmed)

      ping = FactoryBot.create(:ping)

      assert_cannot :answer, ping, message: 'unconfirmed users should not be able to answer a Ping'
    end

    test 'Ping authors cannot answer their own Pings' do
      ping = FactoryBot.create(:ping, author: @current_user)

      assert_cannot :answer, ping, message: 'Ping authors should not be able to answer their own Ping'
    end

    test 'experts who have already answered cannot answer a Ping again' do
      answer = FactoryBot.create(:ping_answer, author: @current_user)
      ping = answer.ping

      assert_cannot :answer, ping, message: 'experts who have already answered should not be able to answer again'
    end

    test 'confirmed experts can answer a ping without voting deadlines' do
      ping = FactoryBot.create(:ping, :published, :disallows_voting_deadlines)

      assert_can :answer, ping, message: 'experts should be able to answer a ping without voting deadlines'
    end

    test 'confirmed experts can answer a ping with voting deadlines' do
      ping = FactoryBot.create(:ping, :published, :allows_voting_deadlines, :voting_unopened)

      assert_can :answer, ping, message: 'experts should be able to answer a ping when voting is unopened'
    end

    test 'confirmed experts cannot answer a ping once voting has opened' do
      ping = FactoryBot.create(:ping, :published, :allows_voting_deadlines, :voting_open)

      assert_cannot :answer, ping, message: 'experts should be able to answer a ping without voting deadlines'
    end

    test 'users can edit own answer when voting is unopened' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_unopened)
      answer = FactoryBot.create(:ping_answer, ping:, author: @current_user)

      assert_can :edit, answer, message: 'should be able to edit own answer'
    end

    test 'users cannot edit own answer once voting has opened' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)
      answer = FactoryBot.create(:ping_answer, ping:, author: @current_user)

      assert_cannot :edit, answer, message: 'should not be able to edit own answer once voting is open'
    end

    test 'users cannot edit others\' answers' do
      answer = FactoryBot.create(:ping_answer)

      assert_cannot :edit, answer, message: 'should not be able to edit another\'s answer'
    end

    test 'non-admins cannot open voting' do
      ping = FactoryBot.create(:ping, :paid, :published, :voting_unopened)

      assert_cannot :open_voting, ping, message: 'should not be able to open voting'
    end

    test 'non-admins cannot close voting' do
      ping = FactoryBot.create(:ping, :paid, :published, :voting_open)

      assert_cannot :open_voting, ping, message: 'should not be able to close voting'
    end

    test 'authors can accept answers on their own Pings' do
      ping = FactoryBot.create(:ping, author: @current_user)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_can :accept, answer, message: 'accept answers on their own Pings'
    end

    test 'authors cannot accept answers on already accepted answers' do
      ping = FactoryBot.create(:ping, author: @current_user)
      answer = FactoryBot.create(:ping_answer, :accepted, ping:)

      assert_cannot :accept, answer, message: 'cannot accept answers on already accepted answers'
    end

    test 'authors cannot accept answers when another answer is accepted' do
      ping = FactoryBot.create(:ping, author: @current_user)
      answer = FactoryBot.create(:ping_answer, :accepted, ping:)

      assert_cannot :accept, answer, message: 'cannot accept answers when another answer is accepted'
    end

    test 'users cannot accept answers on others\' Pings' do
      ping = FactoryBot.create(:ping)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_cannot :accept, answer, message: 'cannot accept answers on their others\' Pings'
    end

    test 'confirmed users can vote on answers' do
      answer = FactoryBot.create(:ping_answer)

      assert_can :vote, answer, message: 'confirmed users should be able to vote on answers'
    end

    test 'users cannot vote on their own answers' do
      answer = FactoryBot.create(:ping_answer, author: @current_user)

      assert_cannot :vote, answer, message: 'users cannot vote on their own answers'
    end

    test 'unconfirmed users can vote on answers' do
      @current_user = FactoryBot.create(:expert, :unconfirmed)

      answer = FactoryBot.create(:ping_answer)

      assert_cannot :vote, answer, message: 'unconfirmed users can vote on answers'
    end

    private

    def ability
      @ability ||= Ability.new(@current_user)
    end

    def logged_out!
      @current_user = nil
    end

    def unverify_user!
      pending = VerificationStatus.pending
      @current_user.profile.update!(verification_status: pending)
    end
  end
end

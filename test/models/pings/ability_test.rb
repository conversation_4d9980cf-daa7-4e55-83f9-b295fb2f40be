# frozen_string_literal: true

require 'test_helper'

module Pings
  class AbilityTest < ActiveSupport::TestCase
    test 'anyone can see published Pings' do
      published = Ping.new(published_at: 1.week.ago)

      ability = Ability.new(nil)

      assert ability.can?(:view, published), 'anyone should be able to see published Pings'
    end

    test 'logged out users cannot see draft Pings' do
      draft = Ping.new(published_at: nil)

      ability = Ability.new(nil)

      assert_not ability.can?(:view, draft), 'logged out uses should not be able to see draft Pings'
    end

    test 'anyone can see answers when voting is open' do
      answer_visible = Ping.new(voting_opens_at: 1.week.ago)

      ability = Ability.new(nil)

      assert ability.can?(:view_answers, answer_visible), 'anyone should be able to see answers when voting is open'
    end

    test 'logged out users cannot see answers when voting is not open' do
      answer_not_visible = Ping.new(voting_opens_at: nil)

      ability = Ability.new(nil)

      assert_not ability.can?(:view_answers, answer_not_visible),
                 'logged out users should not be able to see answers when voting is not open'
    end

    test 'logged out users cannot see unpublished scores' do
      paid_type = pings_ping_types(:paid)
      unpublished_scores = Ping.new(type: paid_type)

      ability = Ability.new(nil)

      assert_not ability.can?(:view_score, unpublished_scores),
                 'logged out users should not be able to see unpublished scores'
    end

    test 'logged out users can see published scores' do
      public_type = pings_ping_types(:public)
      published_scores = Ping.new(type: public_type)

      ability = Ability.new(nil)

      assert ability.can?(:view_score, published_scores),
             'logged out users should be able to see published scores'
    end

    test 'cannot view answerer names durring a voting round' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)

      ability = Ability.new(nil)

      assert_not ability.can?(:view_answerer_name, ping), message: 'should not be able to view display name'
    end

    test 'can view answerer names after a voting round is complete (answer accepted)' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :answer_accepted)

      ability = Ability.new(nil)

      assert ability.can?(:view_answerer_name, ping), message: 'should be able to view display name'
    end

    test 'registered users can :manage_subscription on Pings' do
      user = FactoryBot.create(:user)
      ping = FactoryBot.create(:ping)

      ability = Ability.new(user)

      assert ability.can?(:manage_subscription, ping), message: 'registered users can :manage_subscription on Pings'
    end

    test 'guest users cannot :manage_subscription on Pings' do
      user = FactoryBot.create(:guest)
      ping = FactoryBot.create(:ping)

      ability = Ability.new(user)

      assert_not ability.can?(:manage_subscription, ping), message: 'anonymous users cannot :manage_subscription on Pings'
    end
  end
end

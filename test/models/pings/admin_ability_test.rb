# frozen_string_literal: true

require 'test_helper'

module Pings
  class AdminAbilityTest < ActiveSupport::TestCase
    include ::AbilityTestHelper

    test 'should be able to :index Pings' do
      assert_can :index, Ping, message: 'should be able to :index Pings'
    end

    test 'should be able to view public Pings' do
      ping = FactoryBot.create(:ping)

      assert_can :view, ping, message: 'should be able to view public Pings'
    end

    test 'should be able to view draft Pings' do
      ping = FactoryBot.create(:ping, :draft)

      assert_can :view, ping, message: 'should be able to view draft Pings'
    end

    test 'can create with attributes' do
      assert_can :create, Ping, message: 'Ping admins should be able to create Ping'

      assert_attribute_permitted(Ping, :create, :type_id)
      assert_attribute_permitted(Ping, :create, :title)
      assert_attribute_permitted(Ping, :create, :content)
      assert_attribute_permitted(Ping, :create, :reward_amount)
      assert_attribute_permitted(Ping, :create, :voting_opens_at_date)
      assert_attribute_permitted(Ping, :create, :voting_opens_at_hour)
      assert_attribute_permitted(Ping, :create, :voting_closes_at_date)
      assert_attribute_permitted(Ping, :create, :voting_closes_at_hour)
    end

    test 'ping admins can edit any public (default) ping' do
      ping = FactoryBot.create(:ping)

      assert_can :edit, ping, message: 'Ping admins should be able to edit any public Ping'
    end

    test 'ping admins can edit any paid ping' do
      ping = FactoryBot.create(:ping, :paid)

      assert_can :edit, ping, message: 'Ping admins should be able to edit any paid Ping'

      assert_attribute_not_permitted(ping, :edit, :type_id)

      assert_attribute_permitted(ping, :edit, :title)
      assert_attribute_permitted(ping, :edit, :content)
      assert_attribute_permitted(ping, :edit, :reward_amount)
      assert_attribute_permitted(ping, :edit, :voting_opens_at_date)
      assert_attribute_permitted(ping, :edit, :voting_opens_at_hour)
      assert_attribute_permitted(ping, :edit, :voting_closes_at_date)
      assert_attribute_permitted(ping, :edit, :voting_closes_at_hour)
    end

    test 'admin can view answers when voting is unopened' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_unopened)

      assert_can :view_answers, ping, message: 'Ping admins should be able view answers when voting is unopened'
    end

    test 'admin can view answers when voting is opened' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)

      assert_can :view_answers, ping, message: 'admins should be able view answers when voting is open'
    end

    test 'scores are visible to admins on paid pings without an accepted answer' do
      ping = FactoryBot.create(:ping, :paid, :published, :voting_open)

      assert_can :view_score, ping, message: 'should be able to view the score without an accepted'
    end

    test 'should be able to delete a Ping without answers' do
      ping = FactoryBot.create(:ping)

      assert_can :delete, ping, message: 'should be able to delete a Ping without answers'
    end

    test 'should not be able to delete a Ping with answers' do
      ping = FactoryBot.create(:ping)
      FactoryBot.create(:ping_answer, ping:)

      assert_cannot :delete, ping, message: 'should not be able to delete a Ping with answers'
    end

    test 'should not be able to publish public pings' do
      ping = FactoryBot.create(:ping)

      assert_cannot :publish, ping, message: 'should not be able to publish public pings'
    end

    test 'should be able to publish draft pings' do
      ping = FactoryBot.create(:ping, :draft)

      assert_can :publish, ping, message: 'should be able to publish draft pings'
    end

    test 'should not be able to publish published pings' do
      ping = FactoryBot.create(:ping, :published)

      assert_cannot :publish, ping, message: 'should not be able to publish published pings'
    end

    test 'should not be able to open voting on drafts' do
      ping = FactoryBot.create(:ping, :draft)

      assert_cannot :open_voting, ping, message: 'should not be able to open voting on drafts'
    end

    test 'should be able to open voting when a voting stage is supported' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_unopened)

      assert_can :open_voting, ping, message: 'should be able to open voting'
    end

    test 'should not be able to open voting once already open' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)

      assert_cannot :open_voting, ping, message: 'should be able to open voting'
    end

    test 'should not be able to close when voting is unopened' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_unopened)

      assert_cannot :close_voting, ping, message: 'should not be able to close voting'
    end

    test 'should be able to close when voting is open' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)

      assert_can :close_voting, ping, message: 'should be able to close voting'
    end

    test 'should be able to close when a voting stage is not supported' do
      ping = FactoryBot.create(:ping)

      assert_cannot :close_voting, ping, message: 'should not be able to close voting'
    end

    test 'should be able accept answer when voting is closed' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_closed)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_can :accept, answer, message: 'should be able accept answer when voting is closed'
    end

    test 'should not be able to close when a voting is open' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)

      assert_cannot :accept, ping, message: 'should not be able to close when a voting is open'
    end

    test 'should be able accept multiple answers' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :allows_multiple_answers, :voting_closed)
      FactoryBot.create(:ping_answer, :accepted, ping:)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_can :accept, answer, message: 'should be able accept multiple answers'
    end

    test 'should not be able accept an already answered answer' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :allows_multiple_answers, :voting_closed)
      accepted = FactoryBot.create(:ping_answer, :accepted, ping:)

      assert_cannot :accept, accepted, message: 'should not be able accept an already answered answer'
    end

    test 'should be able undo acceptance' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :allows_multiple_answers, :voting_closed)
      answer = FactoryBot.create(:ping_answer, :accepted, ping:)

      assert_can :undo_acceptance, answer, message: 'should be able undo answer acceptance'
    end

    test 'should not be able undo acceptance of unaccepted answer' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :allows_multiple_answers, :voting_closed)
      answer = FactoryBot.create(:ping_answer, ping:)

      assert_cannot :undo_acceptance, answer, message: 'should not be able undo acceptance of unaccepted answer'
    end

    test 'should be able view answerer display names even in voting rounds' do
      ping = FactoryBot.create(:ping, :allows_voting_deadlines, :voting_open)

      assert_can :view_answerer_name, ping, message: 'should be able to view display name'
    end

    private

    def ability
      current_user = FactoryBot.create(:pings_admin)

      @ability ||= Ability.new(current_user)
    end
  end
end

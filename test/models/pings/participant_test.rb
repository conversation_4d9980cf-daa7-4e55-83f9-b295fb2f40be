# frozen_string_literal: true

require 'test_helper'

module Pings
  class ParticipantTest < ActiveSupport::TestCase
    test 'can post a ping' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name, ping_credits: 2)
      FactoryBot.create(:ping, author: expert)

      assert expert.can_post_ping?, 'should be able to post a ping'
    end

    test 'cannot post a ping with credit' do
      expert = FactoryBot.create(:expert, :confirmed, :with_display_name, ping_credits: 1)
      FactoryBot.create(:ping, author: expert)

      assert_not expert.can_post_ping?, 'should bot be able to post a ping'
    end

    test 'admins always have ping credit' do
      admin = FactoryBot.create(:pings_admin, ping_credits: 0)

      assert admin.ping_credit?, 'admins should always have ping credit'
    end

    test 'expert with ping credit' do
      credit = 4
      pings_remaining = 1
      expert = FactoryBot.create(:expert, :with_display_name, ping_credits: credit)

      (credit - pings_remaining).times { FactoryBot.create(:ping, author: expert) }

      assert expert.ping_credit?, 'should have ping credit'
      assert pings_remaining, expert.available_ping_credits
    end

    test 'expert without ping credit' do
      credit = 4
      expert = FactoryBot.create(:expert, :with_display_name, ping_credits: credit)
      credit.times { FactoryBot.create(:ping, author: expert) }

      assert_not expert.ping_credit?, 'should not have ping credit'
      assert expert.available_ping_credits.zero?
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Pings
  class SearchTest < ActiveSupport::TestCase
    test 'search checks ping title' do
      title_ping = create(:ping, :published, title: 'Unique searchable title')
      control_ping = create(:ping, :published, title: 'Regular title')

      results = Ping.search('searchable')

      assert_includes results, title_ping
      assert_not_includes results, control_ping
    end

    test 'search checks ping content' do
      content_ping = create(:ping, :published, content: 'Content with searchable term inside')
      control_ping = create(:ping, :published, content: 'Regular content')

      results = Ping.search('searchable')

      assert_includes results, content_ping
      assert_not_includes results, control_ping
    end

    test 'search checks ping tags' do
      searchable_expertise = create(:expertise, name: 'Searchable Expertise')
      regular_expertise = create(:expertise, name: 'Regular Expertise')

      tagged_ping = create(:ping, :published)
      tagged_ping.expertises = [searchable_expertise]

      control_ping = create(:ping, :published)
      control_ping.expertises = [regular_expertise]

      results = Ping.search('searchable')

      assert_includes results, tagged_ping
      assert_not_includes results, control_ping
    end
  end
end

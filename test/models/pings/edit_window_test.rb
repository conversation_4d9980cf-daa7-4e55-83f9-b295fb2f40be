# frozen_string_literal: true

require 'test_helper'

module Pings
  class EditWindowTest < ActiveSupport::TestCase
    test 'within' do
      start_time = 1.minute.ago
      end_time = 1.minute.from_now

      window = EditWindow.new(start_time, end_time)

      assert window.within?
    end

    test 'no within' do
      start_time = 3.minutes.ago
      end_time = 1.second.ago

      window = EditWindow.new(start_time, end_time)

      assert_not window.within?
    end
  end
end

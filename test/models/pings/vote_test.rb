# frozen_string_literal: true

require 'test_helper'

module Pings
  class VoteTest < ActiveSupport::TestCase
    test 'zero votes not active' do
      vote = FactoryBot.create(:ping_vote, value: 0)

      assert_not_includes Vote.active, vote, 'zero votes are not active'
    end

    test 'up votes are active' do
      vote = FactoryBot.create(:ping_vote, :upvote)

      assert_includes Vote.active, vote, 'up votes are active'
    end

    test 'down votes are active' do
      vote = FactoryBot.create(:ping_vote, :downvote)

      assert_includes Vote.active, vote, 'down votes are active'
    end
  end
end

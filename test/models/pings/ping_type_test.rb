# frozen_string_literal: true

require 'test_helper'

module Pings
  class PingTypeTest < ActiveSupport::TestCase
    test 'paid pings allow drafts' do
      ping = pings_ping_types(:paid)

      assert_not ping.disallow_draft?, 'should not disallow drafts'
    end

    test 'public pings disallow drafts' do
      ping = pings_ping_types(:public)

      assert ping.disallow_draft?, 'should disallow drafts'
    end

    test 'public Pings published by default' do
      type = pings_ping_types(:public)
      ping = Ping.new

      type.assign_creation_defaults(ping)

      assert ping.published?, 'public Pings should be published by default'
    end

    test 'public Pings voting is open by default' do
      type = pings_ping_types(:public)
      ping = Ping.new

      type.assign_creation_defaults(ping)

      assert ping.voting_open?, 'voting should be open by default for public Pings'
    end
  end
end

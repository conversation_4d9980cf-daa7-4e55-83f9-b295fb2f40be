# frozen_string_literal: true

require 'test_helper'

class AccessTokenTest < ActiveSupport::TestCase
  test 'used access token has a final submission' do
    user = FactoryBot.create(:user)
    scipoll = FactoryBot.create(:scipoll)
    FactoryBot.create(:submission, :final, question_group: scipoll, user:)
    access_token = scipoll.access_tokens.create!(user:)

    assert access_token.used?, 'should be used'
  end

  test 'access token remains unused with draft submission' do
    user = FactoryBot.create(:user)
    scipoll = FactoryBot.create(:scipoll)
    FactoryBot.create(:submission, :draft, question_group: scipoll, user:)
    access_token = scipoll.access_tokens.create!(user:)

    assert_not access_token.used?, 'should be unused'
  end

  test 'unused access token without a user' do
    scipoll = FactoryBot.create(:scipoll)

    access_token = scipoll.access_tokens.create!

    assert_not access_token.used?, 'should not be used'
  end

  test 'unused access token with a user' do
    user = FactoryBot.create(:user)
    scipoll = FactoryBot.create(:scipoll)
    access_token = scipoll.access_tokens.create!(user:)

    assert_not access_token.used?, 'should not be used'
  end

  test 'assign user to unused token' do
    scipoll = FactoryBot.create(:scipoll)
    access_token = scipoll.access_tokens.create!
    user = FactoryBot.create(:user)

    assert_nothing_raised do
      access_token.assign_user!(user)

      assert_equal user, access_token.user
    end
  end

  test 'can re-assign same user to already used token' do
    scipoll = FactoryBot.create(:scipoll)
    submitter = FactoryBot.create(:user)
    access_token = scipoll.access_tokens.create!(user: submitter)
    FactoryBot.create(:submission, :final, question_group: scipoll, user: submitter)

    assert_nothing_raised do
      access_token.assign_user!(submitter)

      assert_equal submitter, access_token.user
    end
  end

  test 'cannot assign user to already used token' do
    scipoll = FactoryBot.create(:scipoll)
    submitter = FactoryBot.create(:user)
    access_token = scipoll.access_tokens.create!(user: submitter)
    FactoryBot.create(:submission, :final, question_group: scipoll, user: submitter)

    user = FactoryBot.create(:user)

    assert_raises(AccessToken::AlreadyUsed) do
      access_token.assign_user!(user)

      assert_nil access_token.user
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module SignNow
  class DocumentCompletedEventTest < ActiveSupport::TestCase
    test 'process!' do
      ENV['SIGN_NOW_WEBHOOK_SECRET_KEY'] = 'super_secret!'

      scipi = FactoryBot.create(:scipi, :invite_only)
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(
        expert:,
        contract_last_sent_at: Time.current,
        sign_now_contract_id: '238f1b7667534575933923eea8b7d4b07b7096dd'
      )

      event_payload = <<~EVENT.squish
        {
          "meta": {
            "timestamp": 1711052291,
            "event": "document.complete",
            "environment": "https://api-eval.signnow.com",
            "callback_url": "https://enwls3rnq1pfh.x.pipedream.net",
            "access_token": "c87ee302e16380b0ff912c1cbd814204e3d4076125eb51501729818b9ff9240e",
            "initiator_id": "f47e7f7f3b854c77a3e68486810f4396f902b134"
          },
          "content": {
            "document_id": "#{panelist.sign_now_contract_id}",
            "document_name": "SciPi 493 - Rita Schoeny Contract",
            "user_id": "e8c6c8740ffc46ca894144b169b6d86fc61924af"
          }
        }
      EVENT

      their_hmac = HMACHelper.base64_hmac(data: event_payload, secret_key: SignNow.configuration.webhook_secret_key)

      DocumentCompletedEvent.new(event_payload, hmac: their_hmac).process!

      assert panelist.reload.contract_signed?, 'contract should be signed after CompleteContractJob is performed'
    end

    test 'hmac verification fails' do
      secret_key = 'super_secret!'
      ENV['SIGN_NOW_WEBHOOK_SECRET_KEY'] = secret_key

      # Skip unnecessary parts of the payload for brevity
      event_payload = { content: { document_id: '12345' } }.to_json

      assert_raises(SignNow::WebhookVerificationFailedError) do
        DocumentCompletedEvent.new(event_payload, hmac: 'not valid').process!
      end
    end

    test 'raises json error' do
      ENV['SIGN_NOW_WEBHOOK_SECRET_KEY'] = 'foo'

      event_payload = 'invalid json'

      hmac = create_hmac(data: event_payload)

      assert_raises(JSON::ParserError) { DocumentCompletedEvent.new(event_payload, hmac:).process! }
    end

    test 'raises contract not found error' do
      ENV['SIGN_NOW_WEBHOOK_SECRET_KEY'] = 'foo'

      # Don't set unused parts of the payload
      event_payload = { content: { document_id: 'invalid_document_id' } }.to_json

      hmac = create_hmac(data: event_payload)

      assert_raises(ActiveRecord::RecordNotFound) { DocumentCompletedEvent.new(event_payload, hmac:).process! }
    end

    private

    def create_hmac(data:)
      HMACHelper.base64_hmac(data:, secret_key: SignNow.configuration.webhook_secret_key)
    end
  end
end

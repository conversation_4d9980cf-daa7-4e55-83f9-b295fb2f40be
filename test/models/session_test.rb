# frozen_string_literal: true

require 'test_helper'

class SessionTest < ActiveSupport::TestCase
  test 'email is required' do
    login = Session.new(email: '')

    assert_error_on(login, :email, :blank)
  end

  test 'password is required' do
    login = Session.new(password: '')

    assert_error_on(login, :password, :blank)
  end

  test 'saving a signup creates a new, confirmed user' do
    password = Faker::Internet.password

    user = FactoryBot.create(:expert, password:)

    session = Session.new(email: user.email, password:)

    assert session.save, 'should have successfully logged in'
    assert_equal user, session.user!, 'session id should match the user id'
  end

  test 'no account' do
    session = Session.new(
      email: Faker::Internet.email,
      password: Faker::Internet.password
    )

    assert_not session.save, 'should not have successfully logged in'
  end

  test 'bad password account' do
    user = FactoryBot.create(:expert, password: Faker::Internet.password)

    session = Session.new(email: user.email, password: Faker::Internet.password)

    assert_not session.save, 'should not have successfully logged in'
  end

  test 'migrate guest user' do
    registered_user = FactoryBot.create(:expert)
    guest_role = FactoryBot.create(:role, :guest)
    scipoll = FactoryBot.create(:scipoll, :published, :access_token_required, :allow_guest_participation, :unlisted)
    access_token = scipoll.access_tokens.create!
    guest_user = guest_role.users.create!(access_token:)
    scipoll.submissions.create!(submitter: guest_user, submitted_at: Time.current)

    session = Session.new(email: registered_user.email, password: 'secret123', guest_user:)

    assert_difference -> { registered_user.submissions.count } do
      session.save

      assert_equal access_token, registered_user.access_token
    end
  end
end

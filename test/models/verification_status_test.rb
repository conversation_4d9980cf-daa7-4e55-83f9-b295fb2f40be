# frozen_string_literal: true

require 'test_helper'

class VerificationStatusTest < ActiveSupport::TestCase
  test 'auto verification? on when default status is verified?' do
    status = VerificationStatus.create!(name: 'New verified status', verified: true)

    VerificationStatus.change_default!(status)

    assert VerificationStatus.auto_verification?,
           'auto_verification? should be on when the default status is verified'
  end

  test 'auto verification? off when default status is unverified' do
    status = VerificationStatus.create!(name: 'New unverified status', verified: false)

    VerificationStatus.change_default!(status)

    assert_not VerificationStatus.auto_verification?,
               'auto_verification? should be off when the default status is unverified'
  end

  test 'change_default!' do
    verified = VerificationStatus.verified
    pending = VerificationStatus.pending

    assert_changes(-> { VerificationStatus.default }, from: verified, to: pending) do
      VerificationStatus.change_default!(pending)
    end
  end

  test 'cannot change the default to the same status' do
    default = VerificationStatus.default

    assert_no_changes(-> { VerificationStatus.default }) do
      assert_raises(ArgumentError, match: 'Auto-verification is already on') do
        VerificationStatus.change_default!(default)
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Contracts
  class SenderTest < ActiveSupport::TestCase
    include ActionMailer::TestHelper

    test 'run!' do
      contract_template = FactoryBot.create(:contract_template)
      scipi = FactoryBot.create(
        :scipi,
        contract_template:,
        engagement_info: EngagementInfo.new,
        sign_now_contracts_folder_id: 'folder_id_abc123'
      )
      expert = FactoryBot.create(:expert)
      panelist = scipi.panelists.create!(expert:)
      callback_url = 'https://example.com/callback'
      redirect_uri = 'https://example.com/redirect'

      sign_now_document = {
        'document_id' => 'document_id_abc123',
        'roles' => [{ 'name' => 'signer', 'unique_id' => 'role_id_abc123' }]
      }
      # This is only saved, and not used. the dummy data is just to assert on.
      sign_now_invite = { 'dummy' => 'data' }

      mock_gateway = SignNow::Gateway.any_instance
      mock_gateway.expects(:upload_document).returns('document_id_abc123')
      mock_gateway
        .expects(:get_document_by_id)
        .with(document_id: 'document_id_abc123')
        .returns(sign_now_document)
      mock_gateway
        .expects(:create_event_subscription)
        .with(event: 'document.complete', entity_id: 'document_id_abc123', callback_url:)
      mock_gateway
        .expects(:move_to_folder)
        .with(document_id: 'document_id_abc123', folder_id: 'folder_id_abc123')
      mock_gateway
        .expects(:create_embed_invite)
        .with(
          document_id: 'document_id_abc123',
          first_name: expert.first_name,
          last_name: expert.last_name,
          email: expert.email,
          role_id: 'role_id_abc123',
          redirect_uri:
        )
        .returns(sign_now_invite)

      sender = Sender.new(panelist, callback_url:, redirect_uri:)
      assert_enqueued_email_with(Scipis::ContractsMailer, :contract_ready, params: { panelist: }) do
        sender.run!
      end

      assert_equal('document_id_abc123', panelist.sign_now_contract_id)
      assert_equal('document_id_abc123', panelist.sign_now_contract_id)
      assert_equal sign_now_document, panelist.sign_now_document
    end
  end
end

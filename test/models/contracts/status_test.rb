# frozen_string_literal: true

require 'test_helper'

module Contracts
  class StatusTest < ActiveSupport::TestCase
    test 'contract unsent' do
      status = Status.new(sent: false, signed: false)

      assert 'Unsent', status.to_s
      assert status.unsent?
      assert_not status.sent?
      assert status.unsigned?
      assert_not status.signed?
    end

    test 'contract sent' do
      status = Status.new(sent: true, signed: false)

      assert 'Sent'
      assert_not status.unsent?
      assert status.sent?
      assert status.unsigned?
      assert_not status.signed?
    end

    test 'contract signed' do
      status = Status.new(sent: true, signed: true)

      assert 'Signed'
      assert_not status.unsent?
      assert status.sent?
      assert_not status.unsigned?
      assert status.signed?
    end

    test 'cannot be unsent and signed' do
      assert_raises(ArgumentError) { Status.new(sent: false, signed: true) }
    end
  end
end

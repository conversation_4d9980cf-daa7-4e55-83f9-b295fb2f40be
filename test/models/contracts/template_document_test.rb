# frozen_string_literal: true

require 'test_helper'

module Contracts
  class TemplateDocumentTest < ActiveSupport::TestCase
    test 'fields' do
      io = file_fixture_string_io('signnow_test_template.docx')

      template = Contracts::TemplateDocument.new(io:)
      fields = template.fields

      assert_includes fields, '{{panelist_signature}}'
    end

    test 'placeholders' do
      io = file_fixture_string_io('signnow_test_template.docx')

      template = Contracts::TemplateDocument.new(io:)
      placeholders = template.placeholders

      assert_includes placeholders, '__SCIPI_NAME__'
    end

    test 'create contract document' do
      io = file_fixture_string_io('signnow_test_template.docx')

      template = TemplateDocument.new(io:)
      document = template.create_contract_document(replacements: { '__SCIPI_NAME__' => 'Test SciPi #123' })

      document_text = document.to_s

      assert_match(/Test SciPi #123/, document_text)
      assert_no_match(/\[\[Test SciPi #123\]\]/, document_text)
      assert_no_match(TemplateDocument::PLACEHOLDER_PATTERN, document_text)
    end

    private

    def file_fixture_string_io(fixture_name)
      StringIO.new(file_fixture(fixture_name).read)
    end
  end
end

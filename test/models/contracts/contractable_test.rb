# frozen_string_literal: true

require 'test_helper'

module Contracts
  class ContractableTest < ActiveSupport::TestCase
    include ActiveJob::TestHelper

    test 'with sent contracts' do
      contract_template = FactoryBot.create(:contract_template)
      scipi = FactoryBot.create(:scipi, :contract_required, contract_template:)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:, contract_last_sent_at: Time.current)

      assert scipi.contracts_sent?
    end

    test 'with unsent contracts' do
      contract_template = FactoryBot.create(:contract_template)
      scipi = FactoryBot.create(:scipi, :contract_required, contract_template:)
      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)

      assert_not scipi.contracts_sent?
    end

    test 'enqueue create folder job' do
      scipi = FactoryBot.create(:scipi, contract_required: false, sign_now_contracts_folder_id: nil)

      assert_enqueued_with(job: CreateScipiFoldersJob) do
        scipi.update!(contract_required: true)
      end
    end

    test 'do not enqueue create folder job when already created' do
      scipi = FactoryBot.create(:scipi, contract_required: true, sign_now_contracts_folder_id: 'dummy_id')

      assert_no_enqueued_jobs { scipi.update!(contract_required: true) }
    end
  end
end

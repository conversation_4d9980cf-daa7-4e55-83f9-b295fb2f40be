# frozen_string_literal: true

require 'test_helper'

class ModerationTest < ActiveSupport::TestCase
  test 'lookup moderators' do
    moderator_role = FactoryBot.create(:role, name: 'Moderator')
    ENV['MODERATOR_ROLE_ID'] = moderator_role.id.to_s
    user = FactoryBot.create(:user, roles: [moderator_role])

    assert_equal [user], Moderation.moderators
  end

  test 'do not report AI score below threshold' do
    ENV['MODERATION_AI_SCORE_THRESHOLD'] = '0.5'

    assert_no_difference 'ModerationRequest.count' do
      Moderation.report_ai_score(Object.new, 0.4)
    end
  end

  test 'report AI score is above threshold' do
    ENV['MODERATION_AI_SCORE_THRESHOLD'] = '0.5'
    ai_detected_reason = ModerationReason.create!(name: 'AI Detected', description: 'Desc', position: 1)
    ENV['MODERATION_AI_DETECTED_REASON_ID'] = ai_detected_reason.id.to_s

    answer = FactoryBot.create(:ping_answer)

    assert_difference -> { answer.moderation_requests.count } do
      Moderation.report_ai_score(answer, 0.6)
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class CustomResultTest < ActiveSupport::TestCase
  test 'is grouped?' do
    result_definition = create(:grouped_result_definition)

    assert result_definition.grouped?, 'should be grouped?'
  end

  test 'is not grouped?' do
    result_definition = create(:result_definition)

    refute result_definition.grouped?, 'should not be grouped?'
  end

  test 'is pie chart' do
    render_type = FactoryBot.create(:render_type, :doughnut_chart)
    result_definition = create(
      :result_definition,
      :question,
      render_type:
    )

    assert result_definition.pie_chart?, 'should be pie chart'
  end

  test 'is not pie chart' do
    result_definition = create(:result_definition, :bar_chart)

    refute result_definition.pie_chart?, 'should not be pie chart'
  end

  test 'is bar chart' do
    render_type = FactoryBot.create(:render_type, :bar_chart)
    result_definition = create(
      :result_definition,
      :question,
      render_type:
    )

    assert result_definition.bar_chart?, 'should be bar chart'
  end

  test 'is not bar chart' do
    result_definition = create(:result_definition, :pie_chart)

    refute result_definition.bar_chart?, 'should not be bar chart'
  end

  test 'is text result' do
    result_definition = create(:text_result_definition)

    assert result_definition.text?, 'should be a text result'
  end

  test 'is not a text result' do
    result_definition = create(:result_definition, :pie_chart)

    refute result_definition.text?, 'should not be a text result'
  end

  test 'is grid result' do
    survey = FactoryBot.create(:survey)
    survey.questions << Questions::Grid.new
    result_type = FactoryBot.create(:result_type, :grid_responses)
    render_type = FactoryBot.create(:render_type, :grid_responses)

    result_definition = survey.result_definitions.create!(question: survey.questions.first, result_type:, render_type:)

    assert result_definition.grid?, 'should be a grid result'
  end

  test 'is not a grid result' do
    result_definition = create(:result_definition, :pie_chart)

    refute result_definition.grid?, 'should not be a grid result'
  end

  test 'question result title is question text' do
    title = Faker::Lorem.sentence

    question = FactoryBot.create(:question, question_text: title)

    result_definition = FactoryBot.create(
      :result_definition,
      :question,
      question:
    )

    assert_equal question.question_text,
                 result_definition.title,
                 'a question result\'s title should be the question text'
  end

  test 'has a render type' do
    render_type = create(:render_type)

    result_definition = create(:result_definition, render_type:)

    assert_equal render_type.value,
                 result_definition.render_type_value,
                 'render type value should come from the associated render_type'
  end

  test 'has comments' do
    survey = FactoryBot.create(:survey, created_by: FactoryBot.create(:expert))
    result_definition = FactoryBot.create(
      :result_definition,
      survey:,
      render_type: FactoryBot.create(:render_type, :diagnostic)
    )
    FactoryBot.create(
      :comment,
      debate_topic: result_definition,
      user: FactoryBot.create(:expert)
    )

    assert survey.result_definitions.comments?, 'should have comments'
  end

  test 'does not have  comments' do
    survey = FactoryBot.create(:survey, created_by: FactoryBot.create(:expert))
    FactoryBot.create(
      :result_definition,
      survey:,
      render_type: FactoryBot.create(:render_type, :diagnostic)
    )

    refute survey.result_definitions.comments?, 'should not have comments'
  end

  test 'create comment' do
    survey = FactoryBot.create(:survey, created_by: FactoryBot.create(:admin))
    result = FactoryBot.create(:result_definition, survey:)

    commenter = FactoryBot.create(:expert)

    assert_difference(-> { result.comments.count }) do
      result.create_comment!(commenter, content: 'I am a comment!!!')
    end
  end

  test 'explanations? is true when question allows explanations and has answers with explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: true)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission, explanation: 'This is my explanation')

    assert result_definition.explanations?,
           'should return true when question allows explanations and has answers with explanations'
  end

  test 'explanations? is false when question does not allow explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: false)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission, explanation: 'This is my explanation')

    assert_not result_definition.explanations?, 'should return false when question does not allow explanations'
  end

  test 'explanations? is false when question allows explanations but has no answers with explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: true)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission)

    assert_not result_definition.explanations?, 'should return false when no answers have explanations'
  end

  test 'explanations? is false when there is no associated question' do
    survey = create(:survey)
    result_definition = create(:result_definition, survey:, question: nil)

    assert_not result_definition.explanations?, 'should return false when result definition has no question'
  end

  test 'explanations? is false when question allows explanations but has only empty explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: true)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission, explanation: '')

    assert_not result_definition.explanations?, 'should return false when explanations are empty'
  end

  private

  attr_reader :survey, :question1, :a_custom_result
end

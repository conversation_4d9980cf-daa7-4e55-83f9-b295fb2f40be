# frozen_string_literal: true

require 'test_helper'

# rubocop:disable Metrics/ClassLength
class PingTest < ActiveSupport::TestCase
  test 'accepted answer' do
    ping = FactoryBot.create(:ping)
    FactoryBot.create(:ping_answer, :accepted, ping:)

    assert ping.accepted_answer?
  end

  test 'accept answer for ping' do
    ping = FactoryBot.create(:ping)
    answer = FactoryBot.create(:ping_answer, ping:)

    ping.accept_answer!(answer)

    assert answer.accepted?
  end

  test 'blacklist ping' do
    ping = FactoryBot.create(:ping)
    pings_admin = FactoryBot.create(:admin)
    ping.blacklist!(pings_admin)

    assert ping.blacklisted?
  end

  test 'change accepted answer for ping' do
    ping = FactoryBot.create(:ping)
    original_accepted = FactoryBot.create(:ping_answer, :accepted, ping:)
    new_accepted = FactoryBot.create(:ping_answer, ping:)

    ping.accept_answer!(new_accepted)

    assert new_accepted.accepted?
    assert_not original_accepted.reload.accepted?,
               'the previously accepted answer should no longer be accepted'
  end

  test 'accept multiple answers' do
    ping = FactoryBot.create(:ping, :allows_multiple_answers)
    answer1 = FactoryBot.create(:ping_answer, ping:)
    answer2 = FactoryBot.create(:ping_answer, ping:)

    ping.accept_answer!(answer1)
    ping.accept_answer!(answer2)

    assert answer1.accepted?
    assert answer2.accepted?
  end

  test 'undo accepted answer' do
    ping = FactoryBot.create(:ping)
    answer = FactoryBot.create(:ping_answer, :accepted, ping:)

    ping.undo_acceptance!(answer)

    assert_not answer.accepted?
  end

  test 'an expert cannot create a ping without a display name' do
    expert = FactoryBot.create(:expert)
    ping_attrs = FactoryBot.attributes_for(:ping)

    ping = expert.pings.create(ping_attrs)

    assert ping.errors.added?(:display_name, :blank)
  end

  test 'ping author' do
    expert = FactoryBot.create(:expert, :with_display_name)
    ping = FactoryBot.create(:ping, author: expert)

    assert ping.authored_by?(expert), 'should be the ping author'
  end

  test 'not ping author' do
    expert = FactoryBot.create(:expert)
    ping = FactoryBot.create(:ping)

    assert_not ping.authored_by?(expert), 'should not be the ping author'
  end

  test 'ping answered by' do
    ping = FactoryBot.create(:ping)
    expert = FactoryBot.create(:expert, :with_display_name)
    FactoryBot.create(:ping_answer, author: expert, ping:)

    assert ping.answered_by?(expert), 'has answered this ping'
  end

  test 'ping not answered by' do
    ping = FactoryBot.create(:ping)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:ping_answer, ping:)

    assert_not ping.answered_by?(expert), 'has answered this ping'
  end

  test 'ping answerable by' do
    expert = FactoryBot.create(:expert, :with_display_name)

    answered_ping = FactoryBot.create(:ping)
    FactoryBot.create(:ping_answer, author: expert, ping: answered_ping)

    unanswered_ping = FactoryBot.create(:ping)
    FactoryBot.create(:ping_answer, ping: unanswered_ping)

    created_by_ping = FactoryBot.create(:ping, author: expert)

    pings = Ping.ping_answerable_by(expert)

    assert_includes pings, unanswered_ping, 'should have found the unanswered_ping'
    assert_not_includes pings, answered_ping, 'should not have found the answered_ping'
    assert_not_includes pings, created_by_ping, 'should not have found the created_by_ping'
  end

  test 'paid ping in draft' do
    paid = pings_ping_types(:paid)
    ping = Ping.new(type: paid, published_at: nil)

    assert ping.draft?, 'should be a draft'
  end

  test 'drafts disallowed' do
    disallows_drafts = pings_ping_types(:public)
    expert = FactoryBot.create(:expert, :with_display_name)
    ping = Ping.create!(
      type: disallows_drafts,
      published_at: nil,
      author: expert,
      title: Faker::Lorem.sentence,
      content: Faker::Lorem.sentences.join("\n"),
      expertises: [expertises(:expertise1)]
    )

    assert_not ping.draft?, 'should not be a draft when the PingType disallows it'
  end

  test 'a ping with answers is not deleteable' do
    ping = FactoryBot.create(:ping, :published)
    FactoryBot.create(:ping_answer, ping:)

    assert_not ping.deleteable?, 'pings with answers are not deletable'
  end

  test 'a ping without answers is deleteable' do
    ping = FactoryBot.create(:ping, :published)

    assert ping.deleteable?, 'pings without answers are deletable'
  end

  test 'public (default) pings are published by default and voting is open' do
    ping = FactoryBot.create(:ping)

    assert ping.published?
    assert ping.voting_open?, 'voting should be open when public ping is created'
    assert_not_nil ping.last_active_at, 'last_active_at should be set when a public ping is created'
  end

  test 'publish ping' do
    ping = FactoryBot.create(:ping, :draft)

    ping.publish!

    assert ping.published?
    assert_not_nil ping.last_active_at
  end

  test 'publish ping with attributes' do
    ping = FactoryBot.create(:ping, :draft)

    title = Faker::Lorem.sentence
    content = Faker::Lorem.sentence

    ping.publish!(content: "<div>#{content}<div>", title:)

    assert ping.published?
    assert_not_nil ping.last_active_at
    assert_equal title, ping.title
    assert_equal content, ping.content.to_plain_text
  end

  test 'most recent Answer created_at is last_active_at' do
    ping = FactoryBot.create(:ping, published_at: 1.week.ago)
    FactoryBot.create(:ping_answer, ping:, created_at: 5.days.ago)
    most_recent = FactoryBot.create(:ping_answer, ping:, created_at: 1.day.ago)

    assert_equal most_recent.created_at, ping.last_active_at
  end

  test 'cannot open voting on draft Pings' do
    ping = FactoryBot.create(:ping, :paid, :draft)

    assert_raises(Ping::CannotAdjustVoting) do
      ping.open_voting!
    end
  end

  test 'voting unopened' do
    ping = FactoryBot.create(:ping, :paid, :published)

    assert ping.voting_unopened?, 'voting should be un-opened'
    assert_not ping.voting_opened?, 'voting not should be opened'
  end

  test 'open voting now' do
    ping = FactoryBot.create(:ping, :paid, :published)

    ping.open_voting!

    assert ping.voting_open?, 'voting should be open'
    assert ping.voting_opened?, 'voting should is not un-opened'
    assert_not ping.voting_unopened?, 'voting should is not un-opened'
  end

  test 'voting is not open when voting_opens_at is set in the future' do
    ping = FactoryBot.create(:ping, :paid, :published, voting_opens_at: 1.hour.from_now)

    assert_not ping.voting_open?, 'voting should not yet be open'
    assert_not ping.voting_opened?, 'voting should not yet be opened'
  end

  test 'voting is closed' do
    ping = FactoryBot.create(:ping, :paid, :voting_closed)

    assert_not ping.voting_unopened?, 'voting should is not un-opened'
    assert_not ping.voting_open?, 'voting should be closed'
    assert ping.voting_opened?, 'voting should be opened'
    assert ping.voting_closed?, 'voting should be closed'
  end

  test 'always show scores on public pings' do
    ping = FactoryBot.create(:ping)

    assert ping.scores_visible?, 'score should be visible'
  end

  test 'do not show scores on paid Pings without an accepted answer' do
    ping = FactoryBot.create(:ping, :paid, :published)

    assert_not ping.scores_visible?, 'score should be visible'
  end

  test 'show scores on paid Pings when an answer is accepted' do
    ping = FactoryBot.create(:ping, :paid, :published)
    FactoryBot.create(:ping_answer, :accepted, ping:)

    assert ping.scores_visible?, 'score should be visible'
  end

  test 'answering is not open on drafts' do
    ping = FactoryBot.create(:ping, :allows_drafts, :draft)

    assert_not ping.answering_open?, 'answering should not be open on drafts'
  end

  test 'answering is always open when voting deadlines are disallowed' do
    ping = FactoryBot.create(:ping, :paid, :published, :disallows_voting_deadlines)

    assert ping.answering_open?, 'answering should be open'
  end

  test 'answering is open when voting deadlines are allowed and voting is unopened' do
    ping = FactoryBot.create(:ping, :allows_voting_deadlines, :published)

    assert ping.answering_open?, 'answering should be open'
  end

  test 'answering is not open when voting deadlines are allowed and voting is opened' do
    ping = FactoryBot.create(:ping, :allows_voting_deadlines, :published, :voting_open)

    assert_not ping.answering_open?, 'answering should not be open when voting is open'
  end

  test 'authors are automatically subscribed to Pings' do
    author = FactoryBot.create(:expert, :with_display_name)
    public = FactoryBot.create(:ping_type)

    ping = author.pings.create!(type: public, title: 'Am I subscribed?', content: '<p>I should be subscribed</p>', expertises: [expertises(:expertise1)])

    assert ping.subscribed?(author)
  end

  test 'authors with auto-subscribe disabled are not automatically subscribed to Pings' do
    author = FactoryBot.create(:expert, :with_display_name, disabled_autosubscribe_at: Time.current)
    public = FactoryBot.create(:ping_type)

    ping = author.pings.create!(type: public, title: 'Am I subscribed?', content: '<p>I should be subscribed</p>', expertises: [expertises(:expertise1)])

    assert_not ping.subscribed?(author)
  end

  test 'add answer' do
    ping = FactoryBot.create(:ping)
    expert = FactoryBot.create(:expert, :with_display_name)
    content = Faker::Lorem.paragraphs.join

    assert_difference -> { ping.answers.count } do
      ping.add_answer(author: expert, content:)
    end
  end

  test 'add answer with display name' do
    ping = FactoryBot.create(:ping)
    expert = FactoryBot.create(:expert)
    content = Faker::Lorem.paragraphs.join
    display_name = Faker::Internet.username

    assert_difference -> { ping.answers.count } do
      ping.add_answer(author: expert, content:, display_name:)
    end

    assert_equal display_name, expert.display_name
  end

  test 'answer author is auto-subscribed' do
    ping = FactoryBot.create(:ping, :published)
    author = FactoryBot.create(:expert, disabled_autosubscribe_at: nil)
    content = Faker::Lorem.paragraphs.join

    ping.add_answer(author:, content:)

    assert ping.subscribed?(author), 'should be subscribed'
  end

  test 'unsent voting open notifications' do
    ping = FactoryBot.create(:ping, :paid, :voting_open)
    pings = Ping.unsent_voting_open_notifications

    assert ping.voting_open_notification_unsent?
    assert_includes pings, ping, 'should have found Ping that supports notifications, and voting open'
  end

  test 'do not send unsent voting open notifications when already sent' do
    ping = FactoryBot.create(:ping, :paid, :voting_open, voting_open_notification_sent_at: Time.current)

    pings = Ping.unsent_voting_open_notifications

    assert_not ping.voting_open_notification_unsent?
    assert_not_includes pings, ping, 'should not have found Ping when voting open notification is already sent'
  end

  test 'do not send voting open notifications when voting is not open yet' do
    ping = FactoryBot.create(:ping, :paid, :voting_unopened)

    pings = Ping.unsent_voting_open_notifications

    assert_not ping.voting_open_notification_unsent?
    assert_not_includes pings, ping, 'should not have found Ping when voting is open'
  end

  test 'do not send voting open notifications if voting is already closed' do
    ping = FactoryBot.create(:ping, :paid, :voting_closed, voting_open_notification_sent_at: nil)

    pings = Ping.unsent_voting_open_notifications

    assert_not ping.voting_open_notification_unsent?
    assert_not_includes pings, ping, 'should not have found Ping when voting is closed'
  end

  test 'do not send voting open notifications if not published' do
    ping = FactoryBot.create(:ping, :paid, :draft, voting_opens_at: Time.current)

    pings = Ping.unsent_voting_open_notifications

    assert_not ping.voting_open_notification_unsent?
    assert_not_includes pings, ping, 'should not have found Ping that is a draft'
  end

  test 'do not send voting open notifications if notifications are not supported' do
    ping = FactoryBot.create(:ping, :voting_open, voting_open_notification_sent_at: Time.current)

    pings = Ping.unsent_voting_open_notifications

    assert_not ping.voting_open_notification_unsent?
    assert_not_includes pings, ping, 'should not have found Ping when notifications are not supported'
  end

  test 'visible answers' do
    ping = FactoryBot.create(:ping)
    visible = FactoryBot.create(:ping_answer, ping:)
    hidden = FactoryBot.create(:ping_answer, :hidden, ping:)

    answers = ping.answers.visible

    assert_includes answers, visible
    assert_not_includes answers, hidden
  end

  test 'unsent accepted answer notifications' do
    ping = FactoryBot.create(:ping, :paid, :answer_accepted)

    assert ping.answer_accepted_notification_unsent?
  end

  test 'accepted answer notifications sent' do
    ping = FactoryBot.create(:ping, :answer_accepted, :paid, answer_accepted_notification_sent_at: Time.current)

    assert_not ping.answer_accepted_notification_unsent?
  end

  test 'featurable scope' do
    blacklisted = FactoryBot.create(:ping, :published, blacklisted_at: Time.current)
    featureable = FactoryBot.create(:ping, :published)

    pings = Ping.featureable

    assert_includes pings, featureable, 'should include featureable pings'
    assert_not_includes pings, blacklisted, 'should not include blacklisted pings'
  end

  test 'drafts scope' do
    draft = FactoryBot.create(:ping, :draft)
    published = FactoryBot.create(:ping, :published)

    pings = Ping.drafts

    assert_includes pings, draft, 'should include drafts pings'
    assert_not_includes pings, published, 'should not include published pings'
  end

  test 'relevant expertise' do
    expertises = [FactoryBot.create(:expertise)]
    relevant_ping = FactoryBot.create(:ping, :published, expertises:)

    pings = Ping.relevant(expertises)

    assert_includes pings, relevant_ping
  end

  test 'relevant expertise without dups' do
    expertise1 = FactoryBot.create(:expertise)
    expertise2 = FactoryBot.create(:expertise)
    relevant_ping = FactoryBot.create(:ping, :published, expertises: [expertise1, expertise2])

    pings = Ping.relevant([expertise1, expertise2])

    assert_equal 1, pings.count
    assert_equal relevant_ping, pings.sole
  end

  test 'not relevant expertise' do
    relevant_ping = FactoryBot.create(:ping, :published, expertises: [FactoryBot.create(:expertise)])
    not_relevant_expertises = [FactoryBot.create(:expertise)]

    pings = Ping.relevant(not_relevant_expertises)

    assert_not_includes pings, relevant_ping
  end

  test 'without_accepted_answers scope' do
    accepted_answer_pings = FactoryBot.create_list(:ping, 5, :published)
    accepted_answer_pings.each do |ping|
      FactoryBot.create(:ping_answer, ping:, accepted_at: Time.current)
    end

    # Create pings with a mix of accepted and non-accepted answers
    # These will count in the 'accepted answer ping' pile
    mixed_answer_pings = FactoryBot.create_list(:ping, 5, :published)
    mixed_answer_pings.each do |ping|
      FactoryBot.create(:ping_answer, ping:)
      FactoryBot.create(:ping_answer, ping:, accepted_at: Time.current)
    end

    # Create pings with only non-accepted answers
    answered_pings = FactoryBot.create_list(:ping, 5, :published)
    answered_pings.each do |ping|
      FactoryBot.create(:ping_answer, ping:)
    end

    # Create pings without any answers at all
    unanswered_pings = FactoryBot.create_list(:ping, 5, :published)

    pings = Ping.without_accepted_answers

    assert_equal Ping.count, 20
    assert_equal pings.count, 10

    accepted_answer_pings.each do |ping|
      assert_not_includes pings, ping
    end

    mixed_answer_pings.each do |ping|
      assert_not_includes pings, ping
    end

    answered_pings.each do |ping|
      assert_includes pings, ping
    end

    unanswered_pings.each do |ping|
      assert_includes pings, ping
    end
  end

  test 'promoted_relevant_random contains promoted and relevant pings first' do
    FactoryBot.create_list(:ping, 5, :published) # Filler pings
    relevant = FactoryBot.create(:ping, :published)
    expertise = FactoryBot.create(:expertise)
    relevant.expertises << expertise
    promoted = FactoryBot.create(:ping, :published, :promoted)

    pings = Ping.promoted_relevant_random(expertises: [expertise])

    assert_equal promoted, pings.first
    assert_equal relevant, pings.second
  end

  test 'promoted_relevant_random excludes Pings with accepted answers' do
    expertise = FactoryBot.create(:expertise)
    ping = FactoryBot.create(:ping, :published, :answer_accepted)
    ping.expertises << expertise

    pings = Ping.promoted_relevant_random(expertises: [expertise])

    assert_not_includes pings, ping
  end

  test 'promoted_relevant_random excludes draft pings' do
    ping = FactoryBot.create(:ping, :draft)

    pings = Ping.promoted_relevant_random(expertises: [])

    assert_not_includes pings, ping
  end

  test 'promoted_relevant_random contains filler pings up to max_pings' do
    max_pings = 5

    filler_pings = FactoryBot.create_list(:ping, max_pings, :published)
    relevant = FactoryBot.create(:ping, :published)
    expertise = FactoryBot.create(:expertise)
    relevant.expertises << expertise

    pings = Ping.promoted_relevant_random(expertises: [expertise], max_pings:)

    assert_equal max_pings, pings.count
    pings.each do |ping|
      if ping == pings.first
        assert_equal ping, relevant
      else
        assert filler_pings.include?(ping)
      end
    end
  end

  test 'promoted_relevant_random contains no dups' do
    max_pings = 5
    promoted = FactoryBot.create(:ping, :published, :promoted)
    filler_pings = FactoryBot.create_list(:ping, max_pings, :published)
    relevant = FactoryBot.create(:ping, :published)
    expertise = FactoryBot.create(:expertise)
    filler_pings.each do |ping|
      ping.expertises << expertise
    end
    relevant.expertises << expertise
    promoted.expertises << expertise

    pings = Ping.promoted_relevant_random(expertises: [expertise], max_pings:)

    assert pings.count == max_pings + 1 # +1 for the promoted ping
    assert_equal pings.uniq(&:id).count, pings.count
  end

  test 'promoted_relevant_random excludes blacklisted pings' do
    blacklisted_ping_count = 3
    featurable_ping_count = 2
    starting_ping_count = Ping.count # Test database seems to already include some pings that we need to account for

    blacklisted_ping_count.times { FactoryBot.create(:ping, :published, blacklisted_at: Time.current) }
    featurable_ping_count.times { FactoryBot.create(:ping, :published) }

    pings = Ping.promoted_relevant_random(expertises: [])

    assert_equal pings.count(&:blacklisted?), 0
    assert_equal pings.count(&:featureable?), starting_ping_count + featurable_ping_count
  end

  test 'to_feed_listing test' do
    base_url = 'test.com/'
    ping = FactoryBot.create(:ping, :paid, :answer_accepted)
    url = base_url + ping.product_uri
    content = ping.content.body.to_plain_text.truncate(200, separator: ' ')

    expected_listing = Feeds::Listing.new(
      id: ping.id,
      type: 'ping',
      title: ping.title,
      content:,
      url:,
      status_label: "#{ping.answers.count} Answers",
      status_class: 'success'
    )

    assert_equal ping.to_feed_listing(base_url:).to_json, expected_listing.to_json
  end
end
# rubocop:enable Metrics/ClassLength

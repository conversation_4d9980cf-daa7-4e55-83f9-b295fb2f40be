# frozen_string_literal: true

require 'test_helper'

class ConfirmationTest < ActiveSupport::TestCase
  test 'confirm!' do
    user = FactoryBot.create(:expert, :unconfirmed)

    confirmation = Confirmation.find(user.confirmation_token)
    confirmation.confirm!

    assert confirmation.confirmed?, 'should be confirmed'
    assert_equal Pings::CONFIRMATION_CREDIT_AMOUNT, user.reload.ping_credits
  end
end

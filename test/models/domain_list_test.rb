# frozen_string_literal: true

require 'test_helper'

class DomainListTest < ActiveSupport::TestCase
  test 'get list for an unknown tld' do
    assert_equal DomainList.default, DomainList.default_for(public_suffix: 'com')
  end

  test 'get list for a specific tld' do
    net_list = DomainList.create!(name: 'Test', tlds: ['net'])

    assert_equal net_list, DomainList.default_for(public_suffix: 'net')
  end

  test 'get list for a tld fragment' do
    net_list = DomainList.create!(name: 'Test', tld_fragments: ['net.'])

    assert_equal net_list, DomainList.default_for(public_suffix: 'net.eg')
  end

  test 'contains domain for' do
    list = DomainList.first!
    Domain.create!(list: list, hostname: 'example.com')

    assert list.contain_email_domain?('<EMAIL>')
  end

  test 'does not contain domain for' do
    list = DomainList.first!
    list.domains.create!(hostname: 'example.com')

    assert_not list.contain_email_domain?('<EMAIL>')
  end
end

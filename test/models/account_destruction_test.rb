# frozen_string_literal: true

require 'test_helper'

class AccountDestructionTest < ActiveSupport::TestCase
  test 'user with only profile data is destroyable' do
    user = FactoryBot.create(:expert)
    account_destruction = AccountDestruction.new(user)

    assert account_destruction.destroyable?
  end

  test 'user with submissions is NOT destroyable' do
    user = FactoryBot.create(:expert)
    survey = FactoryBot.create(:survey)
    FactoryBot.create(:submission, user:, survey:)

    account_destruction = AccountDestruction.new(user)

    assert_not account_destruction.destroyable?
  end

  test 'user with pings is NOT destroyable' do
    author = FactoryBot.create(:expert, :with_display_name)
    FactoryBot.create(:ping, author:)

    account_destruction = AccountDestruction.new(author)

    assert_not account_destruction.destroyable?
  end

  test 'AccountDestruction destroys user AND non-cascading data (like degrees)' do
    user = FactoryBot.create(:expert, :education_complete)

    user_degree_id = user.degrees.first.id
    user_id = user.id

    assert_not_nil user_id
    assert_not_nil user_degree_id

    account_destruction = AccountDestruction.new(user)
    account_destruction.save

    assert_nil User.find_by(id: user_id)
    assert_nil Degree.find_by(id: user_degree_id)
  end

  test 'AccountDestruction validation checks destroyable? before saving' do
    non_destructable_user = FactoryBot.create(:expert)
    survey = FactoryBot.create(:survey)
    FactoryBot.create(:submission, user: non_destructable_user, survey:)

    account_destruction = AccountDestruction.new(non_destructable_user)
    account_destruction.save

    assert_equal User.find_by(id: non_destructable_user.id), non_destructable_user
  end
end

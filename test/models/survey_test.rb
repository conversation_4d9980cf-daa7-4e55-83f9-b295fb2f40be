# frozen_string_literal: true

require 'test_helper'

class SurveyTest < ActiveSupport::TestCase
  setup do
    @user1 = create(:expert, first_name: '<PERSON>', last_name: '<PERSON><PERSON>')
    @user2 = create(:expert, first_name: '<PERSON>')
    @user3 = create(:expert, first_name: 'Pippin')
    @survey1 = create(:survey, published_at: 1.week.ago, results_published: true, created_by_id: @user1.id)
    @question1 = create(:question, survey: @survey1, position: 1, type: 'Questions::Grid')
    create(:grid_structure, input_type: 'Select', question: @question1)
    @survey2 = create(:survey, closes_at: 1.day.ago, access_code: nil, created_by_id: @user1.id)
    @survey3 = create(:survey, published_at: 1.week.ago, results_published: false, created_by_id: @user2.id)
    @survey4 = create(:survey, published: false, results_published: true, created_by_id: @user1.id)
    @answer_group1 = create(:submission,
                            survey: @survey1,
                            user: @user1)
    create(:submission, survey: @survey1, user: @user3)
    create(:submission, survey: @survey2, user: @user3)
    create(:submission, survey: @survey3, user: @user3)
    create(:submission, survey: @survey4, user: @user3)
  end

  test 'requires access code' do
    survey = create(:survey, :access_code_only)

    assert survey.requires_access_code?, 'should require an access code'
  end

  test 'does not require access code' do
    survey = create(:survey)

    refute survey.requires_access_code?, 'should not require an access code'
  end

  test 'draft surveys are a draft?' do
    draft = create(:survey, :draft)
    assert draft.draft?, 'should be a draft'
  end

  test 'published surveys are not a draft?' do
    draft = create(:survey, :published)
    refute draft.draft?, 'should not be a draft'
  end

  test 'results published' do
    assert_equal true, @survey1.results_published?
    assert_equal false, @survey2.results_published?
  end

  test 'validates uniqueness of access code' do
    access_code = 'NOT_UNIQUE'
    create(:survey, access_code:)
    survey = build(:survey, access_code:)

    survey.save

    assert survey.errors[:access_code]
                 .include?('That access code is already in use.')
  end

  test 'validates uniqueness of access code only when present' do
    survey3 = build(:survey, access_code: nil, created_by_id: @user1.id)
    assert survey3.save, "Surveys can't have same access code even when it is null"
  end

  test 'results published scope' do
    assert_same_elements [@survey1, @survey4], QuestionGroup.results_published
  end

  test 'make a copy of the survey' do
    expertise1 = expertises(:expertise1)
    arbitrary_date = Time.zone.now.to_date
    landing_content = Faker::Lorem.paragraph

    source_survey = create(:question_group, closes_at: arbitrary_date, comments_close_date: arbitrary_date, recruitment_closes_on: arbitrary_date, expertises: [expertise1], landing_content:)

    question1 = create(:question, question_group: source_survey)
    creator = source_survey.owners.first

    new_survey = source_survey.make_copy(creator)

    assert_equal "#{source_survey.name} (copy)", new_survey.name
    assert_equal creator, new_survey.created_by
    assert_equal question1.question_text, new_survey.questions.first.question_text
    assert_equal expertise1, new_survey.expertises.first
    assert_equal ActionText::Content.new(landing_content).to_plain_text, new_survey.landing_content.to_plain_text
    assert_not_equal source_survey.closes_at, new_survey.closes_at
    assert_nil new_survey.closes_at
    assert_nil new_survey.comments_close_date
    assert_nil new_survey.recruitment_closes_on
  end

  test 'no one can participate in a draft survey' do
    public_survey = create(:survey, :draft)
    user = create(:expert)

    assert_cannot_participate public_survey, user
  end

  test 'any user can participate in a published public survey' do
    public_survey = create(:survey, :general_participation, :published)
    user = create(:expert)

    assert_can_participate public_survey, user
  end

  test 'a user with an access code can participate in an access-code-only survey' do
    access_code_only = create(:survey, :access_code_only, :published)
    user = create(:expert)
    access_code_only.user_question_group_access_codes.create!(user:, access_code: access_code_only.access_code)

    assert_can_participate access_code_only, user
  end

  test 'can comment by default' do
    survey = create(:survey)

    assert survey.debate_open?, 'should be able to comment on survey by default'
  end

  test 'can comment when before comments close date' do
    survey = create(:survey, :results_published, comments_close_date: 1.month.from_now)

    assert survey.debate_open?, 'should be able to comment on survey before the comments close date'
  end

  test 'cannot comment when after the comments close date' do
    survey = create(:survey, :results_published, comments_close_date: 1.week.ago)

    refute survey.debate_open?, 'should not be able to comment on survey after the comments close date'
  end

  test 'cannot comment when the results are not published (legacy behavior)' do
    skip 'Comment gating is controlled independent from results being published'
    survey = create(:survey, results_published: false)

    refute survey.debate_open?, 'should not be able to comment on survey when the results are not published'
  end

  test 'cannot comment when the comment date is past' do
    survey = create(:survey, :results_published, comments_close_date: 1.week.ago)

    refute survey.debate_open?, 'should not be able to comment on survey when close date is past'
  end

  test 'create un-grouped question result definition' do
    survey = create(:survey)
    question = create(:question, survey:)
    result_type = FactoryBot.create(:result_type, :grouped_multiple_choice_responses)

    result_definition = survey.create_result_definition!(
      question_id: question.id,
      result_type:,
      render_type: result_type.default_render_type
    )

    assert_equal question, result_definition.question
  end

  test 'create grouped question result definition' do
    survey = create(:survey)
    base_question = create(:question, survey:)
    group_by_question = create(:radio, survey:)
    result_type = FactoryBot.create(:result_type, :grouped_multiple_choice_responses)

    result_definition = survey.create_result_definition!(
      question_id: base_question.id,
      group_by_question_id: group_by_question.id,
      result_type:,
      render_type: result_type.default_render_type
    )

    assert_equal base_question, result_definition.question
    assert_equal group_by_question, result_definition.group_by_question
  end

  test 'create free form result' do
    FactoryBot.create(:render_type, :ranked_order_plot)
    FactoryBot.create(:result_type, :free_form)
    survey = create(:survey)
    title = 'my title'
    x_label_value = 'x label'
    y_label_value = 'y label'
    data = {
      'label 1' => 23,
      'label 2' => 93
    }
    data_set = CsvFreeFormDataParser::CsvDataSet.new(x_label_value, y_label_value, data)

    result_def = survey.create_free_form_result!(
      title:,
      data_set:
    )

    assert_equal title, result_def.title
    assert_equal x_label_value, result_def.x_label
    assert_equal y_label_value, result_def.y_label
    assert_equal data.count, result_def.data_points.count
    assert result_def.ranked_order_plot?, 'should be a ranked order plot'
  end

  test 'create file attachment result' do
    FactoryBot.create(:render_type, :file_download)
    FactoryBot.create(:result_type, :file_attachment)
    survey = create(:survey)
    title = 'my title'
    file = uploaded_fixture_file('test_attachment.pdf')

    result_def = survey.create_file_attachment_result!(
      title:,
      attachment: file
    )

    assert_equal title, result_def.title
    assert_not_nil result_def.attachment
    assert result_def.file_download?, 'should be a file download type'
  end

  test 'next question position' do
    current_count = 2
    survey = create(:survey)
    current_count.times { create(:question, survey:) }
    next_position = current_count + 1

    assert_equal next_position,
                 survey.next_question_position,
                 "the next question position should be #{next_position}"
  end

  test 'general participation' do
    survey = QuestionGroup.new(invite_only: false, access_code: nil)

    assert survey.general_participation?,
           'should be general participation code when not invite-only and without an access code'
  end

  test 'invite-only are not general participation' do
    survey = QuestionGroup.new(invite_only: true, access_code: nil)

    assert_not survey.general_participation?, 'invite-only should not be general participation'
  end

  test 'access code surveys are not participation' do
    survey = QuestionGroup.new(invite_only: false, access_code: 'foobar')

    assert_not survey.general_participation?, 'access code surveys should not be general participation'
  end

  test 'add first section' do
    survey = FactoryBot.create(:survey)

    section = assert_difference(-> { survey.question_sections.count }) do
      survey.add_question_section!(name: 'foo')
    end

    assert_equal 1, section.position
  end

  test 'existing questions get moved to first section' do
    survey = FactoryBot.create(:survey)
    FactoryBot.create(:question, survey:)

    section = survey.add_question_section!(name: 'foo')

    assert survey.questions.all? { |q| q.section_id == section.id },
           'all unsectioned questions should be moved to the first section'
  end

  test 'add section' do
    survey = FactoryBot.create(:survey)
    existing_section = FactoryBot.create(:question_section, survey:, position: 1)
    FactoryBot.create(:question, survey:, section: existing_section)

    section = assert_difference(-> { survey.question_sections.count }) do
      survey.add_question_section!(name: 'second')
    end

    assert_equal 2, section.position
    assert section.questions.empty?
  end

  test 'create file upload question' do
    survey = create(:survey)

    assert_difference(-> { survey.questions.count }) do
      survey.create_question!(
        allow_answer_explanation: '1',
        answer_required: '0',
        question_text: Faker::Lorem.sentence,
        question_details: Faker::Lorem.sentences(number: 2),
        section: nil,
        type: Questions::FileUpload.name
      )
    end
  end

  test 'bulk draft an answer group' do
    survey = create(:survey)
    user = create(:expert)
    create(:submission, survey:, user:)

    survey.bulk_submit_or_draft(false, [user.id])

    assert_nil survey.submission_for(user).submitted_at
  end

  test 'bulk submit an answer group' do
    survey = create(:survey)
    user = create(:expert)
    create(:submission, survey:, user:, submitted_at: nil)

    survey.bulk_submit_or_draft(true, [user.id])

    assert_not_nil survey.submission_for(user).submitted_at
  end

  test 'bulk submit action ignores users without answer groups' do
    survey = create(:survey)
    user = create(:expert)
    create(:submission, survey:, user:)
    user2 = create(:expert)

    count = survey.bulk_submit_or_draft(false, [user.id, user2.id])

    assert_equal 1, count
  end

  test 'create result section' do
    survey = create(:survey)

    assert_difference(-> { survey.result_sections.count }) do
      section = survey.create_result_section!(name: 'Section name', hidden: false)

      assert_equal 1, section.position
    end
  end

  test 'create result section appends to the last position' do
    survey = create(:survey)

    survey.create_result_section!(name: 'First', hidden: false)
    section = survey.create_result_section!(name: 'Next', hidden: false)

    assert_equal 2, section.position
  end

  test 'create submission score result definition' do
    FactoryBot.create(:render_type, :ranked_order_plot)
    FactoryBot.create(:result_type, :submission_score)
    survey = create(:survey)

    assert_difference(-> { survey.result_definitions.count }) do
      survey.create_submission_score_result_definition!(title: 'This is a title')
    end
  end

  test 'delete question section after first' do
    survey = question_groups(:draft)
    first = survey.question_sections.create!(name: 'First', position: 1)
    second = survey.question_sections.create!(name: 'Second', position: 2)
    third = survey.question_sections.create!(name: 'Third', position: 3)

    survey.delete_question_section!(first)

    assert first.destroyed?, 'first should be destroyed'
    assert_equal 1,
                 second.reload.position,
                 'the second section should now be the first'
    assert_equal 2,
                 third.reload.position,
                 'the third section should now be the second'
  end

  test 'append results without sections' do
    FactoryBot.create(:result_type, :multiple_choice_responses)
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    question_count = 5
    result_count = 3
    question_count.times do |i|
      question = FactoryBot.create(:radio, survey:, position: i + 1)
      FactoryBot.create(:result_definition, question:, survey:, position: i + 1) if i < result_count
    end

    assert_equal survey.questions.count, question_count, 'should have 5 questions before proceeding'
    assert_equal survey.result_definitions.count, result_count, 'should have 3 results before proceeding'
    assert_equal survey.questions_without_results.count, question_count - result_count, 'should have 2 missing results before  proceeding'

    assert_difference(-> { survey.result_definitions.count }, question_count - result_count) do
      survey.append_result_definitions!
    end
  end

  test 'append results with sections' do
    FactoryBot.create(:result_type, :multiple_choice_responses)
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section_count = 3
    question_count = 5
    result_count = 2
    section_count.times do |i|
      section = FactoryBot.create(
        :question_section,
        survey:,
        position: i + 1
      )
      result_section = FactoryBot.create(
        :result_section,
        survey:,
        position: i + 1
      )
      question_count.times do |j|
        question = FactoryBot.create(
          :radio,
          survey:,
          section:,
          position: j + 1
        )
        FactoryBot.create(:result_definition, question:, survey:, section: result_section, position: j + 1) if j < result_count
      end
    end

    missing_count = (section_count * question_count) - (section_count * result_count)

    assert_equal survey.questions.count, section_count * question_count, 'should have 15 questions before proceeding'
    assert_equal survey.result_definitions.count, section_count * result_count, 'should have 6 results before proceeding'
    assert_equal survey.questions_without_results.count, (section_count * question_count) - (section_count * result_count), 'should have 9 missing results before  proceeding'

    assert_difference(-> { survey.result_definitions.count }, missing_count) do
      survey.append_result_definitions!
    end

    last_section_count = result_count + missing_count

    assert_equal survey.result_sections.order(:position).last.result_definitions.count, last_section_count, 'should have 11 results in last section'
  end

  test 'sync with sections' do
    FactoryBot.create(:result_type, :multiple_choice_responses)
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section_count = 3
    question_count = 2
    section_count.times do |i|
      section = FactoryBot.create(
        :question_section,
        survey:,
        position: i + 1
      )
      question_count.times do |j|
        FactoryBot.create(
          :radio,
          survey:,
          section:,
          position: j + 1
        )
      end
    end

    assert_difference(-> { survey.result_sections.count }, section_count) do
      assert_difference(
        -> { survey.result_definitions.count },
        section_count * question_count
      ) do
        survey.sync_result_definitions!
      end
    end
  end

  test 'sync without sections' do
    FactoryBot.create(:result_type, :multiple_choice_responses)
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    question_count = 6
    question_count.times do |i|
      FactoryBot.create(:radio, survey:, position: i + 1)
    end

    assert_difference(-> { survey.result_definitions.count }, question_count) do
      survey.sync_result_definitions!
    end
  end

  test 'cannot sync with debate threads' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    question = FactoryBot.create(:question, survey:, position: 1)
    result_def = FactoryBot.create(
      :result_definition,
      question:,
      survey:,
      position: 1
    )
    FactoryBot.create(
      :comment,
      debate_topic: result_def,
      user: FactoryBot.create(:expert)
    )

    assert_raise(Survey::SyncProhibited) do
      survey.sync_result_definitions!
    end
  end

  test 'move result within section' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section = FactoryBot.create(:result_section, survey:)
    result1 = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 1
    )
    result2 = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 2
    )
    result3 = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 3
    )

    new_position = 2

    survey.move_result_definition!(result1, section, new_position)

    assert_equal 1, result2.reload.position
    assert_equal 2, result1.reload.position
    assert_equal 3, result3.reload.position
  end

  test 'move result across sections' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    source_section = FactoryBot.create(
      :result_section,
      position: 1,
      survey:
    )
    result_to_move = FactoryBot.create(
      :result_definition,
      survey:,
      section: source_section,
      position: 1
    )
    source_second = FactoryBot.create(
      :result_definition,
      survey:,
      section: source_section,
      position: 2
    )
    source_third = FactoryBot.create(
      :result_definition,
      survey:,
      section: source_section,
      position: 3
    )

    target_section = FactoryBot.create(
      :result_section,
      position: 2,
      survey:
    )
    target_first = FactoryBot.create(
      :result_definition,
      survey:,
      section: target_section,
      position: 1
    )
    target_second = FactoryBot.create(
      :result_definition,
      survey:,
      section: target_section,
      position: 2
    )

    new_position = 2

    survey.move_result_definition!(result_to_move, target_section, new_position)

    # New source positions
    assert_equal 1, source_second.reload.position
    assert_equal 2, source_third.reload.position

    # New target positions
    assert_equal 1, target_first.reload.position
    assert_equal 2, result_to_move.reload.position
    assert_equal 3, target_second.reload.position
  end

  test 'move results to lower position within unsectioned survey' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      position: 2
    )
    third = FactoryBot.create(
      :result_definition,
      survey:,
      position: 3
    )
    fourth = FactoryBot.create(
      :result_definition,
      survey:,
      position: 4
    )

    new_position = 2

    survey.move_result_definition!(third, survey, new_position)

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
    assert_equal 3, second.reload.position
    assert_equal 4, fourth.reload.position
  end

  test 'move results to higher position within unsectioned survey' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      position: 2
    )
    third = FactoryBot.create(
      :result_definition,
      survey:,
      position: 3
    )
    fourth = FactoryBot.create(
      :result_definition,
      survey:,
      position: 4
    )

    new_position = 3

    survey.move_result_definition!(second, survey, new_position)

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
    assert_equal 3, second.reload.position
    assert_equal 4, fourth.reload.position
  end

  test 'destroy result definition' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      position: 2
    )
    third = FactoryBot.create(
      :result_definition,
      survey:,
      position: 3
    )

    assert_difference(-> { CustomResult.count }, -1) do
      assert_difference(-> { survey.result_definitions.count }, -1) do
        survey.destroy_result_definition!(second)
      end
    end

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
  end

  test 'move section to lower position within survey' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    first = FactoryBot.create(
      :result_section,
      survey:,
      position: 1
    )
    second = FactoryBot.create(
      :result_section,
      survey:,
      position: 2
    )
    third = FactoryBot.create(
      :result_section,
      survey:,
      position: 3
    )
    fourth = FactoryBot.create(
      :result_section,
      survey:,
      position: 4
    )

    new_position = 2

    survey.move_result_section!(third, new_position)

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
    assert_equal 3, second.reload.position
    assert_equal 4, fourth.reload.position
  end

  test 'move section to higher position within survey' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    first = FactoryBot.create(
      :result_section,
      survey:,
      position: 1
    )
    second = FactoryBot.create(
      :result_section,
      survey:,
      position: 2
    )
    third = FactoryBot.create(
      :result_section,
      survey:,
      position: 3
    )
    fourth = FactoryBot.create(
      :result_section,
      survey:,
      position: 4
    )

    new_position = 3

    survey.move_result_section!(second, new_position)

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
    assert_equal 3, second.reload.position
    assert_equal 4, fourth.reload.position
  end

  test 'submission for user' do
    survey = FactoryBot.create(:survey)
    participant = FactoryBot.create(:expert)
    submission = FactoryBot.create(
      :submission,
      survey:,
      user: participant
    )

    assert_equal submission,
                 survey.submission_for(participant),
                 'should have a submission for this participant'
  end

  test 'destroy question without section' do
    survey = FactoryBot.create(:survey)
    question1 = FactoryBot.create(:question, survey:)
    question2 = FactoryBot.create(:question, survey:)
    question3 = FactoryBot.create(:question, survey:)

    assert_difference(-> { survey.questions.count }, -1) do
      survey.destroy_question!(question2)
    end

    assert_equal 1, question1.reload.position,
                 'should be in position 1'
    assert_equal 2, question3.reload.position,
                 'should have moved up to position 2'
  end

  test 'destroy question in a section' do
    survey = FactoryBot.create(:survey)
    section = FactoryBot.create(:question_section, survey:)
    question1 = FactoryBot.create(:question, survey:, section:)
    question2 = FactoryBot.create(:question, survey:, section:)
    question3 = FactoryBot.create(:question, survey:, section:)

    assert_difference(-> { survey.questions.count }, -1) do
      survey.destroy_question!(question2)
    end

    assert_equal 1, question1.reload.position,
                 'should be in position 1'
    assert_equal 2, question3.reload.position,
                 'should have moved up to position 2'
  end

  test 'cannot delete a question with answers' do
    survey = FactoryBot.create(:survey)
    question = FactoryBot.create(:question, survey:)
    FactoryBot.create(:answer, question:)

    assert_no_difference(-> { survey.questions.count }) do
      assert_raise(RuntimeError, 'You cannot delete an answered question.') do
        survey.destroy_question!(question)
      end
    end
  end

  test 'drafts without submissions are deleteable' do
    survey = question_groups(:draft)

    assert survey.deleteable?, 'drafts should be deleteable'
  end

  test 'published without submissions are not deleteable' do
    survey = FactoryBot.create(:survey, :published)

    assert_not survey.deleteable?, 'published surveys should not be deleteable'
  end

  test 'drafts with submissions are not deleteable' do
    # this condition can occur between rounds
    survey = FactoryBot.create(:survey, :draft)
    user = users(:expert)
    survey.submissions.create!(submitter: user)

    assert_not survey.deleteable?, 'surveys with submissions should not be deleteable'
  end

  test 'open scope' do
    AnswerGroup.destroy_all
    QuestionGroup.destroy_all

    survey1 = FactoryBot.create(:survey)
    survey2 = FactoryBot.create(:survey, closes_at: 1.day.from_now)
    FactoryBot.create(:survey, :closed)

    assert_same_elements [survey1, survey2], QuestionGroup.open
  end

  test 'effective_closes_at is EOD UTC-11' do
    closes_at = 1.day.ago
    survey = FactoryBot.build(:survey, closes_at:)

    expected_close = closes_at.to_date.in_time_zone('Pacific/Pago_Pago').end_of_day

    assert_equal expected_close, survey.send(:effective_closes_at), 'the effective closes_at time should be 23:59:59 UTC-11'
  end

  test 'a survey without a close date is open' do
    survey = FactoryBot.build(:survey, closes_at: nil)

    assert survey.open?, 'surveys without close dates set are open'
    assert_not survey.closed?, 'surveys without close dates set are open'
  end

  test 'a survey with a future close date is open' do
    survey = FactoryBot.build(:survey, :open)

    assert survey.open?, 'surveys with a future close date are open'
    assert_not survey.closed?, 'surveys with a future close date are open'
  end

  test 'a survey with a past close date is closed' do
    survey = FactoryBot.build(:survey, :closed)

    assert_not survey.open?, 'surveys with a past close date are closed'
    assert survey.closed?, 'surveys with a past close date are closed'
  end

  test 'survey with old close date is closed_for_a_while' do
    survey = FactoryBot.build(:survey, closes_at: 2.years.ago)

    assert survey.closed_for_a_while?, 'surveys with old close date are closed_for_a_while'
  end

  test 'survey without closed date is not closed_for_a_while' do
    survey = FactoryBot.build(:survey)

    assert_not survey.closed_for_a_while?, 'open survey is not closed for a while'
  end

  test 'survey without recent closed date is not closed_for_a_while' do
    survey = FactoryBot.build(:survey, closes_at: Time.current)

    assert_not survey.closed_for_a_while?, 'open survey is not closed for a while'
  end

  test 'debate_closes_on' do
    comments_close_date = Time.zone.local(2023, 3, 1, 19, 30, 45)
    debate_closes_on = Date.new(2023, 3, 1)

    survey = QuestionGroup.new(comments_close_date:)

    assert_equal debate_closes_on, survey.debate_closes_on
  end

  test 'debate_closes_at is the end of the day in the UTC-11' do
    comments_close_date = Time.zone.local(2023, 3, 1, 19, 30, 45)
    eod_in_pago_pago = Time.in_last_time_zone.local(2023, 3, 1, 23, 59, 59).change(nsec: 999_999_999) # rubocop:disable Rails/TimeZone

    survey = QuestionGroup.new(comments_close_date:)

    assert_equal eod_in_pago_pago, survey.debate_closes_at
  end

  test 'debate is open when no close date is set' do
    survey = QuestionGroup.new(comments_close_date: nil)

    assert survey.debate_open?
  end

  test 'debate is open when close date has not passed in the last time zone' do
    closes_at = Time.zone.today

    survey = QuestionGroup.new(comments_close_date: closes_at)

    assert survey.debate_open?
  end

  test 'debate is close when close date has passed at in the last time zone' do
    closes_at = 2.days.ago

    survey = QuestionGroup.new(comments_close_date: closes_at)

    assert_not survey.debate_open?
  end

  test 'debate is not open if not a debate round' do
    scipi = FactoryBot.create(:scipi, :published, :invite_only)
    round = FactoryBot.create(:question_round, scipi:)

    travel_to 1.hour.after(round.opens_at) do
      assert_not scipi.debate_open?
    end
  end

  test 'debate is not open if debate round is not open' do
    scipi = FactoryBot.create(:scipi, :published, :invite_only)
    round = FactoryBot.create(:debate_round, scipi:)

    travel_to 1.hour.before(round.opens_at) do
      assert_not scipi.debate_open?
    end
  end

  test 'debate is open' do
    scipi = FactoryBot.create(:scipi, :published, :invite_only)
    round = FactoryBot.create(:debate_round, scipi:)

    travel_to 1.hour.after(round.opens_at) do
      assert scipi.debate_open?
    end
  end

  test 'to_feed_listing test' do
    base_url = 'test.com/'
    survey = FactoryBot.create(:survey)
    url = base_url + survey.product_uri

    expected_listing = Feeds::Listing.new(
      id: survey.id,
      type: survey.branding.name.parameterize,
      title: survey.title,
      content: survey.description,
      url:,
      status_label: '',
      status_class: ''
    )

    assert_equal survey.to_feed_listing(base_url:).to_json, expected_listing.to_json
  end

  test 'submission deadline not set without rounds' do
    survey = FactoryBot.create(:scipi, :published, closes_at: nil)

    assert_nil survey.submission_deadline
  end

  test 'submission deadline without rounds' do
    # keep in the middle of the day, weirdness happens nearer to midight which causes test failures for dependabot
    closes_at = 1.day.from_now.change(hour: 12)
    deadline = closes_at.in_time_zone('Pacific/Pago_Pago').end_of_day

    survey = FactoryBot.create(:scipi, :published, closes_at:)

    assert_equal deadline, survey.submission_deadline
  end

  test 'submission deadline for currently open rounds' do
    survey = FactoryBot.create(:scipi, :published, closes_at: 1.week.from_now)
    current_round = FactoryBot.create(:question_round, survey:, opens_at: 1.day.ago, closes_at: 1.day.from_now)
    FactoryBot.create(:debate_round, survey:, opens_at: 1.day.from_now, closes_at: 2.days.from_now)
    FactoryBot.create(:question_round, survey:, opens_at: 2.days.from_now, closes_at: 3.days.from_now)
    survey.reload

    assert_equal current_round.closes_at, survey.submission_deadline
  end

  private

  def assert_can_participate(survey, user)
    assert survey.can_participate?(user), 'should be able to participate in this survey'
  end

  def assert_cannot_participate(survey, user)
    refute survey.can_participate?(user), 'should not be able to participate in this survey'
  end
end

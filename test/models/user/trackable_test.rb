# frozen_string_literal: true

require 'test_helper'

class User::TrackableTest < ActiveSupport::TestCase
  test 'record first sign in' do
    user = FactoryBot.create(:user)

    freeze_time do
      user.record_sign_in!(ip: '********')

      assert_nil user.last_sign_in_ip
      assert_nil user.last_sign_in_at
      assert_equal '********', user.current_sign_in_ip.to_s
      assert_equal Time.current, user.current_sign_in_at
      assert_equal 1, user.sign_in_count
    end
  end

  test 'record subsequent sign ins' do
    freeze_time do
      user = FactoryBot.create(
        :user,
        current_sign_in_at: 1.month.ago,
        current_sign_in_ip: '********',
        sign_in_count: 20
      )

      user.record_sign_in!(ip: '***********')

      assert_equal 1.month.ago, user.last_sign_in_at
      assert_equal '********', user.last_sign_in_ip.to_s
      assert_equal Time.current, user.current_sign_in_at
      assert_equal '***********', user.current_sign_in_ip.to_s
      assert_equal 21, user.sign_in_count
    end
  end

  test 'record visitor ip' do
    user = FactoryBot.create(:user)

    freeze_time do
      assert_changes -> { user.last_visited_at }, from: nil, to: Time.current do
        assert_changes -> { user.last_visited_ip&.to_s }, from: nil, to: '***********' do
          user.record_visit!(ip: '***********')
        end
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class User::SuspiciousTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  test 'suspicious user should be in the suspicious scope' do
    user = users(:suspicious)

    users = User.suspicious

    assert_includes users, user, 'suspicious user was not returned by the suspicious scope'
  end

  test 'non-suspicious user should not be in the suspicious scope' do
    user = users(:expert)

    users = User.suspicious

    assert_not_includes users, user, 'non-suspicious user should not have been returned by the suspicious scope'
  end

  test 'trusted user should be in the trusted scope' do
    user = users(:expert)

    users = User.trusted

    assert_includes users, user, 'trusted user was not returned by the trusted scope'
  end

  test 'suspicious user should not be in the trusted scope' do
    user = users(:suspicious)

    users = User.trusted

    assert_not_includes users, user, 'suspicious user should not have been returned by the trusted scope'
  end

  test 'user is suspicious with flag set' do
    user = User.new(marked_suspicious_at: Time.current)

    assert user.suspicious?, 'user should be suspicious with flag set'
  end

  test 'user is not suspicious with flag unset' do
    user = User.new(marked_suspicious_at: nil)

    assert_not user.suspicious?, 'user should not be suspicious with flag unset'
  end

  test 'mark suspicious' do
    user = create_expert!

    user.mark_suspicious!(reason: 'This is a test')

    assert user.suspicious?, 'user should have been marked suspicious'
  end

  test 'trust suspicious user' do
    user = users(:suspicious)

    user.trust!

    assert_not user.suspicious?
  end

  test 'enqueue TrustUserJob if user us confirmed' do
    user = users(:suspicious)

    assert_enqueued_with(job: Users::TrustUserJob, args: [user]) do
      user.update!(confirmed_at: Time.zone.now)
    end
  end
end

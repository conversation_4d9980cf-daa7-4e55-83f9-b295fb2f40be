# frozen_string_literal: true

require 'test_helper'

class User::ConfirmableTest < ActiveSupport::TestCase
  test 'confirmed' do
    user = User.new(confirmed_at: Time.zone.now)

    assert user.confirmed?, 'should be confirmed'
  end

  test 'unconfirmed' do
    user = User.new(confirmed_at: nil)

    assert user.unconfirmed?, 'should be unconfirmed'
  end

  test 'confirmed scope - confirmed' do
    assert_includes User.confirmed, FactoryBot.create(:user, :confirmed)
  end

  test 'confirmed scope - unconfirmed' do
    assert_not_includes User.confirmed, FactoryBot.create(:user, :unconfirmed)
  end

  test 'unconfirmed scope - unconfirmed' do
    assert_includes User.unconfirmed, FactoryBot.create(:user, :unconfirmed)
  end

  test 'unconfirmed scope - confirmed' do
    assert_not_includes User.unconfirmed, FactoryBot.create(:user, :confirmed)
  end

  test 'confirmation_reminder_sent scope - unsent' do
    reminder_not_sent = FactoryBot.create(:user, :unconfirmed)
    reminder_not_sent.update!(confirmation_reminder_sent_at: nil)

    assert_includes User.confirmation_reminder_unsent, reminder_not_sent
  end

  test 'confirmation_reminder_sent scope - sent' do
    reminder_sent = FactoryBot.create(:user, :unconfirmed)
    reminder_sent.update!(confirmation_reminder_sent_at: Time.current)

    assert_not_includes User.confirmation_reminder_unsent, reminder_sent
  end
end

# frozen_string_literal: true

require 'test_helper'

class Message::ResolvableTest < ActiveSupport::TestCase
  test 'resolved scope excludes messages with newer replies' do
    resolved_message = FactoryBot.create(:message, :resolved)
    resolved_with_old_reply = FactoryBot.create(:message, last_resolved_at: 30.minutes.ago, last_resolved_by: FactoryBot.create(:admin))
    FactoryBot.create(:message, parent: resolved_with_old_reply, created_at: 1.hour.ago, subject: nil)
    resolved_with_new_reply = FactoryBot.create(:message, :resolved)
    FactoryBot.create(:message, parent: resolved_with_new_reply, created_at: 30.minutes.ago, subject: nil)
    unresolved_message = FactoryBot.create(:message, :unresolved)

    resolved_messages = Message.resolved

    assert_includes resolved_messages, resolved_message, 'Should include resolved message without replies'
    assert_includes resolved_messages, resolved_with_old_reply, 'Should include resolved message with older replies'
    assert_not_includes resolved_messages, resolved_with_new_reply, 'Should not include resolved message with newer replies'
    assert_not_includes resolved_messages, unresolved_message, 'Should not include unresolved message'
  end

  test 'unresolved scope includes messages with newer replies' do
    resolved_message = FactoryBot.create(:message, :resolved)
    resolved_with_old_reply = FactoryBot.create(:message, last_resolved_at: 30.minutes.ago, last_resolved_by: FactoryBot.create(:admin))
    FactoryBot.create(:message, parent: resolved_with_old_reply, created_at: 1.hour.ago, subject: nil)
    resolved_with_new_reply = FactoryBot.create(:message, :resolved)
    FactoryBot.create(:message, parent: resolved_with_new_reply, created_at: 30.minutes.ago, subject: nil)
    unresolved_message = FactoryBot.create(:message, :unresolved)

    unresolved_messages = Message.unresolved

    assert_not_includes unresolved_messages, resolved_message, 'Should not include resolved message without replies'
    assert_not_includes unresolved_messages, resolved_with_old_reply, 'Should not include resolved message with older replies'
    assert_includes unresolved_messages, resolved_with_new_reply, 'Should include resolved message with newer replies'
    assert_includes unresolved_messages, unresolved_message, 'Should include unresolved message'
  end

  test 'messages from SciPi applicants to owners are unresolved by default' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi)
    message = scipi.messages.create!(user: expert, sent_by: expert, content: 'Test message', subject: 'Test subject')

    assert message.unresolved?, 'should be unresolved?'
  end

  test 'messages from SciPi owners to applicants are resolved by default' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:scipi)
    message = scipi.messages.create!(
      user: expert,
      sent_by: scipi.created_by,
      content: 'Test message',
      subject: 'Test subject'
    )

    assert message.resolved?, 'should be resolved?'
  end

  test 'resolved? returns true when last_resolved_at is set' do
    message = FactoryBot.create(:message, :resolved)

    assert message.resolved?, 'Message should be resolved when last_resolved_at is set'
  end

  test 'resolved? returns false when last_resolved_at is nil' do
    message = FactoryBot.create(:message, :unresolved)

    assert_not message.resolved?, 'Message should not be resolved when last_resolved_at is nil'
  end

  test 'resolved? returns false when there are newer replies after last_resolved_at' do
    message = FactoryBot.create(:message, :resolved)

    FactoryBot.create(:message, parent: message, created_at: 30.minutes.ago, subject: nil)

    assert_not message.resolved?, 'Message should not be resolved when there are newer replies'
  end

  test 'resolved? returns true when all replies are older than last_resolved_at' do
    message = FactoryBot.create(:message, last_resolved_at: 30.minutes.ago, last_resolved_by: FactoryBot.create(:admin))

    FactoryBot.create(:message, parent: message, created_at: 1.hour.ago, subject: nil)

    assert message.resolved?, 'Message should be resolved when all replies are older than last_resolved_at'
  end

  test 'unresolved? returns true when last_resolved_at is nil' do
    message = FactoryBot.create(:message, :unresolved)

    assert message.unresolved?, 'Message should be unresolved when last_resolved_at is nil'
  end

  test 'unresolved? returns false when last_resolved_at is set' do
    message = FactoryBot.create(:message, :resolved)

    assert_not message.unresolved?, 'Message should not be unresolved when last_resolved_at is set'
  end

  test 'resolve! sets last_resolved_at and last_resolved_by' do
    message = FactoryBot.create(:message, :unresolved)
    user = FactoryBot.create(:admin)

    message.resolve!(user)

    assert_not_nil message.last_resolved_at, 'Message should be resolved after calling resolve!'
    assert_equal user, message.last_resolved_by, 'Message should be resolved by the given user'
  end

  test 'unresolve! clears last_resolved_at and last_resolved_by' do
    message = FactoryBot.create(:message, :resolved)

    message.unresolve!

    assert_not message.resolved?, 'Message should be unresolved after calling unresolve!'
    assert_nil message.last_resolved_by, 'Message should have no resolver after unresolve!'
  end

  test 'bulk_resolve!' do
    scipi = FactoryBot.create(:scipi)
    user1 = FactoryBot.create(:expert)
    user2 = FactoryBot.create(:expert)
    admin = FactoryBot.create(:admin)

    message1 = FactoryBot.create(:message, :unresolved, survey: scipi, user: user1)
    message2 = FactoryBot.create(:message, :unresolved, survey: scipi, user: user2)

    other_message = FactoryBot.create(:message, :unresolved)

    messages_scope = Message.where(id: [message1.id, message2.id])
    freeze_time do
      count = messages_scope.bulk_resolve!(admin)

      assert_equal 2, count, 'Should return count of updated messages'

      message1.reload
      message2.reload
      other_message.reload

      assert_equal Time.current, message1.last_resolved_at, 'First message should be resolved'
      assert_equal admin, message1.last_resolved_by, 'First message should be resolved by resolver'

      assert_equal Time.current, message2.last_resolved_at, 'Second message should be resolved'
      assert_equal admin, message2.last_resolved_by, 'Second message should be resolved by resolver'

      assert_nil other_message.last_resolved_at, 'Other message should not be affected'
      assert_nil other_message.last_resolved_by, 'Other message should not be affected'
    end
  end

  test 'bulk_unresolve! updates all messages with nil last_resolved_at and last_resolved_by' do
    scipi = FactoryBot.create(:scipi)
    user1 = FactoryBot.create(:expert)
    user2 = FactoryBot.create(:expert)
    original_resolver = FactoryBot.create(:admin)

    message1 = FactoryBot.create(:message, survey: scipi, user: user1, last_resolved_at: 1.hour.ago, last_resolved_by: original_resolver)
    message2 = FactoryBot.create(:message, survey: scipi, user: user2, last_resolved_at: 2.hours.ago, last_resolved_by: original_resolver)

    other_message = FactoryBot.create(:message, :resolved, last_resolved_at: 1.day.ago, last_resolved_by: original_resolver)

    messages_scope = Message.where(id: [message1.id, message2.id])
    count = messages_scope.bulk_unresolve!

    assert_equal 2, count, 'Should return count of updated messages'

    message1.reload
    message2.reload
    other_message.reload

    assert_nil message1.last_resolved_at, 'First message should be unresolved'
    assert_nil message1.last_resolved_by, 'First message should have no resolver'

    assert_nil message2.last_resolved_at, 'Second message should be unresolved'
    assert_nil message2.last_resolved_by, 'Second message should have no resolver'

    assert_not_nil other_message.last_resolved_at, 'Other message should not be affected'
    assert_equal original_resolver, other_message.last_resolved_by, 'Other message should not be affected'
  end
end

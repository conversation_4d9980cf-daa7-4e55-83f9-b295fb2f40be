# frozen_string_literal: true

require 'test_helper'

class SurveyResultCsvFormatterTest < ActiveSupport::TestCase
  test 'header and row count' do
    survey = create(:survey, :published)
    question = create(:question, question_group: survey)

    answer_group1 = create(:submission, question_group: survey, submitted_at: 1.week.ago)
    answer_group2 = create(:submission, question_group: survey, submitted_at: 1.week.ago)
    answer_group3 = create(:submission, :draft, question_group: survey)

    answer_group1.answers.create!(question:)
    answer_group3.answers.create!(question:)
    answer_group2.answers.create!(question:)

    formatter = SurveyResultCsvFormatter.new(survey, show_user_info: false)

    assert_not_nil formatter.header_row, "CSV's header row should not be nil"
    assert_equal 2, formatter.rows.count, 'should have two rows'
  end

  test 'export with missing answer works (and does not fail with rich_text answer_explanation)' do
    survey = create(:survey, :published)

    create(:submission, question_group: survey, submitted_at: 1.week.ago)
    create(:question, question_group: survey, allow_answer_explanation: true)

    formatter = SurveyResultCsvFormatter.new(survey, show_user_info: false)

    assert_equal '', formatter.rows.first.to_a[3]
  end
end

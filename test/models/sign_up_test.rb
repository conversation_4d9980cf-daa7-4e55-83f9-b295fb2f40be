# frozen_string_literal: true

require 'test_helper'

class SignUpTest < ActiveSupport::TestCase
  test 'email is required' do
    sign_up = SignUp.new(email: '')

    assert_error_on(sign_up, :email, :blank)
  end

  test 'email must be a valid format' do
    sign_up = SignUp.new(email: 'not valid')

    assert_error_on(sign_up, :email, :invalid)
  end

  test 'cannot use a duplicate email' do
    email = Faker::Internet.email
    User.create!(email:, password: valid_password)
    sign_up = SignUp.new(email:)

    assert_error_on(sign_up, :email, :taken)
  end

  test 'password is required' do
    sign_up = SignUp.new(password: '')

    assert_error_on(sign_up, :password, :blank)
  end

  test 'password is too short' do
    sign_up = SignUp.new(password: 'passwor')

    assert_error_on(sign_up, :password, :too_short)
  end

  test 'saving a signup creates a new, unconfirmed expert' do
    role = Role.available_for_signup.first

    email = valid_email
    password = valid_password
    ip_address = Faker::Internet.ip_v4_address

    expect_confirmation_email_sent!

    signup = SignUp.new(
      email:,
      role_id: role.id,
      password:,
      ip_address:
    )

    assert signup.save, 'should have successfully saved'

    user = signup.user
    assert_not user.confirmed?, 'new users should not be confirmed'
    assert_equal email, user.email, 'should match the signup email'
    assert_not_nil user.confirmation_token, 'should have a confirmation token'
    assert_not_nil user.confirmation_sent_at, 'confirmation_sent_at be set'
    assert_equal({ ip_address: }, user.signup_metadata.symbolize_keys)
  end

  test 'subscribes to list' do
    role = FactoryBot.create(:role, :available_for_signup)
    list = FactoryBot.create(:distribution_list, roles: [role])
    email = valid_email
    password = valid_password
    ip_address = Faker::Internet.ip_v4_address

    signup = SignUp.new(email:, role_id: role.id, password:, ip_address:)

    assert signup.save
    assert list.subscriptions.exists?(subscriber: signup.user)
  end

  test 'sign up from guest user' do
    guest_user = FactoryBot.create(:guest)
    role = Role.expert

    email = TestData.email
    password = TestData.password
    ip_address = Faker::Internet.ip_v4_address

    expect_confirmation_email_sent!

    signup = SignUp.new(email:, role_id: role.id, password:, ip_address:, guest_user:)

    assert_no_difference -> { User.count } do
      assert_difference -> { Role.expert.users.count } do
        assert_difference -> { Role.guest.users.count }, -1 do
          assert signup.save, 'should have successfully saved'
        end
      end
    end

    user = signup.user
    assert_equal guest_user, user, 'the user\'s identity should not have changed'
    assert_not user.confirmed?, 'new users should not be confirmed'
    assert_equal email, user.email, 'should match the signup email'
    assert_not_nil user.confirmation_token, 'should have a confirmation token'
    assert_not_nil user.confirmation_sent_at, 'confirmation_sent_at be set'
    assert_equal({ ip_address: }, user.signup_metadata.symbolize_keys)
  end

  private

  def expect_confirmation_email_sent!
    mock_mail = mock
    mock_mail.expects(:deliver_later).once
    UserMailer
      .expects(:confirmation)
      .returns(mock_mail)
      .once
  end

  def valid_email
    Faker::Internet.email
  end

  def valid_password
    Faker::Internet.password
  end
end

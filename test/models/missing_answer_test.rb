# frozen_string_literal: true

require 'test_helper'

class MissingAnswerTest < ActiveSupport::TestCase
  test 'missing answer enhanced answer text should be blank' do
    missing_answer = MissingAnswer.new

    assert_equal '', missing_answer.for_csv_formatter
  end

  test 'missing answer explanation should be blank' do
    missing_answer = MissingAnswer.new

    assert_equal '', missing_answer.explanation.to_plain_text
  end
end

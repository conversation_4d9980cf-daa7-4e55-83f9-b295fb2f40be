# frozen_string_literal: true

require 'test_helper'

class Survey::PublishableTest < ActiveSupport::TestCase
  test 'scheduled survey is a considered a draft' do
    survey = FactoryBot.create(:survey, :scheduled)

    assert survey.draft?
  end

  test 'published_at in the future is a scheduled survey' do
    survey = FactoryBot.create(:survey, published_at: 2.days.from_now)

    assert survey.scheduled?
  end

  test 'published_at in the past is published survey' do
    survey = FactoryBot.create(:survey, published_at: 2.days.ago)

    assert survey.published?
  end

  test 'published_at nil in a draft' do
    survey = FactoryBot.create(:survey, published_at: nil)

    assert survey.draft?
  end
end

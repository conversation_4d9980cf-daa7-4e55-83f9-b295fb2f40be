# frozen_string_literal: true

require 'test_helper'

class Survey::OwnableTest < ActiveSupport::TestCase
  test 'super admins can administer any survey' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey)

    assert survey.administered_by?(admin)
  end

  test 'non-admins cannot administer any survey' do
    user = FactoryBot.create(:user)
    scipi = FactoryBot.create(:scipi)

    assert_not scipi.administered_by?(user)
  end

  test 'owners can administer a scipi' do
    scipi_admin = FactoryBot.create(:scipi_admin)
    scipi = FactoryBot.create(:survey, owners: [scipi_admin])

    assert scipi.administered_by?(scipi_admin)
  end

  test 'non-owner scipi admins cannot administer a scipi' do
    scipi_admin = FactoryBot.create(:scipi_admin)
    scipi = FactoryBot.create(:survey)

    assert_not scipi.administered_by?(scipi_admin)
  end
end

# frozen_string_literal: true

require 'test_helper'

module Moderation
  class ModerationRequestTest < ActiveSupport::TestCase
    include ActionMailer::TestHelper

    test 'a comment is required when a Reason requires it' do
      reason = ModerationReason.new(require_comment: true)
      request = ModerationRequest.new(reason:, comment: '')

      assert_not request.valid?
      assert request.errors[:comment].include?('A comment is required')
    end

    test 'user-generated requests require submitted_by' do
      request = ModerationRequest.new(user_generated: true)

      assert_not request.valid?
      assert request.errors.added?(:submitted_by, :blank)
    end

    test 'system-generated requests do not require submitted_by' do
      request = ModerationRequest.new(system_generated: true)

      request.valid? # trigger validations, but don't assert
      assert_not request.errors.added?(:submitted_by, :blank)
    end

    test 'creating a moderation request notifies moderators' do
      subject = FactoryBot.create(:ping)
      reason = FactoryBot.create(:moderation_reason)
      submitted_by = FactoryBot.create(:expert)

      matcher = lambda do |args|
        request = args.first
        request.subject == subject && request.reason == reason && request.submitted_by == submitted_by
      end

      assert_enqueued_email_with Moderation::Mailer, :notify, args: matcher do
        ModerationRequest.create!(reason:, subject:, submitted_by:)
      end
    end
  end
end

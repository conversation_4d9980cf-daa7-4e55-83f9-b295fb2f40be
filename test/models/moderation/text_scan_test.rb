# frozen_string_literal: true

require 'test_helper'

module Moderation
  class TextScanTest < ActiveSupport::TestCase
    test 'ai score for user and model' do
      expert = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping)
      ping.answers.create!(author: expert, content: 'I am a good answer')
      TextScan.create!(
        context_model: ping,
        model_class_name: 'Pings::Answer',
        model_attribute: 'content',
        user: expert,
        text: 'I am some text',
        human_score: 50.0,
        attempts: 1,
        response_payload: {}
      )

      assert_equal 50.0, TextScan.ai_score_for(context_model: ping, user: expert)
    end
  end
end

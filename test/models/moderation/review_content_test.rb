# frozen_string_literal: true

require 'test_helper'

module Moderation
  class ReviewContentTest < ActiveSupport::TestCase
    test 'review content' do
      ENV['MODERATION_AI_SCORE_THRESHOLD'] = '0.5'
      expert = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping)
      answer = ping.answers.create!(author: expert, content: 'I am a good answer')
      scan = TextScan.create!(
        context_model: ping,
        user: expert,
        model_class_name: 'Pings::Answer',
        model_attribute: 'content',
        text: 'I am a good answer',
        human_score: 40.0,
        attempts: 1,
        response_payload: {}
      )

      Moderation.expects(:report_ai_score).with(answer, 60.0)

      ReviewContent
        .new(
          model: answer,
          context_model: ping,
          user: expert,
          model_class_name: 'Pings::Answer',
          model_attribute: 'content'
        )
        .review!

      assert_not_nil scan.reload.model
      assert_equal answer, scan.model
    end

    test 'do not report is score is nil' do
      expert = FactoryBot.create(:expert, :with_display_name)
      ping = FactoryBot.create(:ping)

      Moderation.expects(:report_ai_score).never

      ReviewContent
        .new(
          model: Object.new,
          context_model: ping,
          user: expert,
          model_class_name: 'Pings::Answer',
          model_attribute: 'content'
        )
        .review!
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class SurveyResultCsvFormatterRowTest < ActiveSupport::TestCase
  def test_to_array_without_user_info
    questions = [
      stub(id: 1, allow_answer_explanation: true, weighted_sum?: false),
      stub(id: 2, allow_answer_explanation: false, weighted_sum?: false)
    ]

    answer1 = stub(for_csv_formatter: 'one', explanation: ActionText::Content.new('foobar'))
    answer2 = stub(for_csv_formatter: 'two')

    answer_group = mock('mock answer group')
    answer_group.stubs(id: 1, submitted_at: Time.now.to_i)
    answer_group.expects(:answer_for).with(questions.first).returns(answer1)
    answer_group.expects(:answer_for).with(questions.last).returns(answer2)

    row = SurveyResultCsvFormatter::Row.new(
      answer_group:,
      questions:,
      user: nil,
      display_id: 'Expert 1'
    )

    assert_equal ['Expert 1', answer_group.submitted_at, 'one', 'foobar', 'two'], row.to_a
  end

  def test_to_array_with_user_info
    questions = [
      stub(id: 1, allow_answer_explanation: false, weighted_sum?: false),
      stub(id: 2, allow_answer_explanation: false, weighted_sum?: false)
    ]

    answer1 = stub(for_csv_formatter: 'one')
    answer2 = stub(for_csv_formatter: 'two')

    answer_group = mock('mock submission')
    answer_group.stubs(id: 1, submitted_at: Time.now.to_i)
    answer_group.expects(:answer_for).with(questions.first).returns(answer1)
    answer_group.expects(:answer_for).with(questions.last).returns(answer2)

    user = stub(
      id: 2,
      display_id: 'foo',
      verified?: true,
      total_work_experience: 10,
      current_employment_sector_name: 'foobar',
      degree_type_names: ['PHD'],
      expertise_names: ['Basket Weaving', 'Navel Gazing']
    )

    row = SurveyResultCsvFormatter::Row.new(
      answer_group:,
      questions:,
      user:,
      display_id: 'Expert 1'
    )

    expected = [
      'Expert 1',
      answer_group.submitted_at,
      'one',
      'two',
      2,
      answer_group.id,
      'TRUE',
      10,
      'foobar',
      'PHD',
      'Basket Weaving; Navel Gazing'
    ]
    assert_equal expected, row.to_a
  end
end

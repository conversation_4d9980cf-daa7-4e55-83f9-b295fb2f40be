# frozen_string_literal: true

require 'test_helper'

class ExpertiseTest < ActiveSupport::TestCase
  test 'cannot delete with taggings' do
    expertise = FactoryBot.create(:expertise)
    user = FactoryBot.create(:expert)
    expertise.taggings.create!(taggable: user.profile)

    assert_no_difference -> { Expertise.count }, -1 do
      assert_raises ActiveRecord::RecordNotDestroyed do
        expertise.reload.destroy!
      end
    end
  end

  test 'profile expertise' do
    profile_expertise = Expertise.create!(name: 'Profile Expertise', profile: true)
    non_profile_expertise = Expertise.create!(name: 'Non-Profile Expertise', profile: false)

    assert_includes Expertise.profile, profile_expertise
    assert_not_includes Expertise.profile, non_profile_expertise
  end

  test 'newly_added? returns true for expertise created within last week' do
    expertise = Expertise.new(created_at: 3.days.ago)

    assert expertise.newly_added?
  end

  test 'newly_added? returns false for expertise created more than a week ago' do
    expertise = Expertise.new(created_at: 2.weeks.ago)

    assert_not expertise.newly_added?
  end
end

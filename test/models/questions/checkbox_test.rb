# frozen_string_literal: true

require 'test_helper'

module Questions
  class CheckboxTest < ActiveSupport::TestCase
    test 'too large max_selected_choices trimmed down' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:, max_selected_choices: 4)

      assert_equal question.max_selected_choices, 3
    end

    test 'Setting max_selected_choices will engage limit_selection?' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:, max_selected_choices: 2)

      assert question.limit_selection?
    end

    test 'Default case: limit_selection? is false if max_selected_choices is null' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:checkbox, survey:)

      assert_not question.limit_selection?
    end

    test 'Setting max_selected_choices on radio will NOT engage limit_selection?' do
      survey = FactoryBot.create(:survey, :published)
      question = FactoryBot.create(:radio, survey:, max_selected_choices: 2)

      assert_not question.limit_selection?
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Questions
  class ProfileYearsOfExperienceTest < ActiveSupport::TestCase
    test 'default years of experience is nil without work experience' do
      expert = FactoryBot.create(:expert)
      survey = FactoryBot.create(:survey)
      question = ProfileYearsOfExperience.create!(survey:, question_text: 'YOE?', position: 1)

      defaults = question.answer_defaults(expert)

      assert_nil defaults[:years_of_experience], 'Expected years of experience to be nil without work experience'
    end

    test 'default years of experience' do
      expert = FactoryBot.create(:expert)
      expert.employment_history_items.create!(years: 5, employment_sector: FactoryBot.create(:employment_sector))
      survey = FactoryBot.create(:survey)
      question = ProfileYearsOfExperience.create!(survey:, question_text: 'YOE?', position: 1)

      assert_equal({ years_of_experience: 5 }, question.answer_defaults(expert))
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Questions
  class ProfileEmploymentSectorTest < ActiveSupport::TestCase
    test 'answer defaults' do
      sector = FactoryBot.create(:employment_sector)
      expert = FactoryBot.create(:expert, current_employment_sector: sector)
      expert.employment_history_items.create!(employment_sector: sector, years: 10)
      survey = FactoryBot.create(:survey)
      question = ProfileEmploymentSector.create!(question_text: 'Sector?', position: 1, survey:)
      default_answer_ids = question.answer_choices.where(data_source: sector).pluck(:id)

      defaults = question.answer_defaults(expert)

      assert_equal default_answer_ids, defaults[:answer_choice_ids]
      assert_equal 10, defaults[:years_of_experience]
    end

    test 'skipped?' do
      survey = FactoryBot.create(:survey)
      submission = FactoryBot.create(:submission, survey:)
      question = ProfileEmploymentSector.create!(question_text: 'Sector?', position: 1, survey:)
      answer = submission.answers.create!(question:)

      assert question.skipped?(answer)
    end

    test 'not skipped?' do
      sector = FactoryBot.create(:employment_sector)
      survey = FactoryBot.create(:survey)
      submission = FactoryBot.create(:submission, survey:)
      question = ProfileEmploymentSector.create!(question_text: 'Sector?', position: 1, survey:)
      answer_choice = question.answer_choices.find_by!(data_source: sector)
      answer = submission.answers.create!(question:, years_of_experience: 10)
      answer.selected_choices.create!(answer_choice:)

      assert_not question.skipped?(answer)
    end

    test 'populates answer choices on create' do
      _sector = FactoryBot.create(:employment_sector)
      survey = FactoryBot.create(:survey)
      question = ProfileEmploymentSector.create!(question_text: 'Sector?', position: 1, survey:)

      assert_equal EmploymentSector.count, question.answer_choices.count
    end

    test 'copy to profile' do
      sector1 = FactoryBot.create(:employment_sector)
      sector2 = FactoryBot.create(:employment_sector)
      expert = FactoryBot.create(:expert, current_employment_sector: sector1)
      expert.employment_history_items.create!(employment_sector: sector1, years: 10)
      survey = FactoryBot.create(:survey)
      question = ProfileEmploymentSector.create!(question_text: 'Sector?', position: 1, survey:)
      answer_group = FactoryBot.create(:submission, submitter: expert)
      answer_choices = [question.answer_choices.find_by!(data_source: sector2)]
      answer = FactoryBot.create(:answer, question:, answer_choices:, answer_group:, years_of_experience: 20)

      question.copy_to_profile(answer)

      assert_equal sector2, expert.reload.current_employment_sector
      assert_equal 20, expert.employment_history_items.find_by!(employment_sector: sector2).years
    end
  end
end

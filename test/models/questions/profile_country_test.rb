# frozen_string_literal: true

require 'test_helper'

module Questions
  class ProfileCountryTest < ActiveSupport::TestCase
    test 'answer defaults' do
      country = FactoryBot.create(:country)
      expert = FactoryBot.create(:expert, country:)
      survey = FactoryBot.create(:survey)
      question = ProfileCountry.create!(question_text: 'Country?', position: 1, survey:)
      default_answer_ids = question.answer_choices.where(data_source: country).pluck(:id)

      defaults = question.answer_defaults(expert)

      assert_equal default_answer_ids, defaults[:answer_choice_ids]
    end

    test 'skipped?' do
      survey = FactoryBot.create(:survey)
      submission = FactoryBot.create(:submission, survey:)
      question = ProfileCountry.create!(question_text: 'Country?', position: 1, survey:)
      answer = submission.answers.create!(question:)

      assert question.skipped?(answer)
    end

    test 'not skipped?' do
      country = FactoryBot.create(:country)
      survey = FactoryBot.create(:survey)
      submission = FactoryBot.create(:submission, survey:)
      question = ProfileCountry.create!(question_text: 'Country?', position: 1, survey:)
      answer_choice = question.answer_choices.find_by!(data_source: country)
      answer = submission.answers.create!(question:)
      answer.selected_choices.create!(answer_choice:)

      assert_not question.skipped?(answer)
    end

    test 'populates answer choices on create' do
      survey = FactoryBot.create(:survey)
      question = ProfileCountry.create!(question_text: 'Country?', position: 1, survey:)

      assert_equal Country.count, question.answer_choices.count
    end

    test 'copy to profile' do
      country1 = FactoryBot.create(:country)
      country2 = FactoryBot.create(:country)
      expert = FactoryBot.create(:expert, country: country1)
      survey = FactoryBot.create(:survey)
      question = ProfileCountry.create!(question_text: 'Country?', position: 1, survey:)
      answer_group = FactoryBot.create(:submission, submitter: expert)
      answer_choices = [question.answer_choices.find_by!(data_source: country2)]
      answer = FactoryBot.create(:answer, question:, answer_choices:, answer_group:)

      question.copy_to_profile(answer)

      assert_equal country2, expert.reload.country
    end
  end
end

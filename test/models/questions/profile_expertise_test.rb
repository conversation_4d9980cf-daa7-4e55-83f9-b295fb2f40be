# frozen_string_literal: true

require 'test_helper'

module Questions
  class ProfileExpertiseTest < ActiveSupport::TestCase
    test 'populates answer choices on create' do
      survey = FactoryBot.create(:survey)
      question = ProfileExpertise.create!(question_text: 'Expertise?', position: 1, survey:)

      assert_equal Expertise.count, question.answer_choices.count
    end

    test 'skipped?' do
      survey = FactoryBot.create(:survey)
      submission = FactoryBot.create(:submission, survey:)
      question = ProfileExpertise.create!(question_text: 'Expertise?', position: 1, survey:)
      answer = submission.answers.create!(question:)

      assert question.skipped?(answer)
    end

    test 'not skipped?' do
      expertise = FactoryBot.create(:expertise)
      survey = FactoryBot.create(:survey)
      submission = FactoryBot.create(:submission, survey:)
      question = ProfileExpertise.create!(question_text: 'Expertise?', position: 1, survey:)
      answer_choice = question.answer_choices.find_by!(data_source: expertise)
      answer = submission.answers.create!(question:)
      answer.selected_choices.create!(answer_choice:)

      assert_not question.skipped?(answer)
    end

    test 'answer defaults' do
      expertise1 = FactoryBot.create(:expertise)
      _expertise2 = FactoryBot.create(:expertise)
      expert = FactoryBot.create(:expert)
      expert.profile.expertises = [expertise1]
      survey = FactoryBot.create(:survey)
      question = ProfileExpertise.create!(question_text: 'Expertise?', position: 1, survey:)
      default_answer_ids = question.answer_choices.where(data_source: expertise1).pluck(:id)

      defaults = question.answer_defaults(expert)

      assert_equal default_answer_ids, defaults[:answer_choice_ids]
    end

    test 'copy to profile' do
      expertise = FactoryBot.create(:expertise)
      expert = FactoryBot.create(:expert)
      expert.profile.expertises = [expertise]
      survey = FactoryBot.create(:survey)
      question = ProfileExpertise.create!(question_text: 'Expertise?', position: 1, survey:)
      answer_group = FactoryBot.create(:submission, submitter: expert)
      answer_choices = [question.answer_choices.find_by!(data_source: expertise)]
      answer = FactoryBot.create(:answer, question:, answer_choices:, answer_group:)

      question.copy_to_profile(answer)

      assert_equal [expertise], expert.profile.reload.expertises
    end
  end
end

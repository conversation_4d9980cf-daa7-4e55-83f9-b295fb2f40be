# frozen_string_literal: true

require 'test_helper'

module Questions
  class GridTest < ActiveSupport::TestCase
    test 'complete weighted sum grids are scorable' do
      question = FactoryBot.create(:grid_question, :weighted)
      FactoryBot.create(:grid_structure, :weight_sum_grid, question:)

      assert question.scorable?,
             'complete weighted sum grids should be scorable'
    end
  end
end

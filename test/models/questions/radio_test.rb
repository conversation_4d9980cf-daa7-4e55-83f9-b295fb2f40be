# frozen_string_literal: true

require 'test_helper'

module Questions
  class RadioTest < ActiveSupport::TestCase
    test 'radio questions without a weight are not scorable' do
      question = Radio.new(weight: nil)

      refute question.scorable?, 'should not be scorable without a weight'
    end

    test 'radio questions without a answer choices are not scorable' do
      question = Radio.new(answer_choices: [], weight: 1)

      refute question.scorable?, 'should not be scorable without answer choices'
    end

    test 'radio questions without answer choice values are not scorable' do
      with_value = AnswerChoice.new(value: nil)
      without_value = AnswerChoice.new(value: 2)
      question = Radio.new(
        weight: 1.0,
        answer_choices: [with_value, without_value]
      )

      refute question.scorable?,
             'should not be scorable without an answer choice value'
    end

    test 'radio questions need answer choice values and a weight to be scorable' do
      choice1 = AnswerChoice.new(value: 3)
      choice2 = AnswerChoice.new(value: 2)
      question = Radio.create(weight: 1.0, answer_choices: [choice1, choice2])

      assert question.scorable?, 'should be scorable'
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class UserTest < ActiveSupport::TestCase
  test 'email is downcased' do
    email = Faker::Internet.email

    user = User.create!(email: email.upcase, password: Faker::Internet.password)

    assert_equal email, user.email
  end

  test 'email whitespace is trimmed' do
    email = Faker::Internet.email

    user = User.create!(
      email: "  #{email}  ",
      password: Faker::Internet.password
    )

    assert_equal email, user.email
  end

  test 'generate display id on create' do
    user = User.create!(
      email: Faker::Internet.email,
      password: Faker::Internet.password
    )

    assert_not_nil user.display_id, 'should have a display id'
  end

  test 'reset password' do
    user = FactoryBot.create(:expert, :reset_password_requested)
    password = Faker::Internet.password

    user.reset_password!(password)
    assert user.reload.authenticate(password)
  end

  # EVERYTHING BELOW HERE IS FROM SCIPINION V1.0 and should only be moved up
  # if it's deemed good to keep

  test 'add result access code' do
    user = FactoryBot.create(:expert)

    assert_difference(-> { user.result_access_codes.count }) do
      user.add_result_access_code!('foobar')
    end
  end

  test 'duplicate result access codes are not readded' do
    code = 'foobar'
    user = FactoryBot.create(:expert)
    user.result_access_codes.create!(value: code)

    assert_no_difference(-> { user.result_access_codes.count }) do
      user.add_result_access_code!('foobar')
    end
  end

  test 'a profile get created when a user is created' do
    user = User.create!(
      email: Faker::Internet.email,
      display_id: 'Foo',
      password: Faker::Internet.password
    )

    assert_not_nil user.profile, 'a user should have a profile when created'
  end

  test 'not an admin' do
    role = FactoryBot.create(:role, admin: false)
    user = FactoryBot.create(:expert, roles: [role])

    assert_not user.admin?, 'should not be an admin'
  end

  test 'is an admin' do
    role = FactoryBot.create(:role, admin: true)
    user = FactoryBot.create(:expert, roles: [role])

    assert user.admin?, 'should be an admin'
  end

  test 'is legacy admin' do
    user = FactoryBot.create(:expert, admin: true)

    assert user.admin?, 'should be an admin'
  end

  test 'has an internal role' do
    role1 = FactoryBot.create(:role)
    role2 = FactoryBot.create(:role, :internal)
    user = FactoryBot.create(:user, roles: [role1, role2])

    assert user.internal?, 'should be an internal user'
  end

  test 'external user' do
    role = FactoryBot.create(:role)
    user = FactoryBot.create(:user, roles: [role])

    assert_not user.internal?, 'should not be an internal user'
  end

  test 'internal_display_name prefers a user\'s full name' do
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    email = Faker::Internet.email

    user = FactoryBot.create(
      :user,
      first_name:,
      last_name:,
      email:
    )

    assert_equal "##{user.id}: #{first_name} #{last_name}",
                 user.internal_display_name
  end

  test 'internal_display_name falls back to email with a full name' do
    first_name = Faker::Name.first_name
    last_name = ''
    email = Faker::Internet.email

    user = FactoryBot.create(
      :user,
      first_name:,
      last_name:,
      email:
    )

    assert_equal "##{user.id}: #{email}", user.internal_display_name
  end

  test 'internal_display_name for anonymous users as guests' do
    role = FactoryBot.create(:role, require_credentials: false)
    user = role.users.create!

    assert_equal "Guest ##{user.id}", user.internal_display_name
  end

  test 'verify_recaptcha! does not overwrite signup metadata ' do
    existing_metadata = { 'foo' => 'bar' }

    user = FactoryBot.create(:user, signup_metadata: existing_metadata)

    user.verify_recaptcha!(Google::Recaptcha::Response.new(true, 0.9, 'signup'))

    assert_equal 'bar', user.signup_metadata['foo'], 'should merge signup metadata'
  end

  test 'check recaptcha passes' do
    user = FactoryBot.create(:user)

    # only using needed fields

    recaptcha_response = Google::Recaptcha::Response.new(true, 0.9, 'signup')

    user.verify_recaptcha!(recaptcha_response)

    assert_not user.suspicious?, 'should not be a suspicious user'
  end

  test 'check recaptcha fails' do
    user = User.create!(email: TestData.email, password: TestData.password)

    # only using needed fields
    recaptcha_response = Google::Recaptcha::Response.new(true, 0.4, 'signup')

    user.verify_recaptcha!(recaptcha_response)

    assert user.suspicious?, 'should be a suspicious user'
  end

  test 'user is in role' do
    role = FactoryBot.create(:role)
    user = FactoryBot.create(:user, roles: [role])

    assert user.in_role?(role)
  end

  test 'user is not in role' do
    role = FactoryBot.create(:role)
    user = FactoryBot.create(:user)

    assert_not user.in_role?(role)
  end

  test '"valid" is neverbounce deliverable' do
    user = User.create!(email: Faker::Internet.email, password: 'password', last_neverbounce_status: 'valid')

    users = User.neverbounce_deliverable

    assert_includes users, user
  end

  test '"accept_all_unverifiable" is neverbounce deliverable' do
    user = User.create!(email: Faker::Internet.email, password: 'password', last_neverbounce_status: 'accept_all_unverifiable')

    users = User.neverbounce_deliverable

    assert_includes users, user
  end

  test 'no status is not neverbounce deliverable' do
    user = User.create!(email: Faker::Internet.email, password: 'password', last_neverbounce_status: nil)

    users = User.neverbounce_deliverable

    assert_not_includes users, user
  end

  test 'credentials required' do
    role = Role.create!(require_credentials: true, name: 'Creds required', description: 'Role')
    user = User.create(email: '', password: '', roles: [role])

    assert user.errors.added?(:email, :blank)
    assert user.errors.added?(:password, :blank)
  end

  test 'credentials are not required' do
    role = Role.create!(require_credentials: false, name: 'Creds not required', description: 'Role')
    user = User.create(email: '', password: '', roles: [role])

    assert_not user.errors.added?(:email, :blank)
    assert_not user.errors.added?(:password, :blank)
  end

  test 'users with emails are registered' do
    role = FactoryBot.create(:role)
    user = role.users.create!(email: '<EMAIL>', password: 'password')

    assert user.registered?, 'should be registered'
    assert_not user.anonymous?, 'should not be anonymous'
  end

  test 'users without emails are anonymous' do
    role = FactoryBot.create(:role, require_credentials: false)
    user = role.users.create!

    assert user.anonymous?, 'should be anonymous'
    assert_not user.registered?, 'should be not be registered'
  end

  test 'registered scope' do
    user = FactoryBot.create(:user)
    guest = FactoryBot.create(:guest)

    registered_users = User.registered

    assert_includes registered_users, user
    assert_not_includes registered_users, guest
  end
end

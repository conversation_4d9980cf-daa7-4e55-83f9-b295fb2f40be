# frozen_string_literal: true

require 'test_helper'

class Scipoll::ResultsTest < ActiveSupport::TestCase
  test 'scipoll results are not public before close (even if results_public is true)' do
    scipoll = FactoryBot.create(:scipoll, :published, results_published: true, closes_at: 1.week.from_now, results_public: true)

    assert_not scipoll.results_public?
  end

  test 'scipoll results are public after close (when results_public is true)' do
    scipoll = FactoryBot.create(:scipoll, :published, results_published: true, closes_at: 1.week.ago, results_public: true)

    assert scipoll.results_public?
  end

  test 'scipoll results do not automatically become public unless results_public is true' do
    scipoll = FactoryBot.create(:scipoll, :published, results_published: true, closes_at: 1.week.ago, results_public: false)

    assert_not scipoll.results_public?
  end
end

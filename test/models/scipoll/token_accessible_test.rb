# frozen_string_literal: true

require 'test_helper'

class Scipoll::TokenAccessibleTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  test 'access-token-required SciPolls are not general participation' do
    survey = QuestionGroup.new(invite_only: false, access_token_required: true)

    assert_not survey.general_participation?, 'access-token-required surveys should not be general participation'
  end

  test 'do not enqueue generate access token job when not required' do
    branding = FactoryBot.create(:survey_branding, :scipoll)
    created_by = FactoryBot.create(:scipoll_admin)

    assert_no_enqueued_jobs do
      FactoryBot.create(:scipoll, access_token_required: false, branding:, created_by:)
    end
  end

  test 'enqueue generate access token job for new SciPoll' do
    branding = FactoryBot.create(:survey_branding, :scipoll)
    created_by = FactoryBot.create(:scipoll_admin)

    assert_enqueued_with(job: GenerateAccessTokensJob, args: ->(args) { args[1] == 10 }) do
      FactoryBot.create(:scipoll, access_token_count: 10, access_token_required: true, branding:, created_by:)
    end
  end

  test 'do not enqueue generate access token job when count does not change' do
    scipoll = FactoryBot.create(:scipoll, access_token_count: 5, access_token_required: true)
    scipoll.access_tokens.create!(Array.new(5) { {} })

    assert_no_enqueued_jobs do
      scipoll.update!(name: 'New name')
    end
  end

  test 'enqueue generate access token job when count changes' do
    scipoll = FactoryBot.create(:scipoll, access_token_count: 5, access_token_required: true)
    scipoll.access_tokens.create!(Array.new(5) { {} })

    assert_enqueued_with(job: GenerateAccessTokensJob, args: ->(args) { args[1] == 1 }) do
      scipoll.update!(access_token_count: 6)
    end
  end

  test 'reset access tokens and submission' do
    scipoll = FactoryBot.create(:scipoll, :access_token_required, access_token_count: 1)
    expert = FactoryBot.create(:expert)
    scipoll.submissions.create!(user: expert, submitted_at: Time.current)
    scipoll.access_tokens.create!(user: expert)

    scipoll.reset_access_tokens!

    assert_empty scipoll.access_tokens.where.not(user_id: nil)
    assert_empty scipoll.submissions
  end
end

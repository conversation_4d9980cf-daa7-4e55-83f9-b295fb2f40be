# frozen_string_literal: true

require 'test_helper'

module Scipoll::Abilities
  class AccessTokenAbilityTest < ActiveSupport::TestCase
    test 'can :create_submission with access token ' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert ability.can?(:create_submission, scipoll), 'should be able to :create_submission'
    end

    test 'cannot :create_submission without access token' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:create_submission, scipoll),
                 'should not be able to :create_submission without an access token'
    end

    test 'cannot :create_submission with access token on draft SciPoll' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :draft, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:create_submission, scipoll),
                 'should not be able to :create_submission on draft SciPoll'
    end

    test 'cannot :create_submission with access token on closed SciPoll' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :closed, :published, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:create_submission, scipoll),
                 'should not be able to :create_submission on closed SciPoll'
    end

    test 'cannot :create_submission as non-user' do
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)

      ability = AccessTokenAbility.new(nil)

      assert_not ability.can?(:create_submission, scipoll), 'should not be able to :create_submission'
    end

    test 'can :view_landing_page with access token ' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert ability.can?(:view_landing_page, scipoll), 'should be able to :view_landing_page'
    end

    test 'cannot :view_landing_page without access token' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:view_landing_page, scipoll), 'should not be able to :view_landing_page'
    end

    test 'cannot :view_landing_page with access token on draft SciPoll' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :draft, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:view_landing_page, scipoll),
                 'should not be able to :view_landing_page on draft SciPoll'
    end

    test 'can :update submission with access token' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)
      submission = scipoll.submissions.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert ability.can?(:update, submission), 'should be able to :update submission'
    end

    # In the case an access token is deleted or revoked for some reason.
    test 'cannot :update submission without access token' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :published, :allow_guest_participation)
      submission = scipoll.submissions.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:update, submission), 'should not be able to :update submission without an access token'
    end

    test 'cannot :update submission on a draft SciPoll' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :draft, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)
      submission = scipoll.submissions.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:update, submission), 'should not be able to :update submission on a draft SciPoll'
    end

    test 'cannot :update submission on a closed SciPoll' do
      user = FactoryBot.create(:user)
      scipoll = FactoryBot.create(:scipoll, :closed, :published, :allow_guest_participation)
      scipoll.access_tokens.create!(user:)
      submission = scipoll.submissions.create!(user:)

      ability = AccessTokenAbility.new(user)

      assert_not ability.can?(:update, submission), 'should not be able to :update submission on a closed SciPoll'
    end

    test 'cannot :view_results without an access token and submission' do
      guest = FactoryBot.create(:guest)
      scipoll = FactoryBot.create(:scipoll, :access_token_required, :allow_guest_participation, :results_published)

      ability = AccessTokenAbility.new(guest)

      assert ability.cannot?(:view_results, scipoll),
             'should be able to :view_results with an access token and submission'
    end

    test 'can :view_results with an access token and submission' do
      guest = FactoryBot.create(:guest)
      scipoll = FactoryBot.create(:scipoll,
                                  :access_token_required,
                                  :allow_guest_participation,
                                  :published,
                                  :results_published)
      scipoll.access_tokens.create!(user: guest)
      scipoll.submissions.create!(user: guest)

      ability = AccessTokenAbility.new(guest)

      assert ability.can?(:view_results, scipoll),
             'should be able to :view_results with an access token and submission'
    end
  end
end

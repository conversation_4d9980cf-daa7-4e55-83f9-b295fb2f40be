# frozen_string_literal: true

require 'test_helper'

class InviteTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  test 'applicant? is true by default' do
    assert Invite.new.applicant?, 'should be an applicant by default'
  end

  test 'observers are not applicants' do
    assert_not Invite.new(observer: true).applicant?, 'observers should not be applicants'
  end

  test 'submitted submission status' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    panelist = scipi.panelists.create!(expert:)
    FactoryBot.create(:submission, :final, scipi:, submitter: expert)

    assert_equal 'Submitted', panelist.submission_status
  end

  test 'draft submission status' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    panelist = scipi.panelists.create!(expert:)
    FactoryBot.create(:submission, :draft, scipi:, submitter: expert)

    assert_equal 'Draft', panelist.submission_status
  end

  test 'pending invite status' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    assert applicant.pending?, 'should be in a pending state by default'
    assert_equal 'Pending', applicant.authorization_status
  end

  test 'rejected invite status' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, rejected_at: Time.current)

    assert applicant.rejected?, 'should be in a pending state by default'
    assert_equal 'Rejected', applicant.authorization_status
  end

  test 'approved invite status' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current)

    assert applicant.approved?, 'should be in a approved state by default'
    assert_equal 'Approved', applicant.authorization_status
  end

  test 'pending applicants should not have an expert number' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)

    applicant = scipi.applicants.create!(expert:)

    assert_nil applicant.expert_number
  end

  test 'un-approving applicants does not change the expert_number' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current)

    assert_no_changes(-> { applicant.reload.expert_number }) do
      applicant.update!(approved_at: nil)
    end
  end

  test 'pending for a while is defacto_rejected' do
    recruitment_closes_on = (Surveys::INACTIVITY_DURATION + 1.day).ago
    scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on:)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    assert applicant.defacto_rejected?, 'should be defacto rejected'
  end

  test 'approved for a while is not defacto_rejected' do
    recruitment_closes_on = (Surveys::INACTIVITY_DURATION + 1.day).ago
    scipi = FactoryBot.create(:scipi, :invite_only, recruitment_closes_on:)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: 1.year.ago)

    assert_not applicant.defacto_rejected?, 'approved applicants should not be defacto rejected'
  end

  test 'a panelist without a submission is "not started"' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    assert applicant.not_started?, 'should be not started since there is no submission'
  end

  test 'a panelist with a submission is not "not started"' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)
    FactoryBot.create(:submission, scipi:, submitter: expert)

    assert_not applicant.not_started?, 'should be started since there is a submission'
  end

  test 'panelists are not payable without a final submission' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current)
    FactoryBot.create(:submission, :draft, scipi:, submitter: expert)

    assert_not applicant.payable?, 'should not payable without a final submission'
  end

  test 'panelists are not payable unless approved with a final submission' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, rejected_at: Time.current)
    FactoryBot.create(:submission, :final, scipi:, submitter: expert)

    assert_not applicant.payable?, 'should not payable unless approved'
  end

  test 'should be payable if approved with final submission' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current)
    FactoryBot.create(:submission, :final, scipi:, submitter: expert)

    assert applicant.payable?, 'should payable when approved with a final submission'
  end

  test 'application is complete when an expert\'s profile is complete' do
    # The :complete_profile trait, specifically the :expertise_complete sub-trait is not working correctly
    publications_count = Faker::Number.between(from: 100, to: 300)
    publications_first_last_count = Faker::Number.between(from: 1, to: 99)
    scipi = FactoryBot.create(:scipi, :invite_only)
    country = FactoryBot.create(:country)
    expertise = FactoryBot.create(:expertise)
    expert = FactoryBot.create(:expert, :education_complete, :employment_complete, :with_cv)
    expert.profile.update!(publications_count:, publications_first_last_count:, country:)
    expert.profile.taggings.create!(expertise:)
    expert.save! # Remove once country is on profile

    applicant = scipi.applicants.create!(expert:)

    assert applicant.application_complete?, 'applications should be complete when the profile is complete'
  end

  test 'applications are not complete without at least one degree' do
    publications_count = Faker::Number.between(from: 100, to: 300)
    publications_first_last_count = Faker::Number.between(from: 1, to: 99)
    scipi = FactoryBot.create(:scipi, :invite_only)
    country = FactoryBot.create(:country)
    expertise = FactoryBot.create(:expertise)
    expert = FactoryBot.create(:expert, :employment_complete, :with_cv)
    expert.profile.update!(publications_count:, publications_first_last_count:, country:)
    expert.profile.taggings.create!(expertise:)
    expert.save! # Remove once country is on profile

    applicant = scipi.applicants.create!(expert:)

    assert_not applicant.application_complete?, 'applications should be complete without at least one profile'
  end

  test 'applications are not complete without work experience' do
    publications_count = Faker::Number.between(from: 100, to: 300)
    publications_first_last_count = Faker::Number.between(from: 1, to: 99)
    scipi = FactoryBot.create(:scipi, :invite_only)
    country = FactoryBot.create(:country)
    expertise = FactoryBot.create(:expertise)
    expert = FactoryBot.create(:expert, :education_complete, :with_cv)
    expert.profile.update!(publications_count:, publications_first_last_count:, country:)
    expert.profile.taggings.create!(expertise:)
    expert.save! # Remove once country is on profile

    applicant = scipi.applicants.create!(expert:)

    assert_not applicant.application_complete?, 'applications should be complete without work experience'
  end

  test 'applications are not complete publications' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    country = FactoryBot.create(:country)
    expertise = FactoryBot.create(:expertise)
    expert = FactoryBot.create(:expert, :education_complete, :employment_complete, :with_cv)
    expert.profile.taggings.create!(expertise:)
    expert.profile.update!(publications_count: 0, publications_first_last_count: 0, country:)

    applicant = scipi.applicants.create!(expert:)

    assert_not applicant.application_complete?, 'applications should be complete without publications'
  end

  test 'applications are not complete without a current employment sector' do
    # The :complete_profile trait, specifically the :expertise_complete sub-trait is not working correctly
    publications_count = Faker::Number.between(from: 100, to: 300)
    publications_first_last_count = Faker::Number.between(from: 1, to: 99)
    scipi = FactoryBot.create(:scipi, :invite_only)
    country = FactoryBot.create(:country)
    expertise = FactoryBot.create(:expertise)
    expert = FactoryBot.create(:expert, :education_complete, :employment_complete, :with_cv)
    expert.profile.update!(publications_count:, publications_first_last_count:, country:)
    expert.update!(current_employment_sector: nil)
    expert.profile.taggings.create!(expertise:)
    expert.save! # Remove once country is on profile

    applicant = scipi.applicants.create!(expert:)

    assert_not applicant.application_complete?, 'applications should be complete without a current employment sector'
  end

  test 'applications are not complete without a country' do
    publications_count = Faker::Number.between(from: 100, to: 300)
    publications_first_last_count = Faker::Number.between(from: 1, to: 99)
    scipi = FactoryBot.create(:scipi, :invite_only)
    expertise = FactoryBot.create(:expertise)
    expert = FactoryBot.create(:expert, :education_complete, :employment_complete, :with_cv)
    expert.profile.update!(publications_count:, publications_first_last_count:, country: nil)
    expert.profile.taggings.create!(expertise:)
    expert.save! # Remove once country is on profile

    applicant = scipi.applicants.create!(expert:)

    assert_not applicant.application_complete?, 'applications should be complete without a country'
  end

  test 'can invite to approved when applications are closed' do
    scipi = FactoryBot.create(:scipi, :invite_only, :recruitment_closed)
    expert = FactoryBot.create(:expert)

    assert_difference(-> { scipi.panelists.count }) do
      scipi.panelists.create!(expert:)
    end
  end

  test 'alternates are not pending' do
    applicant = Invite.new(marked_alternate_at: Time.current)

    assert_not applicant.pending?
  end

  test 'finalists are not pending' do
    applicant = Invite.new(selection_finalist_flagged_at: Time.current)

    assert_not applicant.pending?
  end

  test 'approved panelist is not suspended' do
    panelist = Invite.new(approved_at: Time.current, suspended_at: nil)

    assert_not panelist.suspended?, 'should not be suspended'
  end

  test 'approved panelist is suspended' do
    panelist = Invite.new(approved_at: Time.current, suspended_at: Time.current)

    assert panelist.suspended?, 'should be suspended'
  end

  test 'unapproved panelists cannot be suspended' do
    panelist = Invite.new(approved_at: nil, suspended_at: Time.current)

    assert_not panelist.suspended?, 'unapproved panelists should not be suspended'
  end

  test 'create panelist' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)

    panelist = Invite.create_panelist!(user: expert, scipi:)

    assert panelist.approved?, 'should be approved'
  end

  test 'create panelist assigns expert number' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)

    panelist = Invite.create_panelist!(user: expert, scipi:)

    assert_equal 1, panelist.expert_number
  end

  test 'suspended scope' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert1 = FactoryBot.create(:expert)
    suspended = scipi.panelists.create!(expert: expert1, suspended_at: Time.current)
    expert2 = FactoryBot.create(:expert)
    not_suspended = scipi.panelists.create!(expert: expert2, suspended_at: nil)

    suspended_panelists = scipi.panelists.suspended

    assert_includes suspended_panelists, suspended
    assert_not_includes suspended_panelists, not_suspended
  end

  test 'not suspended scope' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert1 = FactoryBot.create(:expert)
    active = scipi.panelists.create!(expert: expert1, suspended_at: nil)
    expert2 = FactoryBot.create(:expert)
    not_active = scipi.panelists.create!(expert: expert2, suspended_at: Time.current)

    active_panelists = scipi.panelists.not_suspended

    assert_includes active_panelists, active
    assert_not_includes active_panelists, not_active
  end

  test 'approve applicant' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    scipi.applicants.where(id: applicant.id).approve_all!

    assert applicant.reload.approved?, 'applicant should be approved'
  end

  test 'make applicant alternate' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    scipi.applicants.where(id: applicant.id).make_alternate_all!

    assert applicant.reload.alternate?, 'applicant should be a alternate'
  end

  test 'make applicant finalist' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    scipi.applicants.where(id: applicant.id).make_finalist_all!

    assert applicant.reload.finalist?, 'applicant should be a fimnalist'
  end

  test 'revert to pending' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, marked_alternate_at: Time.current)

    scipi.applicants.where(id: applicant.id).revert_all!

    assert applicant.reload.pending?, 'applicant should be pending'
  end

  test 'bulk reject applicant' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current)

    scipi.applicants.where(id: applicant.id).reject_all!

    assert applicant.reload.rejected?, 'applicant should be rejected'
  end

  test 'renumbering removes non-approved expert\'s numbers' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: nil, expert_number: 1)

    scipi.applicants.renumber!

    assert_nil applicant.reload.expert_number, 'the expert number should be nil'
  end

  test 'renumbering does not renumber applicants before the first gap in the sequence' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current, expert_number: 1)
    scipi.applicants.create!(expert:, approved_at: nil, expert_number: 3)

    assert_no_difference -> { applicant.reload.expert_number }, 'the expert number should not have changed' do
      scipi.applicants.renumber!
    end
  end

  test 'renumbering renumbers applicants after the first gap in the sequence' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:, approved_at: Time.current, expert_number: 2)

    scipi.applicants.renumber!

    assert_equal 1, applicant.reload.expert_number
  end

  test 'reject applicant' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    rejected_by = scipi.created_by
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    applicant.reject!(rejected_by:, reason: 'A reason')

    assert applicant.rejected?, 'applicant should be rejected'
    assert 'A reason', expert.notes.exists?(context: scipi, content: 'A reason')
  end

  test 'reject applicant without reason' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    rejected_by = scipi.created_by
    expert = FactoryBot.create(:expert)
    applicant = scipi.applicants.create!(expert:)

    applicant.reject!(rejected_by:, reason: 'A reason')

    assert applicant.rejected?, 'applicant should be rejected'
    assert 'A reason', expert.notes.exists?(context: scipi, content: 'A reason')
  end

  test 'enqueue copy profile job for applicants' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)

    assert_enqueued_with(job: Surveys::CopyProfileDataToApplicantJob, args: ->(args) { args.first.user == expert }) do
      scipi.applicants.create!(expert:)
    end
  end

  test 'do not enqueue copy profile job for non-applicants' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)

    assert_no_enqueued_jobs do
      scipi.applicants.create!(expert:, observer: true)
    end
  end

  test 'signed_contract scope' do
    scipi = FactoryBot.create(:scipi, :invite_only, :contract_required)

    signed = scipi.panelists.create!(expert: FactoryBot.create(:expert), contract_signed_at: Time.current)
    not_signed = scipi.panelists.create!(expert: FactoryBot.create(:expert))

    signed_panelists = scipi.panelists.signed_contract

    assert_includes signed_panelists, signed
    assert_not_includes signed_panelists, not_signed
  end

  test 'active panelists scope without required contract' do
    scipi = FactoryBot.create(:scipi, :invite_only)

    panelist = scipi.panelists.create!(expert: FactoryBot.create(:expert))
    suspended_panelist = scipi.panelists.create!(expert: FactoryBot.create(:expert), suspended_at: Time.current)

    active_panelists = scipi.panelists.active(contract_required: false)

    assert_includes active_panelists, panelist
    assert_not_includes active_panelists, suspended_panelist
  end

  test 'active panelists scope with required contract' do
    scipi = FactoryBot.create(:scipi, :invite_only, :contract_required)

    panelist_with_contract = scipi.panelists.create!(expert: FactoryBot.create(:expert), contract_signed_at: Time.current)
    panelist_without_contract = scipi.panelists.create!(expert: FactoryBot.create(:expert))
    suspended_panelist_with_contract = scipi.panelists.create!(expert: FactoryBot.create(:expert), contract_signed_at: Time.current, suspended_at: Time.current)
    suspended_panelist_without_contract = scipi.panelists.create!(expert: FactoryBot.create(:expert), suspended_at: Time.current)

    active_panelists = scipi.panelists.active(contract_required: true)

    assert_includes active_panelists, panelist_with_contract
    assert_not_includes active_panelists, panelist_without_contract
    assert_not_includes active_panelists, suspended_panelist_with_contract
    assert_not_includes active_panelists, suspended_panelist_without_contract
  end
end

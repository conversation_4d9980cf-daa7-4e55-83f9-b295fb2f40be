# frozen_string_literal: true

require 'test_helper'

class ResultDefinitionRenderTypeTest < ActiveSupport::TestCase
  test 'ranked order plot value' do
    name = 'Foo Bar'
    render_type = create(:render_type, name:)
    value = name.downcase.tr(' ', '-')

    assert_equal value,
                 render_type.value,
                 "the render type's value should be #{value}"
  end

  test 'system name' do
    name = 'Foobar chart'

    render_type = ResultDefinitionRenderType.new(name:)

    assert_equal 'foobar_chart', render_type.system_name
  end
end

# frozen_string_literal: true

require 'test_helper'

module Surveys
  class RequiresSelectionTest < ActiveSupport::TestCase
    test 'stats not out of date with no keywords or applicants' do
      scipi = FactoryBot.create(:scipi)

      assert_not scipi.selection_stats_out_of_date?
    end

    test 'stats not out of date with keywords but no applicants' do
      scipi = FactoryBot.create(:scipi)
      scipi.selection_keywords.create!(terms: ['foobar'])

      assert_not scipi.selection_stats_out_of_date?
    end

    test 'stats out of date when keyword counts are out of date' do
      scipi = FactoryBot.create(:scipi)
      selection_keyword = scipi.selection_keywords.create!(terms: ['foobar'], terms_updated_at: 2.weeks.ago)

      expert = users(:expert)
      expert.profile.update!(cv_text: 'I am a CV', extracted_cv_text_updated_at: Time.current)
      applicant = scipi.applicants.create!(expert:)
      applicant.keyword_counts.create!(keyword: selection_keyword, count: 3, updated_at: 1.week.ago)

      assert scipi.selection_stats_out_of_date?
    end

    test 'default score weights are valid' do
      scipi = FactoryBot.create(
        :survey,
        ge_weight: BigDecimal('0.333'),
        keyword_weight: BigDecimal('0.333'),
        audit_weight: BigDecimal('0.333')
      )

      assert_empty scipi.errors[:base], "Survey should be valid: #{scipi.errors.full_messages}"
    end

    test 'custom score weights add to 1' do
      scipi = FactoryBot.create(:survey,
                                ge_weight: BigDecimal('0.2'),
                                keyword_weight: BigDecimal('0.3'),
                                audit_weight: BigDecimal('0.5'))

      assert_empty scipi.errors[:base], "Survey should be valid: #{scipi.errors.full_messages}"
    end

    test 'score weights do not add to 1' do
      scipi = QuestionGroup.new(
        ge_weight: BigDecimal('0.2'),
        keyword_weight: BigDecimal('0.2'),
        audit_weight: BigDecimal('0.3')
      )

      assert_not scipi.valid?
      assert_includes scipi.errors[:base], 'Score weights must sum to 1.0'
    end

    test 'adjust 0.33 weights to 0.333' do
      scipi = QuestionGroup.new(
        ge_weight: BigDecimal('0.33'),
        keyword_weight: BigDecimal('0.33'),
        audit_weight: BigDecimal('0.33')
      )

      # Run validation to adjust weights
      scipi.valid?

      assert_equal BigDecimal('0.333'), scipi.ge_weight
      assert_equal BigDecimal('0.333'), scipi.keyword_weight
      assert_equal BigDecimal('0.333'), scipi.audit_weight
    end

    test 'do not adjust 0.33 weights if weights are valid' do
      scipi = FactoryBot.create(
        :survey,
        ge_weight: BigDecimal('0.33'),
        keyword_weight: BigDecimal('0.67'),
        audit_weight: BigDecimal('0.0')
      )

      assert_equal BigDecimal('0.33'), scipi.ge_weight
      assert_equal BigDecimal('0.67'), scipi.keyword_weight
      assert_equal BigDecimal('0.0'), scipi.audit_weight
    end

    test 'enabled keyword weights sum to 1' do
      scipi = FactoryBot.create(:survey)
      scipi.update(
        selection_keywords: [
          Surveys::Selection::Keyword.new(terms: ['foo'], weight: BigDecimal('0.5')),
          Surveys::Selection::Keyword.new(terms: ['bar'], weight: BigDecimal('0.5')),
          Surveys::Selection::Keyword.new(terms: ['baz'], enabled: false, weight: BigDecimal('0.5'))
        ]
      )

      assert_empty scipi.errors[:selection_keywords], 'selection_keywords should be valid'
    end

    # Sometimes the weights sum to almost one since not all
    # counts divide evenly into 1, e.g. 1/3 => 0.333
    test 'enabled keyword weights sum to almost 1' do
      scipi = FactoryBot.create(:survey)
      scipi.update(
        selection_keywords: [
          Surveys::Selection::Keyword.new(terms: ['foo'], weight: BigDecimal('0.333')),
          Surveys::Selection::Keyword.new(terms: ['bar'], weight: BigDecimal('0.333')),
          Surveys::Selection::Keyword.new(terms: ['baz'], weight: BigDecimal('0.333'))
        ]
      )

      assert_empty scipi.errors[:selection_keywords], 'selection_keywords should be valid'
    end

    test 'keyword weights do not sum to 1' do
      scipi = FactoryBot.create(:survey)

      scipi.update(
        selection_keywords: [
          Surveys::Selection::Keyword.new(terms: ['foo'], weight: BigDecimal('0.4')),
          Surveys::Selection::Keyword.new(terms: ['bar'], weight: BigDecimal('0.4'))
        ]
      )

      assert_equal 'weights must sum to 1.0 (currently 0.8)',
                   scipi.errors[:selection_keywords].first,
                   'selection_keywords should be not be valid'
    end

    test 'selection_open scope when closed' do
      scipi = FactoryBot.create(:scipi, :invite_only, selection_closes_on: 1.week.ago.to_date)

      surveys = QuestionGroup.selection_open

      assert_not_includes surveys, scipi
    end

    test 'selection_open scope when open' do
      scipi = FactoryBot.create(:scipi, :invite_only, selection_closes_on: 1.week.from_now.to_date)

      surveys = QuestionGroup.selection_open

      assert_includes surveys, scipi
    end

    test 'selection is not open when draft' do
      scipi = FactoryBot.create(:scipi, :invite_only, :draft, selection_closes_on: 1.week.from_now.to_date)

      assert_not scipi.selection_open?
    end

    test 'selection is not open when published with past deadline' do
      scipi = FactoryBot.create(:scipi, :invite_only, :published, selection_closes_on: 1.week.ago.to_date)

      assert_not scipi.selection_open?
    end

    test 'selection is not open with future deadline' do
      scipi = FactoryBot.create(:scipi, :invite_only, :published, selection_closes_on: 1.week.from_now.to_date)

      assert scipi.selection_open?
    end
  end
end

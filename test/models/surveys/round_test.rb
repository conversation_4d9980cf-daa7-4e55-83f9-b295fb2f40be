# frozen_string_literal: true

require 'test_helper'

module Surveys
  class RoundTest < ActiveSupport::TestCase
    include Scipis::Reminders::TestHelper

    test 'title' do
      round = Round.new(name: 'Opening Round', position: 1)

      assert_equal 'Round 1: Opening Round', round.title
    end

    test 'send_closing_reminder_at is a day before closes_at when < 10 days' do
      round = FactoryBot.create(:question_round, duration: 10.days)
      expected = 1.day.before(round.closes_at)

      assert_equal expected, round.send_closing_reminder_at
    end

    test 'set send_closing_reminder_at on for long round' do
      round = FactoryBot.create(:question_round, duration: 11.days)

      expected = 2.days.before(round.closes_at)

      assert_equal expected, round.send_closing_reminder_at
    end

    test 'send_closing_reminder_at is adjusted the close time changes before close' do
      round = FactoryBot.create(:question_round)

      # Needs to be saved before the round closes
      travel_to 1.day.after(round.opens_at) do
        # Just that it check that it changes. We already check the expected value elsewhere
        assert_changes -> { round.send_closing_reminder_at } do
          round.update!(closes_at: round.closes_at + 1.day)
        end
      end
    end

    test 'do not set send_closing_reminder_at if closing_reminder_sent_at is already set' do
      round = FactoryBot.create(:question_round)

      round.closing_reminder_sent!

      # Needs to be saved before the round closes
      travel_to 1.day.after(round.opens_at) do
        assert_no_changes -> { round.send_closing_reminder_at } do
          round.update!(closes_at: round.closes_at + 1.day)
        end
      end
    end

    test 'do not set send_closing_reminder_at if duration_in_days is 1 day' do
      skip('Implement this test when rounds can have a duration of 1 day')
    end

    test 'clear send_closing_reminder_at if duration_in_days goes below 1 day' do
      skip('Implement this test when rounds can have a duration of 1 day')
    end

    test 'do not change send_closing_reminder_at if already closed' do
      round = FactoryBot.create(:question_round)

      new_closes_at = round.closes_at + 1.day

      travel_to 1.hour.after(new_closes_at) do
        assert_no_changes -> { round.send_closing_reminder_at } do
          round.update!(closes_at: new_closes_at)
        end
      end
    end

    test 'next round cannot starts before previous round ends' do
      scipi = FactoryBot.create(:scipi)

      round1 = FactoryBot.create(
        :question_round,
        survey: scipi,
        position: 1,
        opens_at: 3.days.ago,
        closes_at: 1.day.from_now
      )

      # Create second round that starts before first round closes
      round2 = FactoryBot.build(
        :question_round,
        survey: scipi,
        position: 2,
        opens_at: Time.current,
        closes_at: 2.days.from_now
      )

      assert_not round2.valid?, 'Round should be invalid when it starts before the previous round ends'
      assert_includes round2.errors[:opens_at],
                      "must be after Round #{round1.number}'s end time of #{round1.closes_at.strftime('%b %d, %Y at %l:%M %p %Z')}"
    end

    test 'chronological validation is not applied to first round' do
      scipi = FactoryBot.create(:scipi)

      round1 = FactoryBot.build(
        :question_round,
        survey: scipi,
        position: 1,
        opens_at: 1.day.ago,
        closes_at: 2.days.from_now
      )

      assert round1.valid?, 'First round should be valid regardless of dates'
    end
  end
end

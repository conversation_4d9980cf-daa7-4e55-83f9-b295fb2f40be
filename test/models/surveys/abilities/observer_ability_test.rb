# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Abilities
    class ObserverAbilityTest < ActiveSupport::TestCase
      test 'cannot export results from drafts when observer_exports is true' do
        survey = create_survey!(observer_exports: true, published: false)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert_not ability.can?(:export_results, survey),
                   'should not be able to export results from drafts when observer_exports is true'
      end

      test 'cannot export_results when published but observer_exports is false' do
        survey = create_survey!(observer_exports: false, published_at: 1.week.ago)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert_not ability.can?(:export_results, survey),
                   'should not be able to export results when published but observer_exports is false'
      end

      test 'can export results when observer_exports is true' do
        survey = create_survey!(observer_exports: true, published_at: 1.week.ago)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:export_results, survey), 'should be able to export results when observer_exports is true'
      end

      test 'can preview draft surveys' do
        survey = create_survey!
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:preview, survey), 'should be able to preview draft surveys'
      end

      test 'can preview published surveys' do
        survey = create_survey!(published_at: 1.week.ago)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:preview, survey), 'should be able to preview published surveys'
      end

      test 'can view_observe_page for draft surveys' do
        survey = create_survey!
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:view_observe_page, survey), 'should be able to view_observe_page for draft surveys'
      end

      test 'can view_observe_page published surveys' do
        survey = create_survey!(published_at: 1.week.ago)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:view_observe_page, survey), 'should be able to view_observe_page published surveys'
      end

      test 'cannot view_results for draft surveys' do
        survey = create_survey!
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert_not ability.can?(:view_results, survey), 'should not be able to view_results for draft surveys'
      end

      test 'can view_results published surveys with unpublished results' do
        survey = create_survey!(published_at: 1.week.ago, results_published: false)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:view_results, survey),
               'should be able to view_results published surveys with unpublished results'
      end

      test 'can view_results published surveys with published results' do
        survey = create_survey!(published_at: 1.week.ago, results_published: true)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:view_results, survey),
               'should be able to view_results published surveys with published results'
      end

      test 'cannot view_report when observer_exports is false' do
        survey = create_survey!(observer_exports: false, published_at: 1.week.ago)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert_not ability.can?(:view_report, survey),
                   'should not be able to view_report when observer_exports is false'
      end

      test 'cannot view_report from drafts when observer_exports is true' do
        survey = create_survey!(observer_exports: true, published: false)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert_not ability.can?(:view_report, survey),
                   'should not be able to view_report from drafts when observer_exports is true'
      end

      test 'can view_report when observer_exports is true' do
        survey = create_survey!(observer_exports: true, published_at: 1.week.ago)
        user = create_user_as_observer!(survey)

        ability = ObserverAbility.new(user)

        assert ability.can?(:view_report, survey), 'should be able to view_report when observer_exports is true'
      end

      test 'can download_attachment with unpublished results' do
        survey = create_survey!(published_at: 1.week.ago, results_published: false)
        answer = create_answer!(survey)
        user = create_user_as_observer!(survey)

        ability = Ability.new(user)

        assert ability.can?(:download_attachment, answer),
               'should be able to download_attachment with unpublished results'
      end

      test 'cannot download_attachment on draft survey' do
        survey = create_survey!(results_published: false)
        answer = create_answer!(survey)
        user = create_user_as_observer!(survey)

        ability = Ability.new(user)

        assert_not ability.can?(:download_attachment, answer),
                   'should not be able to download_attachment on draft survey'
      end

      private

      def create_answer!(survey)
        question = survey.questions.create!(question_text: '???', position: 1)
        expert = create_expert!
        submission = survey.submissions.create!(submitter: expert)
        submission.answers.create!(question:)
      end

      def create_survey!(**attrs)
        FactoryBot.create(:scipi, name: 'Ability tests', **attrs)
      end

      def create_user!(**)
        User.create!(email: TestData.email, password: TestData.password, **)
      end

      def create_user_as_observer!(survey)
        user = create_user!
        survey.observers.create!(survey:, user:)
        user
      end
    end
  end
end

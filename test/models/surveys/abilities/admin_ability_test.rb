# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Abilities
    class AdminAbilityTest < ActiveSupport::TestCase
      test 'only super admins can :create a survey' do
        role = FactoryBot.create(:role, admin: true)
        user = FactoryBot.create(:user, roles: [role])

        ability = Surveys::Abilities::AdminAbility.new(user)

        assert ability.can?(:create, QuestionGroup)
      end

      test 'scipi admins cannot :create a survey' do
        role = FactoryBot.create(:role, scipi_admin: true)
        user = FactoryBot.create(:user, roles: [role])

        ability = Surveys::Abilities::AdminAbility.new(user)

        assert ability.cannot?(:create, QuestionGroup)
      end

      test 'only super admins can :destroy a survey' do
        role = FactoryBot.create(:role, admin: true)
        user = FactoryBot.create(:user, roles: [role])

        ability = Surveys::Abilities::AdminAbility.new(user)

        assert ability.can?(:destroy, QuestionGroup)
      end

      test 'scipi admins cannot :destroy a survey' do
        role = FactoryBot.create(:role, scipi_admin: true)
        user = FactoryBot.create(:user, roles: [role])

        ability = Surveys::Abilities::AdminAbility.new(user)

        assert ability.cannot?(:destroy, QuestionGroup)
      end

      test 'scipi admins can :use_chart_tools' do
        role = FactoryBot.create(:role, scipi_admin: true)
        user = FactoryBot.create(:user, roles: [role])
        scipi = FactoryBot.create(:scipi, created_by: user)
        result_def = FactoryBot.create(:custom_result, survey: scipi)

        ability = Surveys::Abilities::AdminAbility.new(user)

        assert ability.can?(:use_chart_tools, result_def)
      end

      test 'cannot:use_chart_tools' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi)
        result_def = FactoryBot.create(:custom_result, survey: scipi)

        ability = Surveys::Abilities::AdminAbility.new(user)

        assert ability.cannot?(:use_chart_tools, result_def)
      end
    end
  end
end

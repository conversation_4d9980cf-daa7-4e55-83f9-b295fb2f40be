# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Abilities
    # Tests Experts' debate-related abilities
    class ExpertDebateAbilityTest < ActiveSupport::TestCase
      #
      # :create_comment permissions without rounds
      #

      test 'cannot :create_comment when debate is not open' do
        debate = Debate.new(open: false)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_comment, debate),
                   'should not be able to :create_comment when debate is not open'
      end

      test 'cannot :create_comment if not in debaters list' do
        debater = create_expert!
        debate = Debate.new(open: true, allowed_debaters: User.where(id: debater.id))
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_comment, debate),
                   'should not be able to :create_comment if in not debaters list'
      end

      test 'can :create_comment when debate is open to all' do
        debate = Debate.new(open: true)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert ability.can?(:create_comment, debate), 'should be able to :create_comment when debate is open to all'
      end

      test 'cannot :create_comment if not an expert' do
        debate = Debate.new(open: true)
        user = FactoryBot.create(:observer)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_comment, debate), 'should not be able to :create_comment when not an expert'
      end

      test 'can :create_comment if in debaters list' do
        user = create_expert!
        debate = Debate.new(open: true, allowed_debaters: User.where(id: user.id))

        ability = ExpertAbility.new(user)

        assert ability.can?(:create_comment, debate), 'should be able to :create_comment if in debaters list'
      end

      #
      # :delete comment permissions
      #

      test 'non-authors cannot :delete a comment' do
        user = create_expert!
        comment = FactoryBot.create(:comment)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:delete, comment), 'should not be able to delete another\'s comment'
      end

      test 'authors can :delete a comment while debate is open' do
        user = create_expert!
        survey = FactoryBot.create(:survey, comments_close_date: 1.day.from_now)
        result = FactoryBot.create(:result_definition, survey:)
        comment = result.comments.create!(author: user, debate_topic: result, content: 'I am a comment')

        ability = ExpertAbility.new(user)

        assert ability.can?(:delete, comment), 'should be able to :delete own comment'
      end

      test 'authors cannot :delete a comment once debate is closed' do
        user = create_expert!
        survey = FactoryBot.create(:survey, :comments_closed)
        result = FactoryBot.create(:result_definition, survey:)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:delete, comment), 'should not be able to :delete own comment once debate is closed'
      end

      #
      # :vote permissions
      #

      test 'non-experts cannot vote on comments' do
        user = FactoryBot.create(:observer)
        survey = FactoryBot.create(:survey, :general_participation)
        result = FactoryBot.create(:result_definition, survey:)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:vote, comment), 'should not be able to :vote an a observer'
      end

      test 'authors cannot vote on own comments' do
        user = create_expert!
        survey = FactoryBot.create(:survey, :general_participation)
        result = FactoryBot.create(:result_definition, survey:)
        comment = FactoryBot.create(:comment, author: user, debate_topic: result)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:vote, comment), 'should not be able to :vote on own comment'
      end

      test 'cannot vote once debate is closed' do
        user = create_expert!
        survey = FactoryBot.create(:survey, :comments_closed, :general_participation)
        result = FactoryBot.create(:result_definition, survey:)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:vote, comment), 'should not be able to :vote once debate is closed'
      end

      test 'cannot vote when not allowed to participate' do
        user = create_expert!
        survey = FactoryBot.create(:survey, :invite_only)
        result = FactoryBot.create(:result_definition, survey:)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:vote, comment), 'should not be able to :vote when not allowed to participate'
      end

      test 'can vote when allowed to participate' do
        user = create_expert!
        survey = FactoryBot.create(:survey, :general_participation)
        result = FactoryBot.create(:result_definition, survey:)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(user)

        assert ability.can?(:vote, comment), 'should be able to :vote when allowed to participate'
      end

      #
      # Unverified expert tests for SciPolls
      #

      test 'unverified experts cannot cannot :create_comment on SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, :results_published)
        expert = FactoryBot.create(:expert, :unverified)
        _panelist = scipi.panelists.create!(expert:, approved_at: Time.current)
        result = FactoryBot.create(:result_definition, survey: scipi)
        debate = result.to_debate

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:create_comment, debate), 'should not be able to :create_comment when debate is open'
      end

      test 'unverified experts cannot cannot :delete a comment on SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, :results_published)
        expert = FactoryBot.create(:expert, :unverified)
        _panelist = scipi.panelists.create!(expert:, approved_at: Time.current)
        result = FactoryBot.create(:result_definition, survey: scipi)
        comment = result.comments.create!(author: expert, content: 'I am a comment')

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:delete, comment), 'should not be able to delete a comment'
      end

      test 'unverified experts cannot cannot :vote on a comment on SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, :results_published)
        expert = FactoryBot.create(:expert, :unverified)
        _panelist = scipi.panelists.create!(expert:, approved_at: Time.current)
        result = FactoryBot.create(:result_definition, survey: scipi)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:vote, comment), 'should not be able to vote on a comment'
      end

      #
      # Unverified expert tests for SciPolls
      #

      test 'unverified experts cannot cannot :create_comment on SciPolls' do
        scipoll = FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)
        expert = FactoryBot.create(:expert, :unverified)
        result = FactoryBot.create(:result_definition, survey: scipoll)
        debate = result.to_debate

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:create_comment, debate), 'should not be able to :create_comment when debate is open'
      end

      test 'unverified experts cannot cannot :delete a comment on SciPolls' do
        scipoll = FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)
        expert = FactoryBot.create(:expert, :unverified)
        result = FactoryBot.create(:result_definition, survey: scipoll)
        comment = result.comments.create!(author: expert, content: 'I am a comment')

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:delete, comment), 'should not be able to delete a comment'
      end

      test 'unverified experts cannot cannot :vote on a comment on SciPolls' do
        scipoll = FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)
        expert = FactoryBot.create(:expert, :unverified)
        result = FactoryBot.create(:result_definition, survey: scipoll)
        comment = FactoryBot.create(:comment, debate_topic: result)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:vote, comment), 'should not be able to vote on a comment'
      end
    end
  end
end

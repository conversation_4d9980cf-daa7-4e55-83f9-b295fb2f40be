# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Abilities
    class ExpertAbilityTest < ActiveSupport::TestCase
      #
      # Unverified user cases for SciPis
      #

      test 'unverified users cannot :apply to SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, :recruitment_open)
        expert = FactoryBot.create(:expert, :unverified)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:apply, scipi), 'unverified experts should not be able to :apply to SciPis'
      end

      test 'unverified users cannot :update applications to SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, :selection_open)
        expert = FactoryBot.create(:expert, :unverified)
        applicant = scipi.applicants.create!(user: expert)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:update, applicant),
                   'unverified experts should not be able to :update applications to SciPis'
      end

      test 'unverified users cannot :create_submission on a SciPi' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert, :unverified)
        scipi.panelists.create!(user: expert)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:create_submission, scipi),
                   'unverified experts should not be able to :create_submission on a SciPi'
      end

      test 'unverified users cannot :update submissions on a SciPi' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert, :unverified)
        scipi.panelists.create!(user: expert)
        submission = scipi.submissions.create!(submitter: expert)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:update, submission),
                   'unverified experts should not be able to :update submissions on a SciPi'
      end

      test 'unverified users cannot :download review materials on SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert, :unverified)
        scipi.panelists.create!(user: expert)
        material = scipi.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:download, material),
                   'unverified experts should not be able to :download review materials on SciPis'
      end

      test 'unverified users cannot :view_results on SciPis' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published, :results_published)
        expert = FactoryBot.create(:expert, :unverified)
        scipi.panelists.create!(user: expert)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:view_results, scipi),
                   'unverified experts should not be able to :view_results on SciPis'
      end

      #
      # Unverified user cases for SciPolls
      #

      test 'unverified users cannot :create_submission on a SciPoll' do
        scipoll = FactoryBot.create(:scipoll, :published)
        expert = FactoryBot.create(:expert, :unverified)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:create_submission, scipoll),
                   'unverified experts should not be able to :create_submission on a SciPoll'
      end

      test 'unverified users cannot :update submissions on SciPoll' do
        scipoll = FactoryBot.create(:scipoll, :published)
        expert = FactoryBot.create(:expert, :unverified)
        submission = scipoll.submissions.create!(submitter: expert)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:update, submission),
                   'unverified experts should not be able to :update submissions on SciPoll'
      end

      test 'unverified users can :view_results public results on SciPoll' do
        scipoll = FactoryBot.create(:scipoll, :published, :closed, :results_published, :results_public)
        expert = FactoryBot.create(:expert, :unverified)

        ability = ExpertAbility.new(expert)

        # Anyone can view results on a SciPoll so long as they are public,
        # so unverified experts should not be blocked
        assert ability.can?(:view_results, scipoll), 'unverified experts should be able to :view_results on SciPoll'
      end

      #
      # Unverified user cases for legacy surveys
      #

      test 'unverified users cannot :view_results public results on a legacy survey' do
        survey = FactoryBot.create(:scipoll, :published, :results_published)
        expert = FactoryBot.create(:expert, :unverified)
        survey.submissions.create!(submitter: expert)

        ability = ExpertAbility.new(expert)

        assert_not ability.can?(:view_results, survey),
                   'unverified experts should not be able to :view_results on legacy survey'
      end

      #
      # :create_submission cases for Surveys without Rounds
      #

      test 'cannot :create_submission for a draft general participation survey' do
        survey = QuestionGroup.new(invite_only: false)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_submission, survey),
                   'should not be able to :create_submission for a draft general participation survey'
      end

      test 'non-experts cannot :create_submission for an open, published, general participation survey' do
        survey = QuestionGroup.new(published_at: 1.week.ago, closes_at: nil, invite_only: false, access_code: nil)
        user = FactoryBot.create(:observer)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_submission, survey),
                   'should not be able to :create_submission for an open, published, general participation survey'
      end

      test 'experts can :create_submission for a open, published survey with open participation' do
        survey = QuestionGroup.new(published_at: 1.week.ago, closes_at: nil, invite_only: false, access_code: nil)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert ability.can?(:create_submission, survey),
               'should be able to :create_submission for an open, published, general participation, survey'
      end

      test 'experts cannot :create_submission for a access_token_required survey' do
        survey = QuestionGroup.new(published_at: 1.week.ago, closes_at: nil, invite_only: false, access_token_required: true)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert ability.cannot?(:create_submission, survey),
               'should not be able to :create_submission for a non-general-participation survey'
      end

      test 'non-panelists cannot :create_submission for a scipi' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only, :published)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_submission, scipi),
                   'should not be able to :create_submission as a non-panelist'
      end

      test 'non-experts cannot :create_submission for an open, published, selection-required survey' do
        survey = QuestionGroup.new(published_at: 1.week.ago, closes_at: nil, invite_only: true, access_code: nil)
        user = FactoryBot.create(:observer)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_submission, survey),
                   'should not be able to :create_submission for an open, published, selection-required survey'
      end

      test 'cannot :create_submission without signed contract' do
        scipi = FactoryBot.create(:scipi, :contract_required, :invite_only, :published)
        user = create_expert!
        scipi.panelists.create!(user:)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:create_submission, scipi),
                   'should not be able to :create_submission without a signed contract'
      end

      test 'experts can :create_submission for a open, published survey with selection-required' do
        survey = FactoryBot.create(:scipi, :contract_required, :invite_only, :published)
        user = create_expert!
        survey.panelists.create!(user:, contract_signed_at: 1.week.ago)

        ability = ExpertAbility.new(user)

        assert ability.can?(:create_submission, survey),
               'should be able to :create_submission for an open, published, selection-required, survey'
      end

      #
      # :create_submission cases for SciPis with Rounds
      #

      test 'cannot :create_submission when a question round is not open' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only)
        scipi.panelists.create(user: expert)
        round = FactoryBot.create(:question_round, scipi:)

        ability = ExpertAbility.new(expert)

        travel_to 1.hour.before(round.opens_at) do
          assert ability.cannot?(:create_submission, round),
                 'should not be able to :create_submission when the round is not open'
        end
      end

      test 'cannot :create_submission on question round when panelist is not active' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, :inactive, user: expert, survey: scipi)
        round = FactoryBot.create(:question_round, scipi:)

        ability = ExpertAbility.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.cannot?(:create_submission, round),
                 'should not be able to :create_submission when the panelist is not active'
        end
      end

      test 'can :create_submission on question round' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:question_round, scipi:)

        ability = ExpertAbility.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.can?(:create_submission, scipi), 'should be able to :create_submission when the round is open'
        end
      end

      test 'can :create_submission on question round when survey close date has passed' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published, closes_at: 1.week.ago)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        FactoryBot.create(:question_round, scipi:, opens_at: 1.day.ago)

        scipi.reload

        ability = ExpertAbility.new(expert)

        assert ability.can?(:create_submission, scipi), 'should be able to :create_submission when the round is open'
      end

      #
      # :apply cases
      #

      test 'cannot :apply to a general participation survey' do
        survey = QuestionGroup.new(published_at: 1.week.ago, invite_only: false)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:apply, survey), 'should not be able to :apply to a general participation survey'
      end

      test 'cannot :apply to a requires-selection survey' do
        survey = QuestionGroup.new(published_at: 1.week.ago, invite_only: true)
        user = FactoryBot.create(:observer)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:apply, survey), 'non-experts should not be able to :apply'
      end

      test 'cannot :apply to a draft, requires-selection survey' do
        survey = QuestionGroup.new(invite_only: true)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:apply, survey), 'should not be able to drafts'
      end

      test 'cannot :apply to a requires-selection survey when recruitment is closed' do
        survey = QuestionGroup.new(published_at: 1.week.ago, invite_only: true, recruitment_closes_on: 2.days.ago)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:apply, survey), 'should not be able to :apply when recruitment is closed'
      end

      test 'can :apply to a requires-selection survey when recruitment is open' do
        survey = QuestionGroup.new(published_at: 1.week.ago, invite_only: true, recruitment_closes_on: 1.week.from_now)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert ability.can?(:apply, survey), 'should be able to :apply when recruitment is open'
      end

      #
      # :view_results cases without Rounds
      #

      test 'cannot :view_results when results unpublished' do
        survey = FactoryBot.create(:scipi, :invite_only, :published)
        survey.update!(results_published: false)
        user = create_expert!
        survey.panelists.create!(user:)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:view_results, survey), 'should be able to  :view_results when results unpublished'
      end

      test 'cannot :view_results when not a panelist' do
        survey = FactoryBot.create(:scipi, :invite_only, :published, :results_published)
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:view_results, survey), 'should not be able to :view_results when not a panelist'
      end

      test 'can :view_results when panelist' do
        survey = FactoryBot.create(:scipi, :invite_only, :published, :results_published, :contract_required)
        user = create_expert!
        survey.panelists.create!(user:, contract_signed_at: 1.week.ago)

        ability = ExpertAbility.new(user)

        assert ability.can?(:view_results, survey), 'should be able to :view_results when a panelist'
      end

      #
      # can :view_results, with Rounds
      #
      test 'cannot :view_results when debate round is not open' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:debate_round, scipi:)

        ability = ExpertAbility.new(expert)

        travel_to 1.hour.before(round.opens_at) do
          assert ability.cannot?(:view_results, round),
                 'should not be able to :view_results when the round is not open'
        end
      end

      test 'cannot :view_results on debate round when panelist is not active' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, :inactive, user: expert, survey: scipi)
        round = FactoryBot.create(:debate_round, scipi:)

        ability = ExpertAbility.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.cannot?(:view_results, round),
                 'should not be able to :view_results when the panelist is not active'
        end
      end

      test 'can :view_results on debate round' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:debate_round, scipi:)

        ability = ExpertAbility.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.can?(:view_results, scipi), 'should be able to :view_results when the round is open'
        end
      end

      #
      # :update Answer Group cases without Rounds
      #

      test 'cannot :update another experts\'s submission' do
        survey = create_survey!(closes_at: nil, invite_only: false, published_at: 1.week.ago)
        submitter = create_expert!
        submission = survey.submissions.create!(submitter:)
        another_expert = create_expert!

        ability = ExpertAbility.new(another_expert)

        assert_not ability.can?(:update, submission), 'should not be able to :update another experts\'s submission'
      end

      test 'can :update own submission' do
        survey = create_survey!(closes_at: nil, invite_only: false, published_at: 1.week.ago)
        submitter = create_expert!
        submission = survey.submissions.create!(submitter:)

        ability = ExpertAbility.new(submitter)

        assert ability.can?(:update, submission), 'should be able to :update own submission'
      end

      test 'cannot :update final submission when participation is closed' do
        survey = create_survey!(closes_at: 2.days.ago, invite_only: false, published_at: 1.week.ago)
        submitter = create_expert!
        submission = survey.submissions.create!(submitter:, submitted_at: Time.current)

        ability = ExpertAbility.new(submitter)

        assert_not ability.can?(:update, submission),
                   'should be able to :update final submission when participation is closed'
      end

      test 'can :update draft SciPoll submission when participation is closed' do
        scipoll = FactoryBot.create(:scipoll, :published, closes_at: 1.day.ago)
        submission = FactoryBot.create(:submission, :draft, survey: scipoll)
        submitter = submission.submitter

        ability = ExpertAbility.new(submitter)

        assert ability.can?(:update, submission),
               'should be able to :update draft submission on a closed SciPoll'
      end

      test 'can :update draft SciPi submission when participation is closed' do
        scipi = FactoryBot.create(:scipi, :published, closes_at: 1.day.ago)
        submitter = create_expert!
        scipi.panelists.create!(user: submitter)
        submission = scipi.submissions.create!(submitter:, submitted_at: nil)

        ability = ExpertAbility.new(submitter)

        assert ability.can?(:update, submission),
               'should be able to :update draft submission on a closed SciPi'
      end

      test 'cannot :update submission when submitter is no longer a panelist' do
        survey = create_survey!(closes_at: nil, invite_only: true, published_at: 1.week.ago)
        submitter = create_expert!
        survey.applicants.create!(user: submitter, rejected_at: Time.current)
        submission = survey.submissions.create!(submitter:, submitted_at: nil)

        ability = ExpertAbility.new(submitter)

        assert_not ability.can?(:update, submission),
                   'should be able to :update :update submission when submitter is no longer a panelist'
      end

      #
      # :update Answer Group cases with Rounds
      #
      #
      test 'cannot :update a final submission when a Question round is closed' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:question_round, scipi:)
        submission = FactoryBot.create(:submission, :final, scipi:, submitter: expert)

        ability = Ability.new(expert)

        travel_to 1.hour.after(round.closes_at) do
          assert ability.cannot?(:update, submission),
                 'should not be able to :update a final submission when a Question round is closed'
        end
      end

      test 'can :update a draft submission when a Question round is closed' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:question_round, scipi:)
        submission = FactoryBot.create(:submission, :draft, scipi:, submitter: expert)

        ability = Ability.new(expert)

        travel_to 1.hour.after(round.closes_at) do
          assert ability.can?(:update, submission),
                 'should be able to :update a draft submission when a Question round is closed'
        end
      end

      test 'cannot :update submission during a Debate round' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:debate_round, scipi:)
        submission = FactoryBot.create(:submission, scipi:, submitter: expert)

        ability = Ability.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.cannot?(:update, submission),
                 'should not be able to :update a submission during a Debate round'
        end
      end

      test 'cannot :update submission when not an active panelist' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, :suspended, user: expert, survey: scipi)
        round = FactoryBot.create(:question_round, scipi:)
        submission = FactoryBot.create(:submission, scipi:, submitter: expert)

        ability = Ability.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.cannot?(:update, submission),
                 'should not be able to :update a submission for an inactive panelist'
        end
      end

      test 'can :update own submission when a Question round is open' do
        expert = FactoryBot.create(:expert)
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        FactoryBot.create(:panelist, user: expert, survey: scipi)
        round = FactoryBot.create(:question_round, scipi:)
        submission = FactoryBot.create(:submission, scipi:, submitter: expert)

        ability = Ability.new(expert)

        travel_to 1.hour.after(round.opens_at) do
          assert ability.can?(:update, submission),
                 'should be able to :update a submission during a Question round'
        end
      end

      #
      # :download ReviewMaterial cases without Rounds
      #

      test 'cannot :download review materials for a draft general participation survey' do
        survey = FactoryBot.create(:survey, :draft, :general_participation)
        material = survey.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:download, material),
                   'should not be able to :download review materials for a draft general participation survey'
      end

      test 'non-experts cannot :download review materials for an open, published, general participation survey' do
        survey = FactoryBot.create(:survey, :published, :general_participation)
        material = survey.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))
        user = FactoryBot.create(:observer)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:download, material),
                   'should not be able to :download review materials for an open, published, general participation survey'
      end

      test 'experts can :download review materials for a open, published survey with open participation' do
        survey = FactoryBot.create(:survey, :published, :general_participation)
        material = survey.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))
        user = create_expert!

        ability = ExpertAbility.new(user)

        assert ability.can?(:download, material),
               'should be able to :download material for an open, published, general participation, survey'
      end

      test 'non-experts cannot :download review materials for an open, published, selection-required survey' do
        survey = FactoryBot.create(:survey, :published, :invite_only)
        material = survey.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))
        user = FactoryBot.create(:observer)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:download, material),
                   'should not be able to :download material for an open, published, selection-required survey'
      end

      test 'not-approved can :download review materials for a open, published survey with selection-required' do
        survey = FactoryBot.create(:survey, :published, :invite_only)
        material = survey.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))
        user = create_expert!
        survey.applicants.create!(user:, approved_at: nil)

        ability = ExpertAbility.new(user)

        assert_not ability.can?(:download, material),
                   'should be able to :download for an open, published, selection-required, survey'
      end

      test 'approved can :download review materials for a open, published survey with selection-required' do
        survey = FactoryBot.create(:survey, :published, :invite_only)
        material = survey.review_materials.create!(file: attachable_file_fixture('test_attachment.pdf'))
        user = create_expert!
        survey.panelists.create!(user:, approved_at: Time.current)

        ability = ExpertAbility.new(user)

        assert ability.can?(:download, material),
               'should be able to :download for an open, published, selection-required, survey'
      end

      test 'Scipi applicants can :view_messages on a SciPi' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)

        ability = ExpertAbility.new(expert)

        assert ability.can?(:view_messages, scipi), 'should be able to :view_messages on a SciPi'
      end

      test 'non-applicants cannot :view_messages on a SciPi' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(expert)

        assert ability.cannot?(:view_messages, scipi), 'should not be able to :view_messages on a SciPi'
      end

      test 'applicants can :view a message sent to them' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)
        message = FactoryBot.create(:message, survey: scipi, user: expert)

        ability = ExpertAbility.new(expert)

        assert ability.can?(:view, message), 'should be able to :view a message on a SciPi'
      end

      test 'applicants can :reply to a message sent to them' do
        scipi = FactoryBot.create(:scipi, :published)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)
        message = FactoryBot.create(:message, survey: scipi, user: expert)

        ability = ExpertAbility.new(expert)

        assert ability.can?(:reply, message), 'should be able to :reply to a message on a SciPi'
      end

      private

      def create_survey!(**)
        FactoryBot.create(:scipi, :published, **)
      end
    end
  end
end

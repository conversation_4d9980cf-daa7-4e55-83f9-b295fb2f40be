# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Abilities
    class DefaultAbilityTest < ActiveSupport::TestCase
      test 'cannot :view_landing_page for drafts' do
        survey = FactoryBot.create(:survey, :scipi, :draft)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_landing_page, survey), 'should not be able to :view_landing_page for draft'
      end

      test 'can :view_landing_page for published surveys' do
        survey = FactoryBot.create(:survey, :scipi, :published)

        ability = DefaultAbility.new

        assert ability.can?(:view_landing_page, survey), 'should be able to :view_landing_page for published surveys'
      end

      test 'cannot :view_results for published results when selection is required' do
        survey = FactoryBot.create(:survey, :scipi, :published, invite_only: true, results_public: false, results_published: true)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_results, survey),
                   'should not be able to :view_results for draft when selection is required'
      end

      test 'cannot :view_results for unpublished public results ' do
        skip 'This is invalid configuration, but if results are public, you can view them.'
        survey = FactoryBot.create(:survey, :scipi, :published, invite_only: true, results_public: true, results_published: false)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_results, survey), 'should not be able to view results because they are not published'
      end

      test 'cannot :view_results for published public results for a draft survey' do
        survey = FactoryBot.create(:survey, :scipi, :draft, invite_only: false, results_public: true, results_published: true)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_results, survey), 'should not be able to :view_results for draft'
      end

      test 'can :view_results for published, public results when selection is required' do
        survey = FactoryBot.create(:survey, :scipi, :published, invite_only: true, results_public: true, results_published: true)

        ability = DefaultAbility.new

        assert ability.can?(:view_results, survey),
               'should be able to :view_results published, public results when selection is required'
      end

      test 'cannot :view_results for published results with open participation' do
        survey = FactoryBot.create(:survey, :legacy, :published, invite_only: false, results_public: false, results_published: true)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_results, survey),
                   'should not be able to :view_results with published results with open participation'
      end

      test 'cannot :view_report for published results when selection is required' do
        survey = FactoryBot.create(:survey, :scipi, :published, invite_only: true, results_public: false, results_published: true)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_report, survey),
                   'should not be able to :view_report for draft when selection is required'
      end

      test 'cannot :view_report for published results with open participation' do
        survey = FactoryBot.create(:survey, :scipi, :published, invite_only: false, results_public: false, results_published: true)

        ability = DefaultAbility.new

        assert_not ability.can?(:view_report, survey),
                   'should not be able to :view_report published results with open participation'
      end

      test 'can :view_report for published public results with open participation' do
        survey = FactoryBot.create(:survey, :legacy, :published, invite_only: false, results_public: true, results_published: true)

        ability = DefaultAbility.new

        assert ability.can?(:view_report, survey),
               'should be able to :view_report for published public results with open participation'
      end

      test 'can :view_report for published resultPs when selection is required' do
        survey = FactoryBot.create(:survey, :scipi, :published, invite_only: true, results_public: true, results_published: true)

        ability = DefaultAbility.new

        assert ability.can?(:view_report, survey),
               'should be able to :view_report published results when selection is required'
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Surveys
  class DebateTest < ActiveSupport::TestCase
    test 'cannot participate when debate is closed' do
      expert = create_expert!
      debate = Debate.new(open: false)

      assert_not debate.can_participate?(expert), 'should not be open'
    end

    test 'cannot participate when not in allowed debated list' do
      expert = create_expert!
      allowed_debaters = User.where(id: expert.id)
      disallowed_expert = create_expert!

      debate = Debate.new(open: false, allowed_debaters:)

      assert_not debate.can_participate?(disallowed_expert), 'should not be able to participate'
    end

    test 'cannot participate when open and unrestricted as a guest' do
      guest = FactoryBot.create(:guest)

      debate = Debate.new(open: true)

      assert_not debate.can_participate?(guest), 'should be allowed to participate'
    end

    test 'can participate when open and unrestricted' do
      expert = create_expert!

      debate = Debate.new(open: true)

      assert debate.can_participate?(expert), 'should be allowed to participate'
    end

    test 'can participate when on allowed and unrestricted' do
      expert = create_expert!
      allowed_debaters = User.where(id: expert.id)

      debate = Debate.new(open: true, allowed_debaters:)

      assert debate.can_participate?(expert), 'should be allowed to participate'
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Surveys
  class CollectionTest < ActiveSupport::TestCase
    setup do
      QuestionGroup.destroy_all # Needed because there seem to be record kicking around already
    end

    test 'open_and_closed_scipolls grabs one of each (in ideal situation)' do
      FactoryBot.create_list(:scipoll, 5, :published, closes_at: 2.weeks.from_now)
      FactoryBot.create_list(:scipoll, 5, :published, closes_at: 2.weeks.ago)

      scipolls = QuestionGroup.open_and_closed_scipolls

      assert_equal scipolls.count, 2
      assert scipolls.first.open?
      assert scipolls.last.closed?
    end

    test 'open_and_closed_scipolls backfills with closed scipolls if no opens are available' do
      FactoryBot.create_list(:scipoll, 5, :published, closes_at: 2.weeks.ago)

      scipolls = QuestionGroup.open_and_closed_scipolls

      assert_equal scipolls.count, 2
      assert scipolls.first.closed?
      assert scipolls.last.closed?
    end

    test 'open_and_closed_scipolls gets the most distant close date first and the most recent closed date second' do
      FactoryBot.create_list(:scipoll, 5, :published, closes_at: 2.weeks.from_now)
      FactoryBot.create_list(:scipoll, 5, :published, closes_at: 2.weeks.ago)

      # Build these in the middle so we don't luck out on ordering
      furthest_close = FactoryBot.create(:scipoll, :published, closes_at: 3.weeks.from_now)
      most_recent_close = FactoryBot.create(:scipoll, :published, closes_at: 1.week.ago)

      FactoryBot.create_list(:scipoll, 3, :published, closes_at: 2.weeks.from_now)
      FactoryBot.create_list(:scipoll, 3, :published, closes_at: 2.weeks.ago)

      scipolls = QuestionGroup.open_and_closed_scipolls

      assert_equal scipolls.count, 2
      assert scipolls.first, furthest_close
      assert scipolls.last, most_recent_close
    end
  end
end

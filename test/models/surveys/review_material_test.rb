# frozen_string_literal: true

require 'test_helper'

module Surveys
  class ReviewMaterialTest < ActiveSupport::TestCase
    test 'not downloaded by user' do
      survey = FactoryBot.create(:survey)
      review_material = survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'))
      expert = FactoryBot.create(:expert)

      assert_not review_material.downloaded_by?(expert), 'should not be downloaded by user'
    end

    test 'downloaded by user' do
      survey = FactoryBot.create(:survey)
      review_material = survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'))
      expert = FactoryBot.create(:expert)
      review_material.downloads.create!(user: expert)

      assert review_material.downloaded_by?(expert), 'should be downloaded by user'
    end

    test 'record download' do
      survey = FactoryBot.create(:survey)
      review_material = survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'))
      expert = FactoryBot.create(:expert)

      assert_difference -> { review_material.downloads.where(user: expert).count } do
        review_material.record_download!(by: expert)
      end
    end

    test 'record download only records first download' do
      survey = FactoryBot.create(:survey)
      review_material = survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'))
      expert = FactoryBot.create(:expert)

      # The download is created here
      review_material.record_download!(by: expert)

      assert_no_difference -> { review_material.downloads.where(user: expert).count } do
        review_material.record_download!(by: expert)
      end
    end

    test 'downloaded at' do
      survey = FactoryBot.create(:survey)
      review_material = survey.review_materials.create!(file: uploaded_fixture_file('test_attachment.pdf'))
      expert = FactoryBot.create(:expert)
      download = review_material.downloads.create!(user: expert)

      assert_equal download.created_at, review_material.downloaded_at(by: expert)
    end
  end
end

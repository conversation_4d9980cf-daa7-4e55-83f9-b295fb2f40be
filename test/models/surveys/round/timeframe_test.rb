# frozen_string_literal: true

require 'test_helper'

module Surveys
  class Round::TimeframeTest < ActiveSupport::TestCase
    test 'duration' do
      round = FactoryBot.create(:question_round, duration: 7.days)

      assert_equal 7, round.duration_in_days
    end

    test 'timeframe?' do
      with_timeframe = FactoryBot.create(:question_round)
      assert with_timeframe.timeframe?

      without_timeframe = FactoryBot.create(:question_round, :no_timeframe)
      assert_not without_timeframe.timeframe?
    end

    test 'not open? when opens_at is nil' do
      round = FactoryBot.create(:question_round, :no_timeframe)

      assert_not round.open?
    end

    test 'not open? when scipis is not-published' do
      scipi = FactoryBot.create(:scipi, :draft)
      round = FactoryBot.create(:question_round, scipi:)

      travel_to 1.hour.after(round.opens_at) do
        assert_not round.open?, 'round should not be open when the scipi is a draft'
      end
    end

    test 'open? when between opens_at and closes_at' do
      round = FactoryBot.create(:question_round)

      travel_to 1.minute.after(round.opens_at) do
        assert round.open?
      end
    end

    test 'not open? before opens_at' do
      round = FactoryBot.create(:question_round)

      travel_to 1.minute.before(round.opens_at) do
        assert_not round.open?
      end
    end

    test 'not open? after closed_at' do
      round = FactoryBot.create(:question_round)

      travel_to 1.minute.after(round.closes_at) do
        assert_not round.open?
      end
    end

    test 'always closed? when opens_at is nil' do
      round = FactoryBot.create(:question_round, :no_timeframe)

      assert round.closed?
    end

    test 'closed? when not yet open' do
      round = FactoryBot.create(:question_round)

      travel_to 1.minute.before(round.opens_at) do
        assert round.closed?
      end
    end

    test 'not closed? when between open and close times' do
      round = FactoryBot.create(:question_round)

      travel_to 1.minute.before(round.closes_at) do
        assert_not round.closed?
      end
    end

    test 'closed? when close time is in the past' do
      round = FactoryBot.create(:question_round)

      travel_to 1.minute.after(round.closes_at) do
        assert round.closed?
      end
    end
  end
end

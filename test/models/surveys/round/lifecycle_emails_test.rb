# frozen_string_literal: true

require 'test_helper'

module Surveys
  class Round::LifecycleEmailsTest < ActiveSupport::TestCase
    test 'delegates to email type' do
      expert = FactoryBot.create(:expert)
      scipi = FactoryBot.create(:scipi)
      scipi.panelists.create!(expert:)

      round = FactoryBot.create(:question_round, survey: scipi)

      open_email = ::Scipis::Rounds::RoundOpenNotification.any_instance
      open_email.stubs(:auto_deliverable?).returns(true)
      open_email.expects(:deliver_all).once

      travel_to(1.hour.after(round.opens_at)) do
        round.send_lifecycle_emails
      end
    end
  end
end

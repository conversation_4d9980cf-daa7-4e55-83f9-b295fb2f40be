# frozen_string_literal: true

require 'test_helper'

class Surveys::Rounds::DebateTest < ActiveSupport::TestCase
  test 'participated in debate' do
    expert = FactoryBot.create(:expert)
    round = FactoryBot.create(:debate_round)
    debate_topic = FactoryBot.create(:result_definition, scipi: round.scipi)
    FactoryBot.create(:comment, debate_topic:, user: expert)

    assert round.participated?(expert)
  end

  test 'did not participate in debate' do
    expert = FactoryBot.create(:expert)
    round = FactoryBot.create(:debate_round)
    debate_topic = FactoryBot.create(:result_definition, scipi: round.scipi)
    FactoryBot.create(:comment, debate_topic:) # different user

    assert_not round.participated?(expert)
  end

  test 'send first debate summary' do
    round = FactoryBot.create(:debate_round, :open_email_sent)
    scipi = round.scipi
    debate_topic = FactoryBot.create(:result_definition, scipi:)
    FactoryBot.create(:comment, debate_topic:, created_at: round.round_open_email_sent_at + 1.hour)

    next_delivery_possible_at = round.round_open_email_sent_at + round.debate_summary_interval_in_days

    travel_to(1.minute.after(next_delivery_possible_at)) do
      assert round.send_debate_summary?
    end
  end

  test 'send subsequent debate summary' do
    round = FactoryBot.create(:debate_round)
    last_debate_summary_sent_at = round.opens_at + 1.day
    round.update!(last_debate_summary_sent_at:)
    scipi = round.scipi
    debate_topic = FactoryBot.create(:result_definition, scipi:)
    FactoryBot.create(:comment, debate_topic:, created_at: last_debate_summary_sent_at + 1.hour)

    next_delivery_possible_at = last_debate_summary_sent_at + round.debate_summary_interval_in_days

    travel_to(1.minute.after(next_delivery_possible_at)) do
      assert round.send_debate_summary?
    end
  end

  test 'do not send debate summary before next delivery possible' do
    round = FactoryBot.create(:debate_round, :open_email_sent)
    last_debate_summary_sent_at = round.opens_at + 1.day
    round.update!(last_debate_summary_sent_at:)
    scipi = round.scipi
    debate_topic = FactoryBot.create(:result_definition, scipi:)
    FactoryBot.create(:comment, debate_topic:, created_at: last_debate_summary_sent_at + 1.hour)

    next_delivery_possible_at = round.round_open_email_sent_at + round.debate_summary_interval_in_days

    travel_to(1.minute.before(next_delivery_possible_at)) do
      assert_not round.send_debate_summary?
    end
  end

  test 'do not send debate summary when round is closed' do
    round = FactoryBot.create(:debate_round, last_debate_summary_sent_at: 4.days.ago)
    scipi = round.scipi
    debate_topic = FactoryBot.create(:result_definition, scipi:)
    FactoryBot.create(:comment, debate_topic:, created_at: round.closes_at - 1.hour)

    travel_to(1.minute.after(round.closes_at)) do
      assert_not round.send_debate_summary?
    end
  end

  test 'do not send debate summary if closing reminder has been sent' do
    round = FactoryBot.create(:debate_round, :closing_reminder_sent, last_debate_summary_sent_at: 4.days.ago)
    scipi = round.scipi
    debate_topic = FactoryBot.create(:result_definition, scipi:)
    FactoryBot.create(:comment, debate_topic:, created_at: round.closes_at - 1.hour)

    travel_to(1.hour.before(round.closes_at)) do
      assert_not round.send_debate_summary?
    end
  end

  test 'do not send debate summary when round is open, without current activity' do
    round = FactoryBot.create(:debate_round, :open_email_sent)

    next_delivery_possible_at = round.round_open_email_sent_at + round.debate_summary_interval_in_days

    travel_to(1.hour.after(next_delivery_possible_at)) do
      assert_not round.send_debate_summary?
    end
  end

  test 'send closing reminder' do
    scipi = FactoryBot.create(:survey, :published)
    round = FactoryBot.create(:debate_round, :open_email_sent, scipi:)

    # satisfies any scenario for closing_reminder_deliverable_at
    travel_to 23.hours.before(round.closes_at) do
      assert round.send_closing_reminder?
    end
  end

  test 'do not send debate closing reminder - open email not sent yet' do
    scipi = FactoryBot.create(:survey, :published)
    round = FactoryBot.create(:debate_round, scipi:, round_open_email_sent_at: nil)

    # satisfies any scenario for closing_reminder_deliverable_at
    travel_to 23.hours.before(round.closes_at) do
      assert_not round.send_closing_reminder?,
                 'should not send the closing reminder if the open email has not been sent'
    end
  end

  test 'do not send closing reminder - not yet in reminder window' do
    scipi = FactoryBot.create(:survey, :published)
    round = FactoryBot.create(:debate_round, :open_email_sent, scipi:)

    # Before of any scenario for closing_reminder_deliverable_at
    travel_to 49.hours.before(round.closes_at) do
      assert_not round.send_closing_reminder?
    end
  end

  test 'do not send closing reminder - already closed' do
    scipi = FactoryBot.create(:survey, :published)
    round = FactoryBot.create(:debate_round, :open_email_sent, scipi:)

    travel_to 1.minute.after(round.closes_at) do
      assert_not round.send_closing_reminder?
    end
  end

  test 'do not send closing reminder - reminder already sent' do
    scipi = FactoryBot.create(:survey, :published)
    round = FactoryBot.create(:debate_round, :open_email_sent, :closing_reminder_sent, scipi:)

    # satisfies any scenario for closing_reminder_deliverable_at
    travel_to 23.hours.before(round.closes_at) do
      assert_not round.send_closing_reminder?
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class Surveys::Rounds::QuestionTest < ActiveSupport::TestCase
  include Scipis::Reminders::TestHelper
  include ActiveJob::TestHelper

  test 'set send_midpoint_reminder_at on create' do
    opens_at = 1.week.from_now
    round = FactoryBot.create(:question_round, opens_at:)

    assert_not_nil round.send_midpoint_reminder_at
  end

  test 'do not set send_midpoint_reminder_at if midpoint has already passed' do
    round = FactoryBot.create(:question_round, :no_timeframe)

    opens_at = Time.zone.now
    closes_at = 2.weeks.from_now
    midpoint = midpoint_between(opens_at, closes_at)

    travel_to 1.day.after(midpoint) do
      round.update!(opens_at:, closes_at:)
    end

    assert_nil round.send_midpoint_reminder_at
  end

  test 'do not set send_midpoint_reminder_at if closing_reminder_sent_at is set' do
    round = FactoryBot.create(:question_round, :closing_reminder_sent, :no_timeframe)

    round.update!(opens_at: 1.week.from_now.to_date, closes_at: 2.weeks.from_now.to_date)

    assert_nil round.send_midpoint_reminder_at
  end

  test 'do not set send_midpoint_reminder_at if duration_in_days is < threshold' do
    duration = (::Surveys::Rounds::Question::MIDPOINT_REMINDER_MIN_DURATION - 1).days

    round = FactoryBot.create(:question_round, duration:)

    assert_nil round.send_midpoint_reminder_at
  end

  test 'clear send_midpoint_reminder_at if duration_in_days goes below threshold' do
    round = FactoryBot.create(:question_round)
    short_duration = (::Surveys::Rounds::Question::MIDPOINT_REMINDER_MIN_DURATION - 1).days
    closes_at = round.opens_at + short_duration

    round.update!(closes_at:)

    assert_nil round.send_midpoint_reminder_at, 'send_midpoint_reminder_at should be nil'
  end

  test 'do not set send_midpoint_reminder_at if already sent' do
    round = FactoryBot.create(:question_round, :midpoint_reminder_sent)

    assert_no_changes -> { round.send_midpoint_reminder_at } do
      round.update!(closes_at: round.closes_at + 7.days)
    end
  end

  test 'send submission receipt' do
    expert = FactoryBot.create(:expert)
    round = FactoryBot.create(:question_round)
    scipi = round.scipi
    submission = FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert round.send_submission_receipt?(submission)
  end

  test 'do not send submission receipt for draft submissions' do
    expert = FactoryBot.create(:expert)
    round = FactoryBot.create(:question_round)
    scipi = round.scipi
    submission = FactoryBot.create(:submission, :draft, survey: scipi, user: expert)

    assert_not round.send_submission_receipt?(submission)
  end

  test 'do not send submission receipt when already sent' do
    expert = FactoryBot.create(:expert)
    round = FactoryBot.create(:question_round)
    scipi = round.scipi
    submission = FactoryBot.create(:submission, :final, survey: scipi, user: expert)
    submission.submission_receipt_sent!(round, 'non_final')

    assert_not round.send_submission_receipt?(submission)
  end

  test 'back_up_submissions! does nothing when closes_at is nil' do
    round = FactoryBot.create(:question_round, :no_timeframe)

    assert_no_enqueued_jobs do
      round.back_up_submissions!
    end

    assert_nil round.submission_backup_taken_at
  end

  test 'back_up_submissions! does nothing when closes_at is in the future' do
    round = FactoryBot.create(:question_round, opens_at: 1.week.ago, closes_at: 1.day.from_now)

    assert_no_enqueued_jobs do
      round.back_up_submissions!
    end

    assert_nil round.submission_backup_taken_at
  end

  test 'back_up_submissions! does nothing when submission_backup_taken_at is set' do
    round = FactoryBot.create(:question_round, opens_at: 1.week.ago, closes_at: 1.day.ago)
    round.update!(submission_backup_taken_at: Time.current)

    assert_no_difference 'Surveys::SubmissionBackup.count' do
      round.back_up_submissions!
    end
  end

  test 'back_up_submissions! does not update submission_backup_taken_at if backup fails' do
    round = FactoryBot.create(:question_round, opens_at: 1.week.ago, closes_at: 1.day.ago)
    scipi = round.scipi

    backup = Surveys::SubmissionBackup.new
    backup.stubs(:complete?).returns(false)

    scipi.submission_backups.stubs(:create!).returns(backup)

    round.back_up_submissions!

    assert_nil round.submission_backup_taken_at
  end

  test 'clears submission_backup_taken_at when round is reopened' do
    round = FactoryBot.create(:question_round, opens_at: 1.week.ago, closes_at: 1.day.ago)
    round.update!(submission_backup_taken_at: Time.current)

    round.update!(closes_at: 1.week.from_now)

    assert_nil round.submission_backup_taken_at, 'submission_backup_taken_at should be nil after reopening'
  end

  test 'does not clear submission_backup_taken_at if closes_at changes but stays in the past' do
    round = FactoryBot.create(:question_round, opens_at: 2.weeks.ago, closes_at: 1.week.ago)
    backup_time = Time.current
    round.update!(submission_backup_taken_at: backup_time)

    round.update!(closes_at: 2.days.ago)

    assert_equal backup_time.change(nsec: 0),
                 round.submission_backup_taken_at.change(nsec: 0),
                 'submission_backup_taken_at should not change'
  end

  test 'reset_submissions!' do
    round = FactoryBot.create(:question_round, position: 3, opens_at: 30.minutes.from_now, closes_at: 1.week.from_now)
    scipi = round.scipi

    expert = FactoryBot.create(:expert)
    submission = FactoryBot.create(:submission, :final, survey: scipi, user: expert, close_date_when_submitted: Time.current)

    assert_difference -> { scipi.submissions.submitted.count }, -1 do
      round.reset_submissions!
    end

    submission.reload

    assert_not_nil round.submissions_reset_at
    assert_nil submission.submitted_at
    assert_nil submission.close_date_when_submitted
  end

  test 'do not reset_submissions! for the first rounds' do
    # Assumed final since there are no other rounds
    round = FactoryBot.create(:question_round, position: 1, opens_at: 30.minutes.from_now, closes_at: 1.week.from_now)
    scipi = round.scipi
    submission = FactoryBot.create(:submission, :final, survey: scipi, close_date_when_submitted: Time.current)

    FactoryBot.create(:expert)

    assert_no_difference -> { scipi.submissions.submitted.count } do
      round.reset_submissions!
    end

    submission.reload

    assert_nil round.submissions_reset_at
    assert_not_nil submission.submitted_at
    assert_not_nil submission.close_date_when_submitted
  end

  test 'do not reset_submissions! when already reset' do
    round = FactoryBot.create(
      :question_round,
      position: 3,
      opens_at: 30.minutes.from_now,
      closes_at: 1.week.from_now,
      submissions_reset_at: Time.current
    )

    scipi = round.scipi
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert_no_difference -> { scipi.submissions.submitted.count } do
      round.reset_submissions!
    end
  end

  test 'reset submissions on immediate open from nil' do
    round = FactoryBot.create(:question_round, :no_timeframe, position: 2)
    scipi = round.scipi
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert_enqueued_with(job: BackgroundModelMethodJob, args: [round, :reset_submissions!]) do
      round.update!(opens_at: Time.current, closes_at: 1.week.from_now)
    end
  end

  test 'reset submissions on immediate open from future date' do
    round = FactoryBot.create(:question_round, position: 2, opens_at: 30.minutes.from_now, closes_at: 1.week.from_now)
    scipi = round.scipi
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert_enqueued_with(job: BackgroundModelMethodJob, args: [round, :reset_submissions!]) do
      round.update!(opens_at: Time.current, closes_at: 1.week.from_now)
    end
  end

  test 'do not reset immediately submissions on immediate open when already reset' do
    round = FactoryBot.create(
      :question_round,
      position: 2,
      opens_at: 30.minutes.from_now,
      closes_at: 1.week.from_now,
      submissions_reset_at: Time.current
    )
    scipi = round.scipi
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert_no_enqueued_jobs do
      round.update!(opens_at: Time.current)
    end
  end

  test 'do not immediately reset submissions when opens_at is in the future' do
    round = FactoryBot.create(:question_round, :no_timeframe, position: 2)
    scipi = round.scipi
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert_no_difference -> { scipi.submissions.submitted.count } do
      round.update!(opens_at: 30.minutes.from_now, closes_at: 1.week.from_now)
    end
  end

  test 'do not immediately reset submissions when already open' do
    round = FactoryBot.create(:question_round, position: 2, opens_at: 30.minutes.ago, closes_at: 1.week.from_now)
    scipi = round.scipi
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:submission, :final, survey: scipi, user: expert)

    assert_no_enqueued_jobs do
      round.update!(opens_at: Time.current)
    end
  end
end

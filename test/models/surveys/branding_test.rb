# frozen_string_literal: true

require 'test_helper'

module Surveys
  class BrandingTest < ActiveSupport::TestCase
    test 'Complete Sci<PERSON>oll debate open when survey is closed' do
      scipoll = FactoryBot.create(:scipoll, :published, :closed)

      assert scipoll.published?, '<PERSON><PERSON>oll should be published'
      assert scipoll.debate_open?
    end

    test 'Debate can be closed on complete SciPoll' do
      scipoll = FactoryBot.create(:scipoll, :published, :closed, comments_close_date: 1.month.ago.to_date)

      assert scipoll.published?, 'Scipoll should be published'
      assert scipoll.debate_closed?
    end

    test 'Debate can be closed on open SciPoll' do
      scipoll = FactoryBot.create(:scipoll, :published)

      assert scipoll.debate_open?

      scipoll.update(comments_close_date: 1.month.ago.to_date)

      assert scipoll.debate_closed?
    end

    test 'Public Results become public on close, but not before' do
      scipoll = FactoryBot.create(:scipoll, :published, results_published: true, results_public: true)

      assert_not scipoll.results_public? # <PERSON><PERSON><PERSON> running, result_public has no effect

      scipoll.update(closes_at:  1.week.ago.to_date)

      assert scipoll.published?, '<PERSON><PERSON><PERSON> should be published'
      assert scipoll.closed?, '<PERSON><PERSON><PERSON> should be closed'
      assert scipoll.results_public?, 'Scipoll should have public results'
    end
  end
end

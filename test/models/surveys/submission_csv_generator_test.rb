# frozen_string_literal: true

require 'test_helper'

module Surveys
  class SubmissionCsvGeneratorTest < ActiveSupport::TestCase
    test 'generates filename with round' do
      scipi = FactoryBot.create(:scipi)
      round = FactoryBot.create(:question_round, survey: scipi, position: 3)
      generator = Surveys::SubmissionCsvGenerator.new(scipi)

      expected_filename = "#{scipi.branding.name.parameterize}_#{scipi.id}_round_3_submissions.csv"

      assert_equal expected_filename, generator.filename(round)
    end

    test 'generates filename without round' do
      scipi = FactoryBot.create(:scipi)
      generator = Surveys::SubmissionCsvGenerator.new(scipi)

      travel_to Time.zone.local(2025, 5, 15, 10, 30, 45) do
        expected_filename = "#{scipi.branding.name.parameterize}_#{scipi.id}_submissions_2025-05-15-103045.csv"
        assert_equal expected_filename, generator.filename
      end
    end
  end
end

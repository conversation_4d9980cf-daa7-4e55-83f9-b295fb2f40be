# frozen_string_literal: true

require 'test_helper'

module Surveys
  class ParticipantTest < ActiveSupport::TestCase
    test 'display id with invite' do
      survey = FactoryBot.create(:survey)
      expert = users(:expert)
      survey.submissions.create!(submitter: expert)

      participant = Participant.new(survey:, expert:)

      assert_equal expert.display_id, participant.display_id, 'should default to display id'
    end

    test 'display id for SciPi panelist' do
      scipi = question_groups(:scipi)
      expert = users(:expert)
      panelist = scipi.panelists.create!(expert:)

      participant = Participant.new(survey: scipi, expert:)

      assert_equal panelist.expert_display_id, participant.display_id, 'should use the expert number'
    end

    test 'pay rate is zero for unpaid surveys' do
      survey = FactoryBot.create(:survey)
      expert = users(:expert)
      survey.submissions.create!(submitter: expert)

      participant = Participant.new(survey:, expert:)

      assert_equal 0.0, participant.pay_rate, 'should be zero'
    end

    test 'default pay rate with no custom pay rate is set' do
      scipi = FactoryBot.create(:scipi)
      scipi.update!(engagement_info_attributes: { default_pay_rate: 4444.44 })
      expert = users(:expert)
      scipi.panelists.create!(expert:, pay_rate: nil)

      participant = Participant.new(survey: scipi, expert:)

      assert_equal 4444.44, participant.pay_rate
    end

    test 'custom pay rate with no custom pay rate is set' do
      scipi = FactoryBot.create(:scipi)
      scipi.update!(engagement_info_attributes: { default_pay_rate: 4444.44 })
      expert = users(:expert)
      scipi.panelists.create!(expert:, pay_rate: 5555.55)

      participant = Participant.new(survey: scipi, expert:)

      assert_equal 5555.55, participant.pay_rate
    end
  end
end

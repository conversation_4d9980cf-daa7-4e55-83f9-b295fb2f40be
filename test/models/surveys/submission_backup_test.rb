# frozen_string_literal: true

require 'test_helper'

module Surveys
  class SubmissionBackupTest < ActiveSupport::TestCase
    include ActiveJob::TestHelper

    test 'complete?' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!(file: uploaded_fixture_file('test_attachment.pdf'))

      assert backup.complete?
    end

    test 'not complete?' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!

      assert_not backup.complete?
    end

    test 'failed? returns false when complete' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!(file: uploaded_fixture_file('test_attachment.pdf'))

      assert_not backup.failed?
    end

    test 'failed? returns false when not complete and no error' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!

      assert_not backup.failed?
    end

    test 'failed? returns true when not complete and has error' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!(last_error: 'Something went wrong')

      assert backup.failed?
    end

    test 'running? returns true when not complete and not failed' do
      scipi = FactoryBot.create(:scipi)

      backup = scipi.submission_backups.create!

      assert backup.running?
    end

    test 'running? returns false when complete' do
      scipi = FactoryBot.create(:scipi)

      backup = scipi.submission_backups.create!(file: uploaded_fixture_file('test_attachment.pdf'))

      assert_not backup.running?
    end

    test 'running? returns false when failed' do
      scipi = FactoryBot.create(:scipi)

      backup = scipi.submission_backups.create!(last_error: 'Something went wrong')

      assert_not backup.running?
    end

    test 'manual? returns true when requested_by is present' do
      scipi = FactoryBot.create(:scipi)
      user = FactoryBot.create(:admin)
      backup = scipi.submission_backups.create!(requested_by: user)

      assert backup.manual?
    end

    test 'manual? returns false when requested_by is nil' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!

      assert_not backup.manual?
    end

    test 'requested_by_name returns full name when requested_by is present' do
      scipi = FactoryBot.create(:scipi)
      user = FactoryBot.create(:admin)
      backup = scipi.submission_backups.create!(requested_by: user)

      assert_equal user.full_name, backup.requested_by_name
    end

    test 'requested_by_name returns empty string when requested_by is nil' do
      scipi = FactoryBot.create(:scipi)
      backup = scipi.submission_backups.create!

      assert_equal 'SciBot 🤖', backup.requested_by_name
    end

    test 'enqueues BackgroundModelMethodJob on create when no file is attached' do
      scipi = FactoryBot.create(:scipi)

      assert_enqueued_with(job: BackgroundModelMethodJob) do
        scipi.submission_backups.create!
      end
    end

    test 'does not enqueue BackgroundModelMethodJob when file is already attached' do
      scipi = FactoryBot.create(:scipi)

      assert_no_enqueued_jobs(only: BackgroundModelMethodJob) do
        scipi.submission_backups.create!(file: uploaded_fixture_file('fake_submission_backup.csv'))
      end
    end
  end
end

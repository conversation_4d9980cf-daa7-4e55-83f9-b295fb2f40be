# frozen_string_literal: true

require 'test_helper'

module Surveys
  class FrequencyReportCsvFormatterTest < ActiveSupport::TestCase
    setup do
      @header_row_arr = ['question_id', 'question_number', 'question_type', 'question_text', 'Answer Option 1', 'Answer Option 2', 'Frequency Option 1', 'Frequency Option 2']
      # Load all result types
      load_all_render_types
      load_all_result_types
    end

    test 'frequency csv with checkbox results works correctly' do
      survey = create(:survey)

      checkbox_question = create(:checkbox, question_group: survey, answer_choice_count: 2)
      create(:result_definition, survey:, question: checkbox_question)
      response1 = create(:submission, question_group: survey)
      response2 = create(:submission, question_group: survey)
      create(:answer, answer_group: response1, question: checkbox_question, answer_choices: [checkbox_question.answer_choices.first, checkbox_question.answer_choices.second])
      create(:answer, answer_group: response2, question: checkbox_question, answer_choices: [checkbox_question.answer_choices.second])
      label1 = checkbox_question.answer_choices.first.label
      label2 = checkbox_question.answer_choices.second.label

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      csv_results = Surveys::FrequencyReportCsvFormatter.new(raw_results).to_csv

      expected_result =
        [
          @header_row_arr,
          [checkbox_question.id, "=\"#{checkbox_question.number}\"", 'Checkbox', checkbox_question.question_text, label1, label2, 1, 2]
        ]

      assert_equal expected_result, csv_results
    end

    test 'frequency csv with sections numbers correctly section question' do
      survey = create(:survey)
      section_position = rand(1..5)
      question_position = rand(1..5)
      section = create(:question_section, question_group: survey, position: section_position)
      question = create(:checkbox, position: question_position, section:, question_group: survey, answer_choice_count: 2)
      create(:result_definition, survey:, question:)
      label1 = question.answer_choices.first.label
      label2 = question.answer_choices.second.label

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      csv_results = Surveys::FrequencyReportCsvFormatter.new(raw_results).to_csv

      expected_result =
        [
          @header_row_arr,
          [question.id, "=\"#{section_position}.#{question_position}\"", 'Checkbox', question.question_text, label1, label2, 0, 0]
        ]

      assert_equal expected_result, csv_results
    end

    test 'frequency csv with grid checkbox results works correctly' do
      survey = create(:survey)
      grid_checkbox_question = create(:grid_question, question_group: survey, question_text: 'Q')
      grid_checkbox_question.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Checkbox',
        row_headers: %w[A B],
        column_headers: %w[C D]
      )
      create(:result_definition, survey:, question: grid_checkbox_question)
      response1 = create(:submission, question_group: survey)
      response2 = create(:submission, question_group: survey)
      create(:answer, answer_group: response1, question: grid_checkbox_question, answer_text_on_1st_row: "1\r\n1", answer_text_on_2nd_row: "0\r\n0")
      create(:answer, answer_group: response2, question: grid_checkbox_question, answer_text_on_1st_row: "1\r\n0", answer_text_on_2nd_row: "1\r\n0")

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      csv_results = Surveys::FrequencyReportCsvFormatter.new(raw_results).to_csv

      expected_result =
        [
          @header_row_arr,
          [grid_checkbox_question.id, "=\"#{grid_checkbox_question.number}.1\"", 'Grid: Checkbox', 'Q : A', 'C', 'D', 2, 1],
          [grid_checkbox_question.id, "=\"#{grid_checkbox_question.number}.2\"", 'Grid: Checkbox', 'Q : B', 'C', 'D', 1, 0]
        ]

      assert_equal expected_result, csv_results
    end

    test 'frequency csv with grid select results works correctly' do
      survey = create(:survey)
      grid_select_question = create(:grid_question, question_group: survey, question_text: 'Q')
      grid_select_question.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Select',
        row_headers: %w[A B],
        column_headers: %w[C D]
      )
      create(:result_definition, survey:, question: grid_select_question)

      label1 = grid_select_question.answer_choices.first.label
      label2 = grid_select_question.answer_choices.second.label

      # random answer combos
      answer_text_set1 = "#{label1}\r\n#{label2}"
      answer_text_set2 = "#{label1}\r\n#{label1}"

      response1 = create(:submission, question_group: survey)
      response2 = create(:submission, question_group: survey)

      # random application of answer combos
      create(:answer, answer_group: response1, question: grid_select_question, answer_text_on_1st_row: answer_text_set1, answer_text_on_2nd_row: answer_text_set1)
      create(:answer, answer_group: response2, question: grid_select_question, answer_text_on_1st_row: answer_text_set2, answer_text_on_2nd_row: answer_text_set2)

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      csv_results = Surveys::FrequencyReportCsvFormatter.new(raw_results).to_csv

      expected_result =
        [
          @header_row_arr,
          [grid_select_question.id, "=\"#{grid_select_question.number}.1.1\"", 'Grid: Select', 'Q : A : C', label1, label2, 2, 0],
          [grid_select_question.id, "=\"#{grid_select_question.number}.1.2\"", 'Grid: Select', 'Q : A : D', label1, label2, 1, 1],
          [grid_select_question.id, "=\"#{grid_select_question.number}.2.1\"", 'Grid: Select', 'Q : B : C', label1, label2, 2, 0],
          [grid_select_question.id, "=\"#{grid_select_question.number}.2.2\"", 'Grid: Select', 'Q : B : D', label1, label2, 1, 1]
        ]

      assert_equal expected_result, csv_results
    end
  end
end

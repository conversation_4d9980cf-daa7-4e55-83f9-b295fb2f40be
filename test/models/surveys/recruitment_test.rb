# frozen_string_literal: true

require 'test_helper'

module Surveys
  class RecruitmentTest < ActiveSupport::TestCase
    test 'recruitment is open with a future close date' do
      recruitment = Surveys::Recruitment.new(closes_on: Date.tomorrow, recruitable: true, published: true)

      assert recruitment.open?, 'recruitment should be open with a future close date'
    end

    test 'recruitment is open when the close date is not set' do
      recruitment = Surveys::Recruitment.new(closes_on: nil, recruitable: true, published: true)

      assert recruitment.open?, 'recruitment should be open with a nil close date'
    end

    test 'recruitment is closed for non-recruitable surveys' do
      recruitment = Surveys::Recruitment.new(closes_on: nil, recruitable: false, published: true)

      assert_not recruitment.open?, 'recruitment should not be open if not recruitable'
    end

    test 'recruitment is closed when with past close date' do
      recruitment = Surveys::Recruitment.new(closes_on: 2.days.ago, recruitable: true, published: true)

      assert_not recruitment.open?, 'recruitment should be with closed with a past close date'
    end

    test 'recruitment is closed for drafts' do
      recruitment = Surveys::Recruitment.new(closes_on: 1.week.from_now.to_date, recruitable: true, published: false)

      assert_not recruitment.open?, 'recruitment should be with closed for drafts'
    end

    test 'recruitment is closed for a while' do
      closes_on = (Surveys::INACTIVITY_DURATION + 10.days).ago.to_date
      recruitment = Surveys::Recruitment.new(closes_on:, recruitable: true, published: true)

      assert recruitment.closed_for_a_while?, 'recruitment should be closed for a while'
    end

    test 'recruitment is not closed for a while' do
      closes_on = (Surveys::INACTIVITY_DURATION - 10.days).ago.to_date
      recruitment = Surveys::Recruitment.new(closes_on:, recruitable: true, published: true)

      assert_not recruitment.closed_for_a_while?, 'recruitment is closed but should not be closed for a while'
    end

    test 'recruitment is not closed for a while when open' do
      recruitment = Surveys::Recruitment.new(closes_on: 10.days.from_now.to_date, recruitable: true, published: true)

      assert_not recruitment.closed_for_a_while?, 'recruitment should not be closed for a while when open'
    end

    test 'closes at is at the end of the day at UTC -11' do
      closes_on = Time.zone.today
      recruitment = Surveys::Recruitment.new(closes_on:, recruitable: true, published: true)

      assert_equal closes_on.in_time_zone('Pacific/Pago_Pago').end_of_day, recruitment.closes_at
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Selection
    class ScoredApplicantTest < ActiveSupport::TestCase
      test 'cv_attached?' do
        # owner = FactoryBot.create(:admin)
        # branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, recruitment_closes_on: 2.days.from_now.to_date, invite_only: true, published: true)
        expert = FactoryBot.create(:expert, :with_cv)
        applicant = scipi.applicants.create!(user: expert)
        # TODO: Fix joins in view so
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'])
        applicant.keyword_counts.create!(keyword:, count: 3)

        scored_applicant = scipi.scored_applicants.first

        assert scored_applicant.cv_attached?
      end

      test 'missing keyword counts are nil' do
        scipi = FactoryBot.create(:scipi, recruitment_closes_on: 2.days.from_now.to_date, invite_only: true, published: true)
        expert = FactoryBot.create(:expert, :with_cv)
        scipi.applicants.create!(user: expert)
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'])

        applicant = scipi.scored_applicants.first

        assert_nil applicant.keyword_stats[keyword.id.to_s]['count']
      end

      test 'max keyword counts are 0' do
        scipi = FactoryBot.create(:scipi, recruitment_closes_on: 2.days.from_now.to_date, invite_only: true, published: true)
        expert = FactoryBot.create(:expert, :with_cv)
        applicant = scipi.applicants.create!(user: expert)
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'])
        applicant.keyword_counts.create!(keyword:, count: 0)

        applicant = scipi.scored_applicants.first

        assert 0, applicant.keyword_stats[keyword.id.to_s]['count']
      end

      # Test the scenario where the max GE values are nil. This generally happens when
      # when the first applicants are new users and have empty profiles
      test 'scaled GE score with nil max' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:, total_publications: nil, years_experience: nil)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        assert_nil scored_applicant.scaled_ge_score
      end

      # Test the scenario where the max GE values are nil. This is less likely than the
      # scenario where the max GE values are nil, but it's worth testing anyway.
      test 'scaled GE score with zero max' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:, total_publications: 0, years_experience: 0)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        assert_nil scored_applicant.scaled_ge_score
      end

      # Test the scenario where one of the values used to calculate the GE score is missing
      test 'scaled GE score with missing GE score component' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        expert = FactoryBot.create(:expert)
        # Supply max values
        scipi.applicants.create!(expert:, total_publications: 100, years_experience: 30)
        applicant = scipi.applicants.create!(expert:, total_publications: 40, years_experience: nil)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        assert_nil scored_applicant.scaled_ge_score
      end

      test 'keyword stats' do
        scipi = FactoryBot.create(:scipi, invite_only: true, published: true)
        expert = FactoryBot.create(:expert)
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'])
        applicant = scipi.applicants.create!(user: expert)
        applicant.keyword_counts.create!(keyword:, count: 23)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        stats = scored_applicant.keyword_stats_for(keyword:, max_rank: 10)

        assert_equal 1.0, stats.scaled_score
        assert_equal 23, stats.count
        assert_equal 1, stats.rank
        assert stats.top_ranked?
      end

      test 'keyword stats not top ranked' do
        scipi = FactoryBot.create(:scipi, invite_only: true, published: true)
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'])
        applicant1 = scipi.applicants.create!(user: expert1)
        applicant1.keyword_counts.create!(keyword:, count: 23)
        applicant2 = scipi.applicants.create!(user: expert2)
        applicant2.keyword_counts.create!(keyword:, count: 1)

        scored_applicant2 = scipi.scored_applicants.find(applicant2.id)

        stats = scored_applicant2.keyword_stats_for(keyword:, max_rank: 1)

        assert_not stats.top_ranked?
      end

      test 'selection score with default weights' do
        audited_by = FactoryBot.create(:scipi_admin)
        scipi = FactoryBot.create(:scipi, invite_only: true)
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'], weight: 1)

        # This expert has all of the max values used to calculate the scaled score for the expert below
        max_expert = FactoryBot.create(:expert)
        max_applicant = scipi.applicants.create!(expert: max_expert, total_publications: 100, years_experience: 50)
        max_applicant.keyword_counts.create!(keyword:, count: 100)
        max_applicant.audits.create!(score: 10, audited_by:)

        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:, total_publications: 70, years_experience: 20)
        applicant.keyword_counts.create!(keyword:, count: 46)
        applicant.audits.create!(score: 7, audited_by:)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        assert_in_delta BigDecimal('1.71'), scored_applicant.weighted_selection_score, 0.01
      end

      test 'selection score with alternate weights' do
        audited_by = FactoryBot.create(:scipi_admin)
        scipi = FactoryBot.create(
          :scipi,
          invite_only: true,
          ge_weight: BigDecimal('0.5'),
          keyword_weight: BigDecimal('0.3'),
          audit_weight: BigDecimal('0.2')
        )
        keyword = scipi.selection_keywords.create!(terms: ['Foobar'], weight: 1)

        # This expert has all of the max values used to calculate the scaled score for the expert below
        max_expert = FactoryBot.create(:expert)
        max_applicant = scipi.applicants.create!(expert: max_expert, total_publications: 100, years_experience: 50)
        max_applicant.keyword_counts.create!(keyword:, count: 100)
        max_applicant.audits.create!(score: 10, audited_by:)

        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:, total_publications: 70, years_experience: 20)
        applicant.keyword_counts.create!(keyword:, count: 46)
        applicant.audits.create!(score: 7, audited_by:)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        assert_in_delta BigDecimal('1.66'), scored_applicant.weighted_selection_score, 0.01
      end

      test 'keyword score with custom weights' do
        scipi = FactoryBot.create(:scipi, :invite_only)
        foo_keyword = scipi.selection_keywords.create!(terms: ['Foo'], weight: '0.2'.to_d)
        bar_keyword = scipi.selection_keywords.create!(terms: ['Bar'], weight: '0.8'.to_d)
        expert = FactoryBot.create(:expert)

        # This expert has all of the max values used to calculate the scaled score for the expert below
        max_expert = FactoryBot.create(:expert)
        max_applicant = scipi.applicants.create!(expert: max_expert)
        max_applicant.keyword_counts.create!(keyword: foo_keyword, count: 100)
        max_applicant.keyword_counts.create!(keyword: bar_keyword, count: 100)

        applicant = scipi.applicants.create!(expert:)
        applicant.keyword_counts.create!(keyword: foo_keyword, count: 50)
        applicant.keyword_counts.create!(keyword: bar_keyword, count: 20)

        scored_applicant = scipi.scored_applicants.find(applicant.id)

        assert_equal '0.26'.to_d, scored_applicant.weighted_keyword_component_score
      end
    end
  end
end

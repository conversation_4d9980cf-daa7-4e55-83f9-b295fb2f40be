# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Selection
    class KeywordTest < ActiveSupport::TestCase
      test 'set single term and terms_label from terms_field on validation' do
        survey = FactoryBot.create(:survey)

        keyword = survey.selection_keywords.build(terms_field: 'foo')
        keyword.validate

        assert_equal ['foo'], keyword.terms
        assert_equal 'foo', keyword.terms_label
      end

      test 'set multiple terms and terms_label from terms_field on validation' do
        survey = FactoryBot.create(:survey)

        keyword = survey.selection_keywords.build(terms_field: 'apple, banana, cherry')
        keyword.validate

        assert_equal %w[apple banana cherry], keyword.terms
        assert_equal 'apple', keyword.terms_label
      end
    end
  end
end

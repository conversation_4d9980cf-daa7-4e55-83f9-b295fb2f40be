# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Selection
    class ApplicantTest < ActiveSupport::TestCase
      test 'keyword counts not out of date when applicants do not have cv_text' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        scipi.selection_keywords.create!(terms: ['foobar'])

        expert = FactoryBot.create(:expert)
        expert.profile.update!(cv_text: nil)

        applicants = scipi.applicants
        assert_not applicants.keyword_counts_out_of_date?
      end

      test 'keyword counts are out of date when applicants have CV, but no counts' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        scipi.selection_keywords.create!(terms: ['foobar'])

        expert = FactoryBot.create(:expert)
        expert.profile.update!(cv_text: 'I am a CV')
        scipi.applicants.create!(expert:)

        applicants = scipi.applicants
        assert applicants.keyword_counts_out_of_date?
      end

      test 'stats are out of date with cv text was updated more recently than the keyword count' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        selection_keyword = scipi.selection_keywords.create!(terms: ['foobar'], terms_updated_at: 2.weeks.ago)

        expert = FactoryBot.create(:expert)
        expert.profile.update!(cv_text: 'I am a CV', extracted_cv_text_updated_at: Time.current)
        applicant = scipi.applicants.create!(expert:)
        applicant.keyword_counts.create!(keyword: selection_keyword, count: 3, updated_at: 1.week.ago)

        applicants = scipi.applicants
        assert applicants.keyword_counts_out_of_date?
      end

      test 'stats are out of date when selection keyword\'s content updated after an applicant\'s count is updated' do
        # This is a rare situation where a keyword's content is updated. Perhaps an admin is
        # fixing a typo, is adjusting a term, or perhaps just changing an existing keyword.
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        selection_keyword = scipi.selection_keywords.create!(terms: ['foobar'], terms_updated_at: Time.current)

        expert = FactoryBot.create(:expert)
        expert.profile.update!(cv_text: 'I am a CV', extracted_cv_text_updated_at: 2.weeks.ago)
        applicant = scipi.applicants.create!(expert:)
        applicant.keyword_counts.create!(keyword: selection_keyword, count: 3, updated_at: 1.week.ago)

        applicants = scipi.applicants
        assert applicants.keyword_counts_out_of_date?
      end

      test 'general expertise out of date for a scipi' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert)

        expert.profile.update!(general_expertise_updated_at: Time.current)
        scipi.applicants.create!(expert:, general_expertise_updated_at: 1.day.ago)
        applicants = scipi.applicants

        assert applicants.general_expertise_out_of_date?
      end

      test 'general expertise not out of date for a scipi' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert)

        general_expertise_updated_at = Time.current
        expert.profile.update!(general_expertise_updated_at:)
        scipi.applicants.create!(expert:, general_expertise_updated_at:)
        applicants = scipi.applicants

        assert_not applicants.general_expertise_out_of_date?
      end

      test 'general expertise out of date for applicant' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert)

        expert.profile.update!(general_expertise_updated_at: Time.current)
        applicant = scipi.applicants.create!(expert:, general_expertise_updated_at: 1.day.ago)

        assert applicant.general_expertise_out_of_date?
      end

      test 'general expertise not out of date' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert)

        general_expertise_updated_at = Time.current
        expert.profile.update!(general_expertise_updated_at:)
        applicant = scipi.applicants.create(expert:, general_expertise_updated_at:)

        assert_not applicant.general_expertise_out_of_date?
      end

      test 'update profile fields' do
        scipi = FactoryBot.create(:scipi, :invite_only, :published)
        expert = FactoryBot.create(:expert)
        profile = expert.profile
        profile.update!(publications_count: 40)
        profile.employment_history_items.create!(
          employment_sector: FactoryBot.create(:employment_sector),
          years: 10
        )
        degree_type = FactoryBot.create(:degree_type, :doctorate, name: 'Foo Degree')
        profile.degrees.create!(degree_type:, subject_area: 'Foo', graduation_year: 2000)

        applicant = scipi.applicants.create!(expert:)

        applicant.update_profile_fields!

        assert_equal 40, applicant.total_publications
        assert_equal 10, applicant.total_work_experience
        assert_equal 2000, applicant.first_doctorate_year
        assert_equal 'Foo Degree', applicant.degree_type_names
      end
    end
  end
end

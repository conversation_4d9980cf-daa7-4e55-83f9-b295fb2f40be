# frozen_string_literal: true

require 'test_helper'

module Surveys
  module Selection
    class ScoreWeightsTest < ActiveSupport::TestCase
      test 'sum' do
        score_weights = ScoreWeights.new(ge_weight: 0.2, keyword_weight: 0.2, audit_weight: 0.3)

        assert_equal 0.7, score_weights.sum
      end

      test 'sum is not valid' do
        score_weights = ScoreWeights.new(ge_weight: 0.2, keyword_weight: 0.2, audit_weight: 0.3)

        assert_not score_weights.valid?
      end

      test 'is valid' do
        score_weights = ScoreWeights.new(ge_weight: 0.5, keyword_weight: 0.2, audit_weight: 0.3)

        assert score_weights.valid?
      end
    end
  end
end

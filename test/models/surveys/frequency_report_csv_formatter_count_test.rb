# frozen_string_literal: true

require 'test_helper'

module Surveys
  class FrequencyReportCsvFormatterCountTest < ActiveSupport::TestCase
    setup do
      load_all_render_types
      load_all_result_types
    end

    test 'frequency_column_count is max of chartable answer choice counts' do
      survey = create(:survey)
      question1 = create(:radio, question_group: survey, answer_choice_count: 2)
      question2 = create(:radio, question_group: survey, answer_choice_count: 4)
      question3 = create(:radio, question_group: survey, answer_choice_count: 1)

      create(:result_definition, survey:, question: question1)
      create(:result_definition, survey:, question: question2)
      create(:result_definition, survey:, question: question3)

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      formatter = Surveys::FrequencyReportCsvFormatter.new(raw_results)

      assert_equal 4, formatter.instance_variable_get(:@frequency_column_count), 'should have four frequency columns'
    end

    test 'frequency_column_count is max of grid select answer choice counts' do
      survey = create(:survey)
      grid_question1 = create(:grid_question, question_group: survey, answer_choice_count: 2)
      grid_question2 = create(:grid_question, question_group: survey, answer_choice_count: 4)
      grid_question3 = create(:grid_question, question_group: survey, answer_choice_count: 1)
      grid_question1.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Select'
      )
      grid_question2.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Select'
      )
      grid_question3.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Select'
      )

      create(:result_definition, survey:, question: grid_question1)
      create(:result_definition, survey:, question: grid_question2)
      create(:result_definition, survey:, question: grid_question3)

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      formatter = Surveys::FrequencyReportCsvFormatter.new(raw_results)

      assert_equal 4, formatter.instance_variable_get(:@frequency_column_count), 'should have matching frequency columns'
    end

    test 'frequency_column_count is max of grid checkbox column counts' do
      survey = create(:survey)
      grid_question1 = create(:grid_question, question_group: survey)
      grid_question2 = create(:grid_question, question_group: survey)
      grid_question3 = create(:grid_question, question_group: survey)
      grid_question1.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Checkbox',
        row_headers: %w[A B],
        column_headers: %w[D E]
      )
      grid_question2.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Checkbox',
        row_headers: %w[A B],
        column_headers: %w[D E F G]
      )
      grid_question3.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Checkbox',
        row_headers: %w[A B],
        column_headers: %w[D]
      )

      create(:result_definition, survey:, question: grid_question1)
      create(:result_definition, survey:, question: grid_question2)
      create(:result_definition, survey:, question: grid_question3)

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      formatter = Surveys::FrequencyReportCsvFormatter.new(raw_results)

      assert_equal 4, formatter.instance_variable_get(:@frequency_column_count), 'should have matching frequency columns'
    end

    test 'frequency_column_count correct when chartable, grid select and grid checkbox questions are present' do
      survey = create(:survey)

      choice_count_radio = 3
      choice_count_grid_select = 4
      choice_count_grid_checkbox = 5

      radio_question = create(:radio, question_group: survey, answer_choice_count: choice_count_radio)

      grid_select_question = create(:grid_question, question_group: survey, answer_choice_count: choice_count_grid_select)
      grid_select_question.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Select'
      )

      grid_checkbox_question = create(:grid_question, question_group: survey)
      column_headers = (0..(choice_count_grid_checkbox - 1)).map { SecureRandom.uuid } # unique strings for col headers
      grid_checkbox_question.create_grid_structure!(
        first_cell_text: 'foobar',
        input_type: 'Checkbox',
        row_headers: %w[A B],
        column_headers:
      )

      create(:result_definition, survey:, question: radio_question)
      create(:result_definition, survey:, question: grid_select_question)
      create(:result_definition, survey:, question: grid_checkbox_question)

      raw_results = LegacyReport::SurveyResultsQuery.new(survey, show_hidden: true).results
      formatter = Surveys::FrequencyReportCsvFormatter.new(raw_results)

      expected_max = [choice_count_radio, choice_count_grid_select, choice_count_grid_checkbox].max

      assert_equal expected_max, formatter.instance_variable_get(:@frequency_column_count), 'frequency_column_count should be max of these counts'
    end
  end
end

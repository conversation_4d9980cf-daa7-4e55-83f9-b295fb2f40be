# frozen_string_literal: true

require 'test_helper'

class SurveysTest < ActiveSupport::TestCase
  test 'inactive' do
    date = 100.days.ago
    duration = 50.days

    assert Surveys.inactive?(date, duration:)
  end

  test 'not inactive' do
    date = 40.days.ago
    duration = 80.days

    assert_not Surveys.inactive?(date, duration:)
  end

  test 'QuestionGroup.scipis contains only scipis' do
    # Guarantee that we have at least one scipi
    branding = FactoryBot.create(:survey_branding, :scipi)
    FactoryBot.create(:survey, branding:)

    assert QuestionGroup.scipis.all?(&:scipi?)
  end

  test 'QuestionGroup.scipolls contains only scipolls' do
    # Guarantee that we have at least one scipoll
    branding = FactoryBot.create(:survey_branding, :scipoll)
    FactoryBot.create(:survey, branding:)

    assert QuestionGroup.scipolls.all?(&:scipoll?)
  end

  test 'QuestionGroup.featured_scipolls orders open first' do
    # Guarantee that we have at least one valid open and closed survey in the collection
    branding = FactoryBot.create(:survey_branding, :scipoll)
    FactoryBot.create(:survey, :published, :closed, branding:)
    FactoryBot.create(:survey, :published, :open, branding:)
    FactoryBot.create(:survey, :published, :closed, branding:)

    featured_scipolls = QuestionGroup.featured_scipolls

    assert featured_scipolls.first.open?
    assert featured_scipolls[featured_scipolls.count - 1].closed? # `.last` does NOT work here
  end

  test 'Featured surveys orders by publish date' do
    FactoryBot.create(:survey, published_at: 3.days.ago)
    most_recent_published = FactoryBot.create(:survey, published_at: 1.day.ago)
    FactoryBot.create(:survey, published_at: 3.days.ago)

    featured_surveys = general_paricipation_surveys.featured

    assert_equal featured_surveys.first, most_recent_published
  end

  private

  def general_paricipation_surveys
    QuestionGroup.where('invite_only = FALSE AND (access_code = \'\' OR access_code IS NULL)')
  end
end

# frozen_string_literal: true

require 'test_helper'

class EmailConfirmationTest < ActiveSupport::TestCase
  test 'unconfirmed' do
    confirmation = EmailConfirmation.new(confirmed_at: nil)

    assert confirmation.unconfirmed?
    assert_not confirmation.confirmed?
  end

  test 'confirmed' do
    confirmation = EmailConfirmation.new(confirmed_at: Time.current)

    assert_not confirmation.unconfirmed?
    assert confirmation.confirmed?
  end

  test 'confirm!' do
    expert = FactoryBot.create(:expert)
    email = FactoryBot.create(:email, profile: expert.profile)
    confirmation = FactoryBot.create(:email_confirmation, email:)

    confirmation.confirm!

    assert confirmation.confirmed?
  end

  test 'expired?' do
    confirmation = EmailConfirmation.new(created_at: 8.days.ago)

    assert confirmation.expired?, 'confirmation should be expired'
  end

  test 'not expired' do
    confirmation = EmailConfirmation.new(created_at: 6.days.ago)

    assert_not confirmation.expired?, 'confirmation should not be expired'
  end
end

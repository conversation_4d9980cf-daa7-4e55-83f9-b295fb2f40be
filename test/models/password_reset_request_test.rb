# frozen_string_literal: true

require 'test_helper'

class PasswordResetRequestTest < ActiveSupport::TestCase
  test 'create a password reset request' do
    user = FactoryBot.create(:expert)

    request = PasswordResetRequest.new(email: user.email)

    assert request.save
    user.reload
    assert_not_nil user.reset_password_token
    assert_not_nil user.reset_password_sent_at
  end

  test 'must have a valid email' do
    request = PasswordResetRequest.new(email: 'aaaaaaaaaa')

    assert_not request.save
    assert request.errors.added?(:email, "should be in the format '<EMAIL>'")
  end

  test 'email must exist in the system' do
    email = Faker::Internet.email
    request = PasswordResetRequest.new(email:)

    assert_not request.save
    assert request.errors.added?(:email,
                                 'This email is not associated with an account on our system')
  end
end

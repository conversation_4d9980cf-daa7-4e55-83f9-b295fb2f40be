# frozen_string_literal: true

require 'test_helper'

module Mailchimp
  class SyncSubscriberRequestTest < ActiveSupport::TestCase
    test 'subscriber hash' do
      email = Faker::Internet.email
      hashed_email = Digest::MD5.hexdigest(email)
      user = FactoryBot.create(:user, email:)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_equal hashed_email, request.subscriber_hash
    end

    test 'email address from request body' do
      email = Faker::Internet.email
      user = FactoryBot.create(:user, email:)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      body = request.member_body_params

      assert_equal email, body[:email_address]
    end

    test 'status_if_new is subscribed' do
      user = FactoryBot.create(:user)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      body = request.member_body_params

      assert_equal 'subscribed', body[:status_if_new]
    end

    test 'language' do
      user = FactoryBot.create(:user)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)
      body = request.member_body_params

      assert_equal 'en', body[:language]
    end

    test 'interests for non-experts are empty' do
      user = FactoryBot.create(:user)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)
      body = request.member_body_params

      assert_equal({}, body[:interests])
    end

    test 'interests for experts are the default group ids' do
      interests = { 'aaa' => true, 'bbb' => true }
      ENV['MAILCHIMP_DEFAULT_GROUP_IDS'] = interests.keys.join(',')
      user = FactoryBot.create(:expert)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)
      body = request.member_body_params

      assert_equal interests, body[:interests]
    end

    test 'merge fields FNAME' do
      user = FactoryBot.create(:user, first_name: 'Freddy')

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field request, :FNAME, 'Freddy'
    end

    test 'merge fields LNAME' do
      user = FactoryBot.create(:user, last_name: 'Freeloader')

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field request, :LNAME, 'Freeloader'
    end

    test 'merge fields CV with a CV present' do
      user = FactoryBot.create(:expert, :with_cv)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field request, :CV, user.profile.cv.created_at.strftime('%m/%d/%Y')
    end

    test 'merge fields CV without a CV present' do
      user = FactoryBot.create(:expert, cv: nil)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field_nil request, :CV
    end

    test 'merge fields with expertise' do
      user = FactoryBot.create(:expert)
      expertise = Expertise.create!(name: 'My test expertise')
      user.profile.taggings.create!(expertise:)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field request, :EXPERTISE, 'true'
    end

    test 'merge fields without expertise' do
      user = FactoryBot.create(:expert)
      user.profile.taggings.destroy

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field request, :EXPERTISE, 'false'
    end

    test 'institutional email merge field' do
      unknown = domain_lists(:unknown)
      unknown.domains.create!(hostname: 'example.com')
      user = FactoryBot.create(:expert, email: '<EMAIL>')

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_merge_field request, :INSTEMAIL, 'Unknown'
    end

    test 'expert subscriber type tag' do
      user = FactoryBot.create(:expert)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_tag request, 'Registered expert'
    end

    test 'observer subscriber type tag' do
      user = FactoryBot.create(:observer)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_tag request, 'Registered observer'
    end

    test 'unknown subscriber type tag' do
      user = FactoryBot.create(:user)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_tag request, 'Unknown user type'
    end

    test 'active Expertise tag' do
      user = FactoryBot.create(:expert)
      expertise = Expertise.create!(name: 'Stuff')
      user.profile.taggings.create!(expertise:)

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_tag request, 'Stuff'
    end

    test 'inactive Expertise tag' do
      user = FactoryBot.create(:expert)
      Expertise.create!(name: 'Not an active tag')

      request = SyncSubscriberRequest.new(list_id: 'xxx', user:)

      assert_tag request, 'Not an active tag', active: false
    end

    private

    def assert_merge_field(request, field, expected_value)
      body = request.member_body_params
      merge_fields = body[:merge_fields]
      actual_value = merge_fields[field]

      assert_equal expected_value,
                   actual_value,
                   "expected merge field '#{field}' to be '#{expected_value}' but was '#{actual_value}'"
    end

    def assert_merge_field_nil(request, field)
      body = request.member_body_params
      merge_fields = body[:merge_fields]

      assert_nil merge_fields[field], "expected merge field '#{field}' to be nil but was '#{merge_fields[field]}'"
    end

    def assert_tag(request, expected_name, active: true)
      body = request.tags_body_params
      actual_tags = body[:tags]

      actual_tag = actual_tags.find { |t| expected_name == t['name'] }

      assert_not_nil actual_tag, "expected tag with 'name': '#{expected_name}' to be present in #{actual_tags}"

      actual_status = actual_tag['status']
      expected_status = active ? 'active' : 'inactive'

      assert_equal expected_status,
                   actual_status,
                   "expected tag '#{expected_name}' to be '#{expected_status}' but was '#{actual_status}'"
    end
  end
end

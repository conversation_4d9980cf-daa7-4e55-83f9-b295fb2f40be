# frozen_string_literal: true

require 'test_helper'

module Profiles
  class OtherExpertiseTest < ActiveSupport::TestCase
    test 'return empty array for blank text' do
      terms = Profiles::OtherExpertise.as_terms(nil)

      assert_empty terms
    end

    test 'all white space returns an cempty array' do
      terms = Profiles::OtherExpertise.as_terms("    \t \n \r    ")

      assert_empty terms
    end

    test 'split new term by semicolon' do
      terms = Profiles::OtherExpertise.as_terms('First;Second')

      assert_equal %w[first second], terms
    end

    test 'split new term by comma' do
      terms = Profiles::OtherExpertise.as_terms('One,Two')

      assert_equal %w[one two], terms
    end

    test 'split new term by carriage return' do
      terms = Profiles::OtherExpertise.as_terms("Aaa\rBbb")

      assert_equal %w[aaa bbb], terms
    end

    test 'split new term by newline' do
      terms = Profiles::OtherExpertise.as_terms("Hello\nGoodbye")

      assert_equal %w[hello goodbye], terms
    end

    test 'split new term by tab' do
      terms = Profiles::OtherExpertise.as_terms("Coke\tPepsi")

      assert_equal %w[coke pepsi], terms
    end

    test 'split new term by slash' do
      terms = Profiles::OtherExpertise.as_terms('Axl/Duff')

      assert_equal %w[axl duff], terms
    end

    test 'split term by period' do
      terms = Profiles::OtherExpertise.as_terms('Something. Else')

      assert_equal %w[something else], terms
    end

    test 'strip leading and trailing whitespace' do
      terms = Profiles::OtherExpertise.as_terms('   No spaces here   ')

      assert_equal ['no spaces here'], terms
    end

    test 'do not create empty terms' do
      terms = Profiles::OtherExpertise.as_terms(';Only Term;')

      assert_equal ['only term'], terms
    end

    test 'ensure uniq terms' do
      terms = Profiles::OtherExpertise.as_terms('Redundant;Redundant')

      assert_equal ['redundant'], terms
    end
  end
end

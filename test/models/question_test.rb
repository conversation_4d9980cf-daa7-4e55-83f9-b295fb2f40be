# frozen_string_literal: true

require 'test_helper'

class QuestionTest < ActiveSupport::TestCase
  setup do
    @user = create(:expert)
    @survey = create(:question_group, created_by_id: @user.id)
    @grid_radio_question = create(:grid_question, question_group: @survey, question_text: 'Radio question text')
    create(:grid_structure, :radio, question: @grid_radio_question, row_headers: %w[Yes No], column_headers: %w[Up Down], first_cell_text: 'First Cell')

    @grid_checkbox_question = create(:grid_question, question_group: @survey, question_text: 'Checkbox question text')
    create(:grid_structure, input_type: 'Checkbox', question: @grid_checkbox_question, row_headers: %w[Chocolate Vanilla Strawberry], column_headers: %w[Delicious Pricey], first_cell_text: 'Top Cell')

    @grid_select_question = create(:grid_question, question_group: @survey, question_text: 'Select question text')
    create(:grid_structure, input_type: 'Select', question: @grid_select_question, row_headers: %w[Apple Google Tesla], column_headers: %w[Prestige Pay Convenience], first_cell_text: 'Top Left Cell')
  end

  test 'long type' do
    long_type_question = build(:question, question_group: @survey, type: 'Questions::Long')
    assert_equal true, long_type_question.long_type?
  end

  test 'radio type' do
    long_type_question = build(:question, question_group: @survey, type: 'Questions::Radio')
    assert_equal true, long_type_question.radio_type?
  end

  test 'valid without section' do
    question = build(:question, question_group: @survey, section: nil)
    assert_equal true, question.valid?
  end

  test 'invalid without survey' do
    question = build(:question, question_group: nil)
    assert_equal false, question.valid?
  end

  test 'grid radio type' do
    assert_equal true, @grid_radio_question.grid_type?
    assert_equal true, @grid_radio_question.grid_radio_type?
    assert_equal false, @grid_radio_question.grid_checkbox_type?
    assert_equal false, @grid_radio_question.grid_select_type?
  end

  test 'grid checkbox type' do
    assert_equal true, @grid_checkbox_question.grid_type?
    assert_equal false, @grid_checkbox_question.grid_radio_type?
    assert_equal true, @grid_checkbox_question.grid_checkbox_type?
    assert_equal false, @grid_checkbox_question.grid_select_type?
  end

  test 'grid select type' do
    assert_equal true, @grid_select_question.grid_type?
    assert_equal false, @grid_select_question.grid_radio_type?
    assert_equal false, @grid_select_question.grid_checkbox_type?
    assert_equal true, @grid_select_question.grid_select_type?
  end

  test 'copy structure of grid question' do
    existing_question = FactoryBot.create(:grid_select_question)
    new_question = FactoryBot.create(
      :grid_question,
      grid_structure: GridStructure.new(input_type: 'Select')
    )

    existing_question.copy_grid_to(new_question)

    assert_equal existing_question.grid_structure.input_type,
                 new_question.grid_structure.input_type
    assert_equal existing_question.grid_structure.row_headers,
                 new_question.grid_structure.row_headers
    assert_equal existing_question.grid_structure.column_headers,
                 new_question.grid_structure.column_headers
    assert_equal existing_question.grid_structure.first_cell_text,
                 new_question.grid_structure.first_cell_text
  end

  test 'copy question with answer_choices to new survey' do
    user = create(:expert)

    source_survey = create(:question_group, created_by_id: user.id)
    destination_survey = create(:question_group, created_by_id: user.id)

    source_question = create(:radio, survey: source_survey)
    destination_question = create(:question, survey: destination_survey, type: Questions::Radio.new.class.name) # question WITHOUT answer_choices

    answer_choice_count = 3 # multiple_choice factory default

    # verify setup
    assert_equal answer_choice_count, source_question.answer_choices.count
    assert_equal 0, destination_question.answer_choices.count

    source_question.copy_answer_choices_to(destination_question)

    assert_equal answer_choice_count, source_question.answer_choices.count
    assert_equal answer_choice_count, destination_question.answer_choices.count
    (1..answer_choice_count).each do |i|
      assert_equal destination_question.answer_choices.find_by!(position: i).label, source_question.answer_choices.find_by!(position: i).label
      assert_not_equal destination_question.answer_choices.find_by!(position: i).question_id, source_question.answer_choices.find_by!(position: i).question_id
    end
  end

  test 'skip count' do
    question = create(:radio)
    survey = question.question_group

    answer_group1 = survey.answer_groups.create!(
      submitted_at: Time.zone.now,
      submitter: @user
    )
    answer_group2 = survey.answer_groups.create!(
      submitted_at: Time.zone.now,
      submitter: @user
    )

    answer_group1.answers.create!(
      question:,
      answer_choices: [question.answer_choices.first]
    )
    answer_group2.answers.create!(question:)

    assert_equal 1, question.skip_count, 'Should have a skip count of 1'
  end

  test 'grid radio skip count' do
    question = create(:grid_question, :radio)
    survey = question.question_group

    answer_group1 = survey.answer_groups.create!(
      submitted_at: Time.zone.now,
      submitter: @user
    )
    answer_group2 = survey.answer_groups.create!(
      submitted_at: Time.zone.now,
      submitter: @user
    )

    answer_group1.answers.create!(question:, answer_text_on_1st_row: 'foo')
    answer_group2.answers.create!(question:)

    assert_equal 1, question.skip_count, 'Should have a skip count of 1'
  end

  test 'answer count' do
    question = create(:radio)
    survey = question.question_group

    answer_group1 = survey.answer_groups.create!(
      submitted_at: Time.zone.now,
      submitter: @user
    )
    answer_group2 = survey.answer_groups.create!(
      submitted_at: Time.zone.now,
      submitter: @user
    )

    answer_group1.answers.create!(
      question:,
      answer_choices: [question.answer_choices.first]
    )
    answer_group2.answers.create!(question:)

    assert_equal 1, question.answer_count, 'Should have an answer count of 1'
  end

  test 'answer choice labels' do
    answer_choice_labels = %w[one two three]
    question = create(:question)

    answer_choice_labels.each_with_index do |label, index|
      question.answer_choices.create!(label:, position: index + 1)
    end

    assert_equal answer_choice_labels, question.answer_choice_labels
  end

  test 'clone question' do
    target_survey = create(:survey)
    source_question = create(:question)

    cloned_question = source_question.clone(target_survey:)

    assert_equal target_survey, cloned_question.survey,
                 'the cloned question has the wrong target survey'
    assert_equal source_question.question_text, cloned_question.question_text,
                 'the cloned question\'s text should match the source\'s'
    assert_equal source_question.answer_choice_labels,
                 cloned_question.answer_choice_labels,
                 'the cloned question\'s answer choice labels should match'
  end

  test 'clone question in section' do
    target_survey = create(:survey)
    source_question = create(:question)
    target_section = create(:question_section, survey: target_survey)

    cloned_question = source_question.clone(
      target_survey:,
      target_section:
    )

    assert_equal target_section, cloned_question.section,
                 'should be in the supplied section'
  end

  test 'cloned question should not have comments' do
    target_survey = create(:survey)
    source_question = create(:question)
    create(:comment, debate_topic: source_question)
    target_section = create(:question_section, survey: target_survey)

    cloned_question = source_question.clone(
      target_survey:,
      target_section:
    )

    assert_equal 0, cloned_question.comments.count,
                 'should not have comments'
  end

  test 'question number (without sections)' do
    survey = create(:survey)
    question = create(:question, survey:)

    assert_equal question.position.to_s, question.number,
                 'question number should be same as question position'
  end

  test 'question number (with sections)' do
    survey = create(:survey)
    section = create(:question_section, survey:, position: 2)
    question = create(:question, survey:, section:, position: 1)

    assert_equal '2.1', question.number,
                 'question number should lead with section number'
  end

  test 'update question' do
    question = create(:question)

    new_allow_answer_explanation = !question.allow_answer_explanation
    new_answer_choice_labels = %w[xxx yyy zzz]
    new_question_text = 'New Text'
    new_required_value = question.required? ? '0' : '1' # negate value
    new_question_details = 'New Details'
    new_layout_type = 999 # Not a real value

    question.update_question!(
      type: question.type,
      allow_explanation: new_allow_answer_explanation,
      answer_choice_labels: new_answer_choice_labels,
      answer_required: new_required_value,
      input_type: nil,
      column_headers: nil,
      first_cell_text: nil,
      geometric_mean: nil,
      question_details: new_question_details,
      question_layout_type: new_layout_type,
      question_text: new_question_text,
      row_headers: nil,
      max_selected_choices: nil,
      weighted_sum: nil,
      weighted_sum_label: nil,
      weight: nil
    )

    question.reload

    assert_equal new_allow_answer_explanation,
                 question.allow_answer_explanation,
                 'Allow answer explanation should have been updated'
    assert_equal new_answer_choice_labels,
                 question.answer_choice_labels,
                 'Answer choice labels should have been updated'
    assert_equal new_question_text,
                 question.question_text,
                 'Question text should have been updated'
    assert_equal new_required_value == '1',
                 question.required?,
                 'Answer presence should have been updated'
    assert_equal new_question_details,
                 question.question_details,
                 'Question details should have been updated'
    assert_equal new_layout_type,
                 question.question_layout_type,
                 'Layout type should have been updated'
  end

  test 'update grid question' do
    question = create(:grid_question)
    create(:grid_structure, :radio, question:)

    new_allow_answer_explanation = !question.allow_answer_explanation
    new_answer_choice_labels = %w[xxx yyy zzz]
    new_question_text = 'New Text'
    new_required_value = question.required? ? '0' : '1' # negate value
    new_question_details = 'New Details'
    new_layout_type = 999 # Not a real value

    new_column_headers = %w[aaa bbb ccc]
    new_row_headers = %w[xxx yyy zzz]
    new_first_cell_text = 'New First Cell'
    new_weighted_sum = true
    new_weighted_sum_label = 'New Label'

    question.update_question!(
      type: question.type,
      allow_explanation: new_allow_answer_explanation,
      answer_choice_labels: new_answer_choice_labels,
      answer_required: new_required_value,
      question_details: new_question_details,
      question_layout_type: new_layout_type,
      question_text: new_question_text,
      input_type: question.grid_structure.input_type,
      column_headers: new_column_headers,
      row_headers: new_row_headers,
      first_cell_text: new_first_cell_text,
      geometric_mean: nil,
      max_selected_choices: nil,
      weighted_sum: new_weighted_sum,
      weighted_sum_label: new_weighted_sum_label,
      weight: nil
    )

    question.reload

    assert_equal new_allow_answer_explanation,
                 question.allow_answer_explanation,
                 'Allow answer explanation should have been updated'
    assert_equal new_answer_choice_labels,
                 question.answer_choice_labels,
                 'Answer choice labels should have been updated'
    assert_equal new_question_text,
                 question.question_text,
                 'Question text should have been updated'
    assert_equal new_required_value == '1',
                 question.required?,
                 'Answer presence should have been updated'
    assert_equal new_question_details,
                 question.question_details,
                 'Question details should have been updated'
    assert_equal new_layout_type,
                 question.question_layout_type,
                 'Layout type should have been updated'
    assert_equal new_column_headers,
                 question.column_headers,
                 'The columns headers should have been updated'
    assert_equal new_row_headers,
                 question.row_headers,
                 'The row headers should have been updated'
    assert_equal new_first_cell_text,
                 question.first_cell_text,
                 'First cell text should have been updated'
    assert_equal new_weighted_sum,
                 question.grid_structure.weighted_sum,
                 'The weighted sum flag should have been updated'
    assert_equal new_weighted_sum_label,
                 question.weighted_sum_label,
                 'The weighted sum label should have been updated'
  end

  test 'update question display fields' do
    question = create(:answer).question

    new_required_value = question.required? ? '0' : '1' # negate value
    new_layout_type = 999 # Not a real value

    question.update_display_fields!(
      answer_required: new_required_value,
      question_layout_type: new_layout_type,
      weight: nil
    )

    question.reload

    assert_equal new_required_value == '1',
                 question.required?,
                 'Answer presence should have been updated'
    assert_equal new_layout_type,
                 question.question_layout_type,
                 'Layout type should have been updated'
  end

  test 'questions are not scorable be default' do
    question = Question.new

    refute question.scorable?,
           'questions are not scorable by default'
  end

  test 'answered?' do
    question = FactoryBot.create(:question)
    FactoryBot.create(:answer, question:)

    assert question.answered?, 'should be answered'
    assert_not question.unanswered?, 'should not be unanswered'
  end

  test 'unanswered?' do
    question = FactoryBot.create(:question)

    assert question.unanswered?, 'should be unanswered'
    assert_not question.answered?, 'should not be answered'
  end

  test 'container is a survey' do
    survey = FactoryBot.create(:survey)
    question = FactoryBot.create(:question, survey:)

    assert_equal survey, question.container, 'container should be a Survey'
  end

  test 'container is a section' do
    section = FactoryBot.create(:question_section)
    survey = section.survey
    question = FactoryBot.create(:question, survey:, section:)

    assert_equal section, question.container, 'container should be a section'
  end

  test 'admins can administer any question' do
    admin = FactoryBot.create(:admin)
    question = FactoryBot.create(:question)

    assert question.administered_by?(admin)
  end

  test 'required' do
    question = Question.new(validation_rules: { presence: '1' })

    assert question.required?, 'should be required'
  end

  test 'not required' do
    question = Question.new(validation_rules: { presence: '0' })

    assert_not question.required?, 'should not be required'
  end
end

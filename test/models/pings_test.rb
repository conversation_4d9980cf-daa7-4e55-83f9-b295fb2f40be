# frozen_string_literal: true

require 'test_helper'

class PingsTest < ActiveSupport::TestCase
  test 'logged out users are not participants' do
    assert_not Pings.participant?(nil)
  end

  Role.where(expert: false).find_each do |role|
    test "The '#{role.name}' role  cannot be a Pings participant" do
      non_expert = User.new(roles: [role])

      assert_not Pings.participant?(non_expert)
    end
  end

  test 'experts are Ping participants' do
    expert = User.new(roles: Role.experts)

    assert Pings.participant?(expert)
  end

  Role.where(pings_admin: false).find_each do |role|
    test "non-admin '#{role.name}' is is not a Pings admin" do
      non_expert = User.new(roles: [role])

      assert_not Pings.admin?(non_expert)
    end
  end

  test 'pings_admins are Ping admin' do
    pings_admin = User.new(roles: Role.pings_admins)

    assert Pings.admin?(pings_admin)
  end
end

# frozen_string_literal: true

require 'test_helper'

class OpportunitiesNewsletterTest < ActiveSupport::TestCase
  test 'limits relevant expertise to five' do
    expert = FactoryBot.create(:expert)
    6.times { |n| expert.profile.expertises << FactoryBot.build(:expertise, name: "Expertise #{n}") }

    newsletter = OpportunitiesNewsletter.build(expert:)

    assert_equal 5, newsletter.relevant_expertise.count
  end

  test 'cv missing' do
    expert = FactoryBot.create(:expert)

    newsletter = OpportunitiesNewsletter.build(expert:)

    assert_includes newsletter.profile_alerts, 'You have not uploaded a CV.'
  end

  test 'profile incomplete' do
    expert = FactoryBot.create(:expert, :with_cv)

    newsletter = OpportunitiesNewsletter.build(expert:)

    assert_includes newsletter.profile_alerts, 'Your profile is incomplete.'
  end

  test 'cv stale' do
    expert = FactoryBot.create(:expert, :complete_profile, :with_cv)
    expert.profile.cv.update!(created_at: (Profile::CV_STALE_THRESHOLD_DAYS + 1.day).days.ago)

    newsletter = OpportunitiesNewsletter.build(expert:)

    assert_includes newsletter.profile_alerts,
                    "You have not updated your CV since #{expert.profile.cv_last_updated_at.strftime('%B %Y')}."
  end
end

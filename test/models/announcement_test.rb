# frozen_string_literal: true

require 'test_helper'

class AnnouncementTest < ActiveSupport::TestCase
  test 'draft announcement not promotable (even with future promote_until)' do
    draft = FactoryBot.create(:announcement, :draft, promote_until: TestData.any_future_time)

    assert_not draft.promotable?
  end

  test 'published announcement promotable if promote_until is nil' do
    published = FactoryBot.create(:announcement, :published, promote_until: nil)

    assert published.promotable?
  end
end

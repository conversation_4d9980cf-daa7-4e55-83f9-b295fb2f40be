# frozen_string_literal: true

require 'test_helper'

module Users
  class AbilityTest < ActiveSupport::TestCase
    test 'logged out users cannot :view users by default' do
      ability = Ability.new(nil)

      assert_not ability.can?(:view, User.new), 'logged out users cannot :view a User'
    end

    test 'users cannot :view users by default' do
      user = FactoryBot.create(:user)

      ability = Ability.new(user)

      assert_not ability.can?(:view, User.new), 'users cannot :view a User'
    end

    test 'internal users cannot :view users by default' do
      role = FactoryBot.create(:role, :internal)
      internal_user = FactoryBot.create(:user, roles: [role])

      ability = Ability.new(internal_user)

      assert_not ability.can?(:view, User.new), 'users cannot :view a User'
    end

    test 'distribution list managers can :view users' do
      role = FactoryBot.create(:role, :manage_distribution_lists)
      distribution_list_manager = FactoryBot.create(:user, roles: [role])

      ability = Ability.new(distribution_list_manager)

      assert ability.can?(:view, User.new), 'distribution list managers should be able to :view users'
    end
  end
end

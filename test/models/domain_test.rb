# frozen_string_literal: true

require 'test_helper'

class DomainTest < ActiveSupport::TestCase
  test 'set default type' do
    domain = Domain.create!(hostname: 'example.com')

    assert_equal DomainList.default, domain.list
  end

  test 'assign to default list for tld' do
    list = domain_lists(:known_institution)
    domain = Domain.create!(hostname: "example.#{list.tlds&.first}")

    assert_equal list, domain.list
  end

  test 'create domain for an email if it does not exist' do
    assert_difference('Domain.count') do
      Domain.create_if_not_exists_for!(email: '<EMAIL>')
    end
  end

  test 'do not create domain for an email when it exists' do
    FactoryBot.create(:domain, hostname: 'bar.com')

    assert_no_difference('Domain.count') do
      Domain.create_if_not_exists_for!(email: '<EMAIL>')
    end
  end
end

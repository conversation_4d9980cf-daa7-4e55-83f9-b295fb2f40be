# frozen_string_literal: true

require 'test_helper'

class Scipi::CustomPayRateTest < ActiveSupport::TestCase
  test 'set custom pay rate' do
    branding = FactoryBot.create(:survey_branding, :scipi)
    scipi = FactoryBot.create(:survey,
                              :contract_required,
                              :invite_only,
                              branding:,
                              engagement_info_attributes: { default_pay_rate: 1234.56 })

    panelist_with_custom_rate = create_panelist(scipi)
    panelist_without_custom_rate = create_panelist(scipi)

    custom_pay_rate = Scipi::CustomPayRate.new(
      scipi_id: scipi.id,
      expert_ids: [panelist_with_custom_rate.expert_id],
      pay_rate: 1000.00
    )

    assert custom_pay_rate.save
    assert_equal 1000.00, panelist_with_custom_rate.reload.pay_rate
    assert_nil panelist_without_custom_rate.reload.pay_rate
  end
end

# frozen_string_literal: true

require 'test_helper'

class Scipi::RoundsTest < ActiveSupport::TestCase
  test 'current question round' do
    scipi = FactoryBot.create(:scipi, :published)
    current_round = FactoryBot.create(:question_round, survey: scipi, opens_at: 1.day.ago, closes_at: 1.day.from_now)
    FactoryBot.create(:debate_round, survey: scipi, opens_at: 1.day.from_now, closes_at: 2.days.from_now)
    FactoryBot.create(:question_round, survey: scipi, opens_at: 2.days.from_now, closes_at: 3.days.from_now)

    assert_equal current_round, scipi.current_question_round
  end

  test 'current or most recent question round is still in progress' do
    scipi = FactoryBot.create(:scipi, :published)
    current_round = FactoryBot.create(:question_round, survey: scipi, opens_at: 1.day.ago, closes_at: 1.day.from_now)
    FactoryBot.create(:debate_round, survey: scipi, opens_at: 1.day.from_now, closes_at: 2.days.from_now)
    FactoryBot.create(:question_round, survey: scipi, opens_at: 2.days.from_now, closes_at: 3.days.from_now)

    assert_equal current_round, scipi.current_or_most_recent_question_round
  end

  test 'current or most recent question round has recently closed' do
    scipi = FactoryBot.create(:scipi, :published)
    recent_round = FactoryBot.create(:question_round, survey: scipi, opens_at: 1.day.ago, closes_at: 1.day.ago)
    FactoryBot.create(:debate_round, survey: scipi, opens_at: 1.day.from_now, closes_at: 2.days.from_now)
    FactoryBot.create(:question_round, survey: scipi, opens_at: 2.days.from_now, closes_at: 3.days.from_now)

    assert_equal recent_round, scipi.current_or_most_recent_question_round
  end
end

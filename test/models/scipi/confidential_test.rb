# frozen_string_literal: true

require 'test_helper'

class Scipi::ConfidentialTest < ActiveSupport::TestCase
  test 'auditors require a confidentiality agreement' do
    scipi = FactoryBot.create(:scipi)
    user = FactoryBot.create(:user)
    scipi.auditors << user

    assert scipi.confidentiality_agreement_required?(user), 'auditors require a confidentiality agreement'
  end

  test 'auditors does not require a confidentiality agreement' do
    scipi = FactoryBot.create(:scipi)
    user = FactoryBot.create(:user)
    scipi.auditors << user
    scipi.confidentiality_agreements.create!(user:, accepted_at: Time.current)

    assert_not scipi.confidentiality_agreement_required?(user),
               'auditors does not require a confidentiality agreement after accepting'
  end

  test 'panelists do not require a confidentiality agreement' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    assert_not scipi.confidentiality_agreement_required?(expert),
               'panelists should not require a confidentiality agreement'
  end
end

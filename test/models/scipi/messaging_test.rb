# frozen_string_literal: true

require 'test_helper'

class Scipi::MessagingTest < ActiveSupport::TestCase
  test 'panelist can send messages less than 30 days after scipi closes_at' do
    closes_at = 10.days.ago
    scipi = FactoryBot.create(:scipi, :invite_only, :published, closes_at:)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    travel_to(closes_at + 25.days) do
      assert scipi.can_send_message?(expert), 'approved panelist should be able to send messages within 30 days'
    end
  end

  test 'panelist cannot send messages more than 30 days after scipi closes_at' do
    closes_at = 10.days.ago
    scipi = FactoryBot.create(:scipi, :invite_only, :published, closes_at:)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    travel_to(closes_at + 35.days) do
      assert_not scipi.can_send_message?(expert), 'approved panelist should not be able to send messages after 30 days'
    end
  end

  test 'panelist can send messages less than 30 days after last round closes' do
    scipi = FactoryBot.create(:scipi, :invite_only, :published, closes_at: 20.days.ago)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    round_opens_at = 10.days.ago
    round_closes_at = 5.days.ago
    FactoryBot.create(:question_round, survey: scipi, opens_at: round_opens_at, closes_at: round_closes_at)

    scipi.reload

    travel_to(round_closes_at + 25.days) do
      assert scipi.can_send_message?(expert), 'approved panelist should be able to send messages within 30 days of round closing'
    end
  end

  test 'panelist cannot send messages more than 30 days after last round closes' do
    scipi = FactoryBot.create(:scipi, :invite_only, :published, closes_at: 20.days.ago)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    round_opens_at = 10.days.ago
    round_closes_at = 5.days.ago
    FactoryBot.create(:question_round, survey: scipi, opens_at: round_opens_at, closes_at: round_closes_at)

    scipi.reload

    travel_to(round_closes_at + 35.days) do
      assert_not scipi.can_send_message?(expert), 'approved panelist should not be able to send messages after 30 days from round closing'
    end
  end

  test 'non-approved applicant can send messages less than 30 days after selection closes' do
    selection_closes_on = 10.days.ago.to_date
    scipi = FactoryBot.create(:scipi, :invite_only, :published, selection_closes_on:)
    expert = FactoryBot.create(:expert)
    scipi.applicants.create!(user: expert, rejected_at: Time.current)

    travel_to(selection_closes_on + 25.days) do
      assert scipi.can_send_message?(expert), 'rejected applicant should be able to send messages within 30 days of selection closing'
    end
  end

  test 'non-approved applicant cannot send messages more than 30 days after selection closes' do
    selection_closes_on = 10.days.ago.to_date
    scipi = FactoryBot.create(:scipi, :invite_only, :published, selection_closes_on:)
    expert = FactoryBot.create(:expert)
    scipi.applicants.create!(user: expert, rejected_at: Time.current)

    travel_to(selection_closes_on + 25.days) do
      assert scipi.can_send_message?(expert), 'rejected applicant should be able to send messages within 30 days of selection closing'
    end
  end

  test 'non-applicant cannot send messages' do
    scipi = FactoryBot.create(:scipi, :invite_only, :published)
    expert = FactoryBot.create(:expert)

    assert_not scipi.can_send_message?(expert), 'non-applicant should not be able to send messages'
  end

  test 'can send messages when no deadline is set' do
    scipi = FactoryBot.create(:scipi, :invite_only, :published, closes_at: nil, selection_closes_on: nil)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    assert scipi.can_send_message?(expert), 'should be able to send messages when no deadline is set'
  end
end

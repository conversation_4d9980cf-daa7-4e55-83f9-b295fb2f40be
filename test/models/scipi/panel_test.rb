# frozen_string_literal: true

require 'test_helper'

class Scipi::PanelTest < ActiveSupport::TestCase
  test 'active panelist when contracts are not required' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:panelist, user: expert, survey: scipi)

    assert scipi.active_panelist?(expert), 'should be active'
  end

  test 'suspended panelists are not active' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:panelist, :suspended, user: expert, survey: scipi)

    assert_not scipi.active_panelist?(expert), 'should not be active'
  end

  test 'active panelist when contracts are required' do
    scipi = FactoryBot.create(:scipi, :contract_required)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:panelist, :contract_signed, user: expert, survey: scipi)

    assert scipi.active_panelist?(expert), 'should be active'
  end

  test 'panelists without a contract are not active' do
    scipi = FactoryBot.create(:scipi, :contract_required)
    expert = FactoryBot.create(:expert)
    FactoryBot.create(:panelist, user: expert, survey: scipi)

    assert_not scipi.active_panelist?(expert), 'should not be active'
  end
end

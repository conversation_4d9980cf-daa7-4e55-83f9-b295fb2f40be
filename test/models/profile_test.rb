# frozen_string_literal: true

require 'test_helper'

class ProfileTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  test 'add matching expertise' do
    profile = FactoryBot.create(:profile, other_expertise: "Don't add me, add me, and don't add me either")

    expertise = FactoryBot.create(:expertise, name: 'Add me')

    Profile.add_matching_expertise(expertise)

    assert_includes profile.reload.expertises, expertise
  end

  test 'do not add non-matching expertise' do
    profile = FactoryBot.create(:profile, other_expertise: "Don't add me, and don't add me either")

    expertise = FactoryBot.create(:expertise, name: 'Add me')

    Profile.add_matching_expertise(expertise)

    assert_not_includes profile.reload.expertises, expertise
  end

  test 'do not add already existing expertise' do
    profile = FactoryBot.create(:profile, other_expertise: 'I already exist')
    expertise = FactoryBot.create(:expertise, name: 'I already exist')
    profile.expertises << expertise

    assert_no_difference -> { profile.expertises.count } do
      Profile.add_matching_expertise(expertise)
    end
  end

  test 'complete profile' do
    assert complete_profile.complete?, 'should be complete'
  end

  test 'observer has different standards for complete profile than expert' do
    expert = FactoryBot.create(:expert, :complete_profile)
    expert.roles = [Role.observer]
    expert.save!

    assert_not expert.profile_complete?, 'Observer requires interest section to be complete, among other things'
  end

  test 'not complete without a country' do
    profile = complete_profile

    profile.update!(country: nil)

    assert_not profile.complete?,
               'should not be complete without a country'
    assert_not profile.name_complete?,
               'name section should not be complete without a country'
  end

  test 'not complete without a CV' do
    assert_not incomplete_profile.expertise_complete?,
               'expertise should not be complete without a cv'
    assert_not incomplete_profile.complete?, 'profile should not be complete without a cv'
  end

  test 'not complete without publication_count' do
    assert_not incomplete_profile.expertise_complete?,
               'expertise should not be complete without publications_count'
    assert_not incomplete_profile.complete?,
               'should not be complete without a publications_count'
  end

  test 'not complete without publications_first_last_count' do
    assert_not incomplete_profile.expertise_complete?,
               'expertise should not be complete without publications_first_last_count'
    assert_not incomplete_profile.complete?,
               'should not be complete without a publications_first_last_count'
  end

  test 'cv? when none is attached' do
    profile = Profile.new

    assert_not profile.cv?, 'cv? should false when the attachment does not exist'
  end

  test 'cv? is false when it is not yet saved' do
    profile = Profile.new(cv: file_fixture('cv.pdf'))

    assert_not profile.cv?, 'cv? should false when the attachment is yet saved'
  end

  test 'cv? is true when it is both attached and persisted' do
    profile = FactoryBot.create(:expert).profile

    profile.cv.attach(file_fixture('cv.pdf'))

    assert profile.cv?, 'cv? should true when the attachment is saved'
  end

  test 'not complete without expertise' do
    assert_not incomplete_profile.expertise_complete?,
               'expertise should not be complete without expertise'
    assert_not incomplete_profile.complete?, 'should not be complete without a expertise'
  end

  test 'not complete without at least one degree' do
    assert_not incomplete_profile.education_complete?,
               'education should not be complete without at least one degree'
    assert_not incomplete_profile.complete?,
               'should not be complete without at least one degree'
  end

  test 'not complete without a current sector of employment' do
    assert_not incomplete_profile.employment_complete?,
               'employment should not be complete without a current sector of employment'
    assert_not incomplete_profile.complete?,
               'should not be complete without a current sector of employment'
  end

  test 'not complete without a current employer' do
    assert_not incomplete_profile.employment_complete?,
               'employment should not be complete without a current employer'
    assert_not incomplete_profile.complete?,
               'should not be complete without a current employer'
  end

  test 'not complete without a current job title' do
    assert_not incomplete_profile.employment_complete?,
               'employment should not be complete without a current job title'
    assert_not incomplete_profile.complete?,
               'should not be complete without a current job title'
  end

  test 'current employer not required for retired experts' do
    expert = retired.expert
    expert.current_employer = nil

    assert retired.employment_complete?,
           'employment should not require an employer for retirees'
  end

  test 'current job title not required for retired experts' do
    expert = retired.expert
    expert.title = nil

    assert retired.employment_complete?,
           'title should not require an employer for retirees'
  end

  test 'employment not complete without YOE for current employment sector' do
    profile = FactoryBot.create(:profile, :employment_complete)
    current_sector = profile.current_employment_sector
    profile
      .employment_history_items
      .find_by!(employment_sector_id: current_sector)
      .destroy

    assert_not profile.reload.employment_complete?,
               'employment should not be complete without YOE for current sector'
  end

  test 'employment complete' do
    profile = FactoryBot.create(:profile, :employment_complete)

    assert profile.employment_complete?, 'employment be complete'
  end

  test 'display name is not required if not set' do
    profile = FactoryBot.create(:profile, display_name: nil)

    assert profile.update(display_name: '')
  end

  test 'cannot unset display name once set' do
    profile = FactoryBot.create(:profile)

    assert_not profile.update(display_name: '')
    assert_error_on profile, :display_name, 'Display name can be changed, but cannot be unset'
  end

  test 'display name is taken' do
    current_profile = FactoryBot.create(:profile)
    other_profile = FactoryBot.create(:profile)
    existing_display_name = other_profile.display_name

    assert Profile.display_name_taken?(current_profile, existing_display_name)
  end

  test 'display name is taken - case insensitive' do
    current_profile = FactoryBot.create(:profile)
    other_profile = FactoryBot.create(:profile)
    existing_display_name = other_profile.display_name.upcase

    assert Profile.display_name_taken?(current_profile, existing_display_name)
  end

  test 'ExtractCvTextJob is enqueued when a CV is added' do
    profile = FactoryBot.create(:profile)

    assert_enqueued_with(job: Profiles::ExtractCvTextJob, args: [profile]) do
      profile.cv.attach(io: load_test_file('cv.pdf'), filename: 'test.pdf')
    end
  end

  test 'ExtractCvTextJob is not enqueued when the CV has not changed' do
    profile = FactoryBot.create(:profile)

    assert_no_enqueued_jobs(only: Profiles::ExtractCvTextJob) do
      profile.update!(display_name: Faker::Internet.username)
    end
  end

  test 'ExtractCvTextJob is enqueued when a CV is updated' do
    profile = FactoryBot.create(:profile, :with_cv)

    assert_enqueued_with(job: Profiles::ExtractCvTextJob, args: [profile]) do
      profile.cv.attach(
        io: load_test_file('test_attachment.pdf'),
        filename: 'test.pdf'
      )
    end
  end

  test 'cv_text is cleared when a CV is removed' do
    profile = FactoryBot.create(:profile, :with_cv)

    assert_no_enqueued_jobs(only: Profiles::ExtractCvTextJob) do
      profile.cv.purge
    end

    assert_nil profile.cv_text, 'cv_text should have been cleared'
  end

  test 'cv download name is an expert\'s name, when present' do
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    expert = FactoryBot.create(
      :expert,
      first_name:,
      last_name:
    )
    profile = FactoryBot.create(:profile, :with_cv, expert:)

    assert_equal "#{last_name}_#{first_name}_#{expert.id}.pdf",
                 profile.cv_download_name
  end

  test 'cv download name is an email when name is incomplete' do
    expert = FactoryBot.create(:expert, first_name: '', last_name: '')
    profile = FactoryBot.create(:profile, :with_cv, expert:)

    assert_equal "#{expert.email.parameterize.downcase}_#{expert.id}.pdf",
                 profile.cv_download_name
  end

  test 'most recent grad year' do
    most_recent_year = 2000
    earlier = FactoryBot.create(:degree, graduation_year: 1990)
    most_recent = FactoryBot.create(:degree, graduation_year: most_recent_year)
    expert = FactoryBot.create(:expert, degrees: [earlier, most_recent])

    assert_equal most_recent_year, expert.most_recent_grad_year
  end

  test 'years since last degree' do
    most_recent_year = 2000
    earlier = FactoryBot.create(:degree, graduation_year: 1990)
    most_recent = FactoryBot.create(:degree, graduation_year: most_recent_year)
    expert = FactoryBot.create(:expert, degrees: [earlier, most_recent])

    years_since_last_degree = Time.current.year - most_recent_year

    assert_equal years_since_last_degree, expert.years_since_last_degree
  end

  test 'keyword count for single term' do
    profile = FactoryBot.create(:expert).profile
    profile.update!(cv_text: 'horse cat dog horse')

    count = profile.keyword_count_for('horse')

    assert_equal 2, count
  end

  test 'keyword count for multiple terms' do
    profile = FactoryBot.create(:expert).profile
    profile.update!(cv_text: 'horse mule donkey cat dog horse')

    count = profile.keyword_count_for('horse', 'mule', 'donkey')

    assert_equal 4, count
  end

  test 'keyword count is case insensitive' do
    profile = FactoryBot.create(:expert).profile
    profile.update!(cv_text: 'Apple orange APPLE')

    count = profile.keyword_count_for('apple')

    assert_equal 2, count
  end

  test 'keyword count respects word boundaries' do
    profile = FactoryBot.create(:expert).profile
    profile.update!(cv_text: 'apple pineapple')

    count = profile.keyword_count_for('apple')

    assert_equal 1, count
  end

  test 'keyword count is zero' do
    profile = FactoryBot.create(:expert).profile
    profile.update!(cv_text: 'apple orange apple')

    count = profile.keyword_count_for('horse')

    assert_equal 0, count
  end

  test 'keyword count is nil without CV text' do
    profile = FactoryBot.create(:expert).profile
    profile.update!(cv_text: nil)

    assert_nil profile.keyword_count_for('jackalope')
  end

  test 'clear suspicious flag when a CV is attached' do
    expert = FactoryBot.create(:expert)
    expert.update!(marked_suspicious_at: 1.week.ago, marked_suspicious_reason: 'Shady!')
    profile = expert.profile

    cv = load_test_file('cv.pdf')
    assert_enqueued_with(job: Users::TrustUserJob, args: [profile.expert]) do
      profile.cv.attach(io: cv, filename: 'cv.pdf')
    end
  end

  test 'formatted expertise' do
    profile = FactoryBot.create(:expert).profile
    expertises = FactoryBot.create_list(:expertise, 2)
    profile.expertises = expertises

    expected = expertises.map(&:name).join(', ')

    assert_equal expected, profile.formatted_expertise
  end

  test 'cv is stale' do
    profile = FactoryBot.create(:profile, :with_cv)
    cv_last_updated_at = (Profile::CV_STALE_THRESHOLD_DAYS.days + 1.day).ago
    profile.cv.update!(created_at: cv_last_updated_at)

    assert profile.cv_stale?, "should be stale after #{Profile::CV_STALE_THRESHOLD_DAYS} days"
  end

  test 'cv is not stale' do
    profile = FactoryBot.create(:profile, :with_cv)
    cv_last_updated_at = (Profile::CV_STALE_THRESHOLD_DAYS.days - 1.day).ago
    profile.cv.update!(created_at: cv_last_updated_at)

    assert_not profile.cv_stale?, 'should not be stale'
  end

  test 'coerce nil other_expertise to empty string' do
    user = FactoryBot.create(:user)
    profile = Profile.create!(expert: user, other_expertise: nil)

    assert_equal '', profile.other_expertise
  end

  private

  def complete_profile
    expert = FactoryBot.create(:expert, :complete_profile)
    @complete_profile ||= expert.profile
  end

  def incomplete_profile
    expert = FactoryBot.create(:expert)
    @incomplete_profile ||= expert.profile
  end

  def retired
    @retired ||= FactoryBot.create(:expert, :retired).profile
  end
end

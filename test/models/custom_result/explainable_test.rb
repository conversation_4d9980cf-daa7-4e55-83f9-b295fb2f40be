# frozen_string_literal: true

require 'test_helper'

class CustomResult::ExplainableTest < ActiveSupport::TestCase
  test 'explanations? is true when question allows explanations and has answers with explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: true)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission, explanation: 'This is my explanation')

    assert result_definition.explanations?,
           'should return true when question allows explanations and has answers with explanations'
  end

  test 'explanations? is false when question does not allow explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: false)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission, explanation: 'This is my explanation')

    assert_not result_definition.explanations?, 'should return false when question does not allow explanations'
  end

  test 'explanations? is false when question allows explanations but has no answers with explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: true)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission)

    assert_not result_definition.explanations?, 'should return false when no answers have explanations'
  end

  test 'explanations? is false when there is no associated question' do
    survey = create(:survey)
    result_definition = create(:result_definition, survey:, question: nil)

    assert_not result_definition.explanations?, 'should return false when result definition has no question'
  end

  test 'explanations? is false when question allows explanations but has only empty explanations' do
    survey = create(:survey)
    question = create(:question, survey:, allow_answer_explanation: true)
    result_definition = create(:result_definition, survey:, question:)

    submission = create(:submission, question_group: survey)
    create(:answer, question:, answer_group: submission, explanation: '')

    assert_not result_definition.explanations?, 'should return false when explanations are empty'
  end
end

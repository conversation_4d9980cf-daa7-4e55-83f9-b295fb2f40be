# frozen_string_literal: true

require 'test_helper'

module Admin
  class ExpertSearchQueryTest < ActiveSupport::TestCase
    test 'empty search' do
      build_search!
      build_expected_query!
      assert_valid_query!
    end

    test 'must match on any word' do
      keywords = Faker::Lorem.words.join(' ')

      build_search!(
        search_terms_attributes: {
          '0': { keywords:, condition: 'must', match_type: 'any' }
        }
      )
      build_expected_query!(
        highlight: true,
        must: [
          {
            multi_match: {
              query: keywords,
              fields: %i[cv email expertise.other full_name^2]
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'must match on phrase' do
      keywords = Faker::Lorem.words.join(' ')

      build_search!(
        search_terms_attributes: {
          '0': { keywords:, condition: 'must', match_type: 'phrase' }
        }
      )

      build_expected_query!(
        highlight: true,
        must: [
          {
            multi_match: {
              fields: %i[cv expertise.other],
              query: keywords,
              type: 'phrase'
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'should match on any word' do
      keywords = Faker::Lorem.words.join(' ')

      build_search!(
        search_terms_attributes: {
          '0': { keywords:, condition: 'should' }
        }
      )
      build_expected_query!(
        highlight: true,
        should: [
          {
            multi_match: {
              query: keywords,
              fields: %i[cv email expertise.other full_name^2]
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'must not match on any word' do
      keywords = Faker::Lorem.words.join(' ')

      build_search!(
        search_terms_attributes: {
          '0': { keywords:, condition: 'must_not' }
        }
      )
      build_expected_query!(
        must_not: [
          {
            multi_match: {
              query: keywords,
              fields: %i[cv email expertise.other full_name]
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'filter by min YOE' do
      yoe = Faker::Number.between(from: 1, to: 60)

      build_search!(min_experience: yoe)
      build_expected_query!(
        must: [
          { match_all: {} },
          {
            range: {
              'experience.total_years' => { gte: yoe }
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'filter by min pubs' do
      pubs = Faker::Number.between(from: 1, to: 500)

      build_search!(min_publications: pubs)
      build_expected_query!(
        must: [
          { match_all: {} },
          {
            range: {
              'publications.count' => { gte: pubs }
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'filter by min first and last count' do
      first_last = Faker::Number.between(from: 1, to: 500)

      build_search!(min_first_last_publications: first_last)
      build_expected_query!(
        must: [
          { match_all: {} },
          {
            range: {
              'publications.first_last_count' => { gte: first_last }
            }
          }
        ]
      )
      assert_valid_query!
    end

    test 'include required expertise' do
      expertise_ids = %w[10 13]

      build_search!(required_expertise_ids: expertise_ids)

      terms = expertise_ids.map do |id|
        { term: { 'expertise.ids': id.to_i } }
      end
      build_expected_query!(filter: terms)

      assert_valid_query!
    end

    test 'include optional expertise' do
      expertise_ids = %w[10 13]

      build_search!(optional_expertise_ids: expertise_ids)

      terms = [{ terms: { 'expertise.ids': expertise_ids.map(&:to_i) } }]
      build_expected_query!(should: terms)

      assert_valid_query!
    end

    test 'exclude expertise' do
      expertise_ids = %w[10 13]

      build_search!(excluded_expertise_ids: expertise_ids)

      terms = [{ terms: { 'expertise.ids': expertise_ids.map(&:to_i) } }]
      build_expected_query!(must_not: terms)

      assert_valid_query!
    end

    test 'verified only' do
      build_search!(verified_only: true)
      build_expected_query!(filter: [{ term: { verified: true } }])
      assert_valid_query!
    end

    test 'potential sponsors only' do
      build_search!(potential_sponsor: true)
      build_expected_query!(filter: [{ term: { potential_sponsor: true } }])
      assert_valid_query!
    end

    test 'include employment sectors' do
      sector_ids = %w[3 6]

      build_search!(sector_filter_type: 'include', sector_ids:)
      build_expected_query!(
        filter: [
          { terms: { 'experience.current_sector_id': sector_ids.map(&:to_i) } }
        ]
      )
      assert_valid_query!
    end

    test 'exclude employment sectors' do
      sector_ids = %w[5 9]

      build_search!(sector_filter_type: 'exclude', sector_ids:)
      build_expected_query!(
        must_not: [
          { terms: { 'experience.current_sector_id': sector_ids.map(&:to_i) } }
        ]
      )
      assert_valid_query!
    end

    test 'limit degrees' do
      degree_type_ids = %w[4 5]

      build_search!(degree_type_ids:)
      build_expected_query!(
        filter: [
          { terms: { degree_type_ids: degree_type_ids.map(&:to_i) } }
        ]
      )
      assert_valid_query!
    end

    test 'limit regions' do
      region_ids = %w[4 5]

      build_search!(region_ids:)
      build_expected_query!(
        filter: [
          { terms: { 'location.region_id': region_ids.map(&:to_i) } }
        ]
      )
      assert_valid_query!
    end

    test 'within SciPi' do
      scipi = FactoryBot.create(:scipi)
      gid = scipi.to_global_id

      build_search!(product_gid: gid)
      build_expected_query!(
        filter: [{ term: { 'participation.scipi_ids': scipi.id } }]
      )
      assert_valid_query!
    end

    private

    def assert_valid_query!
      assert_equal @expected, @query.to_hash
    end

    def build_expected_query!(must: [{ match_all: {} }],
                              must_not: nil,
                              highlight: false,
                              should: nil,
                              filter: nil)
      @expected = {
        from: 1,
        highlight: highlight ? highlight_block : nil,
        query: {
          bool: {
            must:,
            must_not:,
            should:,
            filter: filter.presence || nil
          }.compact
        },
        size: 20
      }.compact
    end

    def build_search!(**)
      search = ExpertSearch.new(**)
      @query = ExpertSearchQuery.new(search)
    end

    def highlight_block
      {
        fields: {
          cv: { number_of_fragments: 20 }
        },
        pre_tags: ['<mark class="cv-highlight">'],
        post_tags: ['</mark>']
      }
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class PaymentExportTest < ActiveSupport::TestCase
      test 'to_csv' do
        # This code assumes an invite-only survey since it fetches users from
        # the users association. This seems like a safe assumption for now.
        branding = FactoryBot.create(:survey_branding, :scipi)
        survey = FactoryBot.create(:survey,
                                   :invite_only,
                                   branding:,
                                   engagement_info_attributes: { default_pay_rate: BigDecimal('1000.0') })
        expert = FactoryBot.create(:expert, :complete_profile)
        survey.panelists.create!(expert:)

        csv = CSV.generate(headers: true, force_quotes: true) do |row|
          row << [
            'email',
            'first_name',
            'last_name',
            'Billing address: Country',
            'Vendor',
            'Amount',
            'Due Date',
            'Account',
            'Memo',
            'Description',
            'Customer'
          ]

          row << [
            expert.email,
            expert.first_name,
            expert.last_name,
            expert.country.two_letter_code,
            expert.full_name,
            survey.default_pay_rate,
            1.week.from_now.strftime('%m/%d/%Y'),
            'Subcontractors:Projects',
            "SP#{survey.id}",
            "SP#{survey.id} Expert Services",
            ''
          ]
        end

        export = PaymentExport.new(scipi: survey, expert_ids: [expert.id])

        assert_equal "﻿#{csv}", export.to_csv
      end

      test 'filename' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        survey = FactoryBot.create(:survey, branding:)

        export = PaymentExport.new(scipi: survey, expert_ids: [])

        assert_equal "SciPi ##{survey.id} QuickBooks Payment File.csv", export.filename
      end
    end
  end
end

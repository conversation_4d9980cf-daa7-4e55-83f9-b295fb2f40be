# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class ParticipantExportTest < ActiveSupport::TestCase
      test 'to_csv' do
        survey = FactoryBot.create(:survey)
        expert = users(:expert)
        survey.submissions.create!(submitter: expert)

        csv = CSV.generate(headers: true, force_quotes: true) do |row|
          row << ['ID', 'Email', 'Full Name']
          row << [expert.id, expert.email, expert.full_name]
        end

        export = ParticipantExport.new(survey:, expert_ids: [expert.id])

        assert_equal "﻿#{csv}", export.to_csv
      end

      test 'filename' do
        survey = FactoryBot.create(:survey)

        export = ParticipantExport.new(survey:, expert_ids: [])

        assert_equal "#{survey.display_name} Participants.csv", export.filename
      end
    end
  end
end

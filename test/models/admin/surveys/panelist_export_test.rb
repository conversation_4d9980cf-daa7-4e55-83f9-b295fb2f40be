# frozen_string_literal: true

require 'test_helper'

module Admin
  module Surveys
    class PanelistExportTest < ActiveSupport::TestCase
      test 'to_csv' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        scipi = FactoryBot.create(:scipi, :invite_only, branding:)
        expert = FactoryBot.create(:expert)
        panelist = scipi.panelists.create!(expert:)

        csv = CSV.generate(headers: true, force_quotes: true) do |row|
          row << [
            'Name',
            'Country',
            'Affiliation',
            'Title',
            'Advanced Degree',
            'Years Post-Degree',
            'Years Experience',
            'Publications',
            'Application Statement'
          ]

          row << [
            panelist.full_name,
            panelist.country_name,
            panelist.current_employer, # needs special handling for retired
            panelist.title,
            panelist.degrees.doctorate.any? ? panelist.degrees.doctorate.map(&:name).join(', ') : '',
            panelist.years_since_last_degree,
            panelist.total_work_experience,
            panelist.publications_count,
            panelist&.applicant_statement&.to_plain_text
          ]
        end

        export = PanelistExport.new(scipi:)

        assert_equal "﻿#{csv}", export.to_csv
      end

      test 'filename' do
        branding = FactoryBot.create(:survey_branding, :scipi)
        survey = FactoryBot.create(:survey, branding:)

        export = PanelistExport.new(scipi: survey)

        assert_equal "SciPi ##{survey.id} Panel Summary.csv", export.filename
      end
    end
  end
end

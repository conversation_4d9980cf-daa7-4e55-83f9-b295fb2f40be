# frozen_string_literal: true

require 'test_helper'

module Admin
  module Abilities
    class ExpertAbilityTest < ActiveSupport::TestCase
      test 'expert admins can :index profile' do
        role = FactoryBot.create(:role, :db_admin)
        user = FactoryBot.create(:user, roles: [role])
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.can?(:index, expert.profile), 'should be able to :show any profile'
      end

      test 'expert admins can :show any profile' do
        role = FactoryBot.create(:role, :db_admin)
        user = FactoryBot.create(:user, roles: [role])
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.can?(:show, expert.profile), 'should be able to :show any profile'
      end

      test 'scipi admins can :show profiles for experts they manage' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        scipi = FactoryBot.create(:scipi, :invite_only)
        scipi.owners << user
        expert = FactoryBot.create(:expert)
        FactoryBot.create(:applicant, survey: scipi, expert:)

        ability = ExpertAbility.new(user)

        assert ability.can?(:show, expert.profile), 'should be able to :show profiles for scipi applicants'
      end

      test 'scipi admins cannot :show profiles for experts not on a scipi they manage' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.cannot?(:show, expert.profile),
               'should not be able to :show profiles for experts not on a scipi they manage'
      end

      test 'db admins can :show_notes notes on any expert' do
        user = FactoryBot.create(:db_admin)
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.can?(:show_notes, expert.profile), 'should be able to :show_notes notes on any profile'
      end

      test 'db admins can :create_note on any expert' do
        user = FactoryBot.create(:db_admin)
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.can?(:create_note, expert.profile), 'should be able to :create_note notes on any profile'
      end

      test 'scipi admins can :show_notes notes on experts they manage' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        scipi = FactoryBot.create(:scipi, :invite_only)
        scipi.owners << user
        expert = FactoryBot.create(:expert)
        FactoryBot.create(:applicant, survey: scipi, expert:)

        ability = ExpertAbility.new(user)

        assert ability.can?(:show_notes, expert.profile),
               'should be able to :show_notes notes profiles for scipi applicants'
      end

      test 'scipi admins cannot :show_notes notes profiles for experts not on a scipi they manage' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.cannot?(:show_notes, expert.profile),
               'should not be able to :show_notes notes profiles for experts not on a scipi they manage'
      end

      test 'scipi admins can :create_note on experts they manage' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        scipi = FactoryBot.create(:scipi, :invite_only)
        scipi.owners << user
        expert = FactoryBot.create(:expert)
        FactoryBot.create(:applicant, survey: scipi, expert:)

        ability = ExpertAbility.new(user)

        assert ability.can?(:create_note, expert.profile),
               'should be able to :create_note profiles for scipi applicants'
      end

      test 'scipi admins cannot :create_note profiles for experts not on a scipi they manage' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.cannot?(:create_note, expert.profile),
               'should not be able to :create_note profiles for experts not on a scipi they manage'
      end

      test 'scipi admins cannot :index profiles' do
        role = FactoryBot.create(:role, :scipi_admin)
        user = FactoryBot.create(:user, roles: [role])
        expert = FactoryBot.create(:expert)

        ability = ExpertAbility.new(user)

        assert ability.cannot?(:index, expert.profile), 'should not be able to :index profiles'
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Admin
  module Abilities
    class MessageAbilityTest < ActiveSupport::TestCase
      test 'a scipi owner can resolve a message' do
        scipi = FactoryBot.create(:scipi)
        owner = scipi.created_by
        message = FactoryBot.create(:message, survey: scipi)
        ability = MessageAbility.new(owner)

        assert ability.can?(:resolve, message), 'should be able to resolve messages'
      end

      test 'non-owners cannot resolve a message' do
        scipi = FactoryBot.create(:scipi)
        user = FactoryBot.create(:scipi_admin)
        message = FactoryBot.create(:message, survey: scipi)
        ability = MessageAbility.new(user)

        assert_not ability.can?(:resolve, message), 'should not be able to resolve messages'
      end

      test 'a scipi owner can unresolve a message' do
        scipi = FactoryBot.create(:scipi)
        owner = scipi.created_by
        message = FactoryBot.create(:message, survey: scipi)
        ability = MessageAbility.new(owner)

        assert ability.can?(:resolve, message), 'should be able to resolve messages'
      end

      test 'non-owners cannot unresolve a message' do
        scipi = FactoryBot.create(:scipi)
        user = FactoryBot.create(:scipi_admin)
        message = FactoryBot.create(:message, survey: scipi)
        ability = MessageAbility.new(user)

        assert_not ability.can?(:unresolve, message), 'should not be able to unresolve messages'
      end

      test 'a scipi owner can bulk resolve messages' do
        scipi = FactoryBot.create(:scipi)
        owner = scipi.created_by
        ability = MessageAbility.new(owner)

        assert ability.can?(:bulk_resolve, scipi), 'should be able to bulk resolve messages'
      end

      test 'non-owners cannot bulk resolve messages' do
        scipi = FactoryBot.create(:scipi)
        user = FactoryBot.create(:scipi_admin)
        ability = MessageAbility.new(user)

        assert_not ability.can?(:bulk_resolve, scipi), 'should not be able to bulk resolve messages'
      end

      test 'a scipi owner can bulk unresolve messages' do
        scipi = FactoryBot.create(:scipi)
        owner = scipi.created_by
        ability = MessageAbility.new(owner)

        assert ability.can?(:bulk_unresolve, scipi), 'should be able to bulk unresolve messages'
      end

      test 'non-owners cannot bulk unresolve messages' do
        scipi = FactoryBot.create(:scipi)
        user = FactoryBot.create(:scipi_admin)
        ability = MessageAbility.new(user)

        assert_not ability.can?(:bulk_unresolve, scipi), 'should not be able to bulk unresolve messages'
      end
    end
  end
end

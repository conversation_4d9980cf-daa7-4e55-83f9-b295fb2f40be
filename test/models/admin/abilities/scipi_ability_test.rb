# frozen_string_literal: true

require 'test_helper'

module Admin
  module Abilities
    class ScipiAbilityTest < ActiveSupport::TestCase
      test 'cannot :view_selection unless assigned as an auditor' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:view_applicants, scipi), 'should not be able to :view_applicants unless assigned'
      end

      test 'cannot :view_selection without an accepted confidentiality agreement' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open)
        scipi.auditor_assignments.create!(user:)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:view_applicants, scipi),
               'should not be able to :view_applicants without an accepted confidentiality agreement'
      end

      test 'can :view_selection when selection is open' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open, :selection_open)
        scipi.auditor_assignments.create!(user:)
        scipi.confidentiality_agreements.create!(user:, accepted_at: Time.current)

        ability = ScipiAbility.new(user)

        assert ability.can?(:view_applicants, scipi), 'should be able to :view_applicants when selection is open'
      end

      test 'cannot :audit unless assigned as an auditor' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:audit, applicant), 'should not be able to :audit unless assigned'
      end

      test 'can :audit' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open)
        scipi.auditor_assignments.create!(user:)
        scipi.confidentiality_agreements.create!(user:, accepted_at: Time.current)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        ability = ScipiAbility.new(user)

        assert ability.can?(:audit, applicant), 'should be able to :audit'
      end

      test 'cannot :audit without an accepted confidentiality agreement' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open)
        scipi.auditor_assignments.create!(user:)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:audit, applicant),
               'should not be able to :audit without an accepted confidentiality agreement'
      end

      test 'cannot :audit when selection is closed' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_closed)
        scipi.auditor_assignments.create!(user:)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:audit, applicant), 'should not be able to :audit when selection is closed'
      end

      test 'cannot :show Profile unless assigned as an auditor' do
        scipi = FactoryBot.create(:scipi, :selection_open)
        user = FactoryBot.create(:user)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:show, expert.profile), 'should not be able to :show Profile unless assigned'
      end

      test 'can :show Profile' do
        scipi = FactoryBot.create(:scipi, :selection_open)
        user = FactoryBot.create(:user)
        scipi.auditor_assignments.create!(user:)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)
        scipi.confidentiality_agreements.create!(user:, accepted_at: Time.current)

        ability = ScipiAbility.new(user)

        assert ability.can?(:show, expert.profile), 'should be able to :show Profile'
      end

      test 'can :show Profile without an accepted confidentiality agreement' do
        scipi = FactoryBot.create(:scipi, :selection_open)
        user = FactoryBot.create(:user)
        scipi.auditor_assignments.create!(user:)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:show, expert.profile),
               'should not :show Profile without an accepted confidentiality agreement'
      end

      test 'cannot :show Profile after selection is closed' do
        scipi = FactoryBot.create(:scipi, :selection_closed)
        user = FactoryBot.create(:user)
        scipi.auditor_assignments.create!(user:)
        expert = FactoryBot.create(:expert)
        scipi.applicants.create!(user: expert)

        ability = ScipiAbility.new(user)

        assert ability.cannot?(:show, expert.profile), 'should not be able to :show Profile after selection is closed'
      end

      test 'cannot :view_scaled_audit_score' do
        user = FactoryBot.create(:user)
        scipi = FactoryBot.create(:scipi, :selection_open)
        scipi.auditor_assignments.create!(user:)
        expert = FactoryBot.create(:expert)
        applicant = scipi.applicants.create!(expert:)

        ability = ScipiAbility.new(user)

        assert_not ability.can?(:view_scaled_audit_score, applicant), 'should not be able to :view_scaled_audit_score'
      end
    end
  end
end

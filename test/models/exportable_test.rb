# frozen_string_literal: true

require 'test_helper'

class ExportableTest < ActiveSupport::TestCase
  class MyModel
    include ActiveModel::Model
    include Exportable

    attr_accessor :first_name, :last_name

    def full_name
      "#{first_name} #{last_name}"
    end
  end

  test 'to_csv_row' do
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    attrs_or_methods = %i[first_name last_name full_name]

    user = MyModel.new(first_name:, last_name:)
    full_name = user.full_name

    row_data = user.to_csv_row(attrs_or_methods)

    assert_equal [first_name, last_name, full_name], row_data
  end

  test 'to_csv_row_with_missing_method' do
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name
    attrs_or_methods = %i[first_name last_name does_not_exist]

    user = MyModel.new(first_name:, last_name:)

    assert_raises NoMethodError do
      user.to_csv_row(attrs_or_methods)
    end
  end
end

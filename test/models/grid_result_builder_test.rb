# frozen_string_literal: true

require 'test_helper'

class GridResultBuilderTest < ActiveSupport::TestCase
  setup do
    @comments_count = 5
    @first_cell = 'foobar'
    @row_headers = %w[one two]
    @column_headers = %w[three four]

    @survey = FactoryBot.create(:survey)

    @submission = FactoryBot.create(
      :submission,
      :final,
      survey: @survey
    )
  end

  test 'build default radio grid result' do
    radio_question = FactoryBot.create(
      :grid_question,
      comments_count: @comments_count,
      survey: @survey
    )
    FactoryBot.create(
      :grid_structure,
      :radio,
      column_headers: @column_headers,
      first_cell_text: @first_cell,
      row_headers: @row_headers,
      question: radio_question
    )
    FactoryBot.create(
      :answer,
      question: radio_question,
      submission: @submission,
      answer_text_on_1st_row: '0-0',
      answer_text_on_2nd_row: '1-1'
    )

    builder = GridResultBuilder.new
    result = builder.build_grid_result(radio_question)

    grid_html = "<table class='table table-bordered'>"
    grid_html += '<thead>'
    grid_html += "<tr><th>#{@first_cell}</th><th>#{@column_headers.first}</th><th>#{@column_headers.last}</th><th><span class='total'>Total</span></th></tr>"
    grid_html += '</thead>'
    grid_html += "<tr><td class='grid-row-header'>#{@row_headers.first}</td>"
    grid_html += "<td><span class='percentage'>100.00%</span><span class='count'>1</span></td>"
    grid_html += "<td><span class='percentage'>0.00%</span><span class='count'>0</span></td>"
    grid_html += "<td><span class='total'>1</span></td></tr>"
    grid_html += "<tr><td class='grid-row-header'>#{@row_headers.last}</td>"
    grid_html += "<td><span class='percentage'>0.00%</span><span class='count'>0</span></td>"
    grid_html += "<td><span class='percentage'>100.00%</span><span class='count'>1</span></td>"
    grid_html += "<td><span class='total'>1</span></td></tr>"
    grid_html += '</table>'

    assert_equal @comments_count, result.comments_count
    assert_nil result.custom_result_id, 'should not be set for a default result'
    assert_equal grid_html, result.grid_results_html
  end

  test 'build default checkbox grid result' do
    checkbox_question = FactoryBot.create(
      :grid_question,
      comments_count: @comments_count,
      survey: @survey
    )
    FactoryBot.create(
      :grid_structure,
      :checkbox,
      column_headers: @column_headers,
      first_cell_text: @first_cell,
      row_headers: @row_headers,
      question: checkbox_question
    )
    FactoryBot.create(
      :answer,
      question: checkbox_question,
      submission: @submission,
      answer_text_on_1st_row: "1\r\n1",
      answer_text_on_2nd_row: "1\r\n1"
    )

    builder = GridResultBuilder.new
    result = builder.build_grid_result(checkbox_question)

    grid_html = "<table class='table table-bordered'>"
    grid_html += '<thead>'
    grid_html += "<tr><th>#{@first_cell}</th><th>#{@column_headers.first}</th><th>#{@column_headers.last}</th><th><span class='total'>Total</span></th></tr>"
    grid_html += '</thead>'
    grid_html += "<tr><td class='grid-row-header'>#{@row_headers.first}</td>"
    grid_html += "<td><span class='percentage'>100.00%</span><span class='count'>1</span></td>"
    grid_html += "<td><span class='percentage'>100.00%</span><span class='count'>1</span></td>"
    grid_html += "<td><span class='total'>1</span></td></tr>"
    grid_html += "<tr><td class='grid-row-header'>#{@row_headers.last}</td>"
    grid_html += "<td><span class='percentage'>100.00%</span><span class='count'>1</span></td>"
    grid_html += "<td><span class='percentage'>100.00%</span><span class='count'>1</span></td>"
    grid_html += "<td><span class='total'>1</span></td></tr>"
    grid_html += '</table>'

    assert_equal @comments_count, result.comments_count
    assert_nil result.custom_result_id, 'should not be set for a default result'
    assert_equal grid_html, result.grid_results_html
  end

  test 'build default select grid result' do
    question = FactoryBot.create(
      :grid_question,
      comments_count: @comments_count,
      survey: @survey
    )
    FactoryBot.create(
      :grid_structure,
      :select,
      column_headers: @column_headers,
      first_cell_text: @first_cell,
      row_headers: @row_headers,
      question:
    )
    FactoryBot.create(
      :answer,
      question:,
      submission: @submission,
      answer_text_on_1st_row: "label1\r\nlabel2",
      answer_text_on_2nd_row: "label2\r\nlabel1"
    )

    # Add a second submission so there are two groups in some cells where
    # the answers are different.
    another_submission = FactoryBot.create(
      :submission,
      :final,
      survey: @survey
    )
    FactoryBot.create(
      :answer,
      question:,
      submission: another_submission,
      answer_text_on_1st_row: "label2\r\nlabel2",
      answer_text_on_2nd_row: "label1\r\n"
    )

    builder = GridResultBuilder.new
    result = builder.build_grid_result(question)

    grid_html = "<table class='table table-bordered'>"
    grid_html += '<thead>'
    grid_html += "<tr><th>#{@first_cell}</th><th>#{@column_headers.first} (n)</th><th>#{@column_headers.last} (n)</th><th><span class='total'>Total</span></th></tr>"
    grid_html += '</thead>'
    grid_html += "<tr><td class='grid-row-header'>#{@row_headers.first}</td>"
    grid_html += "<td><div class='rating text-start'>label1 (1)</div><div class='rating text-start'>label2 (1)</div></td>"
    grid_html += "<td><div class='rating text-start text-gray-light'>label1</div><div class='rating text-start'>label2 (2)</div></td>"
    grid_html += "<td><span class='total'>4</span></td></tr>"
    grid_html += "<tr><td class='grid-row-header'>#{@row_headers.last}</td>"
    grid_html += "<td><div class='rating text-start'>label1 (1)</div><div class='rating text-start'>label2 (1)</div></td>"
    grid_html += "<td><div class='rating text-start'>label1 (1)</div><div class='rating text-start text-gray-light'>label2</div></td>"
    grid_html += "<td><span class='total'>3</span></td></tr>"
    grid_html += '</table>'

    assert_equal @comments_count, result.comments_count
    assert_nil result.custom_result_id, 'should not be set for a default result'
    assert_equal grid_html, result.grid_results_html
  end
end

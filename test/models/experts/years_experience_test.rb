# frozen_string_literal: true

require 'test_helper'

module Experts
  class ExperienceTest < ActiveSupport::TestCase
    test 'no years experience' do
      assert_nil YearsExperience.new
    end

    test 'total years experience is years_employment' do
      experience = YearsExperience.new(years_employment: 40, first_doctorate_year: 30.years.ago.year)

      assert_equal 40, experience
    end

    test 'total years experience is years since first doctorate' do
      experience = YearsExperience.new(years_employment: 50, first_doctorate_year: 60.years.ago.year)

      assert_equal 60, experience
    end

    test 'max years experience' do
      experience = YearsExperience.new(years_employment: 90, first_doctorate_year: 70.years.ago.year)

      assert_equal 80, experience
    end

    test 'only years employment' do
      experience = YearsExperience.new(years_employment: 20)

      assert_equal 20, experience
    end

    test 'only education' do
      experience = YearsExperience.new(first_doctorate_year: 30.years.ago.year)

      assert_equal 30, experience
    end
  end
end

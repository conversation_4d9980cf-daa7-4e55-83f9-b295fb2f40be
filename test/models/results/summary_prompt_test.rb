# frozen_string_literal: true

require 'test_helper'

module Results
  class SummaryPromptTest < ActiveSupport::TestCase
    test 'default_prompt for multiple_choice_responses includes formatted answers' do
      survey = FactoryBot.create(:survey)
      question = FactoryBot.create(:radio, survey:)
      answer_choice_labels = question.answer_choices.pluck(:label)
      submitter = FactoryBot.create(:expert)
      submission = FactoryBot.create(:submission, :final, survey:, submitter:)
      selected_choice = SelectedChoice.new(answer_choice: question.answer_choices[0])
      submission.answers.create!(question:, selected_choices: [selected_choice])
      result_definition = FactoryBot.create(
        :result_definition,
        :question,
        survey:,
        question:,
        result_type: FactoryBot.create(:result_type, :multiple_choice_responses)
      )

      summary_prompt = SummaryPrompt.new(result: result_definition)
      generated_prompt = summary_prompt.default_prompt

      assert_includes generated_prompt, result_definition.title
      assert_includes generated_prompt, "Expert: #{submitter.display_id}"
      assert_includes generated_prompt, "Answer: #{answer_choice_labels[0]}"
    end

    test 'default_prompt returns error message for non-summarizable result' do
      result_definition = FactoryBot.create(:result_definition, :non_summarizable)

      summary_prompt = SummaryPrompt.new(result: result_definition)
      generated_prompt = summary_prompt.default_prompt

      assert_equal 'Return an Error', generated_prompt
    end
  end
end

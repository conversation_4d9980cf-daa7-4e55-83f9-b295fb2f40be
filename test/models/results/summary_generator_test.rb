# frozen_string_literal: true

require 'test_helper'

module Results
  class SummaryGeneratorTest < ActiveSupport::TestCase
    test 'preview returns formatted summary for summarizable result' do
      result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses)

      Claude.stubs(:get_response).returns('This is a test summary')

      generator = SummaryGenerator.new(result_definition)
      summary = generator.preview

      assert_includes summary, 'This is a test summary'
      assert_includes summary, '<i>Summary Generated by AI</i>'
    end

    test 'preview with custom prompt uses provided prompt' do
      result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses)

      custom_prompt = 'Custom test prompt'
      Claude.expects(:get_response).with(content: custom_prompt).returns('Custom summary')

      generator = SummaryGenerator.new(result_definition)
      summary = generator.preview(prompt: custom_prompt)

      assert_includes summary, 'Custom summary'
    end

    test 'preview raises error for non-summarizable result' do
      result_definition = FactoryBot.create(:result_definition, :non_summarizable)

      generator = SummaryGenerator.new(result_definition)

      assert_raises StandardError do
        generator.preview
      end
    end

    test 'default_prompt delegates to SummaryPrompt' do
      result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses)

      generator = SummaryGenerator.new(result_definition)

      # Should not raise error and should return prompt string
      prompt = generator.default_prompt
      assert_kind_of String, prompt
      assert prompt.length.positive?
    end

    test 'format_response adds AI label to summary' do
      result_definition = FactoryBot.create(:result_definition, :question, :multiple_choice_responses)
      generator = SummaryGenerator.new(result_definition)

      # Test private method via preview (since format_response is private)
      Claude.stubs(:get_response).returns('Test summary content')

      summary = generator.preview

      assert_includes summary, 'Test summary content'
      assert_includes summary, "<div class='ai-summary-label'><i>Summary Generated by AI</i></div>"
    end
  end
end

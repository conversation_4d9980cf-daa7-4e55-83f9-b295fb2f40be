# frozen_string_literal: true

require 'test_helper'

class Invite::ContractTest < ActiveSupport::TestCase
  test 'not requestable to sign' do
    not_requestable = Invite.new(sign_now_contract_id: nil)

    assert_not not_requestable.contract_requestable_to_sign?
    assert_not not_requestable.contract_awaiting_embed_link?
    assert_not not_requestable.contract_ready_to_sign?
    assert_not not_requestable.contract_request_expired?
    assert_not not_requestable.contract_signed?
  end

  test 'requestable to sign' do
    requestable = Invite.new(sign_now_contract_id: 'dummy_document_id')

    assert requestable.contract_requestable_to_sign?

    assert_not requestable.contract_awaiting_embed_link?
    assert_not requestable.contract_ready_to_sign?
    assert_not requestable.contract_request_expired?
    assert_not requestable.contract_signed?
  end

  test 'awaiting embed link' do
    awaiting_link = Invite.new(
      sign_now_contract_id: 'dummy_document_id',
      contract_embed_link_requested_at: Time.current,
      contract_embed_link: nil,
      contract_signed_at: nil
    )

    assert awaiting_link.contract_awaiting_embed_link?

    assert_not awaiting_link.contract_requestable_to_sign?
    assert_not awaiting_link.contract_ready_to_sign?
    assert_not awaiting_link.contract_request_expired?
    assert_not awaiting_link.contract_signed?
  end

  test 'ready to sign' do
    ready_to_sign = Invite.new(
      sign_now_contract_id: 'dummy_document_id',
      contract_embed_link_requested_at: Time.current,
      contract_embed_link: 'dummy_link',
      contract_signed_at: nil
    )

    assert ready_to_sign.contract_ready_to_sign?

    assert_not ready_to_sign.contract_requestable_to_sign?
    assert_not ready_to_sign.contract_awaiting_embed_link?
    assert_not ready_to_sign.contract_request_expired?
    assert_not ready_to_sign.contract_signed?
  end

  test 'contract signed' do
    signed = Invite.new(
      contract_last_sent_at: Time.current,
      sign_now_contract_id: 'dummy_document_id',
      contract_embed_link_requested_at: Time.current,
      contract_embed_link: 'dummy_link',
      contract_signed_at: Time.current
    )

    assert signed.contract_signed?

    assert_not signed.contract_requestable_to_sign?
    assert_not signed.contract_awaiting_embed_link?
    assert_not signed.contract_ready_to_sign?
    assert_not signed.contract_request_expired?
  end

  test 'contract request expired' do
    expired = Invite.new(
      sign_now_contract_id: 'dummy_document_id',
      contract_embed_link_requested_at: 46.minutes.ago,
      contract_embed_link: 'dummy_link',
      contract_signed_at: nil
    )

    assert expired.contract_request_expired?
    assert expired.contract_requestable_to_sign?

    assert_not expired.contract_awaiting_embed_link?
    assert_not expired.contract_ready_to_sign?
    assert_not expired.contract_signed?
  end

  test 'no contract errors' do
    assert_not Invite.new.contract_error_occurred?
  end

  test 'contract errors' do
    panelist = Invite.new(contract_embed_last_error: 'Boom!', contract_embed_last_error_at: Time.current)

    assert panelist.contract_error_occurred?
  end

  test 'reset contract embed request error state' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    panelist = scipi.panelists.create!(
      expert:,
      contract_embed_last_error: 'Badness happened',
      contract_embed_last_error_at: Time.current,
      contract_embed_link_requested_at: Time.current
    )

    panelist.reset_contract_embed_request_error_state!

    assert_nil panelist.contract_embed_last_error
    assert_nil panelist.contract_embed_last_error_at
    assert_nil panelist.contract_embed_link_requested_at
  end

  test 'reset contract state' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    panelist = scipi.panelists.create!(
      expert:,
      contract_last_sent_at: Time.current,
      contract_signed_at: Time.current,
      sign_now_contract_id: 'abc123',
      contract_embed_link: 'https://example.com/abc123',
      contract_embed_link_requested_at: Time.current,
      compensatable: true,
      donatable: false,
      donation_organization: 'Some Org',
      name_entered: 'John Doe',
      email_entered: '<EMAIL>',
      date_signed: Date.current,
      sign_now_document: { foo: 'bar' },
      us_taxable: true,
      contract_preview_id: 'def456'
    )

    panelist.reset_contract_state!

    assert_nil panelist.contract_last_sent_at
    assert_nil panelist.contract_signed_at
    assert_nil panelist.sign_now_contract_id
    assert_nil panelist.contract_embed_link
    assert_nil panelist.contract_embed_link_requested_at
    assert_nil panelist.compensatable
    assert_nil panelist.donatable
    assert_nil panelist.donation_organization
    assert_nil panelist.name_entered
    assert_nil panelist.email_entered
    assert_nil panelist.date_signed
    assert_nil panelist.sign_now_document
    assert_nil panelist.contract_preview_id
    assert panelist.us_taxable
  end
end

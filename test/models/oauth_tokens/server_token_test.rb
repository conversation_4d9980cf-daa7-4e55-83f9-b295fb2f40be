# frozen_string_literal: true

require 'test_helper'

class OAuthTokens::ServerTokenTest < ActiveSupport::TestCase
  test 'access token for' do
    OAuthTokens::ServerToken.create!(resource_server_name: 'test', access_token: 'abc123', expires_at: 1.day.from_now)

    assert_equal 'abc123', OAuthTokens::ServerToken.access_token_for(resource_server_name: 'test')
  end

  test 'create token' do
    freeze_time do
      assert_difference 'OAuthTokens::ServerToken.count' do
        token = OAuthTokens::ServerToken.create_or_refresh_token!(resource_server_name: 'test', access_token: 'abc123', expires_at: 1.week.from_now)

        assert_equal 'test', token.resource_server_name
        assert_equal 'abc123', token.access_token
        assert_equal 1.week.from_now, token.expires_at
      end
    end
  end

  test 'refresh token' do
    _existing_token = OAuthTokens::ServerToken.create!(resource_server_name: 'test', access_token: 'abc123', expires_at: 1.day.from_now)

    freeze_time do
      assert_no_difference 'OAuthTokens::ServerToken.count' do
        token = OAuthTokens::ServerToken.create_or_refresh_token!(resource_server_name: 'test', access_token: 'def456', expires_at: 1.week.from_now)

        assert_equal 'test', token.resource_server_name
        assert_equal 'def456', token.access_token
        assert_equal 1.week.from_now, token.expires_at
      end
    end
  end
end

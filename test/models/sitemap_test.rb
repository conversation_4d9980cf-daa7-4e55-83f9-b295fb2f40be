# frozen_string_literal: true

require 'test_helper'

class SitemapTest < ActiveSupport::TestCase
  include Rails.application.routes.url_helpers

  setup do
    skip 'Sitemap tests are currently disabled due to AWS integration'

    # Clear the sitemap before each test
    FileUtils.rm_rf(Rails.public_path.join('sitemap.xml'))

    Rails.application.routes.default_url_options[:host] = 'localhost'
    Rails.application.routes.default_url_options[:port] = 3000

    SitemapGenerator::Sitemap.default_host = 'http://localhost:3000'
    SitemapGenerator::Sitemap.compress = false

    SitemapGenerator::Sitemap.reset!
  end

  test 'sitemap can be generated with custom configuration options' do
    load Rails.root.join('config/sitemap.rb')
    assert Rails.public_path.join('sitemap.xml').exist?, 'Sitemap was not generated'
  end

  test 'sitemap spot check contains expected URLs' do
    FactoryBot.create(:survey, :scipi, :published)
    FactoryBot.create(:scipoll, :closed, :published, :results_published, :results_public)
    create(:ping, :published)

    load Rails.root.join('config/sitemap.rb')

    sitemap_content = Rails.public_path.join('sitemap.xml').read

    assert_includes sitemap_content, root_url
    assert_includes sitemap_content, scipis_url
    assert_includes sitemap_content, scipolls_url
    assert_includes sitemap_content, pings_url

    assert_includes sitemap_content, scipi_url(QuestionGroup.scipis.sitemappable.first)
    assert_includes sitemap_content, scipoll_url(QuestionGroup.scipolls.sitemappable.first)
    assert_includes sitemap_content, ping_url(Ping.published.featureable.first)
  end
end

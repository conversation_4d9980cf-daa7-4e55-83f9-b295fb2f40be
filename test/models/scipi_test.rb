# frozen_string_literal: true

require 'test_helper'

class ScipiTest < ActiveSupport::TestCase
  test 'featured scipis orders recruiting first' do
    # Guarantee that we have at least one valid recruiting and non-recruiting survey in the collection
    branding = FactoryBot.create(:survey_branding, :scipi)
    FactoryBot.create(:scipi, :invite_only, :published, :recruitment_closed, branding:)
    FactoryBot.create(:scipi, :invite_only, :published, :recruitment_open, branding:)
    FactoryBot.create(:scipi, :invite_only, :published, :recruitment_closed, branding:)

    featured_scipis = QuestionGroup.featured_scipis

    assert featured_scipis.first.recruiting?
    assert_not featured_scipis[featured_scipis.count - 1].recruiting? # `.last` does NOT work here
  end

  test 'approved panelist can participate in a Scipi' do
    scipi = FactoryBot.create(:scipi, :invite_only, :open)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    assert_can_participate scipi, expert
  end

  test 'rejected applicant cannot participate in a Scipi' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    scipi.applicants.create!(user: expert, rejected_at: Time.zone.now)

    assert_cannot_participate scipi, expert
  end

  test 'draft scipis with applicants are not deleteable' do
    # this condition can occur between rounds
    scipi = FactoryBot.create(:scipi, :invite_only, :draft)
    expert = FactoryBot.create(:expert)
    scipi.applicants.create!(user: expert)

    assert_not scipi.deleteable?, 'scipis with submissions should not be deleteable'
  end

  test 'approved panelist is an approved participant' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)

    assert scipi.approved_participant?(expert), 'should be an approved participant'
  end

  test 'not an approved participant' do
    scipi = FactoryBot.create(:scipi, :invite_only)
    expert = FactoryBot.create(:expert)

    assert_not scipi.approved_participant?(expert), 'should not be an approved participant'
  end

  private

  def assert_can_participate(scipi, user)
    assert scipi.can_participate?(user), 'should be able to participate in this scipi'
  end

  def assert_cannot_participate(scipi, user)
    refute scipi.can_participate?(user), 'should not be able to participate in this scipi'
  end
end

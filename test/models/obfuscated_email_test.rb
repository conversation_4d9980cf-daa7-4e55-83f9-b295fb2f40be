# frozen_string_literal: true

require 'test_helper'

class ObfuscatedEmailTest < ActiveSupport::TestCase
  test 'general case' do
    assert_equal 'so*********@example.com', ObfuscatedEmail.new('<EMAIL>').to_s
  end

  test 'empty email' do
    assert_equal '', ObfuscatedEmail.new('').to_s
  end

  test 'nil email' do
    assert_equal '', ObfuscatedEmail.new(nil).to_s
  end

  test 'single character local part' do
    assert_equal '*@foo.com', ObfuscatedEmail.new('<EMAIL>').to_s
  end

  test 'two character local part' do
    assert_equal 'x*@bar.com', ObfuscatedEmail.new('<EMAIL>').to_s
  end

  test 'crazy email' do
    assert_equal '"f***********@example.com', ObfuscatedEmail.new('"foo@bar"@<EMAIL>').to_s
  end
end

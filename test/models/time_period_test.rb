# frozen_string_literal: true

require 'test_helper'

class TimePeriodTest < ActiveSupport::TestCase
  test 'start_at must be before end_at' do
    assert_raises(ArgumentError) do
      TimePeriod.new(start_at: 2.days.from_now, end_at: 1.day.from_now)
    end
  end

  test 'not started' do
    time_period = TimePeriod.new(start_at: 1.day.from_now, end_at: 2.days.from_now)

    assert_not time_period.in_progress?
    assert time_period.not_started?
    assert_not time_period.finished?
  end

  test 'in progress' do
    time_period = TimePeriod.new(start_at: 1.day.ago, end_at: 1.day.from_now)

    assert time_period.in_progress?
    assert_not time_period.not_started?
    assert_not time_period.finished?
  end

  test 'finished' do
    time_period = TimePeriod.new(start_at: 1.day.ago, end_at: 1.day.ago)

    assert_not time_period.in_progress?
    assert_not time_period.not_started?
    assert time_period.finished?
  end

  test 'duration' do
    start_at = Time.zone.now
    end_at = 2.days.after(start_at)

    time_period = TimePeriod.new(start_at:, end_at:)

    assert_equal 2.days, time_period.duration
  end

  test 'midpoint' do
    start_at = Time.zone.now
    end_at = 50.hours.after(start_at)

    time_period = TimePeriod.new(start_at:, end_at:)

    assert_equal start_at + 25.hours, time_period.midpoint
  end
end

# frozen_string_literal: true

require 'test_helper'

class AbilityTest < ActiveSupport::TestCase
  test 'guest users cannot view the dashboard' do
    role = FactoryBot.create(:role, :guest)
    user = role.users.create!

    ability = Ability.new(user)

    assert_not ability.can?(:view, :dashboard), 'should not be able to view the dashboard'
  end

  test 'registered users can view the dashboard' do
    user = FactoryBot.create(:user)

    ability = Ability.new(user)

    assert ability.can?(:view, :dashboard), 'should be able to view the dashboard'
  end
end

# frozen_string_literal: true

require 'test_helper'

module Communications
  class SubscriptionTest < ActiveSupport::TestCase
    test 'unsubscribe with token for' do
      subscribable = FactoryBot.create(:distribution_list)
      subscriber = FactoryBot.create(:user)
      subscription = subscribable.subscriptions.create!(subscriber:)

      token = subscription.unsubscribe_token

      assert_difference -> { subscribable.subscribed_subscribers.count }, -1 do
        Subscription.unsubscribe!(token)
      end
    end

    test 'unsubscribe using subscribed virtual attribute' do
      subscription = Subscription.new

      subscription.subscribed = false

      assert subscription.unsubscribed?
    end

    test 'subscribe using subscribed virtual attribute' do
      subscription = Subscription.new(unsubscribed_at: Time.current)

      subscription.subscribed = true

      assert subscription.subscribed?
    end

    test 'resubscribe' do
      subscribable = FactoryBot.create(:distribution_list)
      user = FactoryBot.create(:user)
      subscription = subscribable.subscriptions.create!(subscriber: user, unsubscribed_at: Time.current)

      assert subscription.resubscribe!
      assert subscription.subscribed?, 'should be an active subscription'
    end

    test 'unsubscribe' do
      subscribable = FactoryBot.create(:distribution_list)
      user = FactoryBot.create(:user)
      subscription = subscribable.subscriptions.create!(subscriber: user)

      assert subscription.unsubscribe!
      assert_not subscription.subscribed?, 'should not be an active subscription'
    end

    test 'unsubscribed subscribers are not active?' do
      subscribable = FactoryBot.create(:distribution_list)
      user = FactoryBot.create(:user)
      subscription = subscribable.subscriptions.create!(subscriber: user, unsubscribed_at: Time.current)

      assert_not subscription.active?, 'should not be active when unsubscribed'
    end

    test 'subscribers with unconfirmed emails are not active?' do
      subscribable = FactoryBot.create(:distribution_list)
      user = FactoryBot.create(:user, :unconfirmed)
      subscription = subscribable.subscriptions.create!(subscriber: user)

      assert_not subscription.active?, 'should not be active when unconfirmed'
    end

    test 'subscribers with confirmed emails are active?' do
      subscribable = FactoryBot.create(:distribution_list)
      user = FactoryBot.create(:user, :confirmed)
      subscription = subscribable.subscriptions.create!(subscriber: user)

      assert subscription.active?, 'should be active when unsubscribed and confirmed'
    end

    test 'Pings subscriptions are a notification subscription' do
      user = FactoryBot.create(:user)
      ping = FactoryBot.create(:ping)
      subscription = ping.subscribe(user)

      subscriptions = Subscription.notifications

      assert_includes(subscriptions, subscription)
    end

    test 'Distro list subscriptions are a not notification subscription' do
      user = FactoryBot.create(:user)
      list = FactoryBot.create(:distribution_list)
      subscription = list.subscribe(user)

      subscriptions = Subscription.notifications

      assert_not_includes(subscriptions, subscription)
    end
  end
end

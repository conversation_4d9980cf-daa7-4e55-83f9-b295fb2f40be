# frozen_string_literal: true

require 'test_helper'

module Communications
  class DistributionListTest < ActiveSupport::TestCase
    test 'active scope' do
      active = FactoryBot.create(:distribution_list)
      suspended = FactoryBot.create(:distribution_list, :suspended)

      lists = DistributionList.active

      assert_includes lists, active
      assert_not_includes lists, suspended
    end

    test 'active subscribers excludes unconfirmed' do
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, :unconfirmed, roles: [role])
      list = DistributionList.create!(name: 'Foobar', description: 'A list', position: 1, roles: [role])
      subscription = list.subscribe(user)

      subscriptions = list.active_subscriptions
      subscribers = list.active_subscribers

      assert_not_includes subscriptions, subscription
      assert_not_includes subscribers, user
    end

    test 'active subscribers excludes unconfirmed when not verified by neverbounce' do
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, :unconfirmed, last_neverbounce_status: nil, roles: [role])
      list = DistributionList.create!(name: 'Foobar', description: 'A list', position: 1, roles: [role])
      subscription = list.subscribe(user)

      subscriptions = list.active_subscriptions
      subscribers = list.active_subscribers

      assert_not_includes subscriptions, subscription
      assert_not_includes subscribers, user
    end

    test 'active subscribers includes unconfirmed when verified by neverbounce' do
      role = FactoryBot.create(:role)
      user = FactoryBot.create(:user, :neverbounce_validated, :unconfirmed, roles: [role])
      list = DistributionList.create!(
        allow_unconfirmed: true,
        name: 'Foobar',
        description: 'A list',
        position: 1,
        roles: [role]
      )
      subscription = list.subscribe(user)

      subscriptions = list.active_subscriptions
      subscribers = list.active_subscribers

      assert_includes subscriptions, subscription
      assert_includes subscribers, user
    end

    test 'delivery suspended' do
      admin = FactoryBot.create(:user, :distribution_list_manager)
      list = DistributionList.new(delivery_suspended_at: Time.current, delivery_suspended_by: admin)

      assert list.delivery_suspended?, 'this list should be suspended'
    end

    test 'delivery not suspended' do
      list = DistributionList.new

      assert_not list.delivery_suspended?, 'this list should not be suspended'
    end

    test 'delivery? is true when delivery_every is nil' do
      list = DistributionList.new(deliver_every: nil)

      assert list.deliver?
    end

    test 'delivery? is false when delivery_every interval has not passed' do
      list = DistributionList.new(deliver_every: 5.days, last_delivery_attempted_at: 4.days.ago)

      assert_not list.deliver?
    end

    test 'delivery? is true when delivery_every last_delivery_attempted_at is nil' do
      list = DistributionList.new(deliver_every: 5.days, last_delivery_attempted_at: 4.days.ago)

      assert_not list.deliver?
    end

    test 'delivery? is true when delivery_every interval has passed' do
      list = DistributionList.new(deliver_every: 5.days, last_delivery_attempted_at: 5.days.ago)

      assert list.deliver?
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Communications
  class SubscribableTest < ActiveSupport::TestCase
    class DummySubscribable < ApplicationRecord
      include Subscribable

      has_many :subscriptions, as: :subscribable, dependent: :destroy
      has_many :subscribers, through: :subscriptions, class_name: 'User'
    end

    setup do
      ActiveRecord::Base.connection.create_table :communications_dummy_subscribables
    end

    teardown do
      ActiveRecord::Base.connection.drop_table :communications_dummy_subscribables
    end

    test 'subscribe' do
      subscribable = DummySubscribable.create!
      user = FactoryBot.create(:user, :confirmed)

      subscribable.subscribe(user)

      assert subscribable.subscribed?(user), 'should be subscribed'
    end

    test 'unsubscribe' do
      subscribable = DummySubscribable.create!
      user = FactoryBot.create(:user, :confirmed)
      subscribable.subscriptions.create!(subscriber: user)

      subscribable.unsubscribe(user)

      assert_not subscribable.subscribed?(user), 'should be un-subscribed'
    end

    test 'auto subscribe' do
      subscribable = DummySubscribable.create!
      user = FactoryBot.create(:user, :confirmed, disabled_autosubscribe_at: nil)

      subscribable.autosubscribe(user)

      assert subscribable.subscribed?(user), 'should be auto subscribed'
    end

    test 'do not auto subscribe when disabled' do
      subscribable = DummySubscribable.create!
      user = FactoryBot.create(:user, :confirmed, disabled_autosubscribe_at: Time.current)

      subscribable.autosubscribe(user)

      assert_not subscribable.subscribed?(user), 'should be auto subscribed when auto-subscribe is disabled'
    end
  end
end

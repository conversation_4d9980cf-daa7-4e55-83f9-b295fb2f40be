# frozen_string_literal: true

require 'test_helper'

module Communications
  class AbilityTest < ActiveSupport::TestCase
    test 'logged out users cannot :view_communication_preferences' do
      ability = Ability.new(nil)

      assert_not ability.can?(:view_communication_preferences, nil),
                 'should not be able to :view_communication_preferences when logged out'
    end

    test 'guest users cannot :view_communication_preferences' do
      guest = FactoryBot.create(:guest)

      ability = Ability.new(guest)

      assert_not ability.can?(:view_communication_preferences, guest),
                 'guests should not be able to :view_communication_preferences'
    end

    test 'logged in users can :view_communication_preferences' do
      user = FactoryBot.create(:user)

      ability = Ability.new(user)

      assert ability.can?(:view_communication_preferences, user),
             'should be able to :view_communication_preferences when logged in'
    end

    test 'logged out users cannot :update_communication_preferences' do
      ability = Ability.new(nil)

      assert_not ability.can?(:update_communication_preferences, nil),
                 'should not be able to :update_communication_preferences when logged out'
    end

    test 'guest users cannot :update_communication_preferences' do
      guest = FactoryBot.create(:guest)

      ability = Ability.new(guest)

      assert_not ability.can?(:update_communication_preferences, guest),
                 'guests should not be able to :update_communication_preferences'
    end

    test 'logged in users can :update_communication_preferences' do
      user = FactoryBot.create(:user)

      ability = Ability.new(user)

      assert ability.can?(:update_communication_preferences, user),
             'should be able to :update_communication_preferences when logged in'
    end

    test 'cannot :view distribution lists' do
      role = FactoryBot.create(:role, manage_distribution_lists: false)
      user = FactoryBot.create(:user, roles: [role])

      ability = Ability.new(user)

      assert_not ability.can?(:view, DistributionList), 'should not be able to :view a distribution list'
    end

    test 'can :view distribution lists' do
      role = FactoryBot.create(:role, manage_distribution_lists: true)
      user = FactoryBot.create(:user, roles: [role])

      ability = Ability.new(user)

      assert ability.can?(:view, DistributionList), 'should be able to :view a distribution list'
    end

    test 'regular users cannot :unsubscribe from a Subscription' do
      role = FactoryBot.create(:role)
      list = FactoryBot.create(:distribution_list, roles: [role])
      user = FactoryBot.create(:user, roles: [role])
      subscription = user.subscriptions.create!(subscribable: list)

      current_user = FactoryBot.create(:user)

      ability = Ability.new(current_user)

      assert_not ability.can?(:unsubscribe, subscription), 'should not be able to :unsubscribe from a subscription'
    end

    test 'list managers can subscribe from a subscription' do
      role = FactoryBot.create(:role)
      list = FactoryBot.create(:distribution_list, roles: [role])
      user = FactoryBot.create(:user, roles: [role])
      subscription = user.subscriptions.create!(subscribable: list)

      current_user = FactoryBot.create(:user, :distribution_list_manager)

      ability = Ability.new(current_user)

      assert ability.can?(:unsubscribe, subscription), 'should be able to :unsubscribe from a subscription'
    end

    test 'can view own notification subscriptions' do
      current_user = FactoryBot.create(:user)

      ability = Ability.new(current_user)

      assert ability.can?(:view_notification_subscriptions, current_user),
             'should be able to :view_notification_subscriptions for own account'
    end

    test 'cannot view another\'s notification subscriptions' do
      current_user = FactoryBot.create(:user)
      another_user = FactoryBot.create(:user)

      ability = Ability.new(current_user)

      assert_not ability.can?(:view_notification_subscriptions, another_user),
                 'should not be able to :view_notification_subscriptions for another user'
    end

    test 'can :update own notification preferences' do
      current_user = FactoryBot.create(:user)
      notification_preferences = current_user.notification_preferences

      ability = Ability.new(current_user)

      assert ability.can?(:update, notification_preferences),
             'should be able to :update for own notification preferences'
    end

    test 'cannot :update another\'s notification subscriptions' do
      current_user = FactoryBot.create(:user)
      another_user = FactoryBot.create(:user)
      notification_preferences = another_user.notification_preferences

      ability = Ability.new(current_user)

      assert_not ability.can?(:update, notification_preferences),
                 'should not be able to :update notification preferences for another user'
    end

    test 'can unsubscribe from own subscription' do
      subscribable = FactoryBot.create(:subscribable)
      subscriber = FactoryBot.create(:user)
      subscription = subscribable.subscribe(subscriber)

      ability = Ability.new(subscriber)

      assert ability.can?(:unsubscribe, subscription), 'should be able to :unsubscribe for one\'s own subscription'
    end

    test 'cannot unsubscribe from another user\'s subscription' do
      subscribable = FactoryBot.create(:subscribable)
      subscriber = FactoryBot.create(:user)
      subscription = subscribable.subscribe(subscriber)
      current_user = FactoryBot.create(:user)

      ability = Ability.new(current_user)

      assert_not ability.can?(:unsubscribe, subscription),
                 'should not be able to :unsubscribe from another user\'s subscription'
    end

    test 'can subscribe from own subscription' do
      subscribable = FactoryBot.create(:subscribable)
      subscriber = FactoryBot.create(:user)
      subscription = subscribable.subscribe(subscriber)

      ability = Ability.new(subscriber)

      assert ability.can?(:subscribe, subscription), 'should be able to :subscribe for one\'s own subscription'
    end

    test 'cannot subscribe from another user\'s subscription' do
      subscribable = FactoryBot.create(:subscribable)
      subscriber = FactoryBot.create(:user)
      subscription = subscribable.subscribe(subscriber)
      current_user = FactoryBot.create(:user)

      ability = Ability.new(current_user)

      assert_not ability.can?(:subscribe, subscription),
                 'should not be able to :subscribe from another user\'s subscription'
    end
  end
end

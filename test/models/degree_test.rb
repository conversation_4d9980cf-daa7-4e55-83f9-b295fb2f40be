# frozen_string_literal: true

require 'test_helper'

class DegreeTest < ActiveSupport::TestCase
  test 'first doctorate year' do
    expert = FactoryBot.create(:expert)

    non_doctorate = degree_types(:non_doctorate)
    doctorate = degree_types(:doctorate)

    expert.degrees.create!(degree_type: non_doctorate, subject_area: TestData::Degree.subject_area, graduation_year: 1990)
    expert.degrees.create!(degree_type: doctorate, subject_area: TestData::Degree.subject_area, graduation_year: 1995)
    expert.degrees.create!(degree_type: doctorate, subject_area: TestData::Degree.subject_area, graduation_year: 2000)

    assert_equal 1995, expert.degrees.first_doctorate_year
  end
end

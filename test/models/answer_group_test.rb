# frozen_string_literal: true

require 'test_helper'

class AnswerGroupTest < ActiveSupport::TestCase
  setup do
    @user1 = create(:expert, first_name: '<PERSON>', last_name: '<PERSON><PERSON>')
    @user2 = create(:expert)
    @survey1 = create(:question_group, published: true, results_published: true, created_by_id: @user1.id)
    @survey2 = create(:question_group, closes_at: 1.day.ago, access_code: nil, created_by_id: @user1.id)
    @answer_group1 = create(:submission,
                            question_group: @survey1,
                            user: @user1)
  end

  test 'draft - submitted' do
    assert_equal false, @answer_group1.draft?
    assert_equal true, @answer_group1.submitted?

    @answer_group1.update(submitted_at: nil)

    assert_equal true, @answer_group1.draft?
    assert_equal false, @answer_group1.submitted?
  end

  test 'answer for question' do
    survey = create(:survey)
    question = create(:question, question_group: survey)
    answer_group = create(:submission, question_group: survey)

    answer = answer_group.answers.create(question:)

    assert_equal answer, answer_group.answer_for(question)
  end

  test 'missing answer for question' do
    survey = create(:survey)
    question = create(:question, question_group: survey)
    answer_group = create(:submission, question_group: survey)

    assert_equal MissingAnswer, answer_group.answer_for(question).class
  end

  test 'verify submission_receipts json structure after logging a receipts' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:survey)
    round = FactoryBot.create(:question_round, survey: scipi)
    submission = FactoryBot.create(:submission, survey: scipi, user: expert)
    submission.submission_receipt_sent!(round, 'non_final')

    submission.reload

    round_receipt = submission.submission_receipts.find { |r| r['round_id'] == round.id }

    assert_not_nil round_receipt
    assert round_receipt['sent_at'].present?
    assert_equal 'non_final', round_receipt['template']
  end

  test 'logging submission_receipts is idempotent operation' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:survey)
    round = FactoryBot.create(:question_round, survey: scipi)
    submission = FactoryBot.create(:submission, survey: scipi, user: expert)
    submission.submission_receipt_sent!(round, 'non_final')

    submission.reload

    assert_no_changes -> { submission.reload.submission_receipts.to_json } do
      submission.submission_receipt_sent!(round, 'non_final')
    end
  end

  test 'submission receipt already sent' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:survey)
    round = FactoryBot.create(:question_round, survey: scipi)
    submission = FactoryBot.create(:submission, survey: scipi, user: expert)
    submission.submission_receipt_sent!(round, 'non_final')

    assert submission.submission_receipt_sent?(round)
  end

  test 'submission receipt not sent' do
    expert = FactoryBot.create(:expert)
    scipi = FactoryBot.create(:survey)
    round = FactoryBot.create(:question_round, survey: scipi)
    submission = FactoryBot.create(:submission, survey: scipi, user: expert)
    submission.submission_receipt_sent!(round, 'non_final')

    new_round = FactoryBot.create(:question_round, survey: scipi, opens_at: round.closes_at + 1.day)

    assert_not submission.submission_receipt_sent?(new_round)
  end

  test 'find_or_build_answer_for for new answer' do
    survey = FactoryBot.create(:survey)
    question = FactoryBot.create(:question, survey:)
    answer_group = FactoryBot.create(:submission, survey:)

    answer = answer_group.find_or_build_answer_for(question)

    assert answer.new_record?, 'should return a new answer'
  end

  test 'find_or_build_answer_for for existing answer' do
    survey = FactoryBot.create(:survey)
    question = FactoryBot.create(:question, survey:)
    answer_group = FactoryBot.create(:submission, survey:)
    existing_answer = FactoryBot.create(:answer, question:, answer_group:)

    answer = answer_group.find_or_build_answer_for(question)

    assert_equal existing_answer, answer, 'should return the existing answer'
  end

  test 'draft is not late' do
    scipi = FactoryBot.create(:scipi, :published, closes_at: 1.day.from_now)
    submission = FactoryBot.create(:submission, scipi:, submitted_at: nil)

    assert_not submission.draft_late?, 'Submission should not be late when no close date'
  end

  test 'draft is late' do
    scipi = FactoryBot.create(:survey, :published, closes_at: 2.days.ago)
    submission = FactoryBot.create(:submission, question_group: scipi, submitted_at: nil)

    assert submission.draft_late?, 'Submission should be late when submission date is in the past'
  end
end

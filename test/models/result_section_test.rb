# frozen_string_literal: true

require 'test_helper'

class ResultSectionTest < ActiveSupport::TestCase
  test 'create un-grouped question result definition' do
    result_type = FactoryBot.create(:result_type, :multiple_choice_responses)
    survey = create(:survey)
    section = create(:result_section, survey:)
    question = create(:question, survey:)

    result_definition = section.create_result_definition!(
      question_id: question.id,
      result_type:,
      render_type: result_type.default_render_type
    )

    assert_equal question, result_definition.question
    assert_equal section, result_definition.section
    assert_equal survey, result_definition.survey
  end

  test 'create grouped question result definition' do
    result_type = FactoryBot.create(:result_type, :grouped_multiple_choice_responses)
    survey = create(:survey)
    section = create(:result_section, survey:)
    base_question = create(:question, survey:)
    group_by_question = create(:radio, survey:)

    result_definition = section.create_result_definition!(
      question_id: base_question.id,
      group_by_question_id: group_by_question.id,
      result_type:,
      render_type: result_type.default_render_type
    )

    assert_equal base_question, result_definition.question
    assert_equal group_by_question, result_definition.group_by_question
    assert_equal section, result_definition.section
    assert_equal survey, result_definition.survey
  end

  test 'create free form result' do
    FactoryBot.create(:render_type, :ranked_order_plot)
    FactoryBot.create(:result_type, :free_form)
    survey = create(:survey)
    section = create(:result_section, survey:)
    title = 'my title'
    x_label_value = 'x label'
    y_label_value = 'y label'
    data = {
      'label 1' => 23,
      'label 2' => 93
    }
    data_set = CsvFreeFormDataParser::CsvDataSet.new(x_label_value, y_label_value, data)

    result_definition = section.create_free_form_result!(
      title:,
      data_set:
    )

    assert_equal section, result_definition.section
    assert_equal survey, result_definition.survey
    assert_equal title, result_definition.title
    assert_equal x_label_value, result_definition.x_label
    assert_equal y_label_value, result_definition.y_label
    assert_equal data.count, result_definition.data_points.count
    assert result_definition.ranked_order_plot?,
           'should be a ranked order plot'
  end

  test 'create file attachment result' do
    FactoryBot.create(:render_type, :file_download)
    FactoryBot.create(:result_type, :file_attachment)
    survey = create(:survey)
    section = create(:result_section, survey:)
    title = 'my title'
    file = uploaded_fixture_file('test_attachment.pdf')

    result_definition = section.create_file_attachment_result!(
      title:,
      attachment: file
    )

    assert_equal section, result_definition.section
    assert_equal survey, result_definition.survey
    assert_equal title, result_definition.title
    assert_not_nil result_definition.attachment
    assert result_definition.file_download?, 'should be a file download type'
  end

  test 'create submission score result definition' do
    FactoryBot.create(:render_type, :ranked_order_plot)
    FactoryBot.create(:result_type, :submission_score)
    survey = create(:survey)
    section = create(:result_section, survey:)

    assert_difference(-> { section.result_definitions.count }) do
      section.create_submission_score_result_definition!(title: 'This is a title')
    end
  end

  test 'move result definition down' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section = FactoryBot.create(:result_section, survey:)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 2
    )
    third = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 3
    )
    fourth = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 4
    )
    new_position = 2

    section.change_result_definition_position!(third, new_position)

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
    assert_equal 3, second.reload.position
    assert_equal 4, fourth.reload.position
  end

  test 'move result definition up' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section = FactoryBot.create(:result_section, survey:)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 2
    )
    third = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 3
    )
    fourth = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 4
    )
    new_position = 3

    section.change_result_definition_position!(second, new_position)

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
    assert_equal 3, second.reload.position
    assert_equal 4, fourth.reload.position
  end

  # Start: A (1.1), B (1.2), C (2.1)
  # Move : A -> Section 2, Position 1
  # End  : B (1.1), A (2.1), C (2.2)
  test 'move result definition across sections with results' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section1 = FactoryBot.create(:result_section, survey:)
    section2 = FactoryBot.create(:result_section, survey:)
    result_a = FactoryBot.create(
      :result_definition,
      survey:,
      section: section1,
      position: 1
    )
    result_b = FactoryBot.create(
      :result_definition,
      survey:,
      section: section1,
      position: 2
    )
    result_c = FactoryBot.create(
      :result_definition,
      survey:,
      section: section2,
      position: 1
    )

    survey.move_result_definition!(result_a, section2, 1)

    assert_equal 1, result_a.reload.position
    assert_equal 1, result_b.reload.position
    assert_equal 2, result_c.reload.position

    assert_equal section2.id, result_a.section.reload.id
    assert_equal section1.id, result_b.section.reload.id
    assert_equal section2.id, result_c.section.reload.id
  end

  test 'move result definition to blank section' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section1 = FactoryBot.create(:result_section, survey:)
    section2 = FactoryBot.create(:result_section, survey:)
    result1 = FactoryBot.create(
      :result_definition,
      survey:,
      section: section1,
      position: 1
    )
    result2 = FactoryBot.create(
      :result_definition,
      survey:,
      section: section1,
      position: 2
    )

    survey.move_result_definition!(result2, section2, 1)

    assert_equal 1, result1.reload.position
    assert_equal 1, result2.reload.position

    assert_equal section1.id, result1.section.reload.id
    assert_equal section2.id, result2.section.reload.id
  end

  test 'moving result to same position does nothing' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section = FactoryBot.create(:result_section, survey:)
    result1 = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 1
    )
    result2 = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 2
    )
    result3 = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 3
    )
    new_position = result2.position

    section.change_result_definition_position!(result2, new_position)

    assert_equal 1, result1.reload.position
    assert_equal 2, result2.reload.position
    assert_equal 3, result3.reload.position
  end

  test 'add result definition' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    target_section = FactoryBot.create(:result_section, survey:)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      section: target_section,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      section: target_section,
      position: 2
    )
    new_position = 2

    source_section = FactoryBot.create(:result_section, survey:)
    new_result = FactoryBot.create(
      :result_definition,
      survey:,
      section: source_section,
      position: 1
    )

    assert_difference(-> { target_section.result_definitions.count }) do
      target_section.add_result_definition!(new_result, new_position)
    end

    assert_equal 1, first.reload.position
    assert_equal new_position, new_result.reload.position
    assert_equal 3, second.reload.position
  end

  test 'destroy result definition' do
    admin = FactoryBot.create(:admin)
    survey = FactoryBot.create(:survey, created_by: admin)
    section = FactoryBot.create(:result_section, survey:)
    first = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 1
    )
    second = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 2
    )
    third = FactoryBot.create(
      :result_definition,
      survey:,
      section:,
      position: 3
    )

    assert_difference(-> { CustomResult.count }, -1) do
      assert_difference(-> { survey.result_definitions.count }, -1) do
        assert_difference(-> { section.result_definitions.count }, -1) do
          section.destroy_result_definition!(second)
        end
      end
    end

    assert_equal 1, first.reload.position
    assert_equal 2, third.reload.position
  end
end

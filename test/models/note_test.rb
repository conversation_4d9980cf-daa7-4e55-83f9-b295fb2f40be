# frozen_string_literal: true

require 'test_helper'

class NoteTest < ActiveSupport::TestCase
  test 'DB admins cannot administer others\' notes without contexts' do
    note = FactoryBot.create(:note)
    db_admin = FactoryBot.create(:db_admin)

    assert_not note.administered_by?(db_admin)
  end

  test 'DB admins can administer own notes without contexts' do
    db_admin = FactoryBot.create(:db_admin)
    note = FactoryBot.create(:note, created_by: db_admin)

    assert note.administered_by?(db_admin)
  end

  test 'DB admins cannot administer notes with a context' do
    scipi = FactoryBot.create(:scipi)
    note = FactoryBot.create(:note, context: scipi)
    db_admin = FactoryBot.create(:db_admin)

    assert_not note.administered_by?(db_admin)
  end

  test 'SciPi admins can administer notes on their own SciPis' do
    scipi = FactoryBot.create(:scipi)
    scipi_admin = scipi.created_by
    note = FactoryBot.create(:note, context: scipi)

    assert note.administered_by?(scipi_admin)
  end
end

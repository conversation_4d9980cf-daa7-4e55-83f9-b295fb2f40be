# frozen_string_literal: true

require 'test_helper'

class MyNotificationsTest < ActiveSupport::TestCase
  test 'authored_ping_notification_counts' do
    author = FactoryBot.create(:expert, :with_display_name)
    ping = FactoryBot.create(:ping, author:)
    random_count = rand(2..10)
    random_type = Faker::Lorem.word

    add_notifications_to_user(source: ping, user: author, notification_type: random_type, count: random_count)

    notifications = MyNotifications.new(author)

    ping_notification_counts = notifications.authored_ping_notification_counts[ping]

    assert ping_notification_counts[random_type] == random_count
  end

  test 'subscribed ping notifications' do
    subscriber = FactoryBot.create(:expert)
    author = FactoryBot.create(:expert, :with_display_name)
    ping = FactoryBot.create(:ping, author:)
    ping.subscribe(subscriber)

    notification = subscriber.notifications.create!(source: ping, message: Faker::Lorem.sentence, notification_type: 'foo-bar')

    notifications = MyNotifications.new(subscriber)

    subscribed_ping_notifications = notifications.subscribed_ping_notifications[ping]

    assert_includes subscribed_ping_notifications, notification
  end

  test 'delivery attempted!' do
    user = FactoryBot.create(:user)
    subscribable = FactoryBot.create(:ping)
    notification = user.notifications.create!(
      source: subscribable,
      message: Faker::Lorem.sentence,
      notification_type: 'test-notification'
    )
    my_notifications = MyNotifications.new(user)
    my_notifications.delivery_attempted!

    assert_not_nil notification.reload.delivery_attempted_at
  end

  private

  def add_notifications_to_user(source:, user:, notification_type:, count:)
    count.times do
      user.notifications.create!(source:, message: Faker::Lorem.sentence, notification_type:)
    end
  end
end

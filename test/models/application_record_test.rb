# frozen_string_literal: true

require 'test_helper'

class ApplicationRecordTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  test 'enqueue_method enqueues a BackgroundModelMethodJob' do
    user = FactoryBot.create(:user)
    method_name = :update_email
    args = ['<EMAIL>']

    assert_enqueued_with(job: BackgroundModelMethodJob, args: [user, method_name, *args]) do
      user.enqueue_method(method_name, *args)
    end
  end
end

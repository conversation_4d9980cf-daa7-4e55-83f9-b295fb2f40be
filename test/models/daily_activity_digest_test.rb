# frozen_string_literal: true

require 'test_helper'

class DailyActivityDigestTest < ActiveSupport::TestCase
  test 'notification count' do
    ping_author_notifications = [Notification.new]
    ping_subscriber_notifications = [Notification.new]
    profile_alerts = ProfileAlerts.new([])
    digest = DailyActivityDigest.new(ping_author_notifications:, ping_subscriber_notifications:, pings: [],
                                     relevant_expertise: [], profile_alerts:)

    assert_equal 2, digest.notification_count
  end

  test 'most recent notification description' do
    oldest = Notification.new(notification_type: 'test', created_at: 12.hours.ago)
    newest = Notification.new(notification_type: 'pings:new-answer:subscriber', created_at: 1.hour.ago)
    profile_alerts = ProfileAlerts.new([])
    digest = DailyActivityDigest.new(
      ping_author_notifications: [oldest],
      ping_subscriber_notifications: [newest],
      pings: [],
      relevant_expertise: [],
      profile_alerts:
    )

    assert_equal newest.description, digest.most_recent_notification_description
  end

  test 'ping author tallies' do
    user = FactoryBot.create(:expert, :with_display_name)
    source = FactoryBot.create(:ping, author: user)
    notification1 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )
    notification2 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.new(
      ping_author_notifications: [notification1, notification2],
      ping_subscriber_notifications: [],
      pings: [],
      relevant_expertise: [],
      profile_alerts: ProfileAlerts.new([])
    )

    assert_equal(
      { source => { 'pings:new-answer:ping-author' => 2 } },
      digest.authored_ping_notification_tallies
    )
  end

  test 'ping subscriber tallies' do
    user = FactoryBot.create(:expert, :with_display_name)
    source = FactoryBot.create(:ping, author: user)
    notification1 = user.notifications.create!(
      source:,
      notification_type: 'pings:answer-accepted:answer-author',
      message: 'foo'
    )
    notification2 = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:subscriber',
      message: 'foo'
    )

    digest = DailyActivityDigest.new(
      ping_author_notifications: [],
      ping_subscriber_notifications: [notification1, notification2],
      pings: [],
      relevant_expertise: [],
      profile_alerts: ProfileAlerts.new([])
    )

    assert_equal(
      { source => { 'pings:answer-accepted:answer-author' => 1, 'pings:new-answer:subscriber' => 1 } },
      digest.subscribed_ping_notification_tallies
    )
  end

  test 'for method with user having expertises' do
    user = FactoryBot.create(:expert, :with_display_name)

    expertise1 = FactoryBot.create(:expertise, name: 'Biology')
    expertise2 = FactoryBot.create(:expertise, name: 'Chemistry')
    user.profile.expertises = [expertise1, expertise2]

    ping1 = FactoryBot.create(:ping)
    ping2 = FactoryBot.create(:ping)

    # Stub the method to return predictable results
    Ping.stubs(:promoted_relevant_random).with(expertises: user.profile.expertises).returns([ping1, ping2])

    source = FactoryBot.create(:ping, author: user)
    notification = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.for(user, user.notifications.where(id: notification.id))

    assert_equal [ping1, ping2], digest.pings
    assert_equal [expertise1, expertise2], digest.relevant_expertise
    assert_equal [notification], digest.ping_author_notifications
    assert_empty digest.ping_subscriber_notifications
  end

  test 'for method with user having no expertises' do
    user = FactoryBot.create(:expert, :with_display_name)

    user.profile.expertises = []

    ping1 = FactoryBot.create(:ping)
    ping2 = FactoryBot.create(:ping)

    # Stub the method to return predictable results for empty expertises
    Ping.stubs(:promoted_relevant_random).with(expertises: []).returns([ping1, ping2])

    source = FactoryBot.create(:ping, author: user)
    notification = user.notifications.create!(
      source:,
      notification_type: 'pings:new-answer:ping-author',
      message: 'foo'
    )

    digest = DailyActivityDigest.for(user, user.notifications.where(id: notification.id))

    assert_equal [ping1, ping2], digest.pings
    assert_empty digest.relevant_expertise
    assert_equal [notification], digest.ping_author_notifications
    assert_empty digest.ping_subscriber_notifications
  end
end

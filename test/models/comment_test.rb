# frozen_string_literal: true

require 'test_helper'

class CommentTest < ActiveSupport::TestCase
  test 'is invalid without user_id' do
    comment = build(:comment, user_id: nil)
    assert_not comment.valid?, 'A comment should have an associated user_id'
  end

  test 'score reflects net of yay and nay votes' do
    comment = create(:comment, debate_topic: create(:question_group))
    create(:vote, user: create(:expert, first_name: 'First'), comment_id: comment.id, yay: true)
    create(:vote, user: create(:expert, first_name: 'Second'), comment_id: comment.id, yay: false)
    create(:vote, user: create(:expert, first_name: 'Third'), comment_id: comment.id, yay: true)

    assert_equal 1, comment.reload.score
  end
end

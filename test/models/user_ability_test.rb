# frozen_string_literal: true

require 'test_helper'

class UserAbilityTest < ActiveSupport::TestCase
  attr_reader :ability, :user

  setup do
    @user = User.new(admin: false, creator: false)
    @ability = Ability.new(user)
  end

  test 'cannot create surveys' do
    assert ability.cannot?(:create, QuestionGroup), 'should be able to create a survey'
  end

  test 'cannot customize the results of any survey' do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:customize_results, survey), 'should not be able to customize the results of any survey'
  end

  test 'cannot edit any survey' do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:edit_settings, survey), "should be able to edit any survey's settings"
  end

  test "cannot view any surveys' invitations" do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:view_panelists, survey), 'should not be able view invitations for any survey'
  end

  test 'cannot export CSV results any survey' do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:export_results, survey), 'should not be able to export CSV results for any survey'
  end

  test 'cannot clone any surveys' do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:clone, survey), 'should not be able to clone any survey'
  end

  test 'cannot delete any survey' do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:delete, survey), 'should not be able to delete any survey'
  end

  test 'cannot publish any surveys' do
    survey = QuestionGroup.new(created_by: User.new)
    assert ability.cannot?(:publish, survey), 'should not be able to publish any survey'
  end
end

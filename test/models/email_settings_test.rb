# frozen_string_literal: true

require 'test_helper'

class EmailSettingsTest < ActiveSupport::TestCase
  include ActionMailer::TestHelper

  test 'update email settings' do
    password = TestData.password
    user = FactoryBot.create(:expert, password:)
    previous_email = user.email
    new_email = TestData.email
    email_settings = EmailSettings.find(user.id)

    assert email_settings.update(email: new_email, current_password: password)
    assert_enqueued_email_with <PERSON>tingsMail<PERSON>,
                               :confirm_email_change,
                               args: [user, { previous_email: }]
    assert_enqueued_email_with <PERSON><PERSON>s<PERSON>ail<PERSON>,
                               :notify_email_change,
                               args: [user, { previous_email: }]

    user.reload
    assert_not user.confirmed?, 'should not be confirmed after changing an email'
    assert_equal new_email, user.email, 'should match the new email'
    assert_not_nil user.confirmation_token, 'should have a confirmation token'
    assert_not_nil user.confirmation_sent_at, 'confirmation_sent_at be set'
  end
end

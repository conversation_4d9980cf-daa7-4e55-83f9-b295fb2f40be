# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../app/models/survey_result_csv_formatter'

class SurveyResultCsvFormatterHeaderTest < Minitest::Test
  def test_to_array_without_user_info
    questions = [
      stub(
        allow_answer_explanation: true,
        grid_type?: false,
        title_with_number: 'Foo'
      ),
      stub(
        allow_answer_explanation: false,
        grid_type?: false,
        title_with_number: 'Bar'
      ),
      stub(
        allow_answer_explanation: false,
        grid_type?: false,
        title_with_number: 'Baz'
      )
    ]

    question_headings = %w[Foo (explanation) Bar Baz]

    row = SurveyResultCsvFormatter::Header.new(
      questions:,
      show_user_info: false
    )

    assert_equal %w[display_name submitted_at] + question_headings, row.to_a
  end

  def test_to_array_with_user_info
    questions = [
      stub(
        title_with_number: 'Foo',
        allow_answer_explanation: false,
        grid_type?: false
      ),
      stub(
        title_with_number: 'Bar',
        allow_answer_explanation: false,
        grid_type?: false
      ),
      stub(
        title_with_number: 'Baz',
        allow_answer_explanation: false,
        grid_type?: false
      )
    ]

    question_headings = %w[Foo Bar Baz]

    row = SurveyResultCsvFormatter::Header.new(
      questions:,
      show_user_info: true
    )

    assert_equal %w[display_name submitted_at] + question_headings + SurveyResultCsvFormatter::USER_INFO_COLUMNS, row.to_a
  end

  def test_non_select_grid_question
    questions = [
      stub(
        question_text: 'Foo',
        allow_answer_explanation: false,
        grid_type?: true,
        grid_select_type?: false,
        row_headers: %w[one two],
        number: '1.1'
      )
    ]

    question_headings = [
      '1.1) Main question : Foo, sub-question : one',
      '1.1) Main question : Foo, sub-question : two'
    ]

    row = SurveyResultCsvFormatter::Header.new(
      questions:,
      show_user_info: false
    )

    assert_equal %w[display_name submitted_at] + question_headings, row.to_a
  end

  def test_select_grid_question
    questions = [
      stub(
        question_text: 'Foo',
        allow_answer_explanation: false,
        grid_type?: true,
        grid_select_type?: true,
        weighted_sum?: false,
        row_headers: %w[one two],
        column_headers: %w[three four],
        number: '1.1'
      )
    ]

    question_headings = [
      '1.1) Main question : Foo, sub-question (row) : one, sub-question (column) : three',
      '1.1) Main question : Foo, sub-question (row) : one, sub-question (column) : four',
      '1.1) Main question : Foo, sub-question (row) : two, sub-question (column) : three',
      '1.1) Main question : Foo, sub-question (row) : two, sub-question (column) : four'
    ]

    row = SurveyResultCsvFormatter::Header.new(
      questions:,
      show_user_info: false
    )

    assert_equal %w[display_name submitted_at] + question_headings, row.to_a
  end

  def test_wsg_question
    questions = [
      stub(
        question_text: 'Foo',
        allow_answer_explanation: false,
        grid_type?: true,
        grid_select_type?: true,
        weighted_sum?: true,
        row_headers: %w[one two],
        column_headers: %w[three four],
        number: '1.1'
      )
    ]

    question_headings = [
      '1.1) Main question : Foo, sub-question (row) : one, sub-question (column) : three',
      '1.1) Main question : Foo, sub-question (row) : one, sub-question (column) : four',
      '1.1) Main question : Foo, sub-question (row) : two, sub-question (column) : three',
      '1.1) Main question : Foo, sub-question (row) : two, sub-question (column) : four',
      'Score'
    ]

    row = SurveyResultCsvFormatter::Header.new(
      questions:,
      show_user_info: false
    )

    assert_equal %w[display_name submitted_at] + question_headings, row.to_a
  end
end

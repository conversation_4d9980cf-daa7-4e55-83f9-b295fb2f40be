# frozen_string_literal: true

require 'test_helper'

class SuggestedExpertiseTest < ActiveSupport::TestCase
  test 'suggestions cannot match existing expertise' do
    expertise = Expertise.create!(name: 'Existing')

    assert_no_difference -> { SuggestedExpertise.count } do
      SuggestedExpertise.create(term: expertise.name)
    end
  end

  test 'suggestions cannot case-insensitive match existing expertise' do
    Expertise.create!(name: 'Existing')

    assert_no_difference -> { SuggestedExpertise.count } do
      SuggestedExpertise.create(term: 'existing')
    end
  end

  test 'strip spaces on save' do
    suggestion = SuggestedExpertise.create!(term: '      New Term     ', profile_count: 3)

    assert_equal 'New Term', suggestion.term
  end

  test 'create new term' do
    FactoryBot.create(:expert, other_expertise: 'Foo')

    SuggestedExpertise.refresh_all!

    assert SuggestedExpertise.exists?(term: 'foo', profile_count: 1), 'suggestion should exist'
  end

  test 'do not create existing suggestion' do
    SuggestedExpertise.create!(term: 'Existing suggestion', profile_count: 12)
    FactoryBot.create(:expert, other_expertise: 'Existing suggestion')

    assert_no_difference -> { SuggestedExpertise.count } do
      SuggestedExpertise.refresh_all!
    end
  end

  test 'create new term found in multiple profiles' do
    FactoryBot.create(:expert, other_expertise: 'Foo')
    FactoryBot.create(:expert, other_expertise: 'Foo')

    SuggestedExpertise.refresh_all!

    assert SuggestedExpertise.exists?(term: 'foo', profile_count: 2), 'suggestion should exist with a count of 2'
  end

  test 'update existing terms' do
    suggestion = SuggestedExpertise.create!(term: 'Foo', profile_count: 1)
    FactoryBot.create(:expert, other_expertise: 'Foo')
    FactoryBot.create(:expert, other_expertise: 'Foo')

    assert_difference -> { suggestion.reload.profile_count } do
      SuggestedExpertise.refresh_all!
    end
  end

  test 'capitalized the first letter for simple redundancy check' do
    FactoryBot.create(:expert, other_expertise: 'Redundant;redundant')

    assert_difference -> { SuggestedExpertise.count }, 1 do
      SuggestedExpertise.refresh_all!
    end

    assert SuggestedExpertise.exists?(term: 'redundant', profile_count: 1), 'Redundant should occur once'
  end

  test 'remove expertise' do
    suggestion = SuggestedExpertise.create!(term: 'remove me', profile_count: 1)

    suggestion.remove!

    assert_not_includes SuggestedExpertise.visible, suggestion
  end
end

# frozen_string_literal: true

require 'test_helper'

module Sc<PERSON><PERSON>
  # Test Scipi model methods that are used on the dashboard page
  class DashboardTest < ActiveSupport::TestCase
    test 'new applications make for active scipis (even if closed for a while)' do
      scipi = FactoryBot.create(:scipi, :invite_only, :published)
      scipi.update!(closes_at: 2.years.ago)
      expert = FactoryBot.create(:expert)

      scipi.applicants.create!(expert:)

      expert_scipi = Scipis::Scipi.new(survey: scipi, user: expert)

      assert expert_scipi.active?
    end

    test 'rejected applications make for past scipis' do
      scipi = FactoryBot.create(:scipi, :invite_only, :published)
      expert = FactoryBot.create(:expert)
      invite = Invite.new(survey: scipi, expert:)

      invite.update!(approved_at: nil, rejected_at: Time.current)

      expert_scipi = Scipis::Scipi.new(survey: scipi, user: expert)

      assert expert_scipi.past?
    end

    test 'defacto rejected applications make for past scipis' do
      scipi = FactoryBot.create(:scipi, :invite_only, :published)
      expert = FactoryBot.create(:expert)
      application = scipi.applicants.create!(expert:)

      scipi.update!(recruitment_closes_on: 2.years.ago.to_date)

      expert_scipi = Scipis::<PERSON>ipi.new(survey: scipi, user: expert)

      assert application.reload.defacto_rejected?
      assert expert_scipi.past?
    end
  end
end

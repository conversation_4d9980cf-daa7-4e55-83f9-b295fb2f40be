# frozen_string_literal: true

require 'test_helper'

module Scipis
  module Reminders
    class SurveyMidpointRemindersTest < ActiveSupport::TestCase
      include ActionMailer::TestHelper
      include Scipis::Reminders::TestHelper

      test 'send reminders' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        reminder = EmailModel.new(panelist: expert, scipi:, closes_at: round.closes_at, started: true)

        travel_to(1.second.after(round.send_midpoint_reminder_at)) do
          assert_changes -> { round.reload.midpoint_reminder_sent_at } do
            assert_enqueued_email_with(Mailer, :survey_midpoint, params: { reminder: }) do
              SurveyMidpointReminders.find_and_send_all
            end
          end
        end
      end

      test 'send reminders not sent' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        # in this case, not at the midpoint yet
        travel_to(1.second.before(round.send_midpoint_reminder_at)) do
          assert_no_changes -> { round.reload.midpoint_reminder_sent_at } do
            assert_no_enqueued_emails do
              SurveyMidpointReminders.find_and_send_all
            end
          end
        end
      end

      test 'do not include drafts' do
        scipi = FactoryBot.create(:scipi, :draft)
        round = FactoryBot.create(:question_round, survey: scipi)
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        rounds = travel_to(1.second.after(round.send_midpoint_reminder_at)) do
          SurveyMidpointReminders.rounds_needing_midpoint_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include rounds before midpoint' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        rounds = travel_to(1.second.before(round.send_midpoint_reminder_at)) do
          SurveyMidpointReminders.rounds_needing_midpoint_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include rounds shorter than 7 days' do
        round = FactoryBot.create(:question_round, duration: 6.days)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        midpoint = midpoint_between(round.opens_at, round.closes_at)

        rounds = travel_to(1.second.after(midpoint)) do
          SurveyMidpointReminders.rounds_needing_midpoint_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include reminders when already sent' do
        round = FactoryBot.create(:question_round, :midpoint_reminder_sent)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        midpoint = midpoint_between(round.opens_at, round.closes_at)

        rounds = travel_to(1.second.after(midpoint)) do
          SurveyMidpointReminders.rounds_needing_midpoint_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include closed rounds' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        rounds = travel_to(1.second.after(round.closes_at)) do
          SurveyMidpointReminders.rounds_needing_midpoint_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include round with closing reminder already sent' do
        round = FactoryBot.create(:question_round, :closing_reminder_sent)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        midpoint = midpoint_between(round.opens_at, round.closes_at)

        rounds = travel_to(1.second.after(midpoint)) do
          SurveyMidpointReminders.rounds_needing_midpoint_reminders
        end

        assert_not_includes rounds, round
      end
    end
  end
end

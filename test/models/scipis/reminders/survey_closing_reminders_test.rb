# frozen_string_literal: true

require 'test_helper'

module Scipis
  module Reminders
    class SurveyClosingRemindersTest < ActiveSupport::TestCase
      include ActionMailer::TestHelper
      include Scipis::Reminders::TestHelper

      test 'send reminders' do
        expert = FactoryBot.create(:expert)
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        reminder = EmailModel.new(panelist: expert, scipi:, closes_at: round.closes_at, started: true)

        # the time we are going to run it
        runs_at = round.send_closing_reminder_at + 1.hour

        travel_to(runs_at) do
          assert_enqueued_email_with(Mailer, :survey_closing, params: { reminder: }) do
            SurveyClosingReminders.find_and_send_all
          end
          assert_equal runs_at.change(usec: 0), round.reload.closing_reminder_sent_at.change(usec: 0)
        end
      end

      test 'do not send reminders for rapid response' do
        skip('rapid response is not implemented yet')
        round = FactoryBot.create(:question_round)
        expert = FactoryBot.create(:expert)
        scipi = round.survey
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        # the time we are going to run it
        runs_at = opens_on.in_first_time_zone.midnight

        # in this case, not at the midpoint yet
        travel_to(runs_at) do
          assert_no_enqueued_emails do
            SurveyClosingReminders.find_and_send_all
          end
          assert_nil round.reload.closing_reminder_sent_at
        end
      end

      test 'do not include drafts' do
        scipi = FactoryBot.create(:scipi, :draft)
        expert = FactoryBot.create(:expert)
        round = FactoryBot.create(:question_round, survey: scipi)
        scipi = round.survey
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        run_at = round.send_closing_reminder_at + 1.hour

        rounds = travel_to(run_at) do
          SurveyClosingReminders.rounds_needing_closing_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include rounds before send at time' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        run_at = round.send_closing_reminder_at - 1.hour

        rounds = travel_to(run_at) do
          SurveyClosingReminders.rounds_needing_closing_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include when already sent' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        round.update_column(:closing_reminder_sent_at, Time.current)

        run_at = round.send_closing_reminder_at + 1.hour

        rounds = travel_to(run_at) do
          SurveyClosingReminders.rounds_needing_closing_reminders
        end

        assert_not_includes rounds, round
      end

      test 'do not include closed rounds' do
        round = FactoryBot.create(:question_round)
        scipi = round.survey
        expert = FactoryBot.create(:expert)
        scipi.panelists.create!(expert:)
        scipi.submissions.create!(user: expert)

        run_at = round.closes_at + 1.hour

        rounds = travel_to(run_at) do
          SurveyClosingReminders.rounds_needing_closing_reminders
        end

        assert_not_includes rounds, round
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Scipis
  class ParticipationCsvFormatterTest < ActiveSupport::TestCase
    setup do
      branding = FactoryBot.create(:survey_branding, :scipi)
      @scipi = FactoryBot.create(:scipi, :invite_only, branding:)
      invite = FactoryBot.create(:invite, :approved, survey: @scipi)
      @expert1 = invite.expert

      @expert2 = FactoryBot.create(:expert)
      @submission = FactoryBot.create(:submission, :final, scipi: @scipi, submitter: @expert1)
    end

    test 'header_row' do
      expected_header_row = Surveys::ParticipationCsvFormatter::USER_COLUMNS + Surveys::ParticipationCsvFormatter::SUBMISSION_COLUMNS + Surveys::ParticipationCsvFormatter::COMMENT_COLUMNS + Surveys::ParticipationCsvFormatter::VOTE_COLUMNS

      export = Surveys::ParticipationCsvFormatter.new(@scipi)

      assert_equal expected_header_row, export.header_row
    end

    test 'user_data' do
      user_export_row = Surveys::ParticipationCsvFormatter.new(@scipi).rows[0]

      assert_equal user_export_row[locate_index_by('user_id')], @expert1.id
      assert_equal user_export_row[locate_index_by('user_email')], @expert1.email
    end

    test 'submission_data' do
      long_question1 = create(:long_question,  question_group: @scipi)
      long_question2 = create(:long_question,  question_group: @scipi)
      question3 = create(:question, question_group: @scipi)

      answer1_text = Faker::Lorem.sentence
      FactoryBot.create(:answer,
                        submission: @submission,
                        question: long_question1,
                        text_answer_content: answer1_text)

      answer2_text = Faker::Lorem.sentence
      FactoryBot.create(:answer,
                        submission: @submission,
                        question: long_question2,
                        text_answer_content: answer2_text)

      answer3_explanation = Faker::Lorem.sentence
      FactoryBot.create(:answer,
                        submission: @submission,
                        question: question3,
                        explanation: answer3_explanation)

      user_export_row = Surveys::ParticipationCsvFormatter.new(@scipi).rows[0]

      expected_results = {
        submission: 'Submitted',
        freetext_answers: 2,
        ft_total_char: answer1_text.length + answer2_text.length,
        explanations: 1,
        exp_total_char: answer3_explanation.length
      }

      expected_results.each do |key, value|
        assert_equal value, user_export_row[locate_index_by(key.to_s)], "Missmatch with >> #{key}"
      end
    end

    test 'vote_data' do
      result_definition = FactoryBot.create(:text_result_definition, survey: @scipi)

      comment1 = FactoryBot.create(:comment, debate_topic: result_definition, user: @expert1)
      comment2 = FactoryBot.create(:comment, debate_topic: result_definition, user: @expert1)
      comment3 = FactoryBot.create(:comment, debate_topic: result_definition, user: @expert2)

      FactoryBot.create(:vote, comment: comment3, user: @expert1)
      FactoryBot.create(:vote, comment: comment1, user: @expert2)
      FactoryBot.create(:vote, comment: comment2, user: @expert2, yay: false)

      user_export_row = Surveys::ParticipationCsvFormatter.new(@scipi).rows[0]

      expected_results = {
        votes_cast: 1,
        upvotes_cast: 1,
        downvotes_cast: 0,
        votes_received: 2,
        upvotes_received: 1,
        downvotes_received: 1
      }

      expected_results.each do |key, value|
        assert_equal value, user_export_row[locate_index_by(key.to_s)], "Missmatch with >> #{key}"
      end
    end

    test 'comment_data' do
      result_definition = FactoryBot.create(:text_result_definition, survey: @scipi)

      comment1_text = Faker::Lorem.sentence
      FactoryBot.create(
        :comment,
        debate_topic: result_definition,
        user: @expert1,
        content: comment1_text
      )

      comment2_text = Faker::Lorem.sentence
      FactoryBot.create(
        :comment,
        debate_topic: result_definition,
        user: @expert1,
        content: comment2_text
      )

      user_export_row = Surveys::ParticipationCsvFormatter.new(@scipi).rows[0]

      expected_results = {
        comments: 2,
        com_total_char: comment1_text.length + comment2_text.length,
        com_avg_char: (comment1_text.length + comment2_text.length) / 2
      }

      expected_results.each do |key, value|
        assert_equal value, user_export_row[locate_index_by(key.to_s)], "Missmatch with >> #{key}"
      end
    end

    private

    def locate_index_by(col_name)
      Surveys::ParticipationCsvFormatter.header_row.index(col_name)
    end
  end
end

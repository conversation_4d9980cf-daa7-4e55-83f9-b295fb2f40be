# frozen_string_literal: true

require 'test_helper'

# IMPORTANT: Since round open and close at their respective times on the International Date Line,
#            it's good idea to use travel_to and the (open|closes)_at timestamps to ensure an accurate test.
class Scipis::Rounds::RoundOpenNotificationTest < ActiveSupport::TestCase
  include ActionMailer::TestHelper

  test 'not auto-deliverable without a review period' do
    round = FactoryBot.create(:question_round, :no_timeframe)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    assert_not email.auto_deliverable?
  end

  test 'not auto-deliverable before the round is open' do
    round = FactoryBot.create(:question_round)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    travel_to 1.hour.before(round.opens_at) do
      assert_not email.auto_deliverable?
    end
  end

  test 'auto-deliverable after the round is open' do
    scipi = FactoryBot.create(:scipi, :published)
    round = FactoryBot.create(:question_round, scipi:)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    travel_to 1.hour.after(round.opens_at) do
      assert email.auto_deliverable?
    end
  end

  test 'not auto-deliverable after the round is closed' do
    round = FactoryBot.create(:question_round)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    travel_to 1.hour.after(round.closes_at) do
      assert_not email.auto_deliverable?
    end
  end

  test 'not auto-deliverable when already sent' do
    round = FactoryBot.create(:question_round, :open_email_sent)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    travel_to 1.hour.after(round.opens_at) do
      assert_not email.auto_deliverable?
    end
  end

  test 'not auto-deliverable when later emails have already been sent' do
    round = FactoryBot.create(:question_round, :midpoint_reminder_sent)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    travel_to 1.hour.after(round.opens_at) do
      assert_not email.auto_deliverable?
    end
  end

  test 'deliver all' do
    scipi = FactoryBot.create(:scipi)
    expert = FactoryBot.create(:expert)
    scipi.panelists.create!(user: expert)
    round = FactoryBot.create(:question_round, survey: scipi)

    email = Scipis::Rounds::RoundOpenNotification.new(round:)

    travel_to 1.hour.after(round.opens_at) do
      assert_enqueued_email_with(Scipis::PanelistsMailer, :question_round_open, args: [{ scipi:, round:, panelist: expert }]) do
        assert_changes -> { round.open_email_sent? } do
          email.deliver_all([expert])
        end
      end
    end
  end
end

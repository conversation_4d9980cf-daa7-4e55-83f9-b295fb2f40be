# frozen_string_literal: true

require 'test_helper'

module SciPis
  class ResultsTest < ActiveSupport::TestCase
    test 'scipi results can be public regardless of closed status (ignores scipoll specific behavior)' do
      scipi = FactoryBot.create(:survey, :scipi, :published, results_published: true, closes_at: 1.week.from_now, results_public: true)

      assert scipi.results_public?
    end

    test 'scipi results public takes precedence over published' do
      scipi = FactoryBot.create(:survey, :scipi, :published, results_published: false, results_public: true)

      assert scipi.results_public?
    end

    test 'scipi results cannot be public unless survey is also published' do
      scipi = FactoryBot.create(:survey, :scipi, published_at: nil, results_published: true, results_public: true)

      assert_not scipi.results_public?
    end

    test 'scipi results published during debate rounds (overrides results published settings)' do
      scipi = FactoryBot.create(:scipi, :published, number_of_rounds: 1, results_published: false)
      FactoryBot.create(:debate_round, :active, survey: scipi, position: 1)

      assert scipi.results_published?
    end

    test 'scipi results NOT published during question rounds (overrides results published settings)' do
      scipi = FactoryBot.create(:scipi, :published, :results_published, number_of_rounds: 1)
      FactoryBot.create(:question_round, :active, survey: scipi, position: 1)

      assert_not scipi.results_published?
    end

    test 'scipi results NOT published with scheduled (but not active) debate rounds' do
      scipi = FactoryBot.create(:scipi, :published, :results_published, number_of_rounds: 1)
      FactoryBot.create(:debate_round, survey: scipi, position: 1)

      assert_not scipi.results_published?
    end
  end
end

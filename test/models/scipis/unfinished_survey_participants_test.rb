# frozen_string_literal: true

require 'test_helper'

module Scipis
  class UnfinishedSurveyParticipantsTest < ActiveSupport::TestCase
    test 'includes participant that as not started' do
      round = FactoryBot.create(:question_round)
      scipi = round.survey

      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)

      unfinished = UnfinishedSurveyParticipants.new(round).all

      assert_includes unfinished, expert, 'should include unstarted participant'
      assert_not unfinished.find(expert.id).started, 'should not be started'
    end

    test 'includes participant with draft' do
      round = FactoryBot.create(:question_round)
      scipi = round.survey

      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)

      scipi.submissions.create!(user: expert, question_group: scipi)

      unfinished = UnfinishedSurveyParticipants.new(round).all

      assert_includes unfinished, expert, 'should include participant with draft'
      assert unfinished.find(expert.id).started, 'should be started'
    end

    test 'does not include participant with final submission' do
      round = FactoryBot.create(:question_round)
      scipi = round.survey

      expert = FactoryBot.create(:expert)
      scipi.panelists.create!(expert:)

      scipi.submissions.create!(user: expert, question_group: scipi, submitted_at: Time.current)

      unfinished = UnfinishedSurveyParticipants.new(round).all

      assert_not_includes unfinished, expert, 'should not include participant with final submission'
    end

    test 'does not include unapproved applicants' do
      round = FactoryBot.create(:question_round)
      scipi = round.survey

      expert = FactoryBot.create(:expert)
      scipi.observers.create!(expert:)

      unfinished = UnfinishedSurveyParticipants.new(round).all

      assert_not_includes unfinished, expert, 'should not include non-participant'
    end

    test 'does not include non-participants' do
      round = FactoryBot.create(:question_round)
      scipi = round.survey

      expert = FactoryBot.create(:expert)
      scipi.applicants.create!(expert:)

      unfinished = UnfinishedSurveyParticipants.new(round).all

      assert_not_includes unfinished, expert, 'should not include non-participant'
    end
  end
end

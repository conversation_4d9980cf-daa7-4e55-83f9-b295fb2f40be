# frozen_string_literal: true

require 'test_helper'

module SciPis
  class ReportTest < ActiveSupport::TestCase
    test 'report is interim by default' do
      report = Scipis::Report.new

      assert report.interim?
      assert_not report.final?
    end

    test 'final report' do
      report = Scipis::Report.new(final: true)

      assert report.final?
      assert_not report.interim?
    end

    test 'interim report filename' do
      scipi = FactoryBot.create(:scipi)
      report = Scipis::Report.new(scipi:)

      assert_equal "SciPi ##{scipi.id} Report [INTERIM].pdf", report.filename
    end

    test 'final report filename' do
      scipi = FactoryBot.create(:scipi)
      created_by = FactoryBot.create(:user)
      report = Scipis::Report.new(scipi:, created_by:, final: true)

      assert_equal "SciPi ##{scipi.id} Report [FINAL].pdf", report.filename
    end

    test 'pending? by default' do
      report = Scipis::Report.new

      assert report.pending?
      assert_not report.complete?
      assert_not report.failed?
    end

    test 'complete?' do
      ActiveStorage::Current.url_options = { host: 'localhost' }
      scipi = FactoryBot.create(:scipi)
      created_by = FactoryBot.create(:user)
      report = Scipis::Report.create!(scipi:, created_by:, prepared_for: 'Test Client')

      io = StringIO.new('test pdf content')
      report.file.attach(io:, filename: 'test.pdf', content_type: 'application/pdf')
      report.update!(completed_at: Time.current)

      assert report.complete?
      assert_not report.pending?
      assert_not report.failed?
    end

    test 'failed? returns true when failed_at is present' do
      scipi = FactoryBot.create(:scipi)
      created_by = FactoryBot.create(:user)
      report = Scipis::Report.create!(
        scipi:,
        created_by:,
        failed_at: Time.current,
        error_message: 'Test error',
        prepared_for: 'Test Client'
      )

      assert report.failed?
      assert_not report.complete?
      assert_not report.pending?
    end

    test 'attach_file! marks report as complete' do
      ActiveStorage::Current.url_options = { host: 'localhost' }
      scipi = FactoryBot.create(:scipi)
      created_by = FactoryBot.create(:user)
      report = Scipis::Report.create!(scipi:, created_by:, prepared_for: 'Test Client')

      report.attach_file!(StringIO.new('test pdf content'))

      assert report.complete?
      assert report.file.attached?
      assert_not_nil report.completed_at
    end

    test 'record_failure! marks report as failed' do
      scipi = FactoryBot.create(:scipi)
      created_by = FactoryBot.create(:user)
      report = Scipis::Report.create!(scipi:, created_by:, prepared_for: 'Test Client')

      report.record_failure!('Generation failed')

      assert report.failed?
      assert_not_nil report.failed_at
      assert_equal 'Generation failed', report.error_message
    end
  end
end

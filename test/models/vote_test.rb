# frozen_string_literal: true

require 'test_helper'

class VoteTest < ActiveSupport::TestCase
  setup do
    survey = create(:question_group)
    @comment = create(:comment, debate_topic: survey)
    @user = create(:expert)
  end

  test 'is invalid without user_id' do
    vote = build(:vote, user: nil)
    assert_not vote.valid?, 'A vote should have an associated user'
  end

  test 'is invalid without comment_id' do
    vote = build(:vote, comment: nil)
    assert_not vote.valid?, 'A vote should have an associated comment'
  end

  test 'should have only one vote on a comment by a user' do
    first_vote = create(:vote, comment: @comment, user: @user)
    assert first_vote.valid?
    second_vote = build(:vote, comment: @comment, user: @user)
    assert_not second_vote.valid?, 'A user can have only one vote on a comment.'
  end

  test 'adding a yay vote increments the comment score' do
    assert_equal 0, @comment.reload.score
    create(:vote, comment: @comment, yay: true, user: @user)
    assert_equal 1, @comment.reload.score
  end

  test 'adding a nay vote decrements the comment score' do
    create(:vote, comment: @comment, yay: true, user: @user)
    assert_equal 1, @comment.reload.score
    create(:vote, comment: @comment, yay: false, user: create(:expert, first_name: '<PERSON>'))

    assert_equal 0, @comment.reload.score
  end

  test 'deleting a yay vote decrements the comment score' do
    vote = create(:vote, comment: @comment, yay: true, user: @user)
    assert_equal 1, @comment.reload.score
    vote.destroy

    assert_equal 0, @comment.reload.score
  end

  test 'deleting a nay vote decrements the comment score' do
    vote = create(:vote, comment: @comment, yay: false, user: @user)
    assert_equal(-1, @comment.reload.score)
    vote.destroy

    assert_equal 0, @comment.reload.score
  end
end

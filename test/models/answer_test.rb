# frozen_string_literal: true

require 'test_helper'

class AnswerTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  test 'multiple choice skipped' do
    answer = create(:answer, question: create(:checkbox))

    assert answer.skipped?, 'should be skipped'
  end

  test 'multiple choice not skipped' do
    question = create(:checkbox)
    answer = create(:answer,
                    question:,
                    answer_choices: [question.answer_choices.first])

    assert_not answer.skipped?, 'should be skipped'
  end

  test 'text answer skipped' do
    answer = create(:answer, question: create(:long_question))

    assert answer.skipped?, 'should be skipped'
  end

  test 'text answer not skipped' do
    answer = create(:answer,
                    question: create(:long_question),
                    text_answer_content: 'foobar')

    assert_not answer.skipped?, 'should be skipped'
  end

  test 'enqueue CopyToProfileJob' do
    _country = FactoryBot.create(:country)
    survey = FactoryBot.create(:survey)
    question = FactoryBot.create(:profile_country_question, survey:)
    answer_group = FactoryBot.create(:submission, survey:)

    assert_enqueued_with(job: Surveys::CopyToProfileJob,
                         args: ->(args) { args.first.is_a?(Answer) && args.first.question == question }) do
      FactoryBot.create(:answer, question:, answer_group:, answer_choices: [question.answer_choices.first])
    end
  end

  test 'do not enqueue CopyToProfileJob' do
    survey = FactoryBot.create(:survey)
    question = FactoryBot.create(:radio, survey:)
    answer_group = FactoryBot.create(:submission, survey:)

    assert_no_enqueued_jobs do
      FactoryBot.create(:answer, question:, answer_group:, answer_choices: [question.answer_choices.first])
    end
  end
end

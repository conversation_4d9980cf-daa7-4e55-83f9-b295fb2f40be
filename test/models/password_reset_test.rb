# frozen_string_literal: true

require 'test_helper'

class PasswordResetTest < ActiveSupport::TestCase
  test 'reset password' do
    user = FactoryBot.create(:expert, :reset_password_requested)
    reset = PasswordReset.find(user.reset_password_token)
    password = Faker::Internet.password

    assert reset.update(password:, password_confirmation: password)
    assert user.reload.authenticate(password)
  end

  test 'expired' do
    reset = PasswordReset.new(token: 'token', expires_after: 1.hour, sent_at: 2.hours.ago)

    assert reset.expired?
  end

  test 'not expired' do
    reset = PasswordReset.new(token: 'token', expires_after: 2.hours, sent_at: 1.hour.ago)

    assert_not reset.expired?
  end
end

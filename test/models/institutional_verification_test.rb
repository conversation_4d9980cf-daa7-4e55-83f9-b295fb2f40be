# frozen_string_literal: true

require 'test_helper'

class InstitutionalVerificationTest < ActiveSupport::TestCase
  #
  # Start started? test cases
  #
  test 'not started? when login email is unconfirmed' do
    login_email = LoginEmail.new(address: nil, confirmed: false, domain: nil)

    verification = InstitutionalVerification.new(login_email:)

    assert_not verification.started?
  end

  test 'started? returns true if login email is confirmed' do
    login_email = LoginEmail.new(address: nil, confirmed: true, domain: nil)

    verification = InstitutionalVerification.new(login_email:)

    assert verification.started?
  end

  #
  # Start awaiting_institutional_review? test cases
  #
  test 'awaiting review when login email is awaiting review and additional email is nil' do
    domain = create_domain(:unknown)
    login_email = LoginEmail.new(address: nil, confirmed: true, domain:)

    verification = InstitutionalVerification.new(login_email:)

    assert_awaiting_review verification
  end

  test 'awaiting review when one email is personal and one is awaiting review' do
    personal = create_domain(:personal_email)
    unknown = create_domain(:unknown)
    login_email = LoginEmail.new(address: nil, confirmed: true, domain: personal)
    additional_email = FactoryBot.create(:email, :additional, :confirmed, domain: unknown)

    verification = InstitutionalVerification.new(login_email:, additional_email:)

    assert_awaiting_review verification
  end

  #
  # Start pending_completion? test cases
  #
  test 'pending completion when additional email is known_institution and unconfirmed' do
    personal = create_domain(:personal_email)
    known = create_domain(:known_institution)
    login_email = LoginEmail.new(address: nil, confirmed: true, domain: personal)
    additional_email = FactoryBot.create(:email, :additional, :unconfirmed, address: "user@#{known.hostname}")

    verification = InstitutionalVerification.new(login_email:, additional_email:)

    assert_pending_completion verification
  end

  #
  # Start verification_needed? test cases
  #
  test 'verification needed when the login email is personal and confirmed' do
    domain = create_domain(:personal_email)
    expert = FactoryBot.create(:expert, :confirmed, email: "user@#{domain.hostname}")
    login_email = LoginEmail.new(address: expert.email, confirmed: true, domain:)

    institutional_verification = InstitutionalVerification.new(login_email:)

    assert institutional_verification.verification_needed?
  end

  # Completed test cases
  test 'completed when login is a known institution' do
    known_institution = domain_lists(:known_institution)
    known_domain = known_institution.domains.create!(hostname: 'known.com')
    expert = FactoryBot.create(:expert, :confirmed)

    login_email = LoginEmail.new(address: expert.email, confirmed: true, domain: known_domain)
    verification = InstitutionalVerification.new(login_email:)

    assert_complete verification
  end

  private

  def assert_awaiting_review(verification)
    assert verification.awaiting_review?, 'should be awaiting review'

    assert_not verification.complete?, 'should not be complete'
    assert_not verification.pending_completion?, 'should not be pending completion'
    assert_not verification.verification_needed?, 'should not be needed'
  end

  def assert_complete(verification)
    assert verification.complete?, 'should be complete'

    assert_not verification.awaiting_review?, 'should not be awaiting review'
    assert_not verification.pending_completion?, 'should not be pending completion'
    assert_not verification.verification_needed?, 'should not be needed'
  end

  def assert_pending_completion(verification)
    assert verification.pending_completion?, 'should be pending completion'

    assert_not verification.awaiting_review?, 'should not be awaiting review'
    assert_not verification.complete?, 'should not be complete'
    assert_not verification.verification_needed?, 'should not be needed'
  end

  def create_domain(list_name)
    list = domain_lists(list_name)
    list.domains.create!(hostname: "#{list_name.to_s.dasherize}.com")
  end
end

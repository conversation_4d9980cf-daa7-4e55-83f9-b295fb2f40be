# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../app/domain/grid_result'

class GridResultTest < Minitest::Test
  def test_allow_comments
    allow_comments = true

    survey_stub = stub(debate_open?: allow_comments)
    question_stub = stub(survey: survey_stub)

    result = GridResult.new(legacy_results: stub(question: question_stub))

    assert result.debate_open?, 'show allow comments'
  end

  def test_answer_count
    answer_count = 1

    result = GridResult.new(legacy_results: stub(answer_count:))

    assert_equal answer_count,
                 result.answer_count,
                 "the answer count should be #{answer_count}"
  end

  def test_position
    position = 1

    result = GridResult.new(legacy_results: stub(position:))

    assert_equal position,
                 result.position,
                 "the position should be #{position}"
  end

  def test_result_id
    result_id = 85

    result = GridResult.new(legacy_results: stub(result_id:, custom?: true))

    assert_equal result_id,
                 result.result_id,
                 "the chart id should be #{result_id}"
  end

  def test_title
    title = 'I am a question'

    question_stub = stub(title:)
    result = GridResult.new(legacy_results: stub(question: question_stub))

    assert_equal title,
                 result.title,
                 "the question text should be #{title}"
  end

  def test_section_name
    section_name = 'section'

    question_stub = stub(section_name:)
    result = GridResult.new(legacy_results: stub(question: question_stub))

    assert_equal section_name,
                 result.section_name,
                 "the section name text should be #{section_name}"
  end
end

# frozen_string_literal: true

require 'minitest/autorun'
require 'matrix'

require_relative '../../app/domain/weighted_sum_grid_score'

class WeightedSumGridScoreTest < Minitest::Test
  def test_grid_score_value
    wsg_score = WeightedSumGridScore.new(
      column_weights: [0.7, 0.3],
      row_weights: [0.2, 0.8],
      answer_matrix: Matrix[[3, 1], [2, 3]],
      max_answer_value: 3
    )

    assert_equal 77.3, wsg_score.value
  end

  def test_grid_score_value_with_weight
    wsg_score = WeightedSumGridScore.new(
      column_weights: [0.4, 0.6],
      row_weights: [0.9, 0.1],
      answer_matrix: Matrix[[2, 3], [2, 2]],
      max_answer_value: 3,
      weight: 0.3
    )

    assert_equal 25.4, wsg_score.value
  end

  def test_nil_row_weight
    assert_raises ArgumentError do
      WeightedSumGridScore.new(
        column_weights: [0.4, 0.6],
        row_weights: [0.9, nil],
        answer_matrix: Matrix[[2, 3], [2, 2]],
        max_answer_value: 3
      )
    end
  end

  def test_col_weight
    assert_raises ArgumentError do
      WeightedSumGridScore.new(
        column_weights: [nil, 0.6],
        row_weights: [0.9, 0.1],
        answer_matrix: Matrix[[2, 3], [2, 2]],
        max_answer_value: 3
      )
    end
  end

  def test_nil_answer_value
    assert_raises ArgumentError do
      WeightedSumGridScore.new(
        column_weights: [0.4, 0.6],
        row_weights: [0.9, 0.1],
        answer_matrix: Matrix[[2, nil], [2, 2]],
        max_answer_value: 3
      )
    end
  end
end

# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'forwardable'

require_relative '../../app/domain/base_result'

class BaseResultTest < Minitest::Test
  class TestResult < BaseResult
  end

  def test_debate_open
    result = TestResult.new(
      result_definition: stub(debate_open?: false)
    )
    assert !result.debate_open?, 'should not allow comments'
  end

  def test_result_id
    result_id = 34
    result_definition = stub(result_id:)

    result = TestResult.new(
      result_definition:
    )

    assert_equal result_id,
                 result.result_id,
                 "the result_id should be #{result_id}"
  end

  def test_comments_count
    count = 4
    result = TestResult.new(
      result_definition: stub(comments_count: count)
    )
    assert_equal count,
                 result.comments_count,
                 "comments count should be #{count}"
  end

  def test_position
    position = 4
    result_definition = stub(position:)

    result = TestResult.new(
      result_definition:
    )

    assert_equal position,
                 result.position,
                 "the position should be #{position}"
  end

  def test_section_name
    section_name = 'foobar'

    result = TestResult.new(
      result_definition: stub(section_name:)
    )

    assert_equal section_name,
                 result.section_name,
                 "the section name should be #{section_name}"
  end

  def test_title
    text = 'I am a title'
    result = TestResult.new(
      result_definition: stub(title: text)
    )
    assert_equal text,
                 result.title,
                 "the question text should be #{text}"
  end
end

# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'forwardable'
require 'matrix'

require_relative '../../app/domain/weighted_sum_grid_score'
require_relative '../../app/domain/weighted_sum_grid_answer'

class WeightedSumGridAnswerTest < Minitest::Test
  def test_weighted_grid_score
    column_weights = [0.4, 0.2, 0.2, 0.1, 0.1]
    row_weights = [1, 1, 0.1, 0.1, 0.1]
    # This currently come out of the database as strings
    # which is obviously problematic
    answer_values = [
      %w[3 3 3 3 3],
      %w[3 1 1 1 1],
      %w[3 3 3 3 3],
      %w[3 3 3 3 3],
      %w[3 3 3 3 3]
    ]

    question = stub(
      answer_choice_labels: %w[3 2 1 0 -1 -2 -3],
      column_count: column_weights.count,
      column_weights:,
      row_count: row_weights.count,
      row_weights:,
      weight: 1.0
    )

    answer = stub_wsg_answer_for(answer_values)

    grid = WeightedSumGridAnswer.new(
      question:,
      answer:
    )

    assert_equal(82.6, grid.score.round(1))
  end

  def test_weighted_grid_score_with_question_weight
    column_weights = [0.4, 0.2, 0.2, 0.1, 0.1]
    row_weights = [1, 1, 0.1, 0.1, 0.1]
    # This currently come out of the database as strings
    # which is obviously problematic
    answer_values = [
      %w[3 3 3 3 3],
      %w[3 1 1 1 1],
      %w[3 3 3 3 3],
      %w[3 3 3 3 3],
      %w[3 3 3 3 3]
    ]

    question = stub(
      answer_choice_labels: %w[3 2 1 0 -1 -2 -3],
      column_count: column_weights.count,
      column_weights:,
      row_count: row_weights.count,
      row_weights:,
      weight: 0.5
    )

    answer = stub_wsg_answer_for(answer_values)

    grid = WeightedSumGridAnswer.new(
      question:,
      answer:
    )

    assert_equal(41.3, grid.score.round(1))
  end

  def test_empty_grid_score_is_zero
    column_weights = [0.4, 0.4, 0.2]
    row_weights = [0.3, 0.6, 0.1]
    answer_values = [
      Array.new(3, 0),
      Array.new(3, 0),
      Array.new(3, 0)
    ]

    question = stub(
      answer_choice_labels: %w[3 2 1 0 -1 -2 -3],
      column_count: column_weights.count,
      column_weights:,
      row_count: row_weights.count,
      row_weights:,
      weight: 1.0
    )

    answer = stub_wsg_answer_for(answer_values)

    grid = WeightedSumGridAnswer.new(
      question:,
      answer:
    )

    assert_equal(0, grid.score)
  end

  def test_incomplete_grid_score
    column_weights = [0.4, 0.4, 0.2]
    row_weights = [0.3, 0.6, 0.1]
    answer_values = [
      [3, 3, 3],
      [0, 1, 1],
      [3, 3, 0]
    ]

    question = stub(
      answer_choice_labels: %w[3 2 1 0 -1 -2 -3],
      column_count: column_weights.count,
      column_weights:,
      row_count: row_weights.count,
      row_weights:,
      weight: 1.0
    )

    answer = stub_wsg_answer_for(answer_values)

    grid = WeightedSumGridAnswer.new(
      question:,
      answer:
    )

    assert_equal(50.0, grid.score)
  end

  private

  def stub_wsg_answer_for(answer_values)
    Object.new.tap do |answer|
      answer_values.each_with_index do |row, index|
        answer
          .stubs(:row_at)
          .returns(row)
          .with(index)
      end
    end
  end
end

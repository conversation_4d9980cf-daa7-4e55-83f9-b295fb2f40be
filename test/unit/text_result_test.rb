# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'forwardable'

require_relative '../../app/domain/base_result'
require_relative '../../app/domain/text_result'

class TextResultTest < Minitest::Test
  def test_answer_count
    answers = ['foo', 'bar', '']
    expected_count = answers.count { |a| !a.empty? }
    result = TextResult.new(
      result_definition: Object.new,
      answers:
    )

    assert_equal expected_count,
                 result.answer_count,
                 "the answer count should be #{expected_count}"
  end

  def test_formatted_results
    answers = ['foo', 'bar', '']

    result = TextResult.new(
      result_definition: Object.new,
      answers:
    )

    assert_equal answers,
                 result.formatted_results,
                 "the formatted results should be #{answers}"
  end

  def test_skip_count
    answers = ['foo', 'bar', '']
    expected_count = answers.count(&:empty?)

    result = TextResult.new(
      result_definition: Object.new,
      answers:
    )

    assert_equal expected_count,
                 result.skip_count,
                 "the skip count should be #{expected_count}"
  end

  def test_total_response_count
    answers = ['foo', 'bar', '']
    expected_count = answers.count

    result = TextResult.new(
      result_definition: Object.new,
      answers:
    )

    assert_equal expected_count,
                 result.total_response_count,
                 "the total_response_count should be #{expected_count}"
  end
end

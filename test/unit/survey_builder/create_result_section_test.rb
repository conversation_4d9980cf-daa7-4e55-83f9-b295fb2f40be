# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'

require_relative '../../../app/domain/survey_builder/create_result_section'

module SurveyBuilder
  class CreateResultSectionCodeTest < Minitest::Test
    def test_perform
      name = Object.new
      hidden = Object.new

      request = stub(
        'form stub',
        name:,
        hidden:,
        valid?: true
      )

      survey = mock('mock survey')
      survey.expects(:create_result_section!).with(name:, hidden:)

      action = CreateResultSection.new(survey)

      assert action.perform(request)
    end

    def test_perform_not_valid
      survey = Object.new
      request = stub('form stub', valid?: false)

      action = CreateResultSection.new(survey)

      refute action.perform(request)
    end
  end
end

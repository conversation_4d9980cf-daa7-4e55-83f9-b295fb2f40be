# frozen_string_literal: true

require 'minitest/autorun'
require 'mocha/minitest'
require 'forwardable'

require_relative '../../app/domain/base_result'
require_relative '../../app/domain/chart_result'

class ChartResultTest < Minitest::Test
  def test_answer_count
    answer_count = 10

    result = create_result_chart(
      response_count: answer_count
    )

    assert_equal answer_count,
                 result.answer_count,
                 "the answer count should be #{answer_count}"
  end

  def test_total_response_count
    total_response_count = 10

    result = create_result_chart(
      total_response_count:
    )

    assert_equal total_response_count,
                 result.total_response_count,
                 "the total response count should be #{total_response_count}"
  end

  def test_chart_type_delegate_to_the_result_def
    chart_type = 'doughnut'
    result_def = stub('result def', chart_type:)

    result = create_result_chart(result_definition: result_def)

    assert_equal chart_type,
                 result.chart_type,
                 'should delegate to the result definition'
  end

  def test_result_items
    chart_type = 'doughnut'

    answer_choice1_count = 1
    answer_choice2_count = 2
    answer_choice1_text = 'foo'
    answer_choice2_text = 'foo'

    answer_choice1 = stub(
      'answer choice 1',
      count: answer_choice1_count,
      text: answer_choice1_text
    )
    answer_choice2 = stub(
      'answer choice 2',
      count: answer_choice2_count,
      text: answer_choice2_text
    )

    result_def = stub(
      'result def',
      chart_type:
    )

    result_items = [
      LegendItem.new(
        label: answer_choice1_text,
        proportion: 33.33,
        value: answer_choice1_count
      ),
      LegendItem.new(
        label: answer_choice2_text,
        proportion: 66.67,
        value: answer_choice2_count
      )
    ]

    result = create_result_chart(
      result_definition: result_def,
      answer_choices: [answer_choice1, answer_choice2]
    )

    assert_equal result_items,
                 result.result_items
  end

  private

  def create_result_chart(result_definition: Object.new,
                          response_count: 0,
                          total_response_count: 0,
                          answer_choices: [])
    ChartResult.new(
      result_definition:,
      response_count:,
      total_response_count:,
      answer_choices:
    )
  end
end

# frozen_string_literal: true

require 'minitest/autorun'

require_relative '../../../app/domain/value_object'
require_relative '../../../app/domain/results/nav_section'

module Results
  class NavSectionTest < Minitest::Test
    def test_show_hidden_label_for_admin
      section = NavSection.new(
        hidden: true,
        results: [Object.new],
        section_id: Object.new,
        title: 'Section Name'
      )

      assert section.show_hidden_label?(true),
             'show the hidden label to admins if hidden'
    end

    def test_do_not_show_hidden_label_for_admin
      section = NavSection.new(
        hidden: false,
        results: [Object.new],
        section_id: Object.new,
        title: 'Section Name'
      )

      refute section.show_hidden_label?(true),
             'do not show the hidden label to admins if not hidden'
    end

    def test_do_not_show_hidden_label_to_non_admins
      section = NavSection.new(
        hidden: true,
        results: [Object.new],
        section_id: Object.new,
        title: 'Section Name'
      )

      refute section.show_hidden_label?(false),
             'do not show the hidden label to admins if not hidden'
    end
  end
end

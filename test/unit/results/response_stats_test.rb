# frozen_string_literal: true

require 'minitest/autorun'

require_relative '../../../app/domain/results/response_stats'

module Results
  class ResponseStatsTest < Minitest::Test
    def test_no_skips
      stats = ResponseStats.new(answer_count: 3, total_response_count: 3)

      assert_equal 0, stats.skip_count
    end

    def test_skips
      stats = ResponseStats.new(answer_count: 1, total_response_count: 3)

      assert_equal 2, stats.skip_count
    end
  end
end

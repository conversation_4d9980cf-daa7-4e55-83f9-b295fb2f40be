# frozen_string_literal: true

require 'minitest/autorun'

require_relative '../../../app/domain/results/chart_data_result'

module Results
  class ChartDataResultTest < Minitest::Test
    def test_skip_count
      answer_count = 4
      total_response_count = 6
      skip_count = total_response_count - answer_count

      result = ChartDataResult.new(
        answer_count:,
        data_sets: Object.new,
        id: Object.new,
        labels: Object.new,
        legend_items: [],
        chart_type: Object.new,
        total_response_count:
      )

      assert_equal skip_count, result.skip_count
    end
  end
end

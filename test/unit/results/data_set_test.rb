# frozen_string_literal: true

require 'minitest/autorun'

require_relative '../../../app/domain/results/data_set'

module Results
  class DateSetTest < Minitest::Test
    def test_proportions
      data_set = DataSet.new(
        label: '',
        values: [4, 2, 6]
      )

      assert_equal [33.3, 16.7, 50.0], data_set.proportions
    end

    def test_shovel
      value = 42

      data_set = DataSet.new

      data_set << value

      assert_equal [value],
                   data_set.values,
                   'should have added the provided value'
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

class FileSizeValidatorTest < ActiveSupport::TestCase
  class DummyModel < ApplicationRecord
    has_one_attached :file
    has_many_attached :attachments

    validates :file, file_size: { max: 2.megabytes }
    validates :attachments, file_size: { max: 2.megabytes }
  end

  setup do
    ActiveRecord::Base.connection.create_table :dummy_models
  end

  teardown do
    ActiveRecord::Base.connection.drop_table :dummy_models
  end

  test 'has_one_attached it too big' do
    model = DummyModel.create(file: attachable_file_fixture('too_big.pdf'))

    assert(model.errors[:file].any? { |msg| msg.include?('must be smaller than') })
  end

  test 'valid has_one_attached' do
    model = DummyModel.create(file: attachable_file_fixture('cv.pdf'))

    assert model.errors[:file].empty?
  end

  test 'valid has many attachments with all small files' do
    model = DummyModel.create(
      attachments: [
        attachable_file_fixture('cv.pdf'),
        attachable_file_fixture('not_a_pdf.docx')
      ]
    )

    assert model.errors[:attachments].empty?
  end

  test 'invalid has many attachments with one large file' do
    model = DummyModel.create(
      attachments: [
        attachable_file_fixture('cv.pdf'),
        attachable_file_fixture('too_big.pdf')
      ]
    )

    assert(model.errors[:attachments].any? { |msg| msg.include?('must be smaller than') })
  end

  test 'invalid has many attachments with all large files' do
    model = DummyModel.create(
      attachments: [
        attachable_file_fixture('too_big.pdf'),
        attachable_file_fixture('too_big.pdf')
      ]
    )

    assert(model.errors[:attachments].any? { |msg| msg.include?('must be smaller than') })
  end
end

# frozen_string_literal: true

require 'test_helper'

class JsonValidatorTest < ActiveSupport::TestCase
  class DummyModel
    include ActiveModel::Model

    attr_accessor :content
  end

  test 'is valid with valid JSON content' do
    DummyModel.validates :content, json: true

    model = DummyModel.new(content: '{"name": "<PERSON>", "age": 30}')

    assert model.valid?, 'model should be valid'
    assert_not model.errors.added?(:content, :invalid_json)
  end

  test 'is not valid with invalid JSON content' do
    DummyModel.validates :content, json: true

    model = DummyModel.new(content: '{ I am just a string }')

    assert_not model.valid?, 'model should not be valid'
    assert model.errors.added?(:content, :invalid_json)
  end

  test 'nil is not valid with invalid JSON content' do
    DummyModel.validates :content, json: true

    model = DummyModel.new(content: nil)

    assert_not model.valid?, 'model should not be valid'
    assert model.errors.added?(:content, :invalid_json)
  end
end

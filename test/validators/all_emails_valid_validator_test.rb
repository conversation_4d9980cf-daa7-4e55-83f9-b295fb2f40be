# frozen_string_literal: true

require 'test_helper'

class AllEmailsValidValidatorTest < ActiveSupport::TestCase
  class DummyModel
    include ActiveModel::Validations

    attr_reader :email_list

    def initialize(email_list)
      @email_list = email_list
    end
  end

  setup do
    @validator = AllEmailsValidValidator.new(attributes: { dummy: 'value' })
  end

  test 'a valid list of emails' do
    emails = %w[<EMAIL> <EMAIL>]
    model = DummyModel.new(emails)

    @validator.validate_each(model, :email_list, emails)
    assert_empty model.errors
  end

  test 'contains an invalid email' do
    emails = %w[<EMAIL> invalid.com]
    model = DummyModel.new(emails)

    @validator.validate_each(model, :email_list, emails)
    assert model.errors.include?(:email_list)
  end
end

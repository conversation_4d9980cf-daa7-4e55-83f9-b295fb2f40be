# frozen_string_literal: true

require 'test_helper'

class ContentTypeValidatorTest < ActiveSupport::TestCase
  class DummyModel < ApplicationRecord
    has_one_attached :file
    has_one_attached :another_file
    has_many_attached :attachments
    has_one_attached :all_types_file

    validates :file, content_type: { type: :pdf }
    validates :another_file, content_type: { type: %i[pdf doc] }
    validates :attachments, content_type: { type: %i[pdf doc] }
    validates :all_types_file, content_type: { type: :all }
  end

  setup do
    ActiveRecord::Base.connection.create_table :dummy_models
  end

  teardown do
    ActiveRecord::Base.connection.drop_table :dummy_models
  end

  test 'invalid has one for single content type' do
    model = DummyModel.create(file: attachable_file_fixture('not_a_pdf.docx'))

    assert(model.errors[:file].any? { |msg| msg.include?('must be a valid file type') })
  end

  test 'valid has one for single content type' do
    model = DummyModel.create(file: attachable_file_fixture('cv.pdf'))

    assert model.errors[:file].empty?
  end

  test 'valid has one for multiple content types' do
    model = DummyModel.create(another_file: attachable_file_fixture('not_a_pdf.docx'))

    assert model.errors[:another_file].empty?
  end

  test 'invalid has one for multiple content types' do
    model = DummyModel.create(another_file: attachable_file_fixture('free_form_data.csv'))

    assert(model.errors[:another_file].any? { |msg| msg.include?('must be a valid file type') })
  end

  test 'valid has many attachments with all valid types' do
    model = DummyModel.create(
      attachments: [
        attachable_file_fixture('cv.pdf'),
        attachable_file_fixture('not_a_pdf.docx')
      ]
    )

    assert model.errors[:attachments].empty?
  end

  test 'invalid has many attachments with one invalid type' do
    model = DummyModel.create(
      attachments: [
        attachable_file_fixture('cv.pdf'),
        attachable_file_fixture('free_form_data.csv')
      ]
    )

    assert(model.errors[:attachments].any? { |msg| msg.include?('must be a valid file type') })
  end

  test 'invalid has many attachments with all invalid types' do
    model = DummyModel.create(
      attachments: [
        attachable_file_fixture('free_form_data.csv'),
        attachable_file_fixture('free_form_data.csv')
      ]
    )

    assert(model.errors[:attachments].any? { |msg| msg.include?('must be a valid file type') })
  end

  test 'valid all types with PDF' do
    model = DummyModel.create(all_types_file: attachable_file_fixture('cv.pdf'))

    assert model.errors[:all_types_file].empty?
  end

  test 'valid all types with DOC' do
    model = DummyModel.create(all_types_file: attachable_file_fixture('not_a_pdf.docx'))

    assert model.errors[:all_types_file].empty?
  end

  test 'valid all types with CSV' do
    model = DummyModel.create(all_types_file: attachable_file_fixture('free_form_data.csv'))

    assert model.errors[:all_types_file].empty?
  end

  test 'invalid all types with JSON' do
    model = DummyModel.create(all_types_file: attachable_file_fixture('postmark_bounce.json'))

    assert(model.errors[:all_types_file].any? { |msg| msg.include?('must be a valid file type') })
  end
end

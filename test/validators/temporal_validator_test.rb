# frozen_string_literal: true

require 'test_helper'

class TemporalValidatorTest < ActiveSupport::TestCase
  class DummyModel
    include ActiveModel::Model

    attr_accessor :occurs_at
  end

  teardown do
    DummyModel.clear_validators!
  end

  test 'is temporal' do
    DummyModel.validates :occurs_at, temporal: true

    model = DummyModel.new(occurs_at: Time.current)

    assert model.valid?, 'model should be temporal'
    assert_not model.errors.added?(:occurs_at, :not_temporal)
  end

  test 'is not temporal' do
    DummyModel.validates :occurs_at, temporal: true

    model = DummyModel.new(occurs_at: 'xxx')

    assert_not model.valid?, 'model should not be temporal'
    assert model.errors.added?(:occurs_at, :not_temporal)
  end

  test 'now or in the future' do
    DummyModel.validates :occurs_at, temporal: :now_or_later

    model = DummyModel.new(occurs_at: 1.day.from_now)

    assert model.valid?, 'model should be valid'
    assert_not model.errors.added?(:occurs_at, :too_early)
  end

  test 'not now or in the future' do
    DummyModel.validates :occurs_at, temporal: :now_or_later

    model = DummyModel.new(occurs_at: 1.minute.ago)

    assert_not model.valid?
    assert model.errors.added?(:occurs_at, :now_or_later)
  end

  test 'not after' do
    before_attr_name = :occurred_before
    DummyModel.validates :occurs_at, temporal: { after: before_attr_name }

    model = DummyModel.new(occurs_at: 1.day.from_now)
    def model.occurred_before = occurs_at + 1.day

    before_attr_label = model.class.human_attribute_name(before_attr_name)

    assert_not model.valid?
    assert model.errors.added?(:occurs_at, :after, before_attr_name: before_attr_label)
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :scipi, parent: :survey do
    branding factory: %i[survey_branding scipi]
    invite_only { true }

    transient do
      # Keep at zero since most tests do not need rounds
      number_of_rounds { 0 }
    end

    after(:build) do |scipi, context|
      rounds = (1..context.number_of_rounds).map do |round_number|
        factory_name = round_number.odd? ? :question_round : :debate_round

        opens_at = 1.week.from_now + (round_number - 1).weeks
        closes_at = opens_at + 1.week

        build(factory_name, position: round_number, opens_at:, closes_at:)
      end
      scipi.rounds = rounds
    end
  end

  factory :scipi_applicant, class: 'Invite' do
    survey
    expert
  end

  factory :round, class: 'Surveys::Round' do
    survey factory: %i[scipi published]
    sequence(:name) { |n| "Round #{n}" }
    sequence(:position) { |n| n }

    transient do
      # Makes manipulating the timeframe easier in tests without having to specify exact
      # times and do date math
      duration { 7.days }
    end

    # These dates are relative (after) to the :scipi factory's :published trait's published_at date
    opens_at { 1.week.from_now }
    closes_at { duration.after(opens_at) }

    # This can be used in single round context
    trait :active do
      opens_at { 1.day.ago }
      closes_at { 1.week.from_now }
    end

    trait :closing_reminder_sent do
      closing_reminder_sent_at { 1.day.ago }
    end

    trait :no_timeframe do
      opens_at { nil }
      closes_at { nil }
    end

    trait :open_email_sent do
      after(:build) do |round|
        round.round_open_email_sent_at = round.opens_at + 1.hour
      end
    end

    factory :question_round, parent: :round, class: 'Surveys::Rounds::Question' do
      trait :midpoint_reminder_sent do
        midpoint_reminder_sent_at { 1.day.ago }
      end
    end

    factory :debate_round, parent: :round, class: 'Surveys::Rounds::Debate' do
      trait :last_debate_summary_sent do
        last_debate_summary_sent_at { opens_at + 2.days }
      end
    end
  end

  factory :scipi_report, class: 'Scipis::Report' do
    scipi factory: %i[scipi]
    created_by factory: %i[scipi_admin]

    prepared_for { 'Test Client' }
  end
end

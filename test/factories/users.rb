# frozen_string_literal: true

# General user, expert, profile, etc. factories go here.
FactoryBot.define do
  # Base user factory, should rarely be used directly.
  factory :user do
    email { Faker::Internet.email }
    first_name { '<PERSON>' }
    last_name { 'Doe' }
    password { 'secret123' }

    trait :confirmed do
      confirmed_at { Time.zone.now }
      confirmation_token { nil }
    end

    trait :default_scipi_owner do
      default_scipi_owner { true }
    end

    trait :distribution_list_manager do
      roles { [association(:role, :manage_distribution_lists)] }
    end

    trait :neverbounce_validated do
      last_neverbounce_check_at { 1.week.ago }
      last_neverbounce_status { 'valid' }
    end

    trait :ping_participant do
      roles { Role.experts }
    end

    trait :reset_password_requested do
      reset_password_token { SecureRandom.base58(36) }
      reset_password_sent_at { Time.zone.now }
    end

    trait :reset_password_expired do
      reset_password_requested
      reset_password_sent_at { (reset_password_expires_after + 1.hour).ago }
    end

    trait :unconfirmed do
      confirmed_at { nil }
      confirmation_token { SecureRandom.base58(36) }
    end

    trait :unverified do
      after(:build) { |user| user.profile.verification_status = VerificationStatus.pending }
    end

    trait :with_display_id do
      sequence(:display_id) { |n| "#{first_name}.#{last_name}#{n}" }
    end

    trait :with_display_name do
      after(:build) { |user| user.profile.display_name = "#{Faker::Internet.username}-#{rand(1..10_000)}" }
    end
  end

  factory :observer, parent: :user do
    roles { Role.observers }
    confirmed
  end

  # Base expert factory, this should be used for most cases. The factory assumes
  # confirmed by default. Use the :unconfirmed trait when testing scenarios
  # that require an unconfirmed expert.
  factory :expert, parent: :user do
    roles { Role.experts }
    confirmed
    with_display_id

    trait :autosubscribe_disabled do
      disabled_autosubscribe_at { Time.current }
    end

    trait :complete_profile do
      country
      education_complete
      employment_complete
      expertise_complete
    end

    trait :creator do
      admin { false }
      creator { true }
    end

    trait :education_complete do
      after(:create) do |expert|
        create_list(:degree, 1, expert:)
        expert.reload
      end
    end

    trait :employment_complete do
      current_employer { Faker::Company.name }
      current_employment_sector factory: :employment_sector
      title { Faker::Job.title }
      before(:create) do |expert|
        expert.employment_history_items.build(
          employment_sector_id: expert.current_employment_sector.id,
          years: rand(1..10)
        )
      end
    end

    trait :expertise_complete do
      after(:create) do |expert|
        expert.profile.update!(
          publications_count: Faker::Number.between(from: 100, to: 300),
          publications_first_last_count: Faker::Number.between(from: 1, to: 99),
          expertises: [FactoryBot.create(:expertise)]
        )
      end
      with_cv
    end

    trait :out_of_ping_credit do
      after(:create) do |expert|
        create_list(:ping, 1, author: expert)
        # expert.reload
      end
    end

    trait :retired do
      current_employment_sector factory: %i[employment_sector], include_in_total: false # include_in_total false means retired
    end

    trait :stale_cv do
      after(:create) do |expert|
        expert.profile.cv.update!(created_at: (Profile::CV_STALE_THRESHOLD_DAYS + 1.day).days.ago)
      end
    end

    trait :with_cv do
      cv { Rack::Test::UploadedFile.new("#{ActiveSupport::TestCase.file_fixture_path}/cv.pdf") }
    end
  end

  factory :db_admin, parent: :user do
    roles { [association(:role, :db_admin)] }
  end

  factory :guest, parent: :user do
    email { nil }
    password { nil }
    roles { [association(:role, :guest)] }
  end

  factory :pings_admin, parent: :user do
    confirmed
    roles { Role.where(pings_admin: true) }
  end

  factory :scipi_admin, parent: :user do
    roles { [association(:role, :scipi_admin)] }
  end

  factory :scipoll_admin, parent: :user do
    roles { [association(:role, :scipoll_admin)] }
  end

  factory :creator, parent: :user do
    creator { true }
    confirmed
    with_display_id
  end

  factory :super_admin, parent: :user, aliases: %i[admin] do
    admin { true }
    confirmed
    with_display_id

    after(:create) do |user, _evaluator|
      user.roles = [create(:role, :admin)]

      user.reload
    end
  end

  factory :certification do
    expert
    name { Faker::Lorem.sentence(word_count: 4) }
  end

  factory :country do
    region
    name { Faker::Address.country }
    two_letter_code { Faker::Address.country_code }
    three_letter_code { Faker::Address.country_code_long }
    numeric_code { rand(1..999).to_s.rjust(3, '0') }
  end

  factory :degree do
    expert
    degree_type
    subject_area { TestData::Degree.subject_area }
    graduation_year { TestData::Degree.grad_year }
  end

  factory :degree_type do
    sequence(:name) { |n| "Degree Type #{n}" }

    trait :doctorate do
      doctorate { true }
    end
  end

  factory :employment_sector do
    sequence(:name) { |n| "Employment Sector #{n}" }
  end

  factory :expertise do
    sequence(:name) { |n| "Expertise #{n}" }

    trait :non_profile do
      profile { false }
    end
  end

  factory :profile do
    expert { association :expert, profile: instance }
    display_name { "#{Faker::Internet.username}-#{rand(0..1000)}" }

    trait :employment_complete do
      expert factory: %i[expert employment_complete]
    end

    trait :with_cv do
      cv { Rack::Test::UploadedFile.new("#{ActiveSupport::TestCase.file_fixture_path}/cv.pdf") }
    end
  end

  factory :region do
    sequence(:name) { |n| "Region #{n}" }
  end

  factory :role do
    sequence(:name) { |n| "Role #{n}" }
    description { 'This is a role description' }

    trait :default_on_signup do
      default_on_signup { true }
    end

    trait :available_for_signup do
      available_for_signup { true }
    end

    trait :admin do
      admin { true }
      manage_distribution_lists { true }
      internal
    end

    trait :db_admin do
      internal
      verifier { true }
    end

    trait :expert do
      expert { true }
    end

    trait :guest do
      guest { true }
      require_credentials { false }
    end

    trait :internal do
      internal { true }
    end

    trait :manage_distribution_lists do
      internal
      manage_distribution_lists { true }
    end

    trait :observer do
      observer { true }
    end

    trait :pings_admin do
      internal
      pings_admin { true }
    end

    trait :scipi_admin do
      internal
      scipi_admin { true }
    end

    trait :scipoll_admin do
      internal
      scipoll_admin { true }
    end
  end

  factory :note do
    content { Faker::Lorem.paragraph }
    expert
    disposition { 0 }
    created_by factory: %i[db_admin]

    trait :positive do
      disposition { 1 }
    end
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :custom_result, aliases: %w[result_definition] do
    scipi
    sequence(:title) { |n| "Result Definition #{n}" }
    result_type
    render_type { result_type.default_render_type }

    trait :diagnostic do
      result_type { association :result_type, :diagnostic }
    end

    trait :multi_question_score do
      result_type { association :result_type, :multi_question_score }
      ranked_order_plot_render_type

      title { 'Multi-question score' }
    end
  end

  factory :grouped_result_definition, traits: [:question], class: 'CustomResult' do
    question_group
    question
    filter_by_question { create(:question, question_group:).id }
    render_type factory: %i[render_type bar_chart]
    result_type
  end

  trait :question do
    question
  end

  trait :free_form do
    title { 'Free form result' }
    result_type { association :result_type, :free_form }
    y_label { 'Foo bar' }
  end

  trait :file_attachment do
    title { 'File attachment' }
    attachment { Rack::Test::UploadedFile.new("#{ActiveSupport::TestCase.file_fixture_path}/test_attachment.pdf") }
    result_type { association :result_type, :file_attachment }
  end

  trait :upload_list do
    sequence(:title) { |n| "Upload List #{n}" }
    result_type { association :result_type, :participant_upload_list }
  end

  trait :multiple_choice_responses do
    result_type factory: %i[result_type multiple_choice_responses]
  end

  trait :pie_chart do
    render_type factory: %i[render_type doughnut_chart]
  end

  trait :bar_chart do
    question factory: %i[radio]
    render_type factory: %i[render_type bar_chart]
  end

  trait :ranked_order_plot_render_type do
    render_type factory: %i[render_type ranked_order_plot]
  end

  trait :file_download_render_type do
    render_type factory: %i[render_type file_download]
  end

  trait :non_summarizable do
    # Uses a result_type with a system_name that's not in SUPPORTED_RESULT_TYPES
    result_type { association :result_type, system_name: 'unsupported_type' }
  end

  factory :text_result_definition, class: 'CustomResult', traits: [:question] do
    question_group
    question factory: %i[long_question]
    result_type factory: %i[result_type free_form]
    render_type factory: %i[render_type text_responses]
  end

  factory :result_type, class: 'ResultDefinitionType' do
    sequence(:name) { |n| "Result Type #{n}" }
    sequence(:system_name) { |n| "result_type_#{n}" }
    default_render_type factory: %i[render_type]

    trait :descriptive_statistics do
      descriptive_statistics { true }
    end

    trait :answers_grouped_by_region do
      system_name { ResultDefinitionType::ANSWERS_GROUPED_BY_REGION_NAME }
      default_render_type factory: %i[render_type bar_chart]
    end

    trait :answers_grouped_by_sector do
      system_name { ResultDefinitionType::ANSWERS_GROUPED_BY_SECTOR_NAME }
      default_render_type factory: %i[render_type bar_chart]
    end

    trait :answers_grouped_by_years_of_experience do
      system_name { ResultDefinitionType::ANSWERS_GROUPED_BY_YEARS_OF_EXPERIENCE_NAME }
      default_render_type factory: %i[render_type bar_chart]
    end

    trait :diagnostic do
      system_name { ResultDefinitionType::DIAGNOSTIC_NAME }
      default_render_type factory: %i[render_type diagnostic]
    end

    trait :file_attachment do
      system_name { ResultDefinitionType::FILE_ATTACHMENT_NAME }
      default_render_type factory: %i[render_type file_download]
      render_types { [association(:render_type, :file_download)] }
    end

    trait :free_form do
      descriptive_statistics
      system_name { ResultDefinitionType::FREE_FORM_NAME }
      default_render_type factory: %i[render_type ranked_order_plot]
    end

    trait :grid_responses do
      system_name { ResultDefinitionType::GRID_RESPONSES_NAME }
      default_render_type factory: %i[render_type grid_responses]
    end

    trait :grouped_multiple_choice_responses do
      system_name { ResultDefinitionType::GROUPED_MULTIPLE_CHOICE_RESPONSES_NAME }
      default_render_type factory: %i[render_type bar_chart]
    end

    trait :multi_question_score do
      descriptive_statistics
      system_name { ResultDefinitionType::MULTI_QUESTION_SCORE_NAME }
      default_render_type factory: %i[render_type ranked_order_plot]
    end

    trait :participant_upload_list do
      system_name { ResultDefinitionType::PARTICIPANT_UPLOAD_LIST_NAME }
      default_render_type factory: %i[render_type file_list]
      render_types { [association(:render_type, :file_list)] }
    end

    trait :multiple_choice_responses do
      system_name { ResultDefinitionType::MULTIPLE_CHOICE_RESPONSES_NAME }
      default_render_type factory: %i[render_type doughnut_chart]
    end

    trait :ranked_choice do
      system_name { ResultDefinitionType::RANKED_CHOICE_NAME }
      default_render_type factory: %i[render_type horizontal_bar_chart]
    end

    trait :scitrust_score do
      system_name { ResultDefinitionType::SCITRUST_SCORE_NAME }
      default_render_type factory: %i[render_type horizontal_bar_chart]
    end

    trait :text_responses do
      system_name { ResultDefinitionType::TEXT_RESPONSES_NAME }
      default_render_type factory: %i[render_type text_responses]
    end

    trait :submission_score do
      descriptive_statistics
      system_name { ResultDefinitionType::SUBMISSION_SCORE_NAME }
      default_render_type factory: %i[render_type ranked_order_plot]
    end

    trait :weighted_sum_scores do
      descriptive_statistics
      system_name { ResultDefinitionType::WEIGHTED_SUM_SCORES_NAME }
      default_render_type factory: %i[render_type ranked_order_plot]
    end
  end

  factory :render_type, class: 'ResultDefinitionRenderType' do
    sequence(:name) { |n| "Render Type #{n}" }

    trait :bar_chart do
      name { ResultDefinitionRenderType::BAR_CHART }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :diagnostic do
      name { ResultDefinitionRenderType::DIAGNOSTIC }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :doughnut_chart do
      name { ResultDefinitionRenderType::DOUGHNUT_CHART }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :grid_responses do
      name { ResultDefinitionRenderType::GRID_RESPONSES }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :horizontal_bar_chart do
      name { ResultDefinitionRenderType::HORIZONTAL_BAR_CHART }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :ranked_order_plot do
      name { ResultDefinitionRenderType::RANKED_ORDER_PLOT }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :file_download do
      name { ResultDefinitionRenderType::FILE_DOWNLOAD }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :file_list do
      name { ResultDefinitionRenderType::FILE_LIST }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end

    trait :text_responses do
      name { ResultDefinitionRenderType::RENDER_TEXT }

      # Make singleton instance
      initialize_with do
        ResultDefinitionRenderType.find_or_create_by!(name:)
      end
    end
  end

  factory :result_section, class: 'ResultSection' do
    survey
    sequence(:name) { |n| "Result Section #{n}" }
    sequence(:position) { |n| n }
  end

  factory :summary do
    custom_result
    content { '<p>AI generated summary content</p>' }
  end
end

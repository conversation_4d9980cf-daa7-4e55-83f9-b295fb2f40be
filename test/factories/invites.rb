# frozen_string_literal: true

FactoryBot.define do
  factory :invite, aliases: %i[applicant] do
    survey factory: %i[survey invite_only]
    applicant_statement { Faker::Lorem.sentence }
    user

    factory :panelist, parent: :invite do
      approved_at { Time.zone.now }

      trait :contract_signed do
        contract_signed_at { Time.zone.now }
      end

      trait :inactive do
        # Just one reason why a panelist might not be active
        suspended
      end

      trait :suspended do
        suspended_at { Time.zone.now }
      end
    end
  end

  trait :approved do
    approved_at { Time.zone.now }
  end

  trait :observer do
    observer { true }
  end

  trait :rejected do
    rejected_at { Time.current }
  end
end

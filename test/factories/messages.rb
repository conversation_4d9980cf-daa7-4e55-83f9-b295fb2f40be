# frozen_string_literal: true

FactoryBot.define do
  factory :message do
    user factory: %i[expert]
    sent_by factory: %i[admin]
    content { Faker::Lorem.paragraph }
    sent_from_address { Faker::Internet.email }
    subject { Faker::Lorem.sentence }

    trait :with_survey do
      survey factory: %i[scipi]
    end

    trait :resolved do
      last_resolved_at { 1.hour.ago }
      last_resolved_by factory: %i[admin]
    end

    trait :unresolved do
      last_resolved_at { nil }
      last_resolved_by { nil }
    end

    trait :sent_by_expert do
      sent_by { user }
    end

    trait :reply do
      parent factory: %i[message]
      subject { nil }
    end
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :ping do
    author factory: %i[expert confirmed with_display_name]
    title { Faker::Lorem.question }
    content { Faker::Lorem.paragraphs.join }
    type { Pings::PingType.default }

    after(:build) do |ping|
      ping.expertises << FactoryBot.build(:expertise)
    end

    after(:create) do |ping, _evaluator|
      # Loads the generated UUID token
      ping.reload
    end

    trait :allows_drafts do
      paid
      type { association :ping_type, :allows_drafts }
    end

    trait :allows_multiple_answers do
      paid
      type { association :ping_type, :allows_multiple_answers }
    end

    trait :allows_voting_deadlines do
      paid
      type { association :ping_type, :allows_voting_deadlines }
    end

    trait :answer_accepted do
      after(:create) { |ping| create(:ping_answer, :accepted, ping:) }
    end

    trait :disallows_voting_deadlines do
      type { association :ping_type, :disallows_voting_deadlines }
    end

    trait :downvoting_disabled do
      paid
      type { association :ping_type, :downvoting_disabled }
    end

    trait :draft do
      allows_drafts
      published_at { nil }
    end

    trait :outside_change_window do
      created_at { (Ping::EDIT_WINDOW_DURATION + 1).ago }
    end

    trait :published do
      published_at { (1..10).to_a.sample.days.ago }
    end

    trait :promoted do
      promote_until { 1.week.from_now }
    end

    trait :hidden do
      hidden_at { 1.day.ago }
      hidden_by factory: :admin
    end

    trait :paid do
      type { Pings::PingType.find_by!(paid: true) }
      reward_amount { rand(100..1000) }
    end

    trait :unsent_open_voting_notification do
      paid
      voting_open
    end

    trait :voting_closed do
      published
      voting_opens_at { 2.weeks.ago }
      voting_closes_at { 1.week.ago }
    end

    trait :voting_not_yet_closed do
      published
      voting_opens_at { 1.week.ago }
      voting_closes_at { 1.week.from_now }
    end

    trait :voting_unopened do
      published
      voting_opens_at { TestData.any_future_time }
    end

    trait :voting_open do
      published
      voting_opens_at { 1.minute.ago }
    end
  end

  factory :ping_type, class: 'Pings::PingType' do
    sequence(:name) { |n| "Ping Type #{n}" }

    trait :allows_drafts do
      paid
    end

    trait :allows_multiple_answers do
      paid
    end

    trait :allows_voting_deadlines do
      paid
    end

    trait :disallows_voting_deadlines do
      paid { false }
    end

    trait :downvoting_disabled do
      paid
    end

    trait :paid do
      paid { true }
    end
  end

  factory :ping_answer, class: 'Pings::Answer' do
    ping
    author factory: %i[expert confirmed with_display_name]
    content { Faker::Lorem.paragraphs.join }

    transient do
      upvote_count { 0 }
    end

    after(:create) do |answer, evaluator|
      evaluator.upvote_count.times do
        voter = create(:user)
        answer.upvote!(voter)
      end
    end

    trait :accepted do
      accepted_at { Time.current }
    end

    trait :hidden do
      hidden_at { Time.current }
    end
  end

  factory :ping_vote, class: 'Pings::Vote' do
    answer factory: :ping_answer
    voter factory: :expert

    trait :upvote do
      value { 1 }
    end

    trait :downvote do
      value { -1 }
    end
  end
end

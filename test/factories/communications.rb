# frozen_string_literal: true

FactoryBot.define do
  factory :distribution_list, aliases: %i[subscribable], class: 'Communications::DistributionList' do
    sequence(:name) { |n| "Distro list #{n}" }
    position { 1 }
    description { 'I am list' }
    admin_notes { 'Important info for admins' }
    postmark_stream_id { 'test-stream' }
    roles { [create(:role, :available_for_signup)] }

    after(:create) do |list, _evaluator|
      # Loads the generated UUID token
      list.reload
    end

    trait :allow_unconfirmed do
      allow_unconfirmed { true }
    end

    trait :suspended do
      delivery_suspended_at { Time.current }
    end
  end
end

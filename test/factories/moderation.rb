# frozen_string_literal: true

FactoryBot.define do
  factory :moderation_reason do
    name { Faker::Lorem.word }
    description { Faker::Lorem.sentence }
    require_comment { false }
    sequence(:position)

    trait :require_comment do
      require_comment { true }
    end

    trait :user_facing do
      internal { false }
    end
  end

  factory :moderation_request do
    reason factory: %i[moderation_reason]
    submitted_by factory: %i[expert]
    subject factory: %i[ping]
    comment { Faker::Lorem.sentence }
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :contract_template do
    added_by { FactoryBot.create(:admin) }
    signnow_field_definitions do
      <<~FIELDS.squish
        [{
          "tag_name": "panelist_signature",
          "role": "signer",
          "label": "Expert Panelist Signature",
          "required": true,
          "type": "signature",
          "width": 200,
          "height": 20
        }]
      FIELDS
    end
    template_file { Rack::Test::UploadedFile.new("#{ActiveSupport::TestCase.file_fixture_path}/signnow_test_template.docx") }
  end
end

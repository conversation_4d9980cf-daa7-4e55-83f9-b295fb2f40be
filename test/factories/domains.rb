# frozen_string_literal: true

FactoryBot.define do
  factory :domain_list do
    sequence(:name) { |n| "Domain List #{n}" }

    trait :known_institution do
      allowed_institutional_domain { true }
      known_institution { true }
    end

    trait :unknown_institution do
      allowed_institutional_domain { true }
    end
  end

  factory :domain do
    sequence(:hostname) { |n| "example#{n}.com" }

    trait :known_institution do
      list factory: %i[domain_list known_institution]
    end

    trait :unknown_institution do
      list factory: %i[domain_list unknown_institution]
    end
  end
end

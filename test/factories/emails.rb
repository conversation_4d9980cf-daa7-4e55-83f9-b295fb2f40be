# frozen_string_literal: true

FactoryBot.define do
  factory :email do
    profile
    address { Faker::Internet.email }
    role { Email::PRIMARY_ROLE }

    trait :additional do
      role { Email::ADDITIONAL_ROLE }
    end

    trait :confirmed do
      after(:create) { |email| email.confirmations.first.update!(confirmed_at: Time.current) }
    end

    trait :unconfirmed do
      after(:build) { |email| email.confirmations << build(:email_confirmation, :unconfirmed) }
    end
  end

  factory :email_confirmation do
    email

    trait :confirmed do
      confirmed_at { Time.current }
    end

    trait :unconfirmed do
      confirmed_at { nil }
    end
  end
end

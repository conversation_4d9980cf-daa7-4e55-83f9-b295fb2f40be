# frozen_string_literal: true

FactoryBot.define do
  factory :announcement do
    author factory: %i[scipi_admin]
    title { Faker::Lorem.sentence }
    content { Faker::Lorem.paragraph }

    trait :draft do
      published_at { nil }
    end

    trait :promotable do
      promote_until { TestData.any_future_time }
      published_at { TestData.any_past_time }
    end

    trait :published do
      published_at { TestData.any_past_time }
    end

    trait :unpromotable do
      promote_until { TestData.any_past_time }
      published_at { TestData.any_past_time }
    end
  end
end

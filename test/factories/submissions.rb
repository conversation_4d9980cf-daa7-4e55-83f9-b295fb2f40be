# frozen_string_literal: true

FactoryBot.define do
  factory :submission, class: 'AnswerGroup' do
    question_group
    submitter factory: %i[expert]
    # Get rid of me some day. This should not be the default.
    submitted_at { Time.zone.now }

    factory :submission_with_text_answers do
      after(:create) do |submission, _evaluator|
        submission.question_group.questions.each do |question|
          create(:answer, text_answer_content: Faker::Lorem.sentence, answer_group: submission, question:)
        end
      end
    end

    trait :deleted do
      submitted_at { nil }
      deleted_at { Time.zone.now }
    end

    trait :draft do
      submitted_at { nil }
      deleted_at { nil }
    end

    trait :final do
      submitted_at { Time.zone.now }
      deleted_at { nil }
    end
  end
end

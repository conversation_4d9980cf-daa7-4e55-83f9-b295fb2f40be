# frozen_string_literal: true

FactoryBot.define do
  factory :answer do
    answer_group factory: %i[submission]
    question

    factory :grid_answer do
      question factory: %i[grid_question]
    end

    factory :skipped_checkbox do
      question factory: %i[checkbox]
    end

    factory :skipped_radio do
      question factory: %i[radio]
    end

    factory :selected_choice do
      answer
      answer_choice
      rank { nil }
    end
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :survey_branding, class: 'Surveys::Branding' do
    sequence(:name) { |n| "Brand #{n}" }

    trait :scipi do
      name { Surveys::Branding::SCIPI_NAME }

      # Make singleton instance
      initialize_with do
        Surveys::Branding.find_or_create_by!(name:)
      end
    end

    trait :scipoll do
      name { Surveys::Branding::SCIPOLL_NAME }

      # Make singleton instance
      initialize_with do
        Surveys::Branding.find_or_create_by!(name:)
      end
    end

    # Generic branding for legacy surveys
    trait :legacy do
      name { Surveys::Branding::LEGACY_NAME }

      # Make singleton instance
      initialize_with do
        Surveys::Branding.find_or_create_by!(name:)
      end
    end
  end

  # Using the :scipi alias is deprecated because it implies scipi-specific behaviors
  # Eventually we will have a separate scipi factory.
  # In the meantime, use :survey with :scipi trait when you actually want a SciPi
  factory :question_group, aliases: %i[survey] do
    branding factory: %i[survey_branding]
    sequence(:name) { |n| "Survey Number #{n}" }
    created_by factory: %i[scipi_admin]

    transient do
      owners { [] }
    end

    after(:build) do |survey, evaluator|
      evaluator.owners.each do |owner|
        survey.ownerships.build(owner: owner, notify: owner == survey.created_by)
      end

      if survey.ownerships.empty?
        survey.ownerships.build(owner: survey.created_by, notify: true)
      elsif survey.ownerships.none?(&:notify?)
        creator_ownership = survey.ownerships.find { |o| o.owner == survey.created_by }
        if creator_ownership
          creator_ownership.notify = true
        else
          survey.ownerships.first.notify = true
        end
      end
    end

    trait :access_code_only do
      sequence(:access_code) { |n| "code-#{n}" }
    end

    trait :allows_guests do
      allow_guest_participation { true }
    end

    trait :closed do
      # Closed yesterday at EOD SST time. See #open?
      closes_at { Time.find_zone('Pacific/Pago_Pago').yesterday }
    end

    trait :comments_closed do
      comments_close_date { 2.days.ago }
    end

    trait :contract_required do
      contract_required { true }
    end

    trait :draft do
      published_at { nil }
    end

    trait :invite_only do
      invite_only { true }
      access_code { nil }
    end

    trait :legacy do
      branding { association :survey_branding, :legacy }
    end

    trait :observer_exports do
      observer_exports { true }
    end

    trait :open do
      published
      closes_at { 4.months.from_now }
    end

    trait :general_participation do
      access_code { nil }
      invite_only { false }
    end

    trait :published do
      published_at { 1.week.ago }
    end

    trait :scheduled do
      published_at { 1.week.from_now }
    end

    trait :recruitment_closed do
      recruitment_closes_on { 2.days.ago }
    end

    trait :recruitment_open do
      recruitment_closes_on { 1.week.from_now }
    end

    trait :selection_closed do
      selection_closes_on { 1.day.ago }
    end

    trait :selection_open do
      published
      selection_closes_on { 1.week.from_now }
    end

    trait :results_conclusion do
      results_conclusion { '<p>Results Conclusion</p>' }
    end

    trait :results_published do
      published
      results_published { true }
    end

    trait :results_unpublished do
      published
      results_published { false }
    end

    trait :results_public do
      published
      results_published { true }
      results_public { true }
    end

    trait :result_access_code_required do
      result_access_code { 'ABC123' }
    end

    trait :scipi do
      branding { association :survey_branding, :scipi }
      invite_only { true }
    end

    # for when we need full survey capabilities with just scipoll branding
    trait :scipoll do
      branding { association :survey_branding, :scipoll }
    end

    trait :unlisted do
      unlisted { true }
    end

    trait :unpaid do
      paid { false }
    end
  end

  factory :invite_only, class: 'QuestionGroup' do
    sequence(:name) { |n| "Invite Survey #{n}" }
    invite_only { true }
    created_by factory: %i[admin]
  end

  factory :question_section, class: 'Section' do
    survey
    name { 'MyText' }
    sequence(:position) { |n| n }

    trait :hidden do
      hidden { true }
    end
  end

  factory :scipi_panelist, class: 'Invite' do
    scipi
    expert
    approved_at { Time.current }
  end

  factory :question do
    survey
    question_text { "#{Faker::Lorem.question(word_count: 12)}?" }
    sequence(:position) { |n| n }

    factory :long_question, class: 'Questions::Long' do
      type { Questions::Long.name }
    end

    factory :grid_question, class: 'Questions::Grid', traits: [:multiple_choice] do
      type { Questions::Grid.name }
      # grid_structure

      transient do
        answer_choice_count { 2 }
      end

      trait :radio do
        after(:create) do |question, _evaluator|
          create(:grid_structure, :radio, question:)
        end
        # association :grid_structure, :radio
      end

      trait :checkbox do
        grid_structure factory: %i[grid_structure checkbox]
      end

      trait :select do
        grid_structure factory: %i[grid_structure select]
        with_answer_choices_with_value
      end
    end

    # Use this factory to create a generic :grid_question with traits
    factory :grid_select_question, class: 'Questions::Grid', traits: [:multiple_choice] do
      survey
      grid_structure factory: %i[grid_structure select]
    end

    factory :profile_country_question, class: 'Questions::ProfileCountry' do
      type { Questions::ProfileCountry.name }
    end

    factory :profile_sector_question, class: 'Questions::ProfileEmploymentSector' do
      type { Questions::ProfileEmploymentSector.name }
    end

    factory :profile_years_of_experience_question, class: 'Questions::ProfileYearsOfExperience' do
      type { Questions::ProfileYearsOfExperience.name }
    end

    factory :radio, class: 'Questions::Radio', traits: [:multiple_choice] do
      transient do
        answer_choice_count { 3 }
      end

      type { Questions::Radio.name }
    end

    factory :checkbox, class: 'Questions::Checkbox', traits: [:multiple_choice] do
      type { Questions::Checkbox.name }
    end

    factory :ranked_choice, class: 'Questions::RankedChoice', traits: [:multiple_choice] do
      type { Questions::RankedChoice.name }
    end

    factory :file_upload_question, class: 'Questions::FileUpload'

    trait :text do
      type { Questions::Long.name }
    end

    trait :multiple_choice do
      transient do
        answer_choice_count { 3 }
      end
      before(:create) do |question, evaluator|
        evaluator.answer_choice_count.times { |i| question.answer_choices << build(:answer_choice, position: i + 1) }
      end
    end

    trait :with_answer_choices_with_value do
      transient do
        answer_choice_count { 2 }
      end
      before(:create) do |question, evaluator|
        question.answer_choices.clear # remove existing
        evaluator.answer_choice_count.times do |i|
          question.answer_choices << create(
            :answer_choice,
            :with_value,
            position: i + 1
          )
        end
      end
    end

    trait :scorable do
      weighted
      with_answer_choices_with_value
    end

    trait :weighted do
      weight { rand.round(1) }
    end
  end

  factory :answer_choice do
    question
    sequence(:position)
    sequence(:label) { |n| "label#{n}" }

    trait :with_value do
      value { rand.round(1) }
    end
  end

  factory :grid_structure do
    question factory: %i[grid_question]
    first_cell_text { 'MyString' }
    input_type { 'Radio' }
    row_headers { %w[row1 row2 row3] }
    column_headers { %w[col1 col2 col3] }

    trait :checkbox do
      input_type { 'Checkbox' }
    end

    trait :radio do
      input_type { 'Radio' }
    end

    trait :select do
      input_type { 'Select' }
    end

    trait :weight_sum_grid do
      select
      row_headers { ['row1::0.2', 'row2::0.3', 'row3::0.5'] }
      column_headers { ['col::0.4', 'col::0.2', 'col3::0.4'] }
      weighted_sum { true }
      weighted_sum_label { Faker::Lorem.words }
    end
  end
end

# frozen_string_literal: true

FactoryBot.define do
  factory :scipoll, class: 'QuestionGroup' do
    sequence(:name) { |n| "Scipoll #{n}" }
    branding { association :survey_branding, :scipoll }
    created_by factory: %i[scipoll_admin]

    trait :access_token_required do
      access_token_count { 1 } # Overwrite this for more, but having at least one is required for validation
      access_token_required { true }
    end

    trait :allow_guest_participation do
      allow_guest_participation { true }
    end

    trait :closed do
      closes_at { Time.in_last_time_zone.yesterday }
    end

    trait :draft do
      published_at { nil }
    end

    trait :open do
      published
      closes_at { 1.month.from_now }
    end

    trait :published do
      published_at { 1.week.ago }
    end

    trait :scheduled do
      published_at { 1.week.from_now }
    end

    trait :results_public do
      results_public { true }
    end

    trait :results_published do
      results_published { true }
    end

    trait :unlisted do
      unlisted { true }
    end
  end
end

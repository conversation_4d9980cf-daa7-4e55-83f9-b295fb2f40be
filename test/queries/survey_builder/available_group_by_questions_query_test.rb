# frozen_string_literal: true

require 'test_helper'

module SurveyBuilder
  class AvailableGroupByQuestionsQueryTest < ActiveSupport::TestCase
    test 'get available questions for group-able question' do
      survey = FactoryBot.create(:survey)
      base_question = FactoryBot.create(:radio, survey:)
      result_definition = FactoryBot.create(
        :result_definition,
        question: base_question,
        survey:
      )
      radio = FactoryBot.create(:radio, survey:)
      text = FactoryBot.create(:long_question, survey:)
      in_other_survey = FactoryBot.create(:radio)

      query = AvailableGroupByQuestionsQuery.new(result_definition)
      results = query.results

      assert_includes(
        results,
        radio,
        'should include multiple choice questions from the same survey'
      )

      assert_not_includes(
        results,
        base_question,
        'should not include its base question'
      )
      assert_not_includes(
        results,
        text,
        'should not include non-radio button questions'
      )
      assert_not_includes(
        results,
        in_other_survey,
        'should not include questions from other surveys'
      )
    end
  end
end

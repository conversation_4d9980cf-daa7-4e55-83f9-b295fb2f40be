# frozen_string_literal: true

require 'test_helper'

module Pings
  class SuggestionsTest < ActiveSupport::TestCase
    test 'suggested pings for expert' do
      expertise1 = expertises(:expertise1)
      expertise2 = expertises(:expertise2)

      most_related = create_ping!(title: 'Most related', expertises: [expertise1, expertise2])
      related = create_ping!(title: 'Related', expertises: [expertise1])
      not_related = create_ping!(title: 'Not related')

      expert = users(:expert)
      expert.profile.expertises = [expertise1, expertise2]

      pings = Suggestions.call(expert)

      assert_equal most_related, pings[0]
      assert_equal related, pings[1]
      assert_equal not_related, pings[2]
    end

    test 'related to another Ping' do
      expertise1 = expertises(:expertise1)
      expertise2 = expertises(:expertise2)

      current_ping = create_ping!(title: 'Current', expertises: [expertise1, expertise2])

      not_related = create_ping!(title: 'Not related')
      most_related = create_ping!(title: 'Most related', expertises: [expertise1, expertise2])
      related = create_ping!(title: 'Related', expertises: [expertise1])

      expert = users(:expert)

      pings = Suggestions.call(expert, related_to: current_ping)

      assert_equal most_related, pings[0]
      assert_equal related, pings[1]
      assert_equal not_related, pings[2]
    end

    test 'related profile expertise is a tie breaker' do
      expertise1 = expertises(:expertise1)
      expertise2 = expertises(:expertise2)

      current_ping = create_ping!(title: 'Current', expertises: [expertise1])

      not_related = create_ping!(title: 'Not related')
      related = create_ping!(title: 'Related', expertises: [expertise1])
      most_related = create_ping!(title: 'Most related', expertises: [expertise1, expertise2])

      expert = users(:expert)
      expert.profile.expertises = [expertise2]

      pings = Suggestions.call(expert, related_to: current_ping)

      assert_equal most_related, pings[0]
      assert_equal related, pings[1]
      assert_equal not_related, pings[2]
    end

    private

    def create_ping!(title:, expertises: [])
      # Pings have to have an expertise now
      expertises = [expertises(:expertise3)] if expertises.empty?
      Ping.create!(
        author: users(:pings_admin),
        content: 'Foobar',
        published_at: Time.current,
        type: PingType.default,
        title:,
        expertises:
      )
    end
  end
end

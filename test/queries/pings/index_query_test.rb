# frozen_string_literal: true

require 'test_helper'

module Pings
  class IndexQueryTest < ActiveSupport::TestCase
    setup do
      @user = FactoryBot.create(:user, :confirmed, :with_display_name)

      @published_ping = FactoryBot.create(:ping, :published)
      @draft_ping = FactoryBot.create(:ping, :draft)
      @paid_ping = FactoryBot.create(:ping, :published, :paid)
      @hidden_ping = FactoryBot.create(:ping, :published, :hidden)

      @answered_ping = FactoryBot.create(:ping, :published)
      FactoryBot.create(:ping_answer, ping: @answered_ping)

      @accepted_answer_ping = FactoryBot.create(:ping, :published, :answer_accepted)

      @user_ping = FactoryBot.create(:ping, :published, author: @user)

      @user_answered_ping = FactoryBot.create(:ping, :published)
      FactoryBot.create(:ping_answer, ping: @user_answered_ping, author: @user)

      @expertise = FactoryBot.create(:expertise)
      @tagged_ping = FactoryBot.create(:ping, :published)
      @tagged_ping.expertises << @expertise

      @searchable_ping = FactoryBot.create(:ping, :published, title: 'Unique searchable title')
    end

    test 'initializes with base scope' do
      query = IndexQuery.new

      assert_includes query.results, @published_ping
      assert_includes query.results, @paid_ping
      assert_includes query.results, @answered_ping
      assert_not_includes query.results, @draft_ping
      assert_not_includes query.results, @hidden_ping
    end

    test 'initializes with custom scope' do
      custom_scope = Ping.drafts
      query = IndexQuery.new(custom_scope)

      assert_includes query.results, @draft_ping
      assert_not_includes query.results, @published_ping
    end

    test 'counts records correctly' do
      query = IndexQuery.new

      visible_count = Ping.visible.published.count
      assert_equal visible_count, query.count
    end

    test 'filters by my_pings' do
      query = IndexQuery.new.apply_filter('my_pings', @user)

      assert_includes query.results, @user_ping
      assert_not_includes query.results, @published_ping
    end

    test 'my_pings returns all pings when user is nil' do
      query = IndexQuery.new.apply_filter('my_pings', nil)

      assert_includes query.results, @published_ping
      assert_includes query.results, @paid_ping
    end

    test 'filters by my_answers' do
      query = IndexQuery.new.apply_filter('my_answers', @user)

      assert_includes query.results, @user_answered_ping
      assert_not_includes query.results, @published_ping
    end

    test 'my_answers returns all pings when user is nil' do
      query = IndexQuery.new.apply_filter('my_answers', nil)

      assert_includes query.results, @published_ping
      assert_includes query.results, @paid_ping
    end

    test 'filters by paid' do
      query = IndexQuery.new.apply_filter('paid')

      assert_includes query.results, @paid_ping
      assert_not_includes query.results, @published_ping
    end

    test 'filters by answered' do
      query = IndexQuery.new.apply_filter('answered')

      assert_includes query.results, @answered_ping
      assert_includes query.results, @accepted_answer_ping
      assert_not_includes query.results, @published_ping
    end

    test 'filters by answer_accepted' do
      query = IndexQuery.new.apply_filter('answer_accepted')

      assert_includes query.results, @accepted_answer_ping
      assert_not_includes query.results, @answered_ping
      assert_not_includes query.results, @published_ping
    end

    test 'filters by unanswered' do
      query = IndexQuery.new.apply_filter('unanswered')

      assert_includes query.results, @published_ping
      assert_not_includes query.results, @answered_ping
      assert_not_includes query.results, @accepted_answer_ping
    end

    test 'uses default when filter is not recognized' do
      query = IndexQuery.new.apply_filter('not_a_real_filter')

      assert_includes query.results, @published_ping
      assert_includes query.results, @paid_ping
      assert_includes query.results, @answered_ping
    end

    test 'applies tag filter with single tag' do
      query = IndexQuery.new.apply_tag_filter(@expertise.id)

      assert_includes query.results, @tagged_ping
      assert_not_includes query.results, @published_ping
    end

    test 'applies tag filter with array of tags' do
      query = IndexQuery.new.apply_tag_filter([@expertise.id])

      assert_includes query.results, @tagged_ping
      assert_not_includes query.results, @published_ping
    end

    test 'ignores tag filter when tags are empty' do
      query = IndexQuery.new.apply_tag_filter([])

      assert_includes query.results, @published_ping
      assert_includes query.results, @paid_ping
    end

    test 'applies search filter' do
      query = IndexQuery.new.apply_search('searchable')

      assert_includes query.results, @searchable_ping
      assert_not_includes query.results, @published_ping
    end

    test 'search is case insensitive' do
      query = IndexQuery.new.apply_search('SEARCHABLE')

      assert_includes query.results, @searchable_ping
    end

    test 'ignores search when term is blank' do
      query = IndexQuery.new.apply_search('')

      assert_includes query.results, @published_ping
      assert_includes query.results, @paid_ping
    end

    test 'applies ordering' do
      promoted_ping = FactoryBot.create(:ping, :published, :promoted)

      query = IndexQuery.new.apply_ordering
      results = query.results.to_a

      assert_equal promoted_ping, results.first
    end

    test 'paginates results' do
      query = IndexQuery.new
      FactoryBot.create_list(:ping, 10, :published)

      paginated = query.paginate(page: 1, per_page: 3)
      assert_equal 3, paginated.size

      paginated = query.paginate(page: 2, per_page: 3)
      assert_equal 3, paginated.size

      page1 = query.paginate(page: 1, per_page: 5)
      page2 = query.paginate(page: 2, per_page: 5)

      assert_empty page1.to_a & page2.to_a
    end

    test 'chain filter with tag filtering' do
      paid_tagged_ping = FactoryBot.create(:ping, :published, :paid)
      paid_tagged_ping.expertises << @expertise

      query = IndexQuery.new
                        .apply_filter('paid')
                        .apply_tag_filter(@expertise.id)

      assert_includes query.results, paid_tagged_ping
      assert_not_includes query.results, @paid_ping
      assert_not_includes query.results, @tagged_ping
    end

    test 'chain filter with search' do
      answered_searchable_ping = FactoryBot.create(:ping, :published, title: 'Searchable answered ping')
      FactoryBot.create(:ping_answer, ping: answered_searchable_ping)

      query = IndexQuery.new
                        .apply_filter('answered')
                        .apply_search('searchable')

      assert_includes query.results, answered_searchable_ping
      assert_not_includes query.results, @answered_ping
      assert_not_includes query.results, @searchable_ping
    end

    test 'chain tag filter with search' do
      tagged_searchable_ping = FactoryBot.create(:ping, :published, title: 'Searchable tagged ping')
      tagged_searchable_ping.expertises << @expertise

      query = IndexQuery.new
                        .apply_tag_filter(@expertise.id)
                        .apply_search('searchable')

      assert_includes query.results, tagged_searchable_ping
      assert_not_includes query.results, @tagged_ping
      assert_not_includes query.results, @searchable_ping
    end

    test 'chain filter, tag filter, search and pagination together' do
      5.times do |i|
        ping = FactoryBot.create(:ping, :published, :paid, title: "Searchable batch #{i}")
        ping.expertises << @expertise
      end

      query = IndexQuery.new
                        .apply_filter('paid')
                        .apply_tag_filter(@expertise.id)
                        .apply_search('searchable')
                        .apply_ordering

      page1 = query.paginate(page: 1, per_page: 2)
      assert_equal 2, page1.count

      page2 = query.paginate(page: 2, per_page: 2)
      assert_equal 2, page2.count

      assert_empty page1.to_a & page2.to_a
    end
  end
end

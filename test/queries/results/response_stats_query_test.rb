# frozen_string_literal: true

require 'test_helper'

module Results
  class ResponseStatsQueryTest < ActiveSupport::TestCase
    setup do
      @survey = FactoryBot.create(:survey)
      @question = FactoryBot.create(:question, :multiple_choice, survey: @survey)

      @result_definition = FactoryBot.create(
        :result_definition,
        survey: @survey,
        question: @question
      )
    end

    test 'empty response stats' do
      stats = ResponseStatsQuery.for(@result_definition)

      assert_equal 0, stats.answer_count
      assert_equal 0, stats.total_response_count
    end

    test 'no skips' do
      answer_group = FactoryBot.create(:submission, survey: @survey)
      answer = FactoryBot.create(
        :answer,
        answer_group:,
        question: @question
      )
      answer.selected_choices.create!(
        answer:,
        answer_choice: @question.answer_choices.first
      )

      stats = ResponseStatsQuery.for(@result_definition)

      assert_equal 1, stats.answer_count
      assert_equal 1, stats.total_response_count
    end

    test 'with skips' do
      answer_group = FactoryBot.create(:submission, survey: @survey)
      FactoryBot.create(
        :answer,
        answer_group:,
        question: @question
      )

      stats = ResponseStatsQuery.for(@result_definition)

      assert_equal 0, stats.answer_count
      assert_equal 1, stats.total_response_count
    end
  end
end

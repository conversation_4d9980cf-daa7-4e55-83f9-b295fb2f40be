# frozen_string_literal: true

require 'test_helper'

module Results
  class NavQueryTest < ActiveSupport::TestCase
    test 'no active result with sections' do
      survey = create(:survey)
      # This is only a diagnostic, since ResultDefinitionType#title? will
      # return false otherwise. This can be made a generic result type once
      # that has been addressed.
      result_type = FactoryBot.create(:result_type, :diagnostic)

      section1 = survey.result_sections.create!(
        name: 'Section 1',
        position: 1
      )
      result1 = section1.result_definitions.create!(
        position: 1,
        render_type: result_type.default_render_type,
        result_type:,
        question_group_id: survey.id
      )

      section2 = survey.result_sections.create!(
        hidden: true,
        name: 'Section 2',
        position: 2
      )
      result2 = section2.result_definitions.create!(
        position: 1,
        render_type: result_type.default_render_type,
        result_type:,
        question_group_id: survey.id
      )

      nav_item11 = NavResult.new(
        hidden: false,
        ordinal: "#{section1.position}.#{result1.position}",
        result_id: result1.id,
        title: result1.title
      )
      nav_section1 = NavSection.new(
        title: section1.name,
        results: [nav_item11],
        section_id: section1.id,
        hidden: false
      )

      nav_item21 = NavResult.new(
        hidden: true,
        ordinal: "#{section2.position}.#{result2.position}",
        result_id: result2.id,
        title: result2.title
      )
      nav_section2 = NavSection.new(
        title: section2.name,
        results: [nav_item21],
        section_id: section2.id,
        hidden: true
      )

      expected_nav_model = NavModel.new([nav_section1, nav_section2])

      query = NavQuery.new(survey, load_hidden_results: true)
      nav_model = query.results

      assert_equal expected_nav_model, nav_model
    end
  end
end

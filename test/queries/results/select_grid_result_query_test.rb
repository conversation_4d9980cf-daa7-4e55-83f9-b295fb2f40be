# frozen_string_literal: true

require 'test_helper'

module Results
  class SelectGridResultQueryTest < ActiveSupport::TestCase
    test 'select grid question result' do
      answer_choice_label1 = 'label1' # From factory bot
      answer_choice_label2 = 'label2' # From factory bot
      answer_choice_label3 = 'label3'
      survey = FactoryBot.create(:survey)
      question = FactoryBot.create(:grid_question, survey:)
      question.answer_choices.create!(label: answer_choice_label3, position: 3)

      grid_structure = FactoryBot.create(
        :grid_structure,
        :select,
        column_headers: %w[col1],
        row_headers: %w[row1 row2],
        question:
      )
      grid_result_definition = FactoryBot.create(
        :result_definition,
        question:
      )

      submission1 = FactoryBot.create(:submission, :final, survey:)
      submission2 = FactoryBot.create(:submission, :final, survey:)

      answer1 = submission1.answers.create!(question:)

      answer1.update!(answer_text_on_1st_row: answer_choice_label1.to_s)
      answer1.update!(answer_text_on_2nd_row: answer_choice_label2.to_s)

      answer2 = submission2.answers.create!(question:)
      answer2.update!(answer_text_on_1st_row: answer_choice_label1.to_s)
      answer2.update!(answer_text_on_2nd_row: answer_choice_label3.to_s)

      row1_col1_item1 = Results::GridResult::CellLineItem.new(label: answer_choice_label1, count: 2, cell_total: 2)
      row1_col1_item2 = Results::GridResult::CellLineItem.new(label: answer_choice_label2, count: 0, cell_total: 2)
      row1_col1_item3 = Results::GridResult::CellLineItem.new(label: answer_choice_label3, count: 0, cell_total: 2)

      row1_cells = [
        Results::GridResult::MultiValueCell.new(line_items: [row1_col1_item1, row1_col1_item2, row1_col1_item3])
      ]

      row2_col1_item1 = Results::GridResult::CellLineItem.new(label: answer_choice_label1, count: 0, cell_total: 2)
      row2_col1_item2 = Results::GridResult::CellLineItem.new(label: answer_choice_label2, count: 1, cell_total: 2)
      row2_col1_item3 = Results::GridResult::CellLineItem.new(label: answer_choice_label3, count: 1, cell_total: 2)

      row2_cells = [
        Results::GridResult::MultiValueCell.new(line_items: [row2_col1_item1, row2_col1_item2, row2_col1_item3])
      ]

      rows = [
        Results::GridResult::Row.new(
          name: question.row_headings[0],
          cells: row1_cells,
          total: 2
        ),
        Results::GridResult::Row.new(
          name: question.row_headings[1],
          cells: row2_cells,
          total: 2
        )
      ]

      expected_result = Results::GridResult.new(
        column_names: question.column_headings.map { |h| "#{h} (n)" },
        first_cell_text: grid_structure.first_cell_text,
        id: grid_result_definition.id,
        rows:
      )

      actual_result = SelectGridResultQuery.for(grid_result_definition)

      assert_equal expected_result, actual_result
    end
  end
end

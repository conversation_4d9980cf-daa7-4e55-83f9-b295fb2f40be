# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class RankedChoiceQueryTest < ActiveSupport::TestCase
      test 'ranked choice borda count result including a partial ranking' do
        survey = create(:survey, :general_participation, :results_published)
        question_type = Questions::RankedChoice.new.class.name
        question_text = 'Rank these colors in order of preference'

        answer_choices = AnswerChoice.build_list(%w[red green blue])

        question = survey.questions.create!(
          type: question_type,
          question_text:,
          answer_choices:,
          position: 1
        )

        answer_group1 = survey.answer_groups.create!(
          user: create(:expert),
          submitted_at: Time.zone.now
        )

        answer1 = answer_group1.answers.create!(question:)
        answer1.selected_choices.create!(answer_choice: answer_choices[0], rank: 1) # red ranked 1st (3 points)
        answer1.selected_choices.create!(answer_choice: answer_choices[1], rank: 2) # green ranked 2nd (2 points)
        answer1.selected_choices.create!(answer_choice: answer_choices[2], rank: 3) # blue ranked 3rd (1 point)

        answer_group2 = survey.answer_groups.create!(
          user: create(:expert),
          submitted_at: Time.zone.now
        )

        answer2 = answer_group2.answers.create!(question:)
        answer2.selected_choices.create!(answer_choice: answer_choices[2], rank: 1) # blue ranked 1st (3 points)
        answer2.selected_choices.create!(answer_choice: answer_choices[0], rank: 2) # red ranked 2nd (2 points)
        # green is not ranked at all (0 points)

        result_definition = create(
          :result_definition,
          :question,
          survey:,
          question:,
          render_type: FactoryBot.create(:render_type, :horizontal_bar_chart)
        )

        # Expected Borda count:
        # red: 3 (from 1st rank in answer1) + 2 (from 2nd rank in answer2) = 5 points
        # blue: 1 (from 3rd rank in answer1) + 3 (from 1st rank in answer2) = 4 points
        # green: 2 (from 2nd rank in answer1) + 0 (not ranked in answer2) = 2 points
        # So the order by borda count DESC should be red, blue, green
        expected_result = {
          chartType: 'horizontal_bar_chart',
          height: 600,
          labels: %w[red blue green],
          series: [{ name: 'Points', data: [5, 4, 2] }],
          toolbar: {
            show: false,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          }
        }

        result = RankedChoiceQuery.for(result_definition)

        assert_equal expected_result, result
      end
    end
  end
end

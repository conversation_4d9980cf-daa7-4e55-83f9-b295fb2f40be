# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class FreeFormQueryTest < ActiveSupport::TestCase
      test 'free form result' do
        data_points = {
          'foo' => BigDecimal(23),
          'bar' => BigDecimal(42),
          'baz' => BigDecimal(15)
        }

        data_points.values

        result_definition = FactoryBot.create(
          :result_definition,
          :free_form
        )
        data_points.each do |label, value|
          result_definition.data_points.create!(label:, value:)
        end

        chart_data = FreeFormQuery.for(result_definition)

        expected_result = {
          chartType: 'area',
          height: 600,
          labels: %w[foo bar baz],
          toolbar: {
            show: false,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          },
          values: [23.0, 42.0, 15.0],
          yAxisLabel: 'Foo bar'
        }

        assert_equal expected_result, chart_data
      end
    end
  end
end

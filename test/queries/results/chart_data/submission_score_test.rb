# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class SubmissionScoreQueryTest < ActiveSupport::TestCase
      test 'scores' do
        FactoryBot.create(:render_type, :ranked_order_plot)
        survey = FactoryBot.create(:survey)
        question1 = FactoryBot.create(:radio, weight: 0.4)
        q1_answer_choice1 = question1.answer_choices.create(label: 'foo', value: 1, position: 1)
        q1_answer_choice2 = question1.answer_choices.create(label: 'bar', value: 2, position: 2)
        question2 = FactoryBot.create(:radio, weight: 0.6)
        q2_answer_choice1 = question2.answer_choices.create(label: 'baz', value: 2, position: 1)
        q2_answer_choice2 = question2.answer_choices.create(label: 'qux', value: 4, position: 2)

        result_definition = survey.result_definitions.create!(
          render_type: ResultDefinitionRenderType.ranked_order_plot,
          result_type: FactoryBot.create(:result_type, :submission_score)
        )

        participant1 = FactoryBot.create(:expert)
        submission1 = FactoryBot.create(:submission, :final, survey:, user: participant1)
        q1_answer = submission1.answers.create!(question: question1)
        q1_answer.selected_choices.create!(answer_choice: q1_answer_choice1) # question score == 0.4
        q2_answer = submission1.answers.create!(question: question2)
        q2_answer.selected_choices.create!(answer_choice: q2_answer_choice2) # question score == 2.4

        participant2 = FactoryBot.create(:expert)
        submission2 = FactoryBot.create(:submission, :final, survey:, user: participant2)
        q1_answer = submission2.answers.create!(question: question1)
        q1_answer.selected_choices.create!(answer_choice: q1_answer_choice2) # question score == 0.8
        q2_answer = submission2.answers.create!(question: question2)
        q2_answer.selected_choices.create!(answer_choice: q2_answer_choice1) # question score == 1.2

        data_set = DataSet.new(values: [BigDecimal('2.0'), BigDecimal('2.8')])

        expected_result = {
          chartType: 'area',
          height: 600,
          labels: [participant2.display_id, participant1.display_id],
          max: data_set.max,
          mean: data_set.mean,
          min: data_set.min,
          stdDev: data_set.standard_deviation,
          toolbar: {
            show: false,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          },
          values: [2.0, 2.8],
          yAxisLabel: 'Score'
        }

        result = SubmissionScoreQuery.new(result_definition).result

        assert_equal expected_result, result
      end
    end
  end
end

# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class GroupedMultipleChoiceResponseQueryTest < ActiveSupport::TestCase
      test 'grouped data set result' do
        survey = create(:survey, :general_participation, :results_published)

        group_answer_choices = (1..3).map { |n| FactoryBot.build(:answer_choice, position: n, value: n) }
        group_question_attrs = FactoryBot.attributes_for(
          :radio,
          answer_choices: group_answer_choices,
          position: 1
        )
        group_question = survey.questions.create!(group_question_attrs)

        FactoryBot.rewind_sequences # since position is a sequence
        answer_choices = (1..3).map { |n| FactoryBot.build(:answer_choice, position: n, value: n) }
        question_attrs = FactoryBot.attributes_for(
          :radio,
          answer_choices:,
          position: 2
        )
        question = survey.questions.create!(question_attrs)

        result_type = FactoryBot.create(:result_type, :multiple_choice_responses)

        result_def_attrs = FactoryBot.attributes_for(
          :result_definition,
          :question,
          result_type:,
          question_id: group_question.id, # these are backwards, and will eventually be fixed
          filter_by_question: question.id
        )
        result_definition = survey.result_definitions.create!(
          result_def_attrs.merge(
            render_type: FactoryBot.create(:render_type, :bar_chart),
            result_type: FactoryBot.create(:result_type, :grouped_multiple_choice_responses)
          )
        )

        submission = survey.submissions.create!(
          submitted_at: Time.current,
          submitter: FactoryBot.create(:expert)
        )

        # Create answer for grouping question
        selected_grouping_answer_choice = group_question.answer_choices.first
        submission.answers.create!(
          question: group_question,
          selected_choices: [SelectedChoice.new(answer_choice: selected_grouping_answer_choice)]
        )

        # Create answer for base question
        selected_answer_choice = question.answer_choices.first
        submission.answers.create!(
          question:,
          selected_choices: [SelectedChoice.new(answer_choice: selected_answer_choice)]
        )

        expected_result = {
          chartType: 'bar',
          height: 600,
          labels: %w[label1 label2 label3],
          series: [
            { name: 'label1', data: [1, 0, 0] },
            { name: 'label2', data: [0, 0, 0] },
            { name: 'label3', data: [0, 0, 0] }
          ],
          toolbar: {
            show: false,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          }
        }

        result = GroupedMultipleChoiceResponseQuery.for(result_definition)

        assert_equal expected_result, result
      end
    end
  end
end

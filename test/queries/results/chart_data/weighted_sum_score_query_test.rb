# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class WeightedSumScoreQueryTest < ActiveSupport::TestCase
      test 'get WSG scores by user' do
        wsg_label = 'WSG Label'
        result_type = FactoryBot.create(:result_type, :weighted_sum_scores)
        user = FactoryBot.create(:expert)
        survey = FactoryBot.create(:survey, :published, :results_published)

        question = TestDataGenerator
                   .new
                   .create_weight_sum_grid_question(
                     survey,
                     score_label: wsg_label,
                     submitted_by: user
                   )

        result_definition = survey.result_definitions.create!(
          render_type: result_type.default_render_type,
          result_type:,
          question:
        )

        expected_result = {
          chartType: 'area',
          height: 600,
          labels: [user.display_id],
          max: 85.3,
          mean: 85.3,
          min: 85.3,
          stdDev: 0,
          toolbar: {
            show: false,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          },
          values: [85.3],
          yAxisLabel: 'WSG Label'
        }

        result = WeightedSumScoreQuery.for(result_definition)

        assert_equal expected_result, result
      end
    end
  end
end

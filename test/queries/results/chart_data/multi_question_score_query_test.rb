# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class MultiQuestionScoreQueryQueryTest < ActiveSupport::TestCase
      test 'multi question score' do
        expert = FactoryBot.create(:expert)
        survey = FactoryBot.create(:survey)

        question1 = FactoryBot.create(:radio, survey:, weight: BigDecimal('0.4'))
        choice1 = FactoryBot.create(:answer_choice, question: question1, value: BigDecimal('0.25'))
        _choice2 = FactoryBot.create(:answer_choice, question: question1, value: BigDecimal('0.75'))

        question2 = FactoryBot.create(:radio, survey:, weight: BigDecimal('0.6'))
        choice3 = FactoryBot.create(:answer_choice, question: question2, value: BigDecimal('0.33'))
        _choice4 = FactoryBot.create(:answer_choice, question: question2, value: BigDecimal('0.67'))

        submission = survey.submissions.create!(submitter: expert)
        answer1 = submission.answers.create!(question: question1)
        answer1.answer_choices << choice1
        answer2 = submission.answers.create!(question: question2)
        answer2.answer_choices << choice3

        result_definition = FactoryBot.create(:result_definition, :multi_question_score)
        result_definition.questions << question1
        result_definition.questions << question2

        result = MultiQuestionScoreQuery.for(result_definition)

        assert_equal 'area', result[:chartType]
        assert_equal 600, result[:height]
        assert_equal [expert.display_id], result[:labels]
        assert_equal [BigDecimal('0.298')], result[:values]
        assert_equal 'Scores', result[:yAxisLabel]
      end

      test 'multi question score omits submissions with skips' do
        expert1 = FactoryBot.create(:expert)
        expert2 = FactoryBot.create(:expert)
        survey = FactoryBot.create(:survey)

        question1 = FactoryBot.create(:radio, survey:, weight: BigDecimal('0.4'))
        choice1 = FactoryBot.create(:answer_choice, question: question1, value: BigDecimal('0.25'))
        choice2 = FactoryBot.create(:answer_choice, question: question1, value: BigDecimal('0.75'))

        question2 = FactoryBot.create(:radio, survey:, weight: BigDecimal('0.6'))
        choice3 = FactoryBot.create(:answer_choice, question: question2, value: BigDecimal('0.33'))
        _choice4 = FactoryBot.create(:answer_choice, question: question2, value: BigDecimal('0.67'))

        complete_submission = survey.submissions.create!(submitter: expert1)
        answer1 = complete_submission.answers.create!(question: question1)
        answer1.answer_choices << choice1
        answer2 = complete_submission.answers.create!(question: question2)
        answer2.answer_choices << choice3

        partial_submission = survey.submissions.create!(submitter: expert2)
        answer3 = partial_submission.answers.create!(question: question1)
        answer3.answer_choices << choice2
        _answer4 = partial_submission.answers.create!(question: question1)

        result_definition = FactoryBot.create(:result_definition, :multi_question_score)
        result_definition.questions << question1
        result_definition.questions << question2

        result = MultiQuestionScoreQuery.for(result_definition)

        assert_equal 'area', result[:chartType]
        assert_equal 600, result[:height]
        assert_equal [expert1.display_id], result[:labels]
        assert_equal [BigDecimal('0.298')], result[:values]
        assert_equal 'Scores', result[:yAxisLabel]
      end
    end
  end
end

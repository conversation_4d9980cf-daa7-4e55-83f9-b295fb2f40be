# frozen_string_literal: true

require 'test_helper'

module Results
  module ChartData
    class MultipleChoiceResponseQueryTest < ActiveSupport::TestCase
      test 'single data set result' do
        survey = create(:survey, :general_participation, :results_published)
        question_type = Questions::Radio.new.class.name
        question_text = 'What is your fave color?'

        answer_choices = AnswerChoice.build_list(%w[green blue])

        question = survey.questions.create!(
          type: question_type,
          question_text:,
          answer_choices:,
          position: 1
        )

        answer_group = survey.answer_groups.create!(
          user: create(:expert),
          submitted_at: Time.zone.now
        )

        answer_group.answers.create!(
          question:,
          selected_choices: [SelectedChoice.new(answer_choice: answer_choices[0])]
        )

        result_definition = create(
          :result_definition,
          :question,
          survey:,
          question:,
          render_type: FactoryBot.create(:render_type, :bar_chart)
        )

        expected_result = {
          chartType: 'bar',
          height: 600,
          labels: %w[green blue],
          series: [{ name: 'Answers', data: [1, 0] }],
          toolbar: {
            show: false,
            export: { width: 3000 },
            tools: {
              download: true,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
              pan: false,
              reset: false
            }
          }
        }

        result = MultipleChoiceResponseQuery.for(result_definition)

        assert_equal expected_result, result
      end
    end
  end
end

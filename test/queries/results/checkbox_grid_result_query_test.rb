# frozen_string_literal: true

require 'test_helper'

module Results
  class CheckboxGridResultQueryTest < ActiveSupport::TestCase
    test 'checkbox button grid question result' do
      survey = FactoryBot.create(:survey)
      question = FactoryBot.create(:grid_question, survey:)
      grid_structure = FactoryBot.create(
        :grid_structure,
        :checkbox,
        column_headers: %w[row1 row2 row3],
        row_headers: %w[col1 col2 col3],
        question:
      )
      grid_result_definition = FactoryBot.create(
        :result_definition,
        question:
      )

      submission1 = FactoryBot.create(:submission, :final, survey:)
      submission2 = FactoryBot.create(:submission, :final, survey:)

      answer1 = submission1.answers.create!(question:)
      answer1.update!(answer_text_on_1st_row: "1\r\n0\r\n1")
      answer1.update!(answer_text_on_2nd_row: "0\r\n1\r\n0")
      answer1.update!(answer_text_on_3rd_row: "1\r\n1\r\n1")

      answer2 = submission2.answers.create!(question:)
      answer2.update!(answer_text_on_1st_row: "0\r\n1\r\n0")
      answer2.update!(answer_text_on_2nd_row: "0\r\n0\r\n0")
      answer2.update!(answer_text_on_3rd_row: "0\r\n1\r\n0")

      row1_cells = [
        Results::GridResult::SingleValueCell.new(count: 1, total: 3),
        Results::GridResult::SingleValueCell.new(count: 1, total: 3),
        Results::GridResult::SingleValueCell.new(count: 1, total: 3)
      ]

      row2_cells = [
        Results::GridResult::SingleValueCell.new(count: 0, total: 1),
        Results::GridResult::SingleValueCell.new(count: 1, total: 1),
        Results::GridResult::SingleValueCell.new(count: 0, total: 1)
      ]

      row3_cells = [
        Results::GridResult::SingleValueCell.new(count: 1, total: 4),
        Results::GridResult::SingleValueCell.new(count: 2, total: 4),
        Results::GridResult::SingleValueCell.new(count: 1, total: 4)
      ]

      rows = [
        Results::GridResult::Row.new(
          name: question.row_headings[0],
          cells: row1_cells,
          total: 3
        ),
        Results::GridResult::Row.new(
          name: question.row_headings[1],
          cells: row2_cells,
          total: 1
        ),
        Results::GridResult::Row.new(
          name: question.row_headings[2],
          cells: row3_cells,
          total: 4
        )
      ]

      expected_result = Results::GridResult.new(
        column_names: question.column_headings,
        first_cell_text: grid_structure.first_cell_text,
        id: grid_result_definition.id,
        rows:
      )

      actual_result = CheckboxGridResultQuery.for(grid_result_definition)

      assert_equal expected_result, actual_result
    end
  end
end

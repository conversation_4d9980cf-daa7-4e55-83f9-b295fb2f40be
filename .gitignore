# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

/public/system
/public/sitemap.xml
/public/sitemap.xml.gz
/public/sitemaps

# Ignore bundler config.
/.bundle

# Ignore the default SQLite database.
**/db/*.sqlite3
**/db/*.sqlite3-journal

# Ignore all logfiles and tempfiles.
**/log/*.log
**/tmp
/*.log
**/*.log.*
*.dump
.idea

# Generated by RubyMine
**/.generators
**/.rakeTasks

# ds store file
.DS_Store

#swp files
*.swp

#development file used on Todd's C9 account
*.sublime-*

/config/database.yml
/.env
/.env.development.local
/.env.test.local
*~

node_modules
/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity

/storage

/config/credentials/development.key
/config/credentials/test.key
/config/credentials/staging.key
/config/credentials/production.key

/app/assets/builds/*
!/app/assets/builds/.keep

# Ignore Redis dump file
dump.rdb

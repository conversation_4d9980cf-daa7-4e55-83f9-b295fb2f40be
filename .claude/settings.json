{"includeCoAuthoredBy": false, "hooks": {"PostToolUse": [{"matcher": "Edit|Write", "hooks": [{"type": "command", "command": "FILE_PATH=$(jq -r '.tool_input.file_path // empty'); if [[ \"$FILE_PATH\" =~ \\.(rb|rake)$|Rakefile$|Gemfile$ ]]; then echo \"Running RuboCop on $FILE_PATH\"; bundle exec rubocop -A \"$FILE_PATH\"; fi"}]}]}, "permissions": {"allow": ["Bash(bundle:*)", "Bash(find:*)", "Bash(gh:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(rg:*)", "Bash(git:*)", "Bash(yarn:*)", "Bash(bin/rails db:migrate:*)", "Bash(bin/rails db:schema:*)", "Bash(bin/rails generate migration:*)", "Bash(bin/rails routes:*)", "Bash(bin/rails test:*)", "Bash(bin/rails zeitwerk:check:*)", "Bash(bin/rubocop:*)", "Bash(ruby:*)"], "deny": []}}
---
description: Updates Dependencies
model: claude-3-5-haiku-20241022
---
Your task is to update the dependencies in this application, and to make any fixes that are necessit    ated by the updates.

1. If not already on a branch called `update_deps`, create it, based off of `origin/main`.
2. Run the `bundle update` command to update the bundler Gemfile.lock file.
3. Run the `yarn upgrade` command to update the JavaScript dependencies
4. If rubocop was updated, run `bin/rubocop -A` to make any auto-corrections. If any errors remain, fix them manually.
5. Commit the changes with the subject line "Update dependencies", and include a summary of the changes in the commit message itself. Include which cops were fixed.
6. Push the changes to `origin`, and open a pull request detailing the changes.

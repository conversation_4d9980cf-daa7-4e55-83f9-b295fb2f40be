# frozen_string_literal: true

source 'https://rubygems.org'

ruby file: '.ruby-version'

gem 'aws-sdk-s3', '~> 1.199'
gem 'bcrypt', '~> 3.1.18'
gem 'bootsnap', '~> 1.18', require: false
gem 'browser', '~> 6.2'
gem 'cancancan', '~> 3.6.1'
gem 'cssbundling-rails', '~> 1.4'
gem 'csv', '~> 3.3'
gem 'delayed_job_active_record', '~> 4.1'
gem 'descriptive_statistics', '~> 2.5.1', require: 'descriptive_statistics/safe'
gem 'docx', '~> 0.10.0'
gem 'dotenv-rails', '~> 3.1.7'
gem 'elasticsearch', '~> 7.13.3', '< 7.14' # Pinned due to licensing issues in >= 7.14
gem 'elasticsearch-dsl', '~> 0.1.9'
gem 'elasticsearch-model', '~> 7.1.1', '< 7.2'
gem 'elasticsearch-rails', '~> 7.1.1', '< 7.2'
gem 'faker', require: false # for anonymizing staging data
gem 'image_processing', '~> 1.13'
gem 'jbuilder', '~> 2.14.1'
gem 'jsbundling-rails', '~> 1.3'
gem 'kaminari', '~> 1.2.1'
gem 'lograge', '~> 0.14.0'
gem 'MailchimpMarketing', '~> 3.0'
gem 'matrix', '~> 0.4.3'
gem 'mini_magick', '~> 5.3'
gem 'mini_mime', '~> 1.1.5'
gem 'ostruct'
gem 'pdf-reader', '~> 2.14'
gem 'pg', '~> 1.6'
gem 'postmark-rails', '~> 0.22.1'
gem 'public_suffix', '~> 6.0.2'
gem 'puma', '~> 7.0.3' # Use "--with-cflags='-D PUMA_QUERY_STRING_MAX_LENGTH=20480'" when building to allow longer query strings
gem 'rack-attack', '~> 6.7.0'
gem 'rack-cors', '~> 3.0.0'
gem 'rails', '~> 8.0'
gem 'redis', '~> 5.4'
gem 'rollbar', '~> 3.6.0'
gem 'rubyzip', '~> 2.4.0'
gem 'sassc-rails', '~> 2.1.2'
gem 'scenic', '~> 1.6'
gem 'simple_form', '~> 5.3'
gem 'sitemap_generator', '~> 6.3'
gem 'sprockets-rails', '~> 3.5.2'
gem 'stimulus-rails', '~> 1.3'
gem 'terminal-table', '~> 4.0', require: false
gem 'turbo-rails', '~> 2.0.0'
gem 'view_component', '~> 3.22'

# Gems for report generation
gem 'ferrum', '~> 0.17.1'
gem 'prawn', '~> 2.5.0'
gem 'prawn-svg', '~> 0.37.0'
gem 'prawn-table', '~> 0.2.2'

# DEPRECATED GEMS
# The use of these gems is deprecated. All of these are slated for removal.
# Do not expand their use.
#
# bootstrap-wysihtml5-rails has a security vulnerability (CVE-2019-19919), but
# a fix is likely not forthcoming since the gem has not received a release since
# 2016! This editor is only used in a few places, and is in the process of being
# replaced by Trix. For now it is being left in place and ignored by bundler-audit.
gem 'bootstrap-wysihtml5-rails', '~> 0.3.3'
gem 'ejs', '1.1.1' # Replace with server-side rendering
gem 'jquery-rails', '4.6.0' # Remove jQuery eventually
gem 'jquery-ui-rails', '8.0.0' # Move to webpack, remove jQuery eventually?

group :production do
  gem 'rack-timeout', '~> 0.7.0'
end

group :development do
  # Assume safe update unless proven otherwise
  # In general the ramifications of a broken dev
  # dependency is small
  gem 'better_errors'
  gem 'binding_of_caller'
  gem 'launchy'
  gem 'letter_opener'
  gem 'lookbook'
end

group :development, :test do
  gem 'bundler-audit'
  gem 'debug'
  gem 'factory_bot_rails'
  gem 'listen'
  gem 'mutex_m' # Fix for https://youtrack.jetbrains.com/issue/RUBY-33799. Remove when RubyMine 2025.1 is released.
  gem 'rails-controller-testing'
  gem 'rubocop'
  gem 'rubocop-capybara'
  gem 'rubocop-factory_bot'
  gem 'rubocop-performance'
  gem 'rubocop-rails'
end

group :test do
  gem 'capybara'
  gem 'minitest-test_profile'
  gem 'mocha'
  gem 'selenium-webdriver'
  gem 'webmock', '~> 3.25.0'
end

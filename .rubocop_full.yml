plugins:
  - rubocop-capybara
  - rubocop-factory_bot
  - rubocop-performance
  - rubocop-rails

AllCops:
  NewCops: enable
  UseCache: true
  Exclude:
    - config.ru
    - bin/*
    - config/application.rb
    - config/boot.rb
    - config/environment.rb
    - config/environments/*.rb
    - config/initializers/assets.rb
    - config/initializers/backtrace_silencers.rb
    - config/initializers/content_security_policy.rb
    - config/initializers/cors.rb
    - config/initializers/filter_parameter_logging.rb
    - config/initializers/inflections.rb
    - config/initializers/permissions_policy.rb
    - config/initializers/simple_form.rb
    - config/initializers/simple_form_bootstrap.rb
    - config/puma.rb
    - db/schema.rb
    - db/migrate/*.rb
    - node_modules/**/*
    - script/set_geolocated_ips.rb
    - vendor/bundle/**/*

Rails:
  Enabled: true

Capybara/ClickLinkOrButtonStyle:
  EnforcedStyle: strict

Layout/EmptyLinesAroundAttributeAccessor:
  Enabled: true

Layout/MultilineHashKeyLineBreaks:
  Enabled: true

Layout/SpaceAroundMethodCallOperator:
  Enabled: true

Layout/SpaceAroundOperators:
  EnforcedStyleForExponentOperator: space

Lint/DeprecatedOpenSSLConstant:
  Enabled: true

Lint/MissingSuper:
  Exclude:
    - app/components/**/*.rb

Lint/RaiseException:
  Enabled: true

Lint/StructNewOverride:
  Enabled: true

Metrics/AbcSize:
  Exclude:
    - app/models/surveys/abilities/expert_ability.rb

Metrics/BlockLength:
  Max: 29
  AllowedMethods:
    - class_methods
    - included
  Exclude:
    - app/controllers/surveys/custom_results_controller.rb
    - config/**/*.rb
    - lib/tasks/**/*.rake
    - test/**/*.rb

# Bust up the big classes and ratchet this down over time
Metrics/ClassLength:
  Max: 300
  Exclude:
    - app/controllers/question_groups_controller.rb
    - app/models/invite.rb
    - app/models/question_group.rb
    - app/models/question.rb
    - app/models/ping.rb
    - app/models/user.rb
    - app/queries/results/select_grid_result_query.rb # It has a a looong query embedded in it
    - test/**/*.rb

# Ratchet this down over time
Metrics/MethodLength:
  Max: 59
  Exclude:
    - app/models/ability.rb # These abilities need to be broken up

Metrics/ModuleLength:
  Max: 100
  Exclude:
    - app/models/result_definition_container.rb
    - app/models/surveys/selection/applicant.rb

# This rule is kind of arbitrary and should be left to the developer
Metrics/ParameterLists:
  Enabled: false

# Hopefully this can be dropped over time
# 7 is the default
Metrics/PerceivedComplexity:
  Max: 10
  Exclude:
    - app/controllers/application_controller.rb
    - app/controllers/surveys/custom_results_controller.rb
    - app/controllers/question_groups_controller.rb
    - app/helpers/result_definitions_helper.rb
    - app/helpers/questions_helper.rb
    - app/models/answer.rb
    - app/models/ability.rb
    - app/models/surveys/abilities/expert_ability.rb
    - app/services/answer_group_form.rb

# Variable length and expressiveness are not generally related.
Naming/MethodParameterName:
  Enabled: false

Naming/PredicateMethod:
  AllowedMethods:
    - call
    - copy_to
    - radio_value
    - perform
    - save
    - submit
    - update
  AllowedPatterns:
    - ^extract_
    - ^parse_
  AllowBangMethods: true

# This is not smart enough to distinguish between ActiveSupport::TestCase
# test or others.
Rails/AssertNot:
  Enabled: false

Rails/Delegate:
  Exclude:
    - app/domain/**/*.rb
    - app/models/my_notifications.rb

# TODO: fix me.
Rails/EnvironmentVariableAccess:
  AllowReads: true

# This is not smart enough to distinguish between ActiveSupport::TestCase
# test or others.
Rails/RefuteMethods:
  Enabled: false

# update_all and its ilk are wonderful methods when used correctly
Rails/SkipsModelValidations:
  Enabled: false

# This is nice idea, but I'm not sure ...Time.current is an improvement over <=.
Rails/WhereRange:
  Enabled: false

Style/ClassAndModuleChildren:
  Enabled: false

# Because this is an app and not a library.
Style/Documentation:
  Enabled: false

Style/ExponentialNotation:
  Enabled: true

Style/GlobalVars:
  Exclude:
    - script/**/*.rb

Style/HashSyntax:
  Enabled: true
  EnforcedStyle: "ruby19"

Style/HashEachMethods:
  Enabled: true

Style/HashTransformKeys:
  Enabled: true

Style/HashTransformValues:
  Enabled: true

# Modifier and flow-control are not generally
# more readable than a one-line ii/unless body
Style/IfUnlessModifier:
  Enabled: false

# Allow one-liners, or separate lines
Style/MixinGrouping:
  Enabled: false

# Temporarily here because the new report uses it,
# but it will be removed soon.
Style/OpenStructUse:
  Enabled: false

Style/SafeNavigationChainLength:
  Enabled: false

Style/SlicingWithRange:
  Enabled: true

# frozen_string_literal: true

Rails.application.routes.draw do
  mount Lookbook::Engine, at: '/lookbook' if Rails.env.development?

  namespace :admin do
    resources :announcements, except: %i[new show]
    namespace :communications do
      resources :distribution_lists, only: %i[create destroy edit index update] do
        member do
          patch 'resume'
          patch 'suspend'
          patch 'send_preview'
        end
      end
      resources :subscriptions, only: %i[update destroy]

      root to: redirect('/admin/communications/distribution_lists', status: 302)
    end
    resources :contract_templates, only: %i[create destroy edit index update]
    resources :domains, only: :index
    resources :domain_lists, only: [] do
      patch 'domains', to: 'domain_lists#update_domains'
    end
    resources :expertises, only: %i[create destroy edit index update]
    namespace :experts do
      get 'unverified/:status_id', to: 'unverifieds#index', as: :unverified
      resources :unverifieds, only: %i[edit], controller: 'unverifieds' do
        collection do
          patch 'resolve_all/:verification_status_id', to: 'unverifieds#resolve_all', as: :resolve_all
        end
        patch 'resolve/:verification_status_id', to: 'unverifieds#resolve', as: :resolve
      end
    end
    resources :experts, only: %i[index show] do
      get 'cv'
      collection do
        get 'export', to: 'experts#export', format: 'csv'
        post 'async_export', to: 'experts#async_export'
        get 'cvs', to: 'experts#cv_export', format: 'zip'
        patch 'verification_settings', to: 'experts/verification_settings#update'
      end
      resources :notes, only: :create
      resource :verifications, only: :update, controller: 'experts/verifications'
    end
    resources :messages, only: %i[create]
    resources :unprocessable_cvs, only: %i[index update] do
      member do
        patch 'reprocess'
      end
    end
    namespace :moderation do
      post ':subject_type/:subject_id/hide',
           to: 'subjects#hide',
           as: :hide_subject
      post ':subject_type/:subject_id/unhide',
           to: 'subjects#unhide',
           as: :unhide_subject
    end
    namespace :moderation do
      resources :requests, only: [], controller: 'requests' do
        member do
          post 'resolve'
        end
        collection do
          post ':subject_type/:subject_id/resolve_all',
               to: 'requests#resolve_all',
               as: :resolve_all
          get ':subject_type/:subject_id',
              to: 'requests#show',
              as: :show_subject
        end
      end
      root 'requests#index'
    end
    resources :notes, only: :destroy
    resources :pings, only: %i[destroy index show] do
      patch 'blacklist', on: :member
      patch 'unblacklist', on: :member
      patch 'publish', on: :member
      patch 'open_voting', on: :member
      patch 'close_voting', on: :member
    end
    resources :profiles, only: %i[edit update] do
      scope module: 'profiles' do
        resource :cv, only: :destroy
      end
    end
    resources :results, only: :index
    resources :roles, only: %i[create index edit update]
    resources :scipis, module: 'scipis', only: [] do
      collection do
        resources :rapid_reviews, only: %i[new create], as: :scipis_rapid_reviews
      end
      patch 'contract_settings', to: 'contracts#settings'
      resources :auditor_options, only: :index
      resources :auditor_assignments, only: %i[create destroy]
      resources :contracts, only: %i[index show] do
        collection do
          post 'mark_all_sent'
          post 'mark_all_signed'
          post 'mark_all_unsent'
          post 'preview_all'
          post 'send_all'
        end
      end
      resources :messages do
        resources :replies, only: :create
        collection do
          post 'bulk_resolve'
          post 'bulk_unresolve'
        end
        member do
          post 'resolve'
          post 'unresolve'
        end
      end
      resources :pay_rates, only: :create
      resources :reports, only: %i[index create destroy]
      resources :rounds, only: %i[edit index update create new destroy], param: :position do
        collection do
          resources :scipi_rounds, only: :create
        end
      end
    end
    resources :scipolls, only: %i[create edit index new update] do
      resources :access_tokens, only: :index do
        collection do
          resources :resets, only: :create, controller: 'access_tokens/resets', as: :access_tokens_resets
        end
      end
    end
    resources :surveys do
      scope module: 'surveys' do
        resources :attachments, only: :index
        resources :clones, only: :create
        resource :frequency_report, only: :show
        resources :observers, only: :create do
          collection do
            get 'available'
          end
        end
        patch 'observer_settings', to: 'observers#settings'
        resources :panelists, only: %i[create update] do
          collection do
            get 'available'
          end
          scope module: 'panelists', as: :panelists do
            collection do
              resource 'export', only: :show
            end
          end
        end
        resources :participants, only: :show do
          scope module: 'participants', as: :participants do
            collection do
              resource :export, only: :show
              resources :reactivations, only: :create
              resources :suspensions, only: :create
            end
          end
          # TODO: extract note concern
          resource :notes, only: :create
        end
        resources :payments, only: [] do
          scope module: 'payments', as: :payments do
            collection do
              resource 'export', only: :show
            end
          end
        end
        resources :publications, only: %i[destroy create new]
        resources :questions, only: %i[create new edit update], shallow: true
        resources :results, only: %i[index edit update], shallow: true do
          resource :summary, only: %i[create destroy], controller: 'results/summary' do
            post :preview
          end
          collection do
            post :generate_summaries
          end
        end
        resources :result_exports, only: :index
        resources :participation_exports, only: :index

        namespace :selection do
          resources :applicants, only: [] do
            collection do
              post 'export', to: 'applicants#export'
              post 'export_cvs', to: 'cvs#export'
              post 'export_profiles', to: 'profiles#export'
            end
            resources :rejections, only: :create
          end
          resources :alternates, only: :create
          resources :approvals, only: :create
          resources :finalists, only: :create
          resources :rejections, only: :create
          resources :reversions, only: :create
          resource :settings, only: :update
          resources :audits, only: :index

          root 'applicants#index'
        end
        resources :submission_backups, only: %i[create destroy]
        resources :submissions, only: [] do
          collection do
            post 'clean_up'
            post 'complete'
            post 'revert'
          end
        end
      end
      resources :users, only: [] do
        resources :messages, only: :index
      end
    end
    resources :suggested_expertises, only: %i[destroy index]
    namespace :surveys do
      namespace :selection do
        resources :applicants, only: :destroy do
          resource :audit, only: %i[destroy show update]
        end
      end
      resources :answers, only: [] do
        resource :attachment, only: :show, controller: 'answer_attachments'
      end
      resources :observers, only: :destroy
      resources :questions, only: :destroy do
        resources :answer_attachments, only: :index
      end
      resources :submissions, only: %i[destroy show]
    end
    resources :users do
      resources :password_reset_requests, only: :create
    end
    resources :suspicious_users, only: %i[destroy index]

    root 'dashboard#index'
  end

  get 'dashboard', to: 'dashboard#index'

  # Auth and user lifecycle routes
  resources :access_tokens, only: :index
  resources :emails, only: [] do
    resources :email_confirmations, only: %i[show update create], param: :token, shallow: true
  end

  get 'password_reset_expired', to: 'password_resets#expired'
  get 'login', to: 'sessions#new'
  delete 'log_out', to: 'sessions#destroy'
  resource :password_reset_request, only: :create do
    get 'confirm'
  end
  resource :communication_preferences, only: %i[show update], controller: 'communications/preferences'
  resource :notification_preferences, only: :update, controller: 'communications/notification_preferences'
  namespace :communications do
    resources :subscriptions, only: [] do
      member do
        post 'resubscribe'
        post 'unsubscribe'
      end
    end
  end

  get '/lists/:list_token/subscriptions/:subscription_token',
      to: 'public/subscriptions#show',
      as: :public_subscription
  post '/lists/:list_token/subscriptions/:subscription_token',
       to: 'public/subscriptions#create',
       as: :public_subscribe
  delete '/lists/:list_token/subscriptions/:subscription_token',
         to: 'public/subscriptions#destroy',
         as: :public_unsubscribe

  namespace :moderation do
    resources :text_scans, only: :create
  end

  resources :notifications, only: :index
  namespace :notifications do
    resources :subscriptions, only: :index
  end

  post 'unsubscribe/:token', to: 'unsubscribes#create', as: :unsubscribe

  resource :profile, only: %i[edit update] do
    resources :certifications,
              only: :destroy,
              controller: 'profiles/certifications'
    resource :cv, only: :destroy, controller: 'profiles/cvs'
    resources :degrees, only: :destroy, controller: 'profiles/degrees'
    resources :employment_history_items,
              only: :destroy,
              controller: 'profiles/employment_history_items'
    scope module: 'profiles' do
      resource :orcid, only: :new
    end
  end
  get 'profile/orcid/authentication_complete', to: 'profiles/orcids#authentication_complete', as: :orcid_redirect
  get 'profiles/display_name_check', to: 'profiles/display_names#check'

  get 'password_reset/:token', to: 'password_resets#show', as: :password_reset
  patch 'password_reset/:token', to: 'password_resets#update'
  get 'reset_password', to: 'password_reset_requests#new'
  resources :sessions, only: %i[create] do
    post 'set_display', to: 'sessions#set_display'
  end
  resource :settings, only: :edit do
    patch 'update_email', to: 'settings#update_email'
    patch 'update_password', to: 'settings#update_password'
    patch 'update_time_zone', to: 'settings#update_time_zone'
  end
  resources :sign_ups, only: %i[create new]
  get 'claim_account', to: 'sign_ups#claim_account'
  # End auth and user lifecycle routes

  concern :moderatable do
    resources :moderation_requests, only: %i[create new]
  end

  get 'feeds/:branding', to: 'feeds#index', as: :feeds, constraints: { branding: /(announcements|pings|scipolls|scipis|featured)/ }

  scope shallow_path: 'pings', shallow_prefix: 'pings' do
    resources :pings, only: %i[create index new show edit update], concerns: :moderatable do
      scope module: 'pings' do
        resources :answers, only: :create, shallow: true do
          resource :acceptance, only: %i[create destroy]
          resources :answers, only: %i[edit update]
          resource :downvote, only: :create, to: 'votes#downvote'
          resource :upvote, only: :create, to: 'votes#upvote'
        end
        resources :subscriptions, only: %i[destroy show], shallow: true, param: :token do
          member do
            patch :resubscribe
          end
        end
        post 'subscribe', to: 'subscriptions#create'
        post 'unsubscribe', to: 'subscriptions#unsubscribe'
      end
      resources :answers, only: [], shallow: true, concerns: :moderatable
    end
  end

  resources :scipolls, only: %i[index show], controller: 'scipis', defaults: { branding: ENV.fetch('SCIPOLL_BRANDING_ID', 2) }
  resources :surveys, only: %i[index show], controller: 'scipis', defaults: { branding: ENV.fetch('LEGACY_BRANDING_ID', 3) }

  get 'scipolls/:question_group_id/prefill/:answer_choice_id', to: 'answer_groups#prefill', as: :scipoll_prefill

  scope shallow_path: 'scipis', shallow_prefix: 'scipis', defaults: { branding: ENV.fetch('SCIPI_BRANDING_ID', 1) } do
    resources :scipis, only: %i[index show] do
      get 'observe', on: :member, to: 'scipis#observe'
      scope module: 'scipis' do
        resources :answers, only: [], shallow: true do
          scope module: 'answers' do
            resource :attachment, only: :show
          end
        end
        resources :applications, only: %i[create update]
        resources :messages, only: %i[show new create] do
          resources :replies, only: :create
        end

        resource :confidentiality_agreement, only: :create
        resource :contract_signature, only: :new
        resource :contract, only: :show do
          resource :embed_requests, only: :create
          get :completed, to: 'contracts#completed_contract'
          get :redirect_target
        end

        resource :report, only: :show do
          resource :data, only: :show, to: 'reports#data'
        end

        resources :results, only: [], param: :signed_id do
          resource :chart, only: :show, controller: 'result_charts' do
            get :data, on: :member
          end
        end
      end
    end
  end

  namespace :sign_now do
    post 'webhooks/document_complete'
  end

  namespace :webhooks do
    resources :postmark_bounces, only: :create
  end

  # (Almost) Everything below here is legacy and should be removed or
  # *deliberately* moved up as part of the SciPinion 2.0 project.
  namespace :survey_builder do
    resources :questions, only: :update
    resources :result_definitions, only: %i[destroy edit update] do
      member do
        patch 'move', to: 'result_definitions#move'
      end
    end
    resources :result_sections, only: %i[destroy edit update] do
      resources :result_definitions, only: %i[new create]
      member do
        patch 'move', to: 'result_sections#move'
      end
    end
    resources :question_sections, only: %i[edit update destroy] do
      post 'clone', on: :member, to: 'question_sections#clone'
    end

    resources :surveys, only: [] do
      resources :questions, only: :create
      resources :result_definitions, only: %i[create index new] do
        post 'sync', on: :collection
        post 'append', on: :collection
      end
      resources :result_sections, only: %i[create new]
      resources :question_sections, only: %i[new create]
    end
  end

  resources :custom_results, only: [] do
    resources :comments, only: :create
  end

  resources :question_groups, only: [] do
    resources :questions, only: %i[edit index new]
    resources :answer_groups, except: %i[destroy index] do
      get 'submitted', on: :member
    end
  end

  resources :questions, only: [] do
    resources :answers, controller: 'questions/answers', only: :create
    post 'score', to: 'questions/scores#show'
  end

  resources :results, only: [] do
    get 'chart_data', to: 'results/chart_data#show'
  end

  resources :surveys, only: [] do
    resources :custom_results, only: [] do
      get 'download', to: 'surveys/custom_results#download_attachment'
    end
    resources :results, only: %i[index show], controller: 'surveys/results'
    get 'frequency_report', to: 'surveys/frequency_report#index'
    scope module: 'surveys' do
      resources :review_materials, only: :show do
        get 'download_status'
      end
    end
  end

  root 'public#index'

  get 'welcome', to: 'public#welcome'

  resources :comments, only: :destroy
  resources :votes, only: %i[create destroy]

  get 'survey_preview/:question_group_id',
      to: 'answer_groups#survey_preview',
      as: :survey_preview

  post 'question_groups/:id/update_order',
       to: 'question_groups#update_order',
       as: :update_order

  # This helps the transition to the new /scipis/ url base and produces more predictable behavior
  # if someone modifies a results URL ( like /surveys/123/results/456/) in search of the landing page ( like /surveys/123/)
  get '/surveys/:survey_id/',
      to: redirect('/scipis/%{survey_id}')

  # People seem to be clicking links in old emails to get here.
  # This can likely be removed after a few months.
  get '/question_groups/:survey_id/applications/',
      to: redirect('/scipis/%{survey_id}')

  # Permanently redirect from the old custom results URL
  get '/surveys/:survey_id/custom_results/:result_id',
      to: redirect('/surveys/%{survey_id}/results/%{result_id}')
  get '/privacy', to: 'pages#privacy'
  get '/terms', to: 'pages#terms'
  # Permanently redirect old high-voltage static
  # pages to template based static pages.
  get '/pages/privacy', to: redirect('/privacy')
  get '/pages/terms', to: redirect('/terms')

  # ###################################################################################################
  # Deprecated routes - please explain why they are deprecated, and what to use instead, if applicable.
  # ###################################################################################################

  # The confirmations resources are still in use for confirming users' primary emails, but should be
  # migrated to use the newer EmailConfirmation model and controllers.
  resources :confirmations, only: %i[show update], param: :token do
    collection do
      post 'resend'
    end
  end

  # End deprecated routes #############################################################################
end

# frozen_string_literal: true

# Add Cloudflare IPs as trusted proxies
# See https://www.cloudflare.com/ips/
if Rails.env.production?
  Rails.application.config.action_dispatch.trusted_proxies = [
    IPAddr.new('************/20'),
    IPAddr.new('************/22'),
    IPAddr.new('************/22'),
    IPAddr.new('**********/22'),
    IPAddr.new('************/18'),
    IPAddr.new('*************/18'),
    IPAddr.new('************/20'),
    IPAddr.new('************/20'),
    IPAddr.new('*************/22'),
    IPAddr.new('************/17'),
    IPAddr.new('***********/15'),
    IPAddr.new('**********/13'),
    IPAddr.new('**********/14'),
    IPAddr.new('**********/13'),
    IPAddr.new('**********/22')
  ]
end

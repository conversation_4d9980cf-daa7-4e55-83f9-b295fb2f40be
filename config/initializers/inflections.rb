# Be sure to restart your server when you modify this file.

# Add new inflection rules using the following format. Inflections
# are locale specific, and you may define rules for as many different
# locales as you wish. All of these examples are active by default:
ActiveSupport::Inflector.inflections(:en) do |inflect|
  inflect.uncountable %w(contract_template_tags)
end

# These inflection rules are supported but not enabled by default:
ActiveSupport::Inflector.inflections(:en) do |inflect|
  inflect.acronym "CoI" # conflict of interest
  inflect.acronym 'DOI'
  inflect.acronym "HMAC"
  inflect.acronym "OAuth"
  inflect.acronym "ORCID"
  inflect.acronym "PDF"
  inflect.acronym "SciTrust"
end

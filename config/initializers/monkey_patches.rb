# frozen_string_literal: true

# Require all Ruby files in the core_extensions directory
Rails.root.glob('lib/core_extensions/**/*.rb').each { |f| require f }

Date.include(CoreExtensions::Date::TimeZoneHelpers)
ActiveSupport::TimeWithZone.include(CoreExtensions::TimeWithZone::TimeZoneHelpers)
Time.extend(CoreExtensions::Time::TimeZones)
Math.extend(CoreExtensions::Math)

ActiveSupport.on_load(:action_text_rich_text) do
  ActionText::Attachment.include(CoreExtensions::ActionText::Attachment)
  ActionText::Fragment.include(CoreExtensions::ActionText::Fragment)
end
ActiveSupport.on_load(:action_text_content) { include CoreExtensions::ActionText::Content }
ActiveSupport.on_load(:action_text_rich_text) { include CoreExtensions::ActionText::RichText }

Prawn::Table::Cell::Text.prepend(CoreExtensions::Prawn::Table::Cell::Text)

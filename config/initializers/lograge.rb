# frozen_string_literal: true

Rails.application.config.lograge.enabled = true
Rails.application.config.lograge.custom_options = lambda do |event|
  exceptions = %w[controller action format id]
  {
    user_id: Current.user&.id || 'none',
    user_agent: "\"#{Current.user_agent}\"",
    browser: "\"#{Current.browser&.name} #{Current.browser&.version}\"",
    platform: "\"#{Current.browser&.platform&.name}\"",
    device: "\"#{Current.browser&.device&.name}\"",
    params: event.payload[:params]&.except(*exceptions)
  }
end

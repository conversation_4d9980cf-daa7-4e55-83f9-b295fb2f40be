# frozen_string_literal: true

# See note in README about monkey-patching the request object
# https://github.com/rack/rack-attack?tab=readme-ov-file#how-it-works
module Rack
  class Attack
    class Request
      ##
      # Bots that we want to allow, but not at the rate they come at us, often once per second.
      def aggressive_bot?
        # These come from many IPs and hammer us.
        # See https://developers.facebook.com/docs/sharing/webmasters/web-crawlers/
        return true if user_agent.start_with?('meta-externalagent/1.1')
        # https://platform.openai.com/docs/bots/
        return true if user_agent.include?('GPTBot') && user_agent.include?('+https://openai.com/gptbot')

        false
      end

      ##
      # Catch all for bogus paths
      def bogus_path?
        bogus_favicon? || nonsense? || php?
      end

      ##
      # Not a favicon.ico that we support
      def bogus_favicon?
        # We get requests for lots of bogus favicon URLs like /home/<USER>
        # /3/favicon.ico, /view/img/favicon.ico. This is supposedly related to some
        # exploit with favicons. While we are not vulnerable, these create a lot of
        # noise. All these requests also come from China.
        # https://wordpress.org/support/topic/lots-of-favicon-ico-requests-from-china/
        #
        # This does *not* block good ol' /favicon.ico
        path.match?(%r{^/.*/favicon\.ico$})
      end

      ##
      # UAs we identified as being ill-mannered bots
      def naughty_bot?
        return true if user_agent.include?('SemrushBot') # Spammy SEO bot
        return true if user_agent.include?('Baiduspider')
        return true if user_agent.include?('AhrefsBot') # Hammers the pings upvote and downvote endpoints

        false
      end

      ##
      # Random paths that don't make sense for this app
      def nonsense?
        return true if path.in?(%w[/1 /2 /pingsA])

        nonsense_paths = %w[
          /.aws
          /.DS_Store
          /.env
          /.git
          /.git
          /.hg
          /.idea
          /.ssh
          /.vscode
          /administrator
          /gaocc/g445g
          /guu4/j445g
          /mysqladmin
          /mysql
          /myadmin
          /phpMyAdmin
          /phpmyadmin
          /sql
          /templates/system/css/system.css
        ]

        nonsense_paths.any? { |nonsense_path| path.include?(nonsense_path) }
      end

      def user_agent
        # Ensure user_agent is never nil
        super.to_s
      end

      ##
      # Is a .php (or WordPress) path?
      def php?
        return true if path.include?('wp-admin')
        return true if path.include?('wp-login')
        return true if path.include?('wp-content')
        return true if path.include?('wp-includes')

        path =~ /\.php$/
      end
    end
  end
end

# Blocks
# Things we've identified that should be blocked outright, either because
# they are repeatedly hammering the app, or are seemingly malicious.
#
# - Blocked IPs are things that do one or more of the following:
#   1. Flood the app with requestss
#   2. Make requests for paths that are not valid, that looking probing
#   3. Generate lots of 404s
# - Blocked Paths are just those that repeatedly show up with 404s, but from a variety of IPs
Rack::Attack.blocklist_ip('**************')
Rack::Attack.blocklist_ip('**************')
Rack::Attack.blocklist_ip('************') # https://www.abuseipdb.com/check/************
Rack::Attack.blocklist_ip('*************') # https://www.abuseipdb.com/check/*************

Rack::Attack.blocklist('Bogus paths', &:bogus_path?)
Rack::Attack.blocklist('Naughty bots', &:naughty_bot?)

Rack::Attack.throttle('Aggressive Bots', limit: 10, period: 1.hour) do |request|
  if request.aggressive_bot?
    request.user_agent
  end
end

# The default is 403 forbidden, but currently this is for
# stopping 404s from filling up from bad bots.
Rack::Attack.blocklisted_responder = lambda do |_request|
  [404, {}, ["Not Found\n"]]
end

ActiveSupport::Notifications.subscribe('blocklist.rack_attack') do |_name, _start, _finish, request_id, payload|
  request = payload[:request]
  match_type = request.env['rack.attack.match_type']

  if match_type == :blocklist
    request_data = {
      ip: request_id,
      path: request.path,
      query_string: request.query_string,
      request_id:,
      rule: request.env['rack.attack.matched'],
      user_agent: request.user_agent
    }

    Rails.logger.info "Rack::Attack request blocked: #{request_data}"
  end
end

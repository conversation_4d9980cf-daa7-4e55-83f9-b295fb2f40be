require_relative "boot"

require "rails/all"

require 'matrix' # Needed for WSG scores

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Scipinion
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 8.0

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    config.time_zone = "Mountain Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    # Use delayed_job as Active Job queuing backend
    config.active_job.queue_adapter = :delayed_job

    # :vips is the new default, but is not working at present.
    config.active_storage.variant_processor = :mini_magick

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('my', 'locales', '*.{rb,yml}').to_s]
    # config.i18n.default_locale = :de
    config.autoload_paths += %W[#{config.root}/lib]
    config.autoload_lib(ignore: %w(assets tasks templates))

    config.x.default_from_email = 'SciPinion <<EMAIL>>'

    # See config/initializers/rack_attack.rb
    config.middleware.use Rack::Attack

    # See https://discuss.rubyonrails.org/t/cve-2022-32224-possible-rce-escalation-bug-with-serialized-columns-in-active-record/81017
    config.active_record.yaml_column_permitted_classes = [Symbol]

    if ENV['SCIPINION_APP_ENV'].present?
      Rails.application.config.credentials.content_path = Rails.root.join("config/credentials/#{ENV['SCIPINION_APP_ENV']}.yml.enc")
    end

    # This is the old default name but is better than the new one, 'default'
    Rails.application.config.action_mailer.deliver_later_queue_name = 'mailers'

    config.autoload_once_paths += Dir[Rails.root.join("app/serializers")]
  end
end

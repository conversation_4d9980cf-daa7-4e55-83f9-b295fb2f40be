# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more, please read the Rails Internationalization guide
# available at http://guides.rubyonrails.org/i18n.html.

en:
  alerts:
    profiles:
      profile_incomplete_html: |
        <b>Your profile is incomplete.</b> Go to <a href="%{url}">your profile</a> page and complete your profile.
      cv_stale_html: |
        <b>You have not updated your CV since %{cv_date}.</b>  Go to <a href="%{url}">your profile</a> page to update your CV.
      cv_missing_html: |
        <b>You have not uploaded a CV.</b>  Go to <a href="%{url}">your profile</a> page and complete your profile.
  activemodel:
    attributes:
      bulk_message:
        content: "Message"
        sent_from_address: "From"
    errors:
      messages:
        content_type: "%{filename} must be a valid file type (%{types})"
        too_big: "%{filename} must be smaller than %{max}"
      models:
        monitor:
          attributes:
            role:
              blank: "must be selected"
        session:
          cannot_authenticate: "Sorry, we cannot log you in with that email and password. Please try again."
        sign_up:
          attributes:
            email:
              blank: "An email address is required to sign up"
              invalid: "This email doesn't look right. It should look like '<EMAIL>'"
              taken: "This email already exists in our system. Perhaps you are trying to login?"
            password:
              blank: "A password is required to sign up"
              too_short: "Sorry, but this password is too short. It must be at least 8 characters"
  activerecord:
    attributes:
      pings:
        expertises: "Expertise Tags"
      question_group:
        expertises: "Expertise Tags"
    errors:
      messages:
        content_type: "%{filename} must be a valid file type (%{types})"
        too_big: "%{filename} must be smaller than %{max}"
      models:
        scipis/auditor:
          attributes:
            user:
              required: "must be selected"
        suggested_expertise:
          attributes:
            term:
              taken: '"%{value}" already exists'
        question_group:
          attributes:
            branding:
              required: 'must be selected'
        user:
          attributes:
            degrees:
              invalid: 'are not correct. Each degree must have a type (e.g. Ph.D.), a subject area and a graduation year'
  admin:
    communications:
      distribution_lists:
        index:
          buttons:
            create_list: "Create list"
        create:
          success: "A new list was created."
        edit:
          buttons:
            update: "Save changes"
            destroy: "Delete"
            suspend_delivery: "Suspend Delivery"
            resume_delivery: "Resume Delivery"
            send_preview: "Send Preview"
        update:
          success: "Your changes were saved."
        destroy:
          success: "This list has been deleted."
        suspend:
          success: "Delivery has been suspended"
        resume:
          success: "Delivery has been resumed"
        send_preview:
          success: "Preview sent to admin accounts"
          not_supported: "Preview emails are not supported for this list"
      subscriptions:
        destroy:
          success: "User successfully unsubscribed!"
        update:
          success: "User successfully resubscribed!"
    expertises:
      create:
        success: "\"%{expertise}\" was added"
      destroy:
        success: "\"%{expertise}\" was successfully deleted"
      update:
        success: "Expertise successfully updated"
    messages:
      index:
        blank_slate:
          heading: "No messages have been sent to this user."
    moderation:
      subjects:
        hide:
          success: "\"%{name}\" is now hidden"
        unhide:
          success: "\"%{name}\" is now visible"
    notes:
      create:
        success: "Note added."
      destroy:
        success: "Note deleted."
    observer_mailer:
      notify_existing_user:
        subject: "[SciPinion] You have been invited to observe %{scipi_name}"
      notify_new_user:
        subject: "[SciPinion] You have been invited to observe %{scipi_name}"
    pings:
      blacklist:
        success: "'%{ping_name}' is now Blacklisted and will NOT be featured on the site or in emails."
      close_voting:
        success: "Voting is now closed! Ping is in the REVIEW Stage"
      open_voting:
        success: "Voting is now open!"
      publish:
        success: "This Ping is now published, and open for answering"
      unblacklist:
        success: "'%{ping_name}' has been removed from the Blacklist and may now be featured on the site or in emails."
    profiles:
      update:
        failure: "Profile Update Failed"
        success: "Profile saved"
      cvs:
        destroy:
          success: "%{expert}'s CV was successfully deleted"
    scipis:
      contracts:
        index:
          page_name: "Contract Management"
          template_missing:
            title: "There is no contract template set for this SciPi."
          no_contracts:
            description: "There are no contracts to view at this time."
      pay_rates:
        create:
          success: "New rates set."
      rapid_reviews:
        create:
          success: "New rapid review created."
    suggested_expertises:
      destroy:
        success: '"%{suggestion}" was removed and will not re-appear'
      index:
        blank:
          description: |
            There are currently no suggested expertise to process. If this is because you have
            added or removed past suggestions, then just wait a week or so until recently updated
            profiles have been updated, and new suggestions will appear.
          heading: "There are no suggested expertise"
        explanation: |
          All of the terms below were extracted from the "Other Expertise" field of experts' profiles. Terms have to appear in 5 or
          more profiles to appear in the list. This list is updated periodically as experts update their profiles.
        instructions: |
          After you add a term as expertise, it automatically gets added to all experts' profiles with that term listed in 'Other Expertise'.
        blank_search:
          heading: There are no suggested expertise were found matching your search
          description: Try another search, and perhaps check your spelling.
    surveys:
      edit:
        submit: 'Update Survey'
        submit_and_publish: 'Update and Go to Publish Page'
      form:
        landing_content:
          hint: Appears on the survey landing page. Landing page is always public, but survey can be made 'unlisted' to reduce discoverability
        participant_instructions:
          hint: Appears on survey landing page to those authorized to take survey. If present, this will replace the default 'Application Approved' message
      new:
        submit: 'Create Survey'
      participant_table:
        button:
          participant_export: "Participants CSV"
        change_status_dropdown:
          reactivate: "Reactivate"
          toggle: "Status"
          suspend: "Suspend"
        change_submission_dropdown:
          make_drafts: "Revert to draft"
          submit: "Submit"
          toggle: "Submissions"
        contract_actions_dropdown:
          toggle: "Contracts"
          mark_unsent: "Mark as unsent"
          mark_sent: "Mark as sent (unsigned)"
          mark_signed: "Mark as signed"
          set_rate: "Set Rates..."
          request_signatures: "Request signature(s)"
          sign_now_preview: "Create preview(s) in Sign Now"
        no_panelists_heading: "This SciPi does not have panelists yet."
        no_panelists_description: "As applicants are approved, they will appear here."
        no_participants_heading: "This survey does not have participants yet."
        no_participants_description: "Experts will appear here once they submit their responses."
      show:
        button:
          panelist_export: "Panel Summary"
          payment_export: "QB Payment File"
      update:
        success: "Your changes were saved!"
      bulk_messages:
        create:
          success: "Your message \"%{subject}\" has been sent"
      notes:
        create:
          success: "Note added."
      panelists:
        create:
          success: "%{name} was added as %{status}"
        update:
          success: "%{panelist} has been updated"
      questions:
        create:
          success: "New question created successfully"
        destroy:
          success: "The question has been deleted."
        update:
          success: "The question has been updated."
      selection:
        audits:
          destroy:
            success: "Audit deleted"
          update:
            success: "Audit saved"
        applicants:
          index:
            export:
              toggle: "Export"
              profiles: "Profile Data"
              cvs: "CVs (Zip file)"
              selection_table: "Selection Table"
            reject_applicant_modal_button:
              label: "Reject..."
            remove_applicant_button:
              confirm: "Are you sure you want to remove %{name} from this SciPi?"
              label: "Remove"
            selection_closed:
              heading: "Selection is closed for this %{branding}"
              applicants_up_to_date_notice: "This means that applicants selection stats are not automatically updating. If you wish to re-enable automatic updates, ensure that the survey is published and 'selection closes on' date is in the future."
          destroy:
            success: "%{name} was removed from this SciPi"
        not_permitted: "Selection is not permitted for this survey"
        settings:
          update:
            success: "Settings saved!"
      participants:
        reactivations:
          create:
            success: "You successfully reactivate %{count} participants"
        suspensions:
          create:
            success: "You successfully suspended %{count} participants"
      submissions:
        complete:
          success:
            zero: "No drafts were submitted"
            one: "One draft was submitted"
            other: "%{count} drafts were submitted"
        revert:
          success:
            zero: "No completed submissions were reverted to drafts"
            one: "One completed submission was reverted to a draft"
            other: "%{count} completed submissions were reverted to drafts"
      publications:
        create:
          success:
            now: "This %{branding_name} was published."
            later: "This %{branding_name} is scheduled for publication on %{published_at}."
        destroy:
          success: "This %{branding_name} was reverted to a draft."
      rate_dialog:
        form:
          pay_rate:
            label: "New Rate"
            hint: "Sets a new rate for the currently selected panelists"
          submit: "Save rate"
        header: "Assign Custom Rates"
      results:
        update:
          success: "Result updated successfully"
        summarize:
          not_summarizable: "AI Summary not supported for this result type yet"
        summary:
          destroy:
            success: "Summary deleted successfully"
            no_summary: "No summary to delete"
    unprocessable_cvs:
      reprocess:
        success: "%{expert}'s CV was resubmitted for processing"
    users:
      update:
        success: "User has been updated."
      destroy:
        failure: "Account destruction failed."
    scipolls:
      new:
        submit: "Create SciPoll"
        submit_and_publish: "Create and Publish SciPoll"
      form:
        landing_content:
          hint: Appears on the SciPoll landing page. Landing page is always public.
        participant_instructions:
          hint: Appears on SciPoll landing page to those authorized to take survey.
        access_token_count:
          hint: "The number of access codes to generate for this SciPoll"
      create:
        success: "SciPoll was successfully created."
      edit:
        submit: "Save SciPoll"
        submit_and_publish: "Save, go to publication page..."
      update:
        success: "SciPoll was successfully updated."
    roles:
      update:
        success: "Your changes were saved."
      create:
        success: "A new role was created."
    access_tokens:
      resets:
        create:
          success: 'Access tokens have been reset.'
    contract_templates:
      create:
        success: "New template saved"
      form:
        signnow_field_definitions:
          hint_html: "The tags used to creating fields in SignNow. Must be JSON as shown in the SignNow API docs. <strong>This field is required.</strong>"
        template_file:
          hint_html: "A Word (.docx) template file with the correct placeholders. <strong>This field is required.</strong>"
      index:
        blank_slate:
          description: "There are no contract templates to view at this time. Add one using the form to the right."
      destroy:
        success: "Template deleted"
      update:
        success: "Template updated"
  answer_groups:
    review_materials:
      heading: "Review Materials"
      instructions: "Below are the materials you will need to review for this phase of the SciPi."
      optional_instructions: "Files marked \"optional\" are not required, but may provide additional, useful context."
  claude:
    instructions:
      system: |
        You are an API service, returning either HTML content or JSON content as explicitly requested. Critical requirements:
        1. If HTML mode, output must start with an HTML tag - no plain text allowed
        2. If HTML mode, only use simple HTML <p>, <b>, <i>, <ul>, and <li> tags
        3. If HTML mode, Zero text outside of HTML tags
        4. In all cases, No greetings, introductions, or meta-commentary, respond only with payload requested
        5. For errors: return only {error: "message"}
      result_summary: |
        We need to create a summary of expert responses to a scientific survey question. The question and responses are included below.
        Requirements:
        1. The summary should be an brief blurb formatted with HTML
        2. Focus on showing agreement and disagreement between experts
        3. Reminder: You are an API in HTML mode, not a conversation bot. 
        4. Return only simple HTML that can be inserted into a webpage directly
  claim_account:
    intro: "Claim your account: Engage with leading scientific peers, participate in meaningful discourse, and access compensated review panels."
  communications:
    preferences:
      update:
        success: 'Your preferences have been updated.'
      show:
        nav_item:
          comms_prefs: "Communication Preferences"
        save_button: "Save preferences"
    subscriptions:
      unsubscribe:
        success: "You have successfully unsubscribed."
      resubscribe:
        success: "You have successfully resubscribed."
    subscribe_button_component:
      button_text: 'Subscribe'
    unsubscribe_button_component:
      button_text: 'Unsubscribe'
    notification_preferences:
      update:
        success: "You notification preferences have been updated."
  confirmations:
    update:
      email_confirmed: "Thank you! Your email address has been confirmed."
    resend:
      success: "Confirmation email resent! Check your inbox (and spam folder) for %{email}."
    show:
      already_confirmed: "Your email has already been confirmed. There is nothing else to do."
  dashboard:
    card_notifications:
      unconfirmed_email_html: |
        Your account is <strong>unconfirmed</strong>. We sent an email to the address you signed up with. Please look for that email and click 'Confirm My Account'.
  email_confirmation_mailer:
    confirm_email:
      subject: "Please confirm your email address"
      subscription_reason: "you added an institutional email to your SciPinion profile"
  email_confirmations:
    update:
      email_confirmed: "Thank you! Your email address has been confirmed."
  email:
    reason_for_receiving: "You are receiving this email because %{reason}"
  errors:
    attributes:
      default_pay_rate:
        greater_than: "must be greater than $%{count}"
      pay_rate:
        greater_than: "must be greater than $%{count}"
      email:
        invalid: "should be in the format '<EMAIL>'"
        no_account: "This email is not associated with an account on our system."
    messages:
      after: "should be after '%{before_attr_name}'"
      invalid_json: "is not valid JSON"
      not_temporal: "does not appear to be a valid date"
      now_or_later: "should be equal to the current time or in the future"
  experts:
    mailer:
      export:
        subject: Your export is ready
  invite_mailer:
    subject: "You've been invited to participate in \"SciPi %{scipi_name}\""
  layouts:
    user_nav:
      account_nav:
        comms_prefs: "Communication Preferences"
  marketing:
    scipi:
      one_liner:
        default_html: "Apply for paid peer review panels where your expertise is needed."
        expert_html: "Apply for paid peer review panels where your expertise is needed."
        observer_html: "View the public results of peer review panels."
      tooltip_html: "A SciPi is a paid peer review panel. The name refers to a collection of <strong><em>Sci</em></strong>entific o<strong><em>Pi</em></strong>nions."
    scipoll:
      one_liner:
        default_html: "Volunteer your expertise in quick polls and debate with peers."
        expert_html: "Volunteer your expertise in quick polls and debate with peers."
        observer_html: "Analyze the input of the expert community from quick polls and debate."
      tooltip_html: "A <strong>SciPoll</strong> is a public survey where you can volunteer your expertise and debate with peers."
    survey:
      one_liner:
        default_html: ""
        expert_html: ""
        observer_html: ""
      tooltip_html: "Legacy Survey Archive"
    ping:
      one_liner:
        default_html: "Add to the collective wisdom and vote on the best answer."
        expert_html: "Add to the collective wisdom and vote on the best answer."
        observer_html: "Explore the collective wisdom of the world's experts."
      tooltip_html: "A <strong>Ping</strong> is a single science-based question. The question comes from a fellow SciPinion Expert who is seeking input from the expert community."
  messages:
    logged_out: "You have been logged out"
    unconfirmed_account: "Your account is unconfirmed. We sent an email to the address you signed up with. Please look for that email and click 'Confirm Account'"
  opportunities_mailer:
    opportunities:
      subject: "SciPinion Opportunities for %{month}"
      subscription_reason: "you are subscribed to '%{list_name}'"
  opportunities_newsletter:
    alerts:
      cv_missing: "You have not uploaded a CV."
      cv_stale: "You have not updated your CV since %{cv_date}."
      profile_incomplete: "Your profile is incomplete."
  opportunities_newsletter_mailer:
    opportunities:
      subject: "SciPinion Opportunities for %{month}"
      subscription_reason: "you are subscribed to '%{list_name}'"
  password_resets:
    update:
      success: "Success! Your password has been reset. You may log in."
  personal_details:
    update:
      success: "Your personal details have been updated."
  pings:
    notice:
      card_wrong_role:
        content_html: |
          <p class="mb-0">Your account is registered as an observer account. Only experts can post, answer and vote on Pings.</p>
    form:
      expertise_ids:
        hint: "Please tag this Ping with relevant areas of expertise (Min 1, Max 5). This will help the right experts find it."
    notification_mailer:
      answer_accepted:
        subject: "An Answer Has Been Accepted for %{ping_title}"
        subscription_reason: "you %{subscription_action} this Ping."
      voting_open:
        subject: "Voting is open for %{ping_title}"
        subscription_reason: "you %{subscription_action} this Ping."
    instructions:
      draft: 'Per-stage instructions will appear here'
      answered: 'Answer Accepted' # Not used
      open: 'Open' # Not used
      answer: 'During this stage, please submit your answer to the question posted above.  You may include text, tables, figures, and links in your answer as needed.  You may only provide one answer to this question.'
      vote: 'During this stage, please review the answers provided by other experts, and then up-vote the answer(s) that you feel do a good job of answering the question. You may up-vote as many answers are you like. You cannot up-vote your own answer.'
      review: 'SciPinion is reviewing the answers and votes, after which all answers will be posted to the website in order of the number of upvotes received (highest to lowest), and the cash prize will be awarded to the top answer provider(s). We will need an appropriate tax document (e.g., W9, W8-BEN) on file for you prior to issuing payment.'
      award: 'Winner of the contest has been decided.'
    show:
      hidden: "This Ping has been removed by SciPinion moderators"
      not_found: "Unable to locate the Ping you requested"
    answers:
      sort:
        toggle: "Sort"
        most_votes: "Accepted, Highest Scores first"
        newest_first: "Newest first"
  profiles:
    cvs:
      destroy:
        success: "Your CV was deleted."
    institutional_verifications:
      awaiting_review:
        status:
          html:
            one: |
              <div class="fs-5">Your email address is not associated with any known institutions in our database.
              However, our admins periodically review unknown email domains, and update our records. If it is your
              institutional email address, this page will reflect that once they do, and <strong>no further action is
              needed</strong> on your part.</div>
            other: |
              <div class="fs-5"> Your email addresses are not associated with any known institutions in our database.
              However, our admins periodically review unknown email domains, and update our records. However, if either
              is your institutional email address, this page will reflect that once they do, and <strong>no further
              action is needed</strong> on your part.</div>
      pending_completion:
        status:
          html:
            <div class="fs-5">
            The additional email address you entered (<span class="font-monospace">%{email}</span>) is from a known
            scientific institution, but it is unconfirmed. Please check your email for a confirmation link.
            </div>
      verification_needed:
        status:
          html:
            one: |
              <div class="fs-5">Your login email is from a known personal email provider.</div>
            other: |
              <div class="fs-5">Your email addresses are from known personal email providers.</div>
    update:
      success: "Thank you for updating your profile."
  public:
    subscriptions:
      create:
        success: "You have successfully resubscribed to '%{subscribable_name}'"
      destroy:
        success: "You have successfully unsubscribed from '%{subscribable_name}'"
      show:
        card_title: "Change your Subscription"
        title: "Subscription settings for %{subscribable_name}"
        resubscribe:
          instructions:
            part1: "If you'd like to resubscribe to '%{subscribable_name}' you can do so, by clicking the button below."
            part2: "After resubscribing we will continue to send notifications for this subscription to %{obfuscated_email}."
          button_text: "Resubscribe"
        unsubscribe:
          instructions:
            part1: "If you'd no longer like to receive notifications for '%{subscribable_name}', you can unsubscribe by clicking the button below."
            part2: "After unsubscribing we will no longer send emails for this subscription to %{obfuscated_email}."
  settings:
    update_email:
      success: "Success! Your email address is now set to %{email}. Please confirm your new address by clicking the link in the email we sent you."
  unauthorized:
    view_results:
      all: "No results are available for this survey."
    check_access_code:
      all: "The access code you entered is incorrect."
  simple_form:
    hints:
      expertise:
        publications_count: "Total number of publications or book chapters where you are a listed author"
        publications_first_last_count: "Number of publications or book chapters where you are listed as the FIRST or LAST author"
    labels:
      expertise:
        publications_count: "Number of Publications and Book Chapters"
        publications_first_last_count: "Number where first OR last author"
    placeholders:
      expertise:
        other_expertise: "Please describe"
  sign_ups:
    create:
      success: 'Welcome! Your account is currently pending. Please check your email for confirmation instructions. (Check your spam folder if you do not see the confirmation email soon.)'
    intro: 'Create your account and join one of the most exclusive networks of scientists in the world. Enjoy access to ask questions of your peers, participate in polls and apply for top tier paid peer review panels.'
  scipi:
    application:
      default_application_question: 'Briefly, provide 3-5 bullets why you are qualified for one of the panel positions.'
      incomplete_profile_instructions: 'If your profile is incomplete or outdated, you will be prompted to finalize your profile before your application is accepted for review. Please be as complete as possible and upload your full CV (with complete publication/report list) when prompted.'
  scipoll:
    prefill:
      login_encouragement: 'Please Log In or Sign Up to submit your SciPoll response'
      finish_reminder: 'Make sure to finish this survey to record your choice!'
  scipis:
    admins_mailer:
      question_round_opening_soon:
        subject: "Please Review: SciPi #%{scipi_id} Round %{round_number} is Opening Soon"
      debate_round_opening_soon:
        subject: "Please Review: SciPi #%{scipi_id} Round %{round_number} is Opening Soon"
      new_message_notification:
        subject: "SciPi #%{scipi_id} New Message from %{sender_name}: %{subject}"
      new_reply_notification:
        subject: "SciPi #%{scipi_id} New Reply from %{sender_name}: %{subject}"
      report_complete:
        subject: "SciPi #%{scipi_id} Report Generation Complete"
    applicants_mailer:
      new_message_notification:
        subject: "New Message for SciPi #%{scipi_id}: %{subject}"
        subscription_reason: "you are a panelist on %{scipi_name}."
      new_reply_notification:
        subject: "New Reply for SciPi #%{scipi_id}: %{subject}"
        subscription_reason: "you are a panelist on %{scipi_name}."
    applications:
      recruitment_closed: "We're sorry, but the application period for this SciPi is closed."
    contracts_mailer:
      contract_complete:
        subject: "SciPi #%{scipi_id}: Your Signed Contract is Attached"
        subscription_reason: "you are a panelist on %{scipi_name}."
      contract_ready:
        subject: "SciPi #%{scipi_id}: Your Contract is Ready"
        subscription_reason: "you are a panelist on %{scipi_name}."
    engagement_info:
      blinding:
        label: "Blinding"
      compensation:
        label: "Compensation"
      follow_up_work:
        label: "Follow-up work"
      level_of_effort:
        label: "Level of effort"
      number_of_panelists:
        label: "Number of panelists"
      review_format:
        label: "Review Format"
      review_period:
        details: "From %{start_date} until %{end_date}"
        disclaimer: "Dates are approximate and subject to change"
        label: "Review Period"
      travel:
        label: "Travel"
      work_description:
        label: "Work Description"
    panelists_mailer:
      debate_closing_reminder:
        subject: "Reminder: Round %{round_number} is closing soon for SciPi %{display_name}"
        subscription_reason: "you are a panelist on %{scipi_name}."
      debate_open:
        subject: "Round %{round_number} is open for SciPi %{display_name}"
        subscription_reason: "you are a panelist on %{scipi_name}."
      debate_summary:
        subject: "Round %{round_number} summary for SciPi %{display_name}"
        subscription_reason: "you are a panelist on %{scipi_name}."
      question_round_open:
        subject: "Round %{round_number} is open for SciPi %{display_name}"
        subscription_reason: "you are a panelist on %{scipi_name}."
        review_period_in_words:
          about_x_hours:
            one: approximately %{count} hour
            other: approximately %{count} hours
          about_x_months:
            one: approximately %{count} month
            other: approximately %{count} months
          about_x_years:
            one: approximately %{count} year
            other: approximately %{count} years
          almost_x_years:
            one: approximately %{count} year
            other: approximately %{count} years
          half_a_minute: half a minute
          less_than_x_seconds:
            one: less than %{count} second
            other: less than %{count} seconds
          less_than_x_minutes:
            one: less than a minute
            other: less than %{count} minutes
          over_x_years:
            one: over %{count} year
            other: over %{count} years
          x_seconds:
            one: "%{count} second"
            other: "%{count} seconds"
          x_minutes:
            one: "%{count} minute"
            other: "%{count} minutes"
          x_days:
            one: "approximately %{count} day"
            other: "approximately %{count} days"
          x_months:
            one: "approximately %{count} month"
            other: "approximately %{count} months"
          x_years:
            one: "approximately %{count} year"
            other: "approximately %{count} years"
      submission_receipt:
        subject: "SciPi #%{scipi_id}: Submission Received"
        subscription_reason: "you are a panelist on %{scipi_name}."
        final_message_html: |
          <p>Thank you for submitting your final responses to this review. Pending review for completeness and clarity we will initiate your payment.</p>
          <p><strong>Here's what happens next...</strong></p>
          <p>Your prompt payment is important to us. It is our policy is to pay our experts as quickly as possible, often before we have been paid by the review Sponsor.</p>
          <p>We rely upon a payment service provider, <a href='https://veem.com'>Veem</a>, which allows us to provide fast payments to experts all over the world. You will receive an email from Veem to accept the payment.</p>
          <p>Please note that in most cases payments from Veem take approximately 2-3 business days to complete once you have completed the setup of your Veem account. Your first payment can take a little longer due to the bank account verification process, but subsequent payments will be quicker. Mailed checks (available upon request for US recipients only) take longer (usually 5-10 business days) and are subject to variations in mail delivery and bank processing times.</p>
          <p>We appreciate your efforts and input on this panel, and hope to have an opportunity to work together again in the near future!</p>
        non_final_message_html: |
          <p>Thank you for submitting your responses to the charge questions for the current round.</p>
          <p>Please stand by as your fellow panelists finish up.</p>
          <p>We will send additional instructions at the start of the next round.</p>
    reminders:
      mailer:
        survey_closing:
          in_progress_message: |
            Our records show that you have begun, but not yet completed your review. Please allow yourself enough time to complete your review.
          in_progress_message_html: |
            <p>Our records show that you have begun, but not yet completed your review. Please allow yourself enough time to complete your review.</p>
          not_started_message: |
            Our records show that you have not yet started your review. Please allow yourself enough time to review the materials and answer your charge questions.
          not_started_message_html: |
            <p>Our records show that you have not yet started your review. Please allow yourself enough time to review the materials and answer your charge questions.</p>
          subject: "SciPi #%{scipi_id}: Closing Reminder"
          subscription_reason: "you are a panelist on %{scipi_name}."
        survey_midpoint:
          in_progress_message: |
            Our records show that you have begun, but not yet completed your review. Please allow yourself enough time to complete your review.
          in_progress_message_html: |
            <p>Our records show that you have begun, but not yet completed your review. Please allow yourself enough time to complete your review.</p>
          not_started_message: |
            Our records show that you have not yet started your review. Please allow yourself enough time to review the materials and answer your charge questions.
          not_started_message_html: |
            <p>Our records show that you have not yet started your review. Please allow yourself enough time to review the materials and answer your charge questions.</p>
          subject: "SciPi #%{scipi_id}: Midpoint Reminder"
          subscription_reason: "you are a panelist on %{scipi_name}."
    show:
      default_introduction_html: |
        <h3 class="alt">Welcome to this %{branding}.</h3>
        <p class="mb-0">If you are able to participate in the survey or view the results, those options will appear above.<p>
      default_instructions_html: |
        <h3 class="alt">Application Approved</h3>
        <p>Congratulations! You have been selected to participate in this survey.</p>
        <p class="mb-0">The survey administrator will contact you with next steps.</p>
  survey_user_mailer:
    send_message:
      applicant_subscription_reason: "you applied to %{scipi_name}."
      panelist_subscription_reason: "you are a panelist on %{scipi_name}."
  survey:
    landing:
      generic_introduction_html: |
        <h3 class="alt">Welcome to this %{branding}.</h3>
        <p class="mb-0">If you are able to participate in the survey or view the results, those options will appear above.<p>
      invite_pending_html: |
        <h3 class="alt">Application Pending</h3>
        <p>Thank you for applying to this opportunity. Your application is currently under review.</p>
        <p class="mb-0">If you have not already done so, please take a moment to <a href="%{edit_profile_path}">refresh your CV</a> (with full publication list) and profile information. Panel selection decisions will be guided by this information.  We will be making panel selection soon and will contact you in the near future.</p>
      invite_alternate_html: |
        <h3 class="alt">Alternate</h3>
        <p class="mb-0">Thank you very much for applying to this opportunity. Based on your expertise you are well qualified for this opportunity, and fall within the top 5% of the applications we received. For this reason, you have been identified as an alternate for this panel. If one of the selected panelists is unable to fulfill their duties, we will contact you to confirm your willingness to participate.</p>
      invite_incomplete_html: |
        <h3 class="alt">Application Incomplete</h3>
        <p>Your application has been received but is incomplete.</p>
        <p class="mb-0">Your application will not be reviewed unless you <a href="%{edit_profile_path}">upload a PDF file of your CV</a> (with full publication list) and complete your profile. Panel selection decisions will be guided by this information.  We will be making panel selection soon and will contact you in the near future.</p>
      invite_rejected_html: |
        <p>Thank you very much for applying to this opportunity. Unfortunately, you were not selected for this panel.</p>
        <p>We received a high volume of applications from highly qualified scientists to fill a relatively small number of expert panel positions. Panel members are selected based upon a consideration of various factors including expertise, and in some cases demographic factors (e.g., geographic location, sector of employment) are used and based on the preferences of the review sponsor. Our selection decision does not necessarily reflect upon your expertise, and in some cases may have occurred for other reasons (e.g., failure to upload a complete CV).</p>
        <p class="mb-0">Please check back with us for future opportunities. We continue to need highly qualified scientists such as yourself for expert panel opportunities to ensure that relevant resources are properly vetted prior to decision-making.</p>
  surveys:
    applicant_mailer:
      application_complete:
        subject: "Your application was received"
        subscription_reason: "you applied to %{survey_name}."
      application_incomplete:
        subject: "Your CV is Needed"
        subscription_reason: "you applied to %{survey_name}."
    recruitment_status_label_component:
      closed: Closed
      open: Open
      recruiting_until: "Recruiting Until: %{closes_on}"
  subscriptions_mailer:
    pings:
      subject: "Pings Activity Digest for %{day}"
      subscription_reason: "you are subscribed to '%{list_name}'"
  time:
    formats:
      # e.g. Thursday, May 30, 2024 at 01:22 PM (MDT)
      full: "%A, %B %-d, %Y at %I:%M %p (%Z)"
  terms:
    ai_adherence_html: |
      Please ensure that your contribution adheres to the <a href="%{ai_terms_path}" target="_blank">SciPinion AI Policy</a> and other <a href="%{terms_path}" target="_blank">Terms of Use</a>.
  user_mailer:
    confirmation_reminder:
      subject: "Reminder: Confirm Your SciPinion Account (it's quick)"
      products_intro:
        expert: "Confirm your account & get involved:"
        observer: "Confirm your account and access the expert's insights:"
    password_reset:
      subject: "[SciPinion] Please reset your password"
  access_tokens:
    already_used_access_token: "This invitation has already been used."
    invalid_access_token: "Your invitation is not valid. If you copy and pasted this link, please make sure you copied the entire link."

GEM
  remote: https://rubygems.org/
  specs:
    Ascii85 (2.0.1)
    MailchimpMarketing (3.0.80)
      excon (>= 0.76.0, < 1)
      json (~> 2.1, >= 2.1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    afm (1.0.0)
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1156.0)
    aws-sdk-core (3.232.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      bigdecimal
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.112.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.199.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.2.3)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    bootstrap-wysihtml5-rails (*******)
      railties (>= 3.0)
    browser (6.2.0)
    builder (3.3.0)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    cancancan (3.6.1)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    childprocess (5.1.0)
      logger (~> 1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.4)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.21.1)
      addressable
    cssbundling-rails (1.4.3)
      railties (>= 6.0.0)
    csv (3.3.5)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    delayed_job (4.1.13)
      activesupport (>= 3.0, < 9.0)
    delayed_job_active_record (4.1.11)
      activerecord (>= 3.0, < 9.0)
      delayed_job (>= 3.0, < 5)
    descriptive_statistics (2.5.1)
    docx (0.10.0)
      nokogiri (~> 1.13, >= 1.13.0)
      rubyzip (>= 2.0, < 4)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    drb (2.2.3)
    ejs (1.1.1)
    elasticsearch (7.13.3)
      elasticsearch-api (= 7.13.3)
      elasticsearch-transport (= 7.13.3)
    elasticsearch-api (7.13.3)
      multi_json
    elasticsearch-dsl (0.1.10)
    elasticsearch-model (7.1.1)
      activesupport (> 3)
      elasticsearch (> 1)
      hashie
    elasticsearch-rails (7.1.1)
    elasticsearch-transport (7.13.3)
      faraday (~> 1)
      multi_json
    erb (5.0.2)
    erubi (1.13.1)
    excon (0.112.0)
    factory_bot (6.5.5)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.1)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.1)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    ferrum (0.17.1)
      addressable (~> 2.5)
      base64 (~> 0.2)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.17.2)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.2.1)
    hashery (2.1.2)
    hashie (5.0.0)
    htmlbeautifier (1.4.3)
    htmlentities (4.3.4)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.14.1)
      actionview (>= 7.0.0)
      activesupport (>= 7.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-ui-rails (8.0.0)
      railties (>= 3.2.16)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.13.2)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    lograge (0.14.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lookbook (2.3.13)
      activemodel
      css_parser
      htmlbeautifier (~> 1.3)
      htmlentities (~> 4.3.4)
      marcel (~> 1.0)
      railties (>= 5.0)
      redcarpet (~> 3.5)
      rouge (>= 3.26, < 5.0)
      view_component (>= 2.0)
      yard (~> 0.9)
      zeitwerk (~> 2.5)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.3)
    method_source (1.1.0)
    mini_magick (5.3.1)
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    minitest-test_profile (0.2.2)
      minitest
    mocha (2.7.1)
      ruby2_keywords (>= 0.0.5)
    msgpack (1.8.0)
    multi_json (1.17.0)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    net-imap (0.5.10)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    ostruct (0.6.3)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pdf-reader (2.15.0)
      Ascii85 (>= 1.0, < 3.0, != 2.0.0)
      afm (>= 0.2.1, < 2)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pg (1.6.2)
    pg (1.6.2-arm64-darwin)
    pg (1.6.2-x86_64-darwin)
    pg (1.6.2-x86_64-linux)
    postmark (1.25.1)
      json
    postmark-rails (0.22.1)
      actionmailer (>= 3.0.0)
      postmark (>= 1.21.3, < 2.0)
    pp (0.6.2)
      prettyprint
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-svg (0.37.0)
      css_parser (~> 1.6)
      matrix (~> 0.4.2)
      prawn (>= 0.11.1, < 3)
      rexml (>= 3.3.9, < 4)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (7.0.3)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.2.1)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rack-timeout (0.7.0)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redcarpet (3.6.1)
    redis (5.4.1)
      redis-client (>= 0.22.0)
    redis-client (0.25.2)
      connection_pool
    regexp_parser (2.11.2)
    reline (0.6.2)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    rexml (3.4.3)
    rollbar (3.6.2)
    rouge (4.6.0)
    rubocop (1.80.2)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.46.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.46.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.26.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails (2.33.3)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby-vips (2.2.5)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scenic (1.9.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    securerandom (0.4.1)
    selenium-webdriver (4.35.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 4.0)
      websocket (~> 1.0)
    simple_form (5.3.1)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.4.0)
    tilt (2.6.1)
    timeout (0.4.3)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.5)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.37)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-24
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  MailchimpMarketing (~> 3.0)
  aws-sdk-s3 (~> 1.199)
  bcrypt (~> 3.1.18)
  better_errors
  binding_of_caller
  bootsnap (~> 1.18)
  bootstrap-wysihtml5-rails (~> 0.3.3)
  browser (~> 6.2)
  bundler-audit
  cancancan (~> 3.6.1)
  capybara
  cssbundling-rails (~> 1.4)
  csv (~> 3.3)
  debug
  delayed_job_active_record (~> 4.1)
  descriptive_statistics (~> 2.5.1)
  docx (~> 0.10.0)
  dotenv-rails (~> 3.1.7)
  ejs (= 1.1.1)
  elasticsearch (~> 7.13.3, < 7.14)
  elasticsearch-dsl (~> 0.1.9)
  elasticsearch-model (~> 7.1.1, < 7.2)
  elasticsearch-rails (~> 7.1.1, < 7.2)
  factory_bot_rails
  faker
  ferrum (~> 0.17.1)
  image_processing (~> 1.13)
  jbuilder (~> 2.14.1)
  jquery-rails (= 4.6.0)
  jquery-ui-rails (= 8.0.0)
  jsbundling-rails (~> 1.3)
  kaminari (~> 1.2.1)
  launchy
  letter_opener
  listen
  lograge (~> 0.14.0)
  lookbook
  matrix (~> 0.4.3)
  mini_magick (~> 5.3)
  mini_mime (~> 1.1.5)
  minitest-test_profile
  mocha
  mutex_m
  ostruct
  pdf-reader (~> 2.14)
  pg (~> 1.6)
  postmark-rails (~> 0.22.1)
  prawn (~> 2.5.0)
  prawn-svg (~> 0.37.0)
  prawn-table (~> 0.2.2)
  public_suffix (~> 6.0.2)
  puma (~> 7.0.3)
  rack-attack (~> 6.7.0)
  rack-cors (~> 3.0.0)
  rack-timeout (~> 0.7.0)
  rails (~> 8.0)
  rails-controller-testing
  redis (~> 5.4)
  rollbar (~> 3.6.0)
  rubocop
  rubocop-capybara
  rubocop-factory_bot
  rubocop-performance
  rubocop-rails
  rubyzip (~> 2.4.0)
  sassc-rails (~> 2.1.2)
  scenic (~> 1.6)
  selenium-webdriver
  simple_form (~> 5.3)
  sitemap_generator (~> 6.3)
  sprockets-rails (~> 3.5.2)
  stimulus-rails (~> 1.3)
  terminal-table (~> 4.0)
  turbo-rails (~> 2.0.0)
  view_component (~> 3.22)
  webmock (~> 3.25.0)

RUBY VERSION
   ruby 3.4.5p51

BUNDLED WITH
   2.7.1

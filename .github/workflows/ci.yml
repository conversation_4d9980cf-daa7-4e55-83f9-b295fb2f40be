name: SciPinion CI
on: push
jobs:
  test:
    name: Linters and base tests
    runs-on: ubuntu-latest
    env:
      AWS_REGION: testregion
      AWS_ACCESS_KEY_ID: testkey
      AWS_SECRET_ACCESS_KEY: testsecret
      BUNDLE_JOBS: 4
      BUNDLE_RETRY: 3
      BUNDLE_WITHOUT: development
      CI: true
      DATABASE_URL: "postgres://rails:password@localhost:5432/rails_test"
      MAILCHIMP_API_SERVER: dummyvalue
      MAILCHIMP_DEFAULT_GROUP_IDS: dummyvalue
      MAILCHIMP_MASTER_AUDIENCE_LIST_ID: dummyvalue
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      RAILS_ENV: test
      RAILS_MASTER_KEY: 1341c158c154d016771e88a3dd3b208c
      REDIS_HOST: redis
      REDIS_PORT: 6379
      SCIPINION_ACTIVE_STORAGE_S3_BUCKET: dummyvalue
      SELENIUM_TIMEOUT: 120
    services:
      postgres:
        image: postgres:16-alpine
        ports:
          - "5432:5432"
        env:
          POSTGRES_DB: rails_test
          POSTGRES_USER: rails
          POSTGRES_PASSWORD: password
      redis:
        image: redis
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v5
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true
      - run: bin/bundler-audit --update
      - uses: actions/setup-node@v5
        with:
          node-version-file: '.nvmrc'
          cache: 'yarn'
      - run: yarn install --frozen-lockfile
      - name: Run linters
        run: |
          bin/rubocop --parallel --config .rubocop.yml
          yarn prettier app/javascript --check
          yarn eslint
      - run: bin/rails db:schema:load
      - name: Check Zeitwerk compatibility
        run: bin/rails zeitwerk:check
      - run: yarn test
      - name: Run base Rails tests
        run: |
          bin/rails test

  system_tests:
    name: System Tests
    runs-on: ubuntu-latest
    env:
      AWS_REGION: testregion
      AWS_ACCESS_KEY_ID: testkey
      AWS_SECRET_ACCESS_KEY: testsecret
      BUNDLE_JOBS: 4
      BUNDLE_RETRY: 3
      BUNDLE_WITHOUT: development
      CI: true
      DATABASE_URL: "postgres://rails:password@localhost:5432/rails_test"
      MAILCHIMP_API_SERVER: dummyvalue
      MAILCHIMP_DEFAULT_GROUP_IDS: dummyvalue
      MAILCHIMP_MASTER_AUDIENCE_LIST_ID: dummyvalue
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      RAILS_ENV: test
      RAILS_MASTER_KEY: 1341c158c154d016771e88a3dd3b208c
      REDIS_HOST: redis
      REDIS_PORT: 6379
      SCIPINION_ACTIVE_STORAGE_S3_BUCKET: dummyvalue
      SELENIUM_TIMEOUT: 120
    services:
      postgres:
        image: postgres:16-alpine
        ports:
          - "5432:5432"
        env:
          POSTGRES_DB: rails_test
          POSTGRES_USER: rails
          POSTGRES_PASSWORD: password
      redis:
        image: redis
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v5
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: .ruby-version
          bundler-cache: true
      - uses: actions/setup-node@v5
        with:
          node-version-file: '.nvmrc'
          cache: 'yarn'
      - run: yarn install --frozen-lockfile
      - run: bin/rails db:schema:load
      - name: Run System Tests
        id: 'system-tests'
        run: bin/rails test:system
      - name: Upload failing screenshots
        if: failure() && steps.system-tests.outcome == 'failure'
        uses: actions/upload-artifact@v4
        with:
          name: failing-screenshots
          path: ${{ github.workspace }}/tmp/screenshots/
          retention-days: 5

# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2022-03-21 14:46:21 UTC using RuboCop version 1.26.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
# Configuration parameters: AllowSafeAssignment.
Lint/AssignmentInCondition:
  Exclude:
    - 'app/services/answer_group_form.rb'

# Offense count: 1
Lint/IneffectiveAccessModifier:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 1
Lint/ReturnInVoidContext:
  Exclude:
    - 'app/models/ability.rb'

# Offense count: 2
Lint/UselessAssignment:
  Exclude:
    - 'app/models/questions/date.rb'
    - 'app/services/answer_group_form.rb'

# Offense count: 85
# Configuration parameters: IgnoredMethods, CountRepeatedAttributes.
Metrics/AbcSize:
  Max: 45

# Offense count: 20
# Configuration parameters: IgnoredMethods.
Metrics/CyclomaticComplexity:
  Max: 21

Metrics/ModuleLength:
  Exclude:
    - 'app/models/scipi/rapid_review.rb'
    - 'app/models/result_definition_container.rb'

# Offense count: 5
Naming/AccessorMethodName:
  Exclude:
    - 'app/models/question_group.rb'
    - 'app/services/answer_group_form.rb'

# Migrate or remove these controller tests
Rails/ActionControllerTestCase:
  Exclude:
    - 'test/controllers/answer_groups_controller_test.rb'
    - 'test/controllers/application_controller_test.rb'
    - 'test/controllers/invites_controller_test.rb'
    - 'test/controllers/question_groups_controller_test.rb'
    - 'test/controllers/questions/answers_controller_test.rb'
    - 'test/controllers/questions_controller_test.rb'
    - 'test/controllers/survey_builder/questions_controller_test.rb'
    - 'test/controllers/surveys/frequency_report_controller_test.rb'
    - 'test/controllers/surveys/messages_controller_test.rb'
    - 'test/controllers/votes_controller_test.rb'

# Offense count: 20
# Configuration parameters: Include.
# Include: app/models/**/*.rb
Rails/HasManyOrHasOneDependent:
  Exclude:
    - 'app/models/comment.rb'
    - 'app/models/message_template.rb'
    - 'app/models/result_definition_render_type.rb'
    - 'app/models/result_definition_type.rb'
    - 'app/models/user.rb'

# Migrate this eventually
Rails/I18nLocaleTexts:
  Enabled: false

# Offense count: 6
Rails/OutputSafety:
  Exclude:
    - 'app/controllers/answer_groups_controller.rb'
    - 'app/helpers/report_helper.rb'
    - 'app/services/answer_group_form.rb'

# Offense count: 2
# This cop supports safe auto-correction (--auto-correct).
Style/CommentedKeyword:
  Exclude:
    - 'app/models/invite.rb'

# Offense count: 57
# Configuration parameters: MinBodyLength.
Style/GuardClause:
  Enabled: false

# Offense count: 3
# This cop supports safe auto-correction (--auto-correct).
# Configuration parameters: AllowIfModifier.
Style/IfInsideElse:
  Exclude:
    - 'app/models/invite.rb'

# Offense count: 1
# This cop supports safe auto-correction (--auto-correct).
# Configuration parameters: AllowMethodComparison.
Style/MultipleComparison:
  Exclude:
    - 'app/helpers/grid_helper.rb'

# Offense count: 178
# This cop supports safe auto-correction (--auto-correct).
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 1392
